'use server';
import { z } from 'zod';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioLiquidacoes } from '@/components/relatorios/relatorioLiquidacoes';
import { gerarRelatorioLiquidacoesNaoProcessados } from '@/lib/database/relatorios/liquidacoes';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permisso<PERSON> } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function ImprimirLiquidacoesNaoProcessadosPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_LIQUIDACAO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const currSearchParams = await searchParams;
  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }

  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();

  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }

  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const params = {
    exercicio: currSearchParams.exercicio
      ? parseInt(currSearchParams.exercicio as string)
      : result.exercicio,
    idSecretaria: currSearchParams.secretaria
      ? parseInt(currSearchParams.secretaria as string)
      : undefined,
    idDepartamento: currSearchParams.departamento
      ? parseInt(currSearchParams.departamento as string)
      : undefined,
    idSubdepartamento: currSearchParams.subdepartamento
      ? parseInt(currSearchParams.subdepartamento as string)
      : undefined,
    status: currSearchParams.status
      ? Array.isArray(currSearchParams.status)
        ? currSearchParams.status.map((s) => parseInt(s as string))
        : [parseInt(currSearchParams.status as string)]
      : undefined,
    idFornecedor: currSearchParams.fornecedor
      ? parseInt(currSearchParams.fornecedor as string)
      : undefined,
    dataInicio: currSearchParams.dataInicio
      ? new Date(currSearchParams.dataInicio as string)
      : undefined,
    dataFim: currSearchParams.dataFim
      ? new Date(currSearchParams.dataFim as string)
      : undefined,
    bearer,
  };

  const liquidacoesResult =
    await gerarRelatorioLiquidacoesNaoProcessados(params);

  if (liquidacoesResult.error) {
    return <ErrorAlert error={liquidacoesResult.error} />;
  }

  if (!liquidacoesResult.data) {
    return <ErrorAlert error='Falha ao obter liquidações não processadas.' />;
  }

  const filtros = {
    exercicio: params.exercicio || result.exercicio,
    secretaria: currSearchParams.secretariaNome as string,
    departamento: currSearchParams.departamentoNome as string,
    subdepartamento: currSearchParams.subdepartamentoNome as string,
    fornecedor: currSearchParams.fornecedorNome as string,
    status: params.status,
    dataInicio: currSearchParams.dataInicio as string,
    dataFim: currSearchParams.dataFim as string,
    incluirProcessados: false,
    incluirNaoProcessados: true,
  };

  return (
    <>
      <RelatorioLiquidacoes
        liquidacoes={liquidacoesResult.data.liquidacoes}
        titulo='Relatório de Liquidações Não Processadas'
        filtros={filtros}
        resumo={liquidacoesResult.data.resumo}
      />
      <ClientCompletionTrigger />
    </>
  );
}
