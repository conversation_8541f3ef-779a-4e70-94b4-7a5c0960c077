import { ErrorAlert } from '@/components/error-alert';
import { listarDotacoes } from '@/lib/database/gerenciamento/dotacoes';
import DotacoesDatatable from './dotacoesDataTable';

export default async function DotacoesDatatableWrapper() {
  const result = await listarDotacoes();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Dotações não encontradas.'} />;

  return <DotacoesDatatable data={result} />;
}
