-- AlterTable
ALTER TABLE "liquidacoes" ADD COLUMN     "audespCorrenteImportacao" SMALLINT,
ADD COLUMN     "audespDataInicio" DATE,
ADD COLUMN     "audespExecucaoContrato" SMALLINT,
ADD COLUMN     "audespRegimeExecucao" SMALLINT,
ADD COLUMN     "dataMaterialServico" DATE,
ADD COLUMN     "mesReferencia" VARCHAR(15),
ADD COLUMN     "obs" TEXT,
ADD COLUMN     "resumo" TEXT;

-- CreateTable
CREATE TABLE "liquidacoes_documentos" (
    "id" SERIAL NOT NULL,
    "idLiquidacao" INTEGER NOT NULL,
    "numeroDocumento" VARCHAR(50) NOT NULL,
    "dataEmissao" DATE NOT NULL,
    "dataRecebimento" DATE NOT NULL,
    "dataMaterialServico" DATE,
    "valorDocumento" DECIMAL(18,3) NOT NULL,
    "nomeArquivo" VARCHAR(255),
    "caminhoArquivo" VARCHAR(500),
    "tamanho" INTEGER,
    "dataUpload" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "marcaDocumento" VARCHAR(1),
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "liquidacoes_documentos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "liquidacoes_itens" (
    "id" SERIAL NOT NULL,
    "idLiquidacao" INTEGER NOT NULL,
    "empenhoJan" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoFev" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoMar" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoAbr" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoMai" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoJun" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoJul" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoAgo" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoSet" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoOut" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoNov" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "empenhoDez" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarJan" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarFev" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarMar" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarAbr" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarMai" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarJun" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarJul" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarAgo" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarSet" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarOut" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarNov" DECIMAL(18,4) NOT NULL DEFAULT 0,
    "usarDez" DECIMAL(18,4) NOT NULL DEFAULT 0,

    CONSTRAINT "liquidacoes_itens_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "liquidacoes_documentos_idLiquidacao_numeroDocumento_idx" ON "liquidacoes_documentos"("idLiquidacao", "numeroDocumento");

-- CreateIndex
CREATE INDEX "liquidacoes_documentos_ativo_idx" ON "liquidacoes_documentos"("ativo");

-- CreateIndex
CREATE UNIQUE INDEX "liquidacoes_itens_idLiquidacao_key" ON "liquidacoes_itens"("idLiquidacao");

-- CreateIndex
CREATE INDEX "liquidacoes_itens_idLiquidacao_idx" ON "liquidacoes_itens"("idLiquidacao");

-- AddForeignKey
ALTER TABLE "liquidacoes_documentos" ADD CONSTRAINT "liquidacoes_documentos_idLiquidacao_fkey" FOREIGN KEY ("idLiquidacao") REFERENCES "liquidacoes"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "liquidacoes_itens" ADD CONSTRAINT "liquidacoes_itens_idLiquidacao_fkey" FOREIGN KEY ("idLiquidacao") REFERENCES "liquidacoes"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
