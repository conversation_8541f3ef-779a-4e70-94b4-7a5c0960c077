-- CreateTable
CREATE TABLE "reservas_assinaturas" (
    "id" SERIAL NOT NULL,
    "idReserva" INTEGER NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "left" DECIMAL(65,30) NOT NULL,
    "top" DECIMAL(65,30) NOT NULL,
    "assinado" BOOLEAN NOT NULL DEFAULT false,
    "dataAssinatura" TIMESTAMP,

    CONSTRAINT "reservas_assinaturas_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "reservas_assinaturas" ADD CONSTRAINT "reservas_assinaturas_idReserva_fkey" FOREIGN KEY ("idReserva") REFERENCES "reservas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddF<PERSON>ign<PERSON>ey
ALTER TABLE "reservas_assinaturas" ADD CONSTRAINT "reservas_assinaturas_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
