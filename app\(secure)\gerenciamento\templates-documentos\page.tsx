import { Suspense } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { TemplatesDocumentosDatatableWrapper } from '@/components/gerenciamento/templatesDocumentos/templatesDocumentosDatatableWrapper';

export default function TemplatesDocumentosPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Gerenciamento de Templates de Documentos</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='mb-4 flex w-full justify-end pr-6'>
            <Link href='/gerenciamento/templates-documentos/novo'>
              <Button>
                Novo Template <PlusCircle className='ml-1 h-4 w-4' />
              </Button>
            </Link>
          </div>
          <TemplatesDocumentosDatatableWrapper />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
