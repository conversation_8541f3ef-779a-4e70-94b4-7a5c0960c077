'use client';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';
import { AlertAtivarSuperavit } from './alertAtivarSuperavit';
import { AlertDesativarSuperavit } from './alertDesativarSuperavit';
import { listarSuperavits } from '@/lib/database/gerenciamento/superavit';
import { currencyOptions } from '@/lib/utils';
import currency from 'currency.js';

export default function SuperavitsDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarSuperavits>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'fonte',
      header: 'Fonte',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'codAplicacao',
      header: 'Código da Aplicação',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'valorReceita', // pensar em como obter saldo
      header: 'Valor',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptions).format();
      },
    },

    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link href={`/gerenciamento/superavits/editar/${row.original.id}`}>
            <Button type='button' variant='outline'>
              <Pencil className='mr-2 size-4' /> Editar
            </Button>
          </Link>
          {row.original.ativo ? (
            <AlertDesativarSuperavit
              idSuperavit={row.original.id}
              descSuperavit={row.original.nome}
            />
          ) : (
            <AlertAtivarSuperavit
              idSuperavit={row.original.id}
              descSuperavit={row.original.nome}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatable columns={columns} data={data.data} />
    </div>
  );
}
