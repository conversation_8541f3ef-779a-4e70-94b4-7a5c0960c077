'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { obterProtocolo } from '@/lib/database/movimento/protocolos';
import { ErrorAlert } from '@/components/error-alert';
import FormEditarProtocolo from '@/components/movimento/protocolo/formEditarProtocolo';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default async function EditarProtocoloPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const protocolo = await obterProtocolo({
    id: Number(id),
  });

  if (protocolo.error) {
    return <ErrorAlert error={protocolo.error} />;
  }
  if (!protocolo.data) {
    return <ErrorAlert error='Falha ao obter protocolo.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <Link href={`/movimento/protocolo/visualizar/${id}`}>
          <Button variant='ghost' size='sm'>
            <ArrowLeft className='mr-2 size-4' />
            Voltar
          </Button>
        </Link>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormEditarProtocolo protocolo={protocolo.data} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
