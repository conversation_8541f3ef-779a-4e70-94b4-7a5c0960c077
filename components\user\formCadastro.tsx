'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { SquarePen } from 'lucide-react';
import { formCadastroSchema } from '@/lib/validation';
import { toast } from 'sonner';
import { Icons } from '@/components/icons';
import { useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { atualizarCadastroUsuario } from '@/lib/database/usuarios';
import { revalidateLayoutAndRedirectHome } from '@/lib/supabase/actions';

export const FormCadastro = () => {
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<z.infer<typeof formCadastroSchema>>({
    resolver: zodResolver(formCadastroSchema),
    defaultValues: {
      nome: '',
      senha: '',
      confirmarSenha: '',
    },
  });

  const supabase = createClient();

  async function onSubmit(data: z.infer<typeof formCadastroSchema>) {
    setIsLoading(true);
    try {
      // if (!token_hash || !type) {
      //   toast.error(toastAlgoDeuErrado);
      //   return;
      // }
      if (data.senha === data.confirmarSenha) {
        const {
          data: { user },
          error,
        } = await supabase.auth.updateUser({
          password: data.senha,
        });
        if (error || !user) {
          if (error?.status === 422) {
            toast.error('Sua senha nova não pode ser igual a antiga.');
            setIsLoading(false);
            return;
          }
          toast.error(toastAlgoDeuErrado);
          setIsLoading(false);
          return;
        }

        const { error: errorUpdateUser } = await atualizarCadastroUsuario({
          nome: data.nome,
        });
        if (errorUpdateUser) {
          toast.error(error);
          setIsLoading(false);
          return;
        }
        return await revalidateLayoutAndRedirectHome();
      } else {
        throw new Error('Senhas não conferem');
      }
    } catch (error) {
      toast.error(toastAlgoDeuErrado);
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className='flex flex-wrap justify-evenly gap-6 text-left'
      >
        <FormField
          control={form.control}
          name='nome'
          render={({ field }) => (
            <FormItem className='w-full max-w-[300px]'>
              <FormLabel>Nome Completo</FormLabel>
              <FormControl>
                <Input placeholder='Digite seu nome...' {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='senha'
          render={({ field }) => (
            <FormItem className='w-full max-w-[300px]'>
              <FormLabel>Nova Senha</FormLabel>
              <FormControl>
                <Input
                  placeholder='Digite uma senha...'
                  {...field}
                  type='password'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='confirmarSenha'
          render={({ field }) => (
            <FormItem className='w-full max-w-[300px]'>
              <FormLabel>Confirme a Senha</FormLabel>
              <FormControl>
                <Input
                  placeholder='Digite a mesma senha...'
                  {...field}
                  type='password'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormMessage />
        <div className='w-full text-center'>
          <Button
            type='submit'
            disabled={form.formState.isSubmitting || isLoading}
            className='ml-4'
          >
            Cadastrar
            {form.formState.isSubmitting ? (
              <Icons.loader className='ml-1 h-4 w-4 animate-spin' />
            ) : (
              <SquarePen className='ml-1 h-4 w-4' />
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};
