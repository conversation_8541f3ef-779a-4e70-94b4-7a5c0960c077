/*
  Warnings:

  - You are about to drop the column `grau` on the `economicas` table. All the data in the column will be lost.
  - Added the required column `categoria` to the `economicas` table without a default value. This is not possible if the table is not empty.
  - Added the required column `elemento` to the `economicas` table without a default value. This is not possible if the table is not empty.
  - Added the required column `grupo` to the `economicas` table without a default value. This is not possible if the table is not empty.
  - Added the required column `modalidade` to the `economicas` table without a default value. This is not possible if the table is not empty.
  - Added the required column `subelemento` to the `economicas` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "economicas" DROP COLUMN "grau",
ADD COLUMN     "ativo" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "categoria" SMALLINT NOT NULL,
ADD COLUMN     "elemento" SMALLINT NOT NULL,
ADD COLUMN     "grupo" SMALLINT NOT NULL,
ADD COLUMN     "modalidade" SMALLINT NOT NULL,
ADD COLUMN     "subelemento" SMALLINT NOT NULL;

-- CreateTable
CREATE TABLE "gerenciamentoEconomicas_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idEconomica" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gerenciamentoEconomicas_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "gerenciamentoEconomicas_audit" ADD CONSTRAINT "gerenciamentoEconomicas_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoEconomicas_audit" ADD CONSTRAINT "gerenciamentoEconomicas_audit_idEconomica_fkey" FOREIGN KEY ("idEconomica") REFERENCES "economicas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
