import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { aprovarAltOrc } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { cn, obterDataAtual, toastAlgoDeuErrado } from '@/lib/utils';
import { CalendarIcon, ThumbsUp } from 'lucide-react';
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TiposDecreto } from '@/lib/enums';
import { ptBR } from 'date-fns/locale';
import { aprovarAlteracaoOrcamentariaSchema } from '@/lib/validation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { AlertDialogTrigger } from '@radix-ui/react-alert-dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export function AprovarAlteracaoOrcamentaria({
  alteracaoOrcamentariaCheck,
  unmarkSelect,
}: {
  alteracaoOrcamentariaCheck: Array<any>;
  unmarkSelect: Function;
}) {
  const [loading, setLoading] = useState(false);
  const [date, setDate] = useState<Date>();

  const form = useForm<z.infer<typeof aprovarAlteracaoOrcamentariaSchema>>({
    resolver: zodResolver(aprovarAlteracaoOrcamentariaSchema),
    defaultValues: {
      numDecreto: 0,
    },
  });

  const onSubmit = async (
    values: z.infer<typeof aprovarAlteracaoOrcamentariaSchema>
  ) => {
    if (!alteracaoOrcamentariaCheck) {
      toast.error('Nenhuma alteração orçamentária marcada para aprovação');
    }

    if (!values.dataDecreto) {
      toast.error('Data do Decreto não preenchida');
    }

    if (!values.tipoDecreto) {
      toast.error('Tipo do Decreto não preenchido');
    }

    if (!values.numDecreto) {
      toast.error('Numero do Decreto não preenchido');
    }

    try {
      setLoading(true);
      for (let index = 0; index < alteracaoOrcamentariaCheck.length; index++) {
        const id = alteracaoOrcamentariaCheck[index].original.id;
        if (!alteracaoOrcamentariaCheck[index].original.ativo) {
          toast.error('Alteração Orçamentária ID ' + id + ' está inativa');
          setLoading(false);
          return;
        }

        const data: z.infer<typeof aprovarAlteracaoOrcamentariaSchema> = {
          id,
          dataDecreto: values.dataDecreto,
          numDecreto: values.numDecreto,
          tipoDecreto: values.tipoDecreto,
        };

        const res = await aprovarAltOrc(data);

        if (!res || res.error) {
          setLoading(false);
          toast.error(
            'Alteração Orçamentária ID ' + id + ' - ' + res ? res.error : ''
          );
        } else {
          toast.success('Alterações Orçamentárias Aprovada.');
        }
      }
      form.setValue('numDecreto', 0);
      unmarkSelect();
    } catch (error: any) {
      console.log(error);
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
    setLoading(false);
  };

  const onInvalid = (errors: any) => {
    console.log(errors);
    toast.error(JSON.stringify(errors));
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline'>
          <ThumbsUp className='mr-2 size-4' /> Aprovar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className='sm:max-w-[425px]'>
        <AlertDialogHeader>
          <AlertDialogTitle>Aprovar Alteração Orçamentária</AlertDialogTitle>
          <AlertDialogDescription>
            Insira os dados para atualizar aprovar as alterações orçamentárias.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit, onInvalid)}>
            <div className='flex gap-4 py-4'>
              <FormField
                control={form.control}
                name='dataDecreto'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Descrição
                    </FormLabel>
                    <FormControl>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-[240px] justify-start text-left font-normal',
                              !date && 'text-muted-foreground'
                            )}
                          >
                            <CalendarIcon className='mr-2 h-4 w-4' />
                            {date ? (
                              format(date, 'PPP', { locale: ptBR })
                            ) : (
                              <span>Escolha a Data</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className='w-auto p-0' align='start'>
                          <Calendar
                            {...field}
                            locale={ptBR}
                            mode='single'
                            // selected={date}
                            onSelect={(value) => {
                              form.setValue(
                                'dataDecreto',
                                value || obterDataAtual(),
                                {
                                  shouldDirty: true,
                                }
                              );
                              setDate(value);
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='grid gap-8 py-4'>
              <FormField
                name='tipoDecreto'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Tipo do Decreto
                    </FormLabel>
                    <Select
                      value={`${field.value}`}
                      onValueChange={(e) => {
                        form.setValue('tipoDecreto', Number(e), {
                          shouldDirty: true,
                        });
                      }}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Selecione a fonte' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(TiposDecreto)
                          .filter(
                            (v) => !isNaN(Number(v[0])) && Number(v[0]) > 0
                          )
                          .map((decreto) => (
                            <SelectItem
                              key={decreto[0]}
                              value={`${decreto[0]}`}
                            >
                              {decreto[0]} - {decreto[1]}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
            <div className='grid gap-8 py-4'>
              <FormField
                name='numDecreto'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Numero do Decreto
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        pattern='[0-99]*'
                        placeholder='Digite o numero do Decreto'
                        className='max-w-[100px] text-right text-base'
                        value={`${field.value}`}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('numDecreto', Number(value));
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              onSubmit({
                dataDecreto: form.getValues('dataDecreto'),
                tipoDecreto: form.getValues('tipoDecreto'),
                numDecreto: form.getValues('numDecreto'),
              });
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Aprovar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
