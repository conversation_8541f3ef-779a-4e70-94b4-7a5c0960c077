'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarEconomicasAtivas } from '@/lib/database/gerenciamento/dotacoes';
import { UseFormReturn } from 'react-hook-form';

export function ComboboxSelecionaEconomica({
  economicas,
  form,
}: {
  economicas: Awaited<ReturnType<typeof listarEconomicasAtivas>>;
  form: UseFormReturn<
    {
      desc: string;
      despesa: number;
      fonte: number;
      codAplicacao: number;
      funcionalId: number;
      economicaId: number;
      valorInicial: number;
      cotaReducaoInicial: number;
      suplementacao: number;
      anulacao: number;
      secretariaId?: number | undefined;
      departamentoId?: number | undefined;
      subdepartamentoId?: number | undefined;
    },
    any,
    undefined
  >;
}) {
  const [open, setOpen] = React.useState(false);

  const economicaSelecionada =
    economicas.data?.find((economica) => {
      return form.getValues('economicaId') === economica.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {economicaSelecionada
              ? economicaSelecionada.codigo + ' - ' + economicaSelecionada.desc
              : 'Selecione a econômica...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar econômica...' />
            <CommandEmpty>Econômica não encontrada.</CommandEmpty>
            <CommandList>
              {economicas.data?.map((eco) => (
                <CommandItem
                  key={eco.id}
                  value={`${eco.codigo} - ${eco.desc}`}
                  onSelect={() => {
                    form.setValue('economicaId', eco.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      form.getValues('economicaId') === eco.id
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  {eco.codigo} - {eco.desc}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
