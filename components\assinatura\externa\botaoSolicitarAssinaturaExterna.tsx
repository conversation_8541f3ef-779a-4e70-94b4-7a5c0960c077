'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileSignature } from 'lucide-react';
import { DialogSolicitarAssinaturaExterna } from './dialogSolicitarAssinaturaExterna';

interface BotaoSolicitarAssinaturaExternaProps {
  idReserva: number;
  reserva: {
    id: number;
    resumo: string;
    dotacao: {
      id: number;
      despesa: number;
    };
    exercicio: number;
  };
  disabled?: boolean;
}

export function BotaoSolicitarAssinaturaExterna({
  idReserva,
  reserva,
  disabled = false,
}: BotaoSolicitarAssinaturaExternaProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleClick = () => {
    if (disabled) return;
    setIsDialogOpen(true);
  };

  return (
    <>
      <Button
        onClick={handleClick}
        disabled={disabled}
        variant='outline'
        className='w-full sm:w-auto'
      >
        <FileSignature className='mr-2 h-4 w-4' />
        Solicitar Assinatura Externa
      </Button>

      <DialogSolicitarAssinaturaExterna
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        idReserva={idReserva}
        reserva={reserva}
        onSuccess={() => {
          setIsDialogOpen(false);
          // Revalidar página
          window.location.reload();
        }}
      />
    </>
  );
}
