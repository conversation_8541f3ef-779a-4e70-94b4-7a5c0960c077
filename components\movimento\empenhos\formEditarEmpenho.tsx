'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save } from 'lucide-react';
import {
  cn,
  codigoSecretariaMask,
  currencyOptionsNoSymbol,
  economicaMask,
  moneyMask,
  moneyUnmask,
  obterAnoAtual,
  obterMesAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { alterarEmpenhoSchema, idSchema } from '@/lib/validation';
import { Label } from '@/components/ui/label';
import { Fontes } from '@/lib/enums';
import { toast } from 'sonner';
import { editarEmpenho, obterEmpenho } from '@/lib/database/movimento/empenhos';
import {
  buscarDespesa,
  buscarReservaParaEmpenho,
} from '@/lib/database/movimento/reservas';
import currency from 'currency.js';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';

export default function FormEditarEmpenho({
  empenho,
}: {
  empenho: NonNullable<Awaited<ReturnType<typeof obterEmpenho>>['data']>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [despesa, setDespesa] = useState<Awaited<
    ReturnType<typeof buscarDespesa>
  > | null>(null);
  const [reservaData, setReservaData] = useState<Awaited<
    ReturnType<typeof buscarReservaParaEmpenho>
  > | null>(null);
  const [aumentarDecimais, setAumentarDecimais] = useState(false);
  const decimais = aumentarDecimais ? 3 : 2;

  const anoAtual = obterAnoAtual();
  const mesAtual =
    empenho.exercicio && empenho.exercicio < anoAtual ? 12 : obterMesAtual();

  const form = useForm<z.infer<typeof alterarEmpenhoSchema>>({
    resolver: zodResolver(alterarEmpenhoSchema),
    defaultValues: {
      id: empenho.id,
      obs: empenho.obs || '',
      resumo: empenho.resumo || '',
      usarMes1: empenho.usarMes1,
      usarMes2: empenho.usarMes2,
      usarMes3: empenho.usarMes3,
      usarMes4: empenho.usarMes4,
      usarMes5: empenho.usarMes5,
      usarMes6: empenho.usarMes6,
      usarMes7: empenho.usarMes7,
      usarMes8: empenho.usarMes8,
      usarMes9: empenho.usarMes9,
      usarMes10: empenho.usarMes10,
      usarMes11: empenho.usarMes11,
      usarMes12: empenho.usarMes12,
    },
  });

  const [mesesBRL, setMesesBRL] = useState(() => {
    const initialMeses: Record<string, string> = {};
    for (let i = 1; i <= 12; i++) {
      const usarMes = (empenho[`usarMes${i}` as keyof typeof empenho] ||
        0) as number;
      if (usarMes !== null) {
        initialMeses[`mes${i}`] = currency(usarMes, currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      }
    }
    return initialMeses;
  });

  const updateMesBRL = (mesNum: number, value: string) => {
    setMesesBRL((prev) => ({ ...prev, [`mes${mesNum}`]: value }));
  };

  const total = Object.values(mesesBRL).reduce(
    (acc, mesBRL: string) => acc.add(currency(mesBRL, currencyOptionsNoSymbol)),
    currency(0, currencyOptionsNoSymbol)
  );

  useEffect(() => {
    const buscarDados = async () => {
      try {
        setLoading(true);
        const data: z.infer<typeof idSchema> = {
          id: empenho.idDotacao,
        };

        // Load both despesa and reserva data
        const [despesaRes, reservaRes] = await Promise.all([
          buscarDespesa(data),
          buscarReservaParaEmpenho({ id: empenho.idReserva }),
        ]);

        if (despesaRes?.error) {
          toast.error(despesaRes.error);
          setDespesa(null);
        } else {
          setDespesa(despesaRes);
        }

        if (reservaRes?.error) {
          toast.error(reservaRes.error);
          setReservaData(null);
        } else {
          setReservaData(reservaRes);
        }

        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.log(JSON.stringify(error));
        toast.error(toastAlgoDeuErrado);
      }
    };

    buscarDados();
  }, [empenho.idDotacao, empenho.idReserva]);

  const onSubmit = async (values: z.infer<typeof alterarEmpenhoSchema>) => {
    try {
      setLoading(true);
      const res = await editarEmpenho(values);
      if (res?.error) {
        setLoading(false);
        toast(res.error, { duration: 10000 });
      } else {
        toast.success('Empenho atualizado', {
          description: 'Número do empenho: ' + empenho.numero,
          action: {
            label: 'Visualizar',
            onClick: () =>
              router.push('/movimento/empenhos/visualizar/' + empenho.id),
          },
        });
        router.push('/movimento/empenhos');
      }
    } catch (error) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const subdepartamento = empenho.dotacao?.subdepartamento || null;
  const departamento = subdepartamento
    ? empenho.dotacao?.subdepartamento?.departamento || null
    : empenho.dotacao?.departamento || null;
  const secretaria = subdepartamento
    ? empenho.dotacao?.subdepartamento?.departamento?.secretaria || null
    : departamento
      ? empenho.dotacao?.departamento?.secretaria || null
      : empenho.dotacao?.secretaria || null;

  const valorAnulado = currency(
    empenho.valorAnulado || 0,
    currencyOptionsNoSymbol
  );
  const valorLiquidado = currency(
    empenho.valorLiquidado || 0,
    currencyOptionsNoSymbol
  );
  const valorDisponivelParaEdicao = currency(
    empenho.valorTotal,
    currencyOptionsNoSymbol
  )
    .subtract(valorAnulado)
    .subtract(valorLiquidado);

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex gap-2'>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Empenho Nº
                </FormLabel>
                <Input disabled={true} value={empenho.id} />
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>Despesa</FormLabel>
                <Input disabled={true} value={empenho.idDotacao} />
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Descrição
                </FormLabel>
                <Input disabled={true} value={empenho.dotacao?.despesa || ''} />
              </FormItem>
            </div>

            <div className='mt-12 flex flex-wrap gap-4'>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Secretaria
                </FormLabel>
                <Input
                  disabled={true}
                  value={
                    secretaria
                      ? `${codigoSecretariaMask(secretaria.codigo.toString())}.00.00 - ${secretaria.nome}`
                      : ''
                  }
                />
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Departamento
                </FormLabel>
                <Input
                  disabled={true}
                  value={
                    secretaria && departamento
                      ? `${codigoSecretariaMask(secretaria.codigo.toString())}.${codigoSecretariaMask(departamento.codigo.toString())}.00 - ${departamento.nome}`
                      : ''
                  }
                />
              </FormItem>
              {subdepartamento && (
                <FormItem className='w-full'>
                  <FormLabel className='flex w-full text-left'>
                    Subdepartamento
                  </FormLabel>
                  <Input
                    disabled={true}
                    value={
                      secretaria && departamento && subdepartamento
                        ? `${codigoSecretariaMask(secretaria.codigo.toString())}.${codigoSecretariaMask(departamento.codigo.toString())}.${codigoSecretariaMask(subdepartamento.codigo.toString())} - ${subdepartamento.nome}`
                        : ''
                    }
                  />
                </FormItem>
              )}
            </div>

            <div className='mt-12 flex flex-wrap gap-4'>
              <Label className='w-full text-left'>Econômica</Label>
              <Input
                disabled={true}
                value={
                  empenho.dotacao
                    ? `${economicaMask(empenho.dotacao.economica?.codigo || '')} - ${empenho.dotacao.economica?.desc || ''}`
                    : ''
                }
              />

              <Label className='w-full text-left'>Funcional</Label>
              <Input
                disabled={true}
                value={
                  empenho.dotacao
                    ? `${empenho.dotacao.funcional?.codigo || ''} - ${empenho.dotacao.funcional?.desc || ''}`
                    : ''
                }
              />
            </div>

            <div className='mt-12 flex gap-2'>
              <FormItem className='flex w-full flex-wrap'>
                <FormLabel className='w-full text-left'>Fonte</FormLabel>
                <Input
                  disabled={true}
                  value={
                    empenho.dotacao
                      ? `${empenho.dotacao.fonte} - ${Fontes[empenho.dotacao.fonte || 0]}`
                      : ''
                  }
                />
              </FormItem>
              <FormItem className='flex w-full flex-wrap'>
                <FormLabel className='w-full text-left'>
                  Cód. Aplicação
                </FormLabel>
                <Input
                  disabled={true}
                  value={
                    empenho.dotacao ? `${empenho.dotacao.codAplicacao}` : ''
                  }
                />
              </FormItem>
            </div>

            <div className='mt-12 flex gap-2'>
              <FormItem className='flex w-full flex-wrap'>
                <FormLabel className='flex w-full text-left'>
                  Fornecedor
                </FormLabel>
                <Input disabled={true} value={empenho.fornecedor?.nome || ''} />
              </FormItem>
            </div>

            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='obs'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>Obs</FormLabel>
                    <FormControl>
                      <Input {...field} className='w-full' />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='resumo'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>
                      Resumo
                    </FormLabel>
                    <FormControl>
                      <Input {...field} className='w-full' />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Enhanced Reserva Values Display */}
            {reservaData?.data?.reserva && (
              <>
                <Separator className='my-12' />
                <div className='space-y-6'>
                  <div className='flex items-center justify-between'>
                    <span className='text-lg font-semibold'>
                      Valores da Reserva (Referência)
                    </span>
                    <div className='text-muted-foreground text-sm'>
                      Reserva #{reservaData.data.reserva.id} -{' '}
                      {reservaData.data.reserva.resumo}
                    </div>
                  </div>

                  {/* Summary Cards */}
                  <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
                    <div className='rounded-lg border border-blue-200 bg-blue-50 p-4'>
                      <div className='text-sm font-medium text-blue-800'>
                        Valor Original da Reserva
                      </div>
                      <div className='text-lg font-bold text-blue-900'>
                        {currency(
                          reservaData.data.reserva.valoresOriginais.total,
                          currencyOptionsNoSymbol
                        ).format()}
                      </div>
                    </div>
                    <div className='rounded-lg border border-orange-200 bg-orange-50 p-4'>
                      <div className='text-sm font-medium text-orange-800'>
                        Já Empenhado (Outros)
                      </div>
                      <div className='text-lg font-bold text-orange-900'>
                        {currency(
                          reservaData.data.reserva.jaEmpenhado.total,
                          currencyOptionsNoSymbol
                        ).format()}
                      </div>
                    </div>
                    <div className='rounded-lg border border-green-200 bg-green-50 p-4'>
                      <div className='text-sm font-medium text-green-800'>
                        Saldo + Este Empenho
                      </div>
                      <div className='text-lg font-bold text-green-900'>
                        {currency(
                          reservaData.data.reserva.saldoDisponivel.total +
                            empenho.valorTotal,
                          currencyOptionsNoSymbol
                        ).format()}
                      </div>
                    </div>
                  </div>

                  {/* Monthly Breakdown */}
                  <div className='space-y-4'>
                    <div className='text-base font-medium'>
                      Distribuição Mensal:
                    </div>
                    <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                      {Array.from({ length: 12 }, (_, i) => {
                        const mesNum = i + 1;
                        const mesNome = [
                          'Janeiro',
                          'Fevereiro',
                          'Março',
                          'Abril',
                          'Maio',
                          'Junho',
                          'Julho',
                          'Agosto',
                          'Setembro',
                          'Outubro',
                          'Novembro',
                          'Dezembro',
                        ][i];

                        const valorOriginal = reservaData.data.reserva
                          .valoresOriginais[
                          `mes${mesNum}` as keyof typeof reservaData.data.reserva.valoresOriginais
                        ] as number;
                        const jaEmpenhado = reservaData.data.reserva
                          .jaEmpenhado[
                          `mes${mesNum}` as keyof typeof reservaData.data.reserva.jaEmpenhado
                        ] as number;
                        const valorEsteEmpenho =
                          (empenho[
                            `usarMes${mesNum}` as keyof typeof empenho
                          ] as number) || 0;
                        const saldoDisponivel = reservaData.data.reserva
                          .saldoDisponivel[
                          `mes${mesNum}` as keyof typeof reservaData.data.reserva.saldoDisponivel
                        ] as number;
                        const saldoComEsteEmpenho =
                          saldoDisponivel + valorEsteEmpenho;

                        // Only show months with values
                        if (valorOriginal === 0) return null;

                        return (
                          <div
                            key={mesNum}
                            className='space-y-2 rounded-lg border bg-gray-50 p-3'
                          >
                            <div className='text-sm font-medium text-gray-700'>
                              {mesNome}
                            </div>
                            <div className='space-y-1 text-xs'>
                              <div className='flex justify-between'>
                                <span className='text-blue-600'>Original:</span>
                                <span className='font-medium text-blue-800'>
                                  {currency(
                                    valorOriginal,
                                    currencyOptionsNoSymbol
                                  ).format()}
                                </span>
                              </div>
                              {jaEmpenhado > 0 && (
                                <div className='flex justify-between'>
                                  <span className='text-orange-600'>
                                    Outros Empenhos:
                                  </span>
                                  <span className='font-medium text-orange-800'>
                                    {currency(
                                      jaEmpenhado,
                                      currencyOptionsNoSymbol
                                    ).format()}
                                  </span>
                                </div>
                              )}
                              {valorEsteEmpenho > 0 && (
                                <div className='flex justify-between'>
                                  <span className='text-purple-600'>
                                    Este Empenho:
                                  </span>
                                  <span className='font-medium text-purple-800'>
                                    {currency(
                                      valorEsteEmpenho,
                                      currencyOptionsNoSymbol
                                    ).format()}
                                  </span>
                                </div>
                              )}
                              <div className='flex justify-between border-t pt-1'>
                                <span className='font-medium text-green-600'>
                                  Disponível + Este:
                                </span>
                                <span
                                  className={cn(
                                    'font-bold',
                                    saldoComEsteEmpenho > 0
                                      ? 'text-green-800'
                                      : 'text-red-600'
                                  )}
                                >
                                  {currency(
                                    saldoComEsteEmpenho,
                                    currencyOptionsNoSymbol
                                  ).format()}
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      }).filter(Boolean)}
                    </div>
                  </div>
                </div>
              </>
            )}

            <Separator className='my-12' />
            <span className='text-lg'>Valores do Empenho</span>

            <div className='mt-8 flex items-center space-x-2'>
              <Switch
                id='aumentar-decimais'
                checked={aumentarDecimais}
                onCheckedChange={() => {
                  setAumentarDecimais(!aumentarDecimais);
                }}
                disabled={!despesa}
              />
              <Label htmlFor='aumentar-decimais' className='text-lg'>
                Aumentar Decimais
              </Label>
            </div>

            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              {Array.from({ length: 12 }, (_, i) => {
                const mesNum = i + 1;
                const mesNome = [
                  'Janeiro',
                  'Fevereiro',
                  'Março',
                  'Abril',
                  'Maio',
                  'Junho',
                  'Julho',
                  'Agosto',
                  'Setembro',
                  'Outubro',
                  'Novembro',
                  'Dezembro',
                ][i];

                const currentValue = Number(
                  moneyUnmask(mesesBRL[`mes${mesNum}`] || '0')
                );
                const originalValue =
                  (empenho[
                    `usarMes${mesNum}` as keyof typeof empenho
                  ] as number) || 0;
                const saldoDisponivel =
                  (reservaData?.data?.reserva?.saldoDisponivel?.[
                    `mes${mesNum}` as keyof typeof reservaData.data.reserva.saldoDisponivel
                  ] as number) || 0;
                const saldoComOriginal = saldoDisponivel + originalValue;
                const isOverLimit = currentValue > saldoComOriginal;
                const hasReservaData = reservaData?.data?.reserva;

                return (
                  <FormField
                    key={mesNum}
                    name={
                      `usarMes${mesNum}` as `usarMes${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12}`
                    }
                    render={({ field }) => (
                      <FormItem
                        className={cn(
                          'flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs',
                          isOverLimit && hasReservaData
                            ? 'border-red-300 bg-red-50'
                            : '',
                          currentValue > 0 ? 'border-green-300 bg-green-50' : ''
                        )}
                      >
                        <FormLabel className='flex w-full text-left'>
                          {mesNome}
                        </FormLabel>
                        {hasReservaData && (
                          <div className='text-muted-foreground mb-1 w-full text-xs'>
                            Disponível:{' '}
                            {currency(
                              saldoComOriginal,
                              currencyOptionsNoSymbol
                            ).format()}
                          </div>
                        )}
                        <FormLabel className='mt-2 flex w-full text-left'>
                          Usar
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className={cn(
                              'max-w-[150px] text-right',
                              isOverLimit && hasReservaData
                                ? 'border-red-500 text-red-700'
                                : '',
                              currentValue > 0
                                ? 'font-medium text-green-700'
                                : ''
                            )}
                            value={mesesBRL[`mes${mesNum}`]}
                            onChange={(e) => {
                              updateMesBRL(
                                mesNum,
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                `usarMes${mesNum}` as `usarMes${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12}`,
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                            disabled={!despesa || mesAtual > mesNum}
                          />
                        </FormControl>
                        {isOverLimit && hasReservaData && (
                          <div className='mt-1 w-full text-xs text-red-600'>
                            Excede saldo disponível
                          </div>
                        )}
                      </FormItem>
                    )}
                  />
                );
              })}
            </div>

            <div className='mt-12 flex flex-wrap justify-between gap-6'>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>Valor Original</FormLabel>
                <FormControl>
                  <Input
                    className='text-right text-base text-green-600 disabled:opacity-100'
                    value={currency(empenho.valorTotal, currencyOptionsNoSymbol)
                      .format()
                      .replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>Valor Anulado</FormLabel>
                <FormControl>
                  <Input
                    className='text-right text-base text-red-600 disabled:opacity-100'
                    value={valorAnulado.format().replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>
                  Valor Liquidado
                </FormLabel>
                <FormControl>
                  <Input
                    className='text-right text-base text-blue-600 disabled:opacity-100'
                    value={valorLiquidado.format().replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>
                  Disponível para Edição
                </FormLabel>
                <FormControl>
                  <Input
                    className={cn(
                      'text-right text-base disabled:opacity-100',
                      total.value > valorDisponivelParaEdicao.value
                        ? 'text-red-600'
                        : 'text-green-600'
                    )}
                    value={valorDisponivelParaEdicao.format().replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>Novo Total</FormLabel>
                <FormControl>
                  <Input
                    className={cn(
                      'text-right text-base disabled:opacity-100',
                      total.value === 0 ||
                        total.value > valorDisponivelParaEdicao.value
                        ? 'text-red-600'
                        : 'text-green-600'
                    )}
                    value={total.format().replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
            </div>
          </form>
        </Form>
      </div>

      <div className='mt-12 flex w-full justify-between'>
        <Button
          variant={'destructive'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/movimento/empenhos');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={
            loading ||
            !despesa ||
            total.value === 0 ||
            total.value > valorDisponivelParaEdicao.value ||
            (reservaData?.data?.reserva &&
              total.value >
                reservaData.data.reserva.saldoDisponivel.total +
                  empenho.valorTotal)
          }
        >
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <Save className='mr-2 h-4 w-4' /> Salvar Alterações
            </>
          )}
        </Button>
      </div>
    </>
  );
}
