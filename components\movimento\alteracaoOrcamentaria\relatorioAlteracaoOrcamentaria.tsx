import { siteConfig } from '@/config/site';
import { obterAlteracaoParaRelatorio } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { Fontes, tiposAcao, tiposAlteracao } from '@/lib/enums';
import { fontMono } from '@/lib/fonts';
import currency from 'currency.js';
import { toCurrency } from '@/lib/serverUtils';
import { cn, codigoSecretariaMask, currencyOptions } from '@/lib/utils';
import Image from 'next/image';

export const RelatorioAlteracaoOrcamentaria = ({
  alteracaoOrcamentaria,
}: {
  alteracaoOrcamentaria: Awaited<
    ReturnType<typeof obterAlteracaoParaRelatorio>
  >;
}) => {
  const dadosAlteracaoOrcamentaria = alteracaoOrcamentaria.data!;
  const secretaria = alteracaoOrcamentaria.data?.secretaria;
  const departamento = alteracaoOrcamentaria.data?.departamento;

  if (!secretaria || !departamento) return null;

  const getSecretariaDot = (dotacao: any) => {
    if (!dotacao) {
      return { nome: '' };
    }
    const subdepartamento = dotacao.subdepartamento || null;
    const departamento = subdepartamento
      ? dotacao.subdepartamento?.departamento || null
      : dotacao.departamento || null;
    const secretaria = subdepartamento
      ? dotacao.subdepartamento?.departamento?.secretaria || null
      : departamento
        ? dotacao.departamento?.secretaria || null
        : dotacao.secretaria || null;

    return secretaria;
  };

  return (
    <>
      {
        <div
          className={cn(
            'relative mt-8 text-center print:mt-0',
            fontMono.className
          )}
          style={{ breakAfter: 'always' }}
        >
          <div className='w-[277.5mm] border-[1px]'>
            <div className='flex h-[50px] border-b-[1px]'>
              <div className='flex justify-center border-r-[1px] align-middle'>
                <Image
                  className='p-1'
                  src='/imagens/brasao.jpg'
                  width={46}
                  height={46}
                  alt='brasão'
                  priority={true}
                />
              </div>
              <div className='my-auto flex w-full justify-center pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='mr-8 ml-8 text-[12pt] font-bold'>
                  {siteConfig.cliente.toUpperCase()}
                </span>
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>SECRETARIA</span>
                <span>{`${codigoSecretariaMask(secretaria.codigo.toString())}.00.00`}</span>{' '}
                -{' '}
                <span
                  style={{
                    fontSize: secretaria.nome.length > 53 ? '7.5pt' : '10pt',
                  }}
                >
                  {secretaria.nome}
                </span>
              </div>
            </div>
            <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
              Pedido de Decreto
            </div>
            <div className='border-b-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center text-[10pt] font-bold'>
              {tiposAcao[dadosAlteracaoOrcamentaria.tipoAcao] +
                ' por ' +
                tiposAlteracao[dadosAlteracaoOrcamentaria.tipoAlteracao]}
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[50%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 text-[12pt] font-bold'>
                  Pedido de Decreto Nº{' '}
                </span>
                <span className='text-[12pt]'>
                  {' '}
                  {dadosAlteracaoOrcamentaria.id}{' '}
                </span>
              </div>
              <div className='w-[50%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='text-[12pt]'>
                  <strong>Data </strong>
                  {dadosAlteracaoOrcamentaria.dataAbertura.toLocaleDateString(
                    'pt-br',
                    {}
                  )}
                </span>
              </div>
            </div>

            {dadosAlteracaoOrcamentaria.tipoAcao ==
              tiposAcao['Suplementação'] ||
            dadosAlteracaoOrcamentaria.tipoAcao == tiposAcao['Debito'] ? (
              dadosAlteracaoOrcamentaria.tipoAlteracao ==
              tiposAlteracao['Anulação'] ? (
                <div>
                  <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                    Despesa a Anular
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Secretaria:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {
                          getSecretariaDot(
                            dadosAlteracaoOrcamentaria.dotacaoAcao
                          ).nome
                        }{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Departamento:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {
                          dadosAlteracaoOrcamentaria.dotacaoAcao.departamento
                            ?.nome
                        }{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Despesa:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.despesaAcao +
                          ' - ' +
                          dadosAlteracaoOrcamentaria.dotacaoAcao.desc}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Dotação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(
                            dadosAlteracaoOrcamentaria.dotacaoAcao.valorAtual ||
                              0
                          ),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Econômica:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.dotacaoAcao.economica
                            ?.codigo || 0
                        ).toString() +
                          ' - ' +
                          dadosAlteracaoOrcamentaria.dotacaoAcao.economica
                            ?.desc}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Cód.Aplicação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {
                          dadosAlteracaoOrcamentaria.dotacaoAcao.codAplicacao
                        }{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Funcional:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.dotacaoAcao.funcional
                            .codigo || 0
                        ).toString() +
                          ' - ' +
                          dadosAlteracaoOrcamentaria.dotacaoAcao.funcional
                            ?.desc}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Fonte:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.dotacaoAcao.fonte.toString() +
                          ' - ' +
                          Fontes[
                            dadosAlteracaoOrcamentaria.dotacaoAcao.fonte
                          ]}{' '}
                      </span>
                    </div>

                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Valor:{' '}
                      </span>
                      <span className='border-l-[2px] pl-2 text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(dadosAlteracaoOrcamentaria.valorTotal),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                  <div>
                    <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                      Despesa a Suplementar
                    </div>
                    <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                      <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Secretaria:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {
                            getSecretariaDot(
                              dadosAlteracaoOrcamentaria.dotacaoNova
                            ).nome
                          }{' '}
                        </span>
                      </div>
                    </div>
                    <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                      <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Departamento:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {dadosAlteracaoOrcamentaria.dotacaoNova?.departamento
                            ?.nome || ''}{' '}
                        </span>
                      </div>
                    </div>
                    <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                      <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Despesa:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {dadosAlteracaoOrcamentaria.despesaNova ||
                            '' +
                              ' - ' +
                              dadosAlteracaoOrcamentaria.dotacaoNova?.desc ||
                            ''}{' '}
                        </span>
                      </div>
                      <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Dotação:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {currency(
                            toCurrency(
                              dadosAlteracaoOrcamentaria.dotacaoNova
                                ?.valorAtual || 0
                            ),
                            currencyOptions
                          ).format()}{' '}
                        </span>
                      </div>
                    </div>
                    <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                      <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Econômica:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {(
                            dadosAlteracaoOrcamentaria.dotacaoNova?.economica
                              ?.codigo || 0
                          ).toString() +
                            ' - ' +
                            dadosAlteracaoOrcamentaria.dotacaoNova?.economica
                              ?.desc || ''}{' '}
                        </span>
                      </div>
                      <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Cód.Aplicação:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {
                            dadosAlteracaoOrcamentaria.dotacaoNova?.codAplicacao
                          }{' '}
                        </span>
                      </div>
                    </div>
                    <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                      <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Funcional:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {(
                            dadosAlteracaoOrcamentaria.dotacaoNova?.funcional
                              ?.codigo || 0
                          ).toString() +
                            ' - ' +
                            dadosAlteracaoOrcamentaria.dotacaoNova?.funcional
                              ?.desc || ''}{' '}
                        </span>
                      </div>
                    </div>
                    <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                      <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Fonte:{' '}
                        </span>
                        <span className='text-[10pt]'>
                          {' '}
                          {dadosAlteracaoOrcamentaria.dotacaoNova?.fonte.toString() +
                            ' - ' +
                            Fontes[
                              dadosAlteracaoOrcamentaria.dotacaoNova?.fonte
                            ] || ''}{' '}
                        </span>
                      </div>
                      <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                        <span className='mr-2 text-[12pt] font-bold'>
                          Valor:{' '}
                        </span>
                        <span className='border-l-[2px] pl-2 text-[10pt]'>
                          {' '}
                          {currency(
                            toCurrency(dadosAlteracaoOrcamentaria.valorTotal),
                            currencyOptions
                          ).format()}{' '}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                    Despesa a Suplementar
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Secretaria:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {
                          getSecretariaDot(
                            dadosAlteracaoOrcamentaria.dotacaoNova
                          ).nome
                        }{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Departamento:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.dotacaoNova?.departamento
                          ?.nome || ''}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Despesa:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.despesaNova ||
                          '' +
                            ' - ' +
                            dadosAlteracaoOrcamentaria.dotacaoNova?.desc ||
                          ''}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Dotação:{' '}
                      </span>
                      <span className='border-l-[2px] pl-2 text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(dadosAlteracaoOrcamentaria.valorTotal),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Econômica:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.dotacaoNova?.economica
                            ?.codigo || 0
                        ).toString() +
                          ' - ' +
                          dadosAlteracaoOrcamentaria.dotacaoNova?.economica
                            ?.desc || ''}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Cód.Aplicação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {
                          dadosAlteracaoOrcamentaria.dotacaoNova?.codAplicacao
                        }{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Funcional:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.dotacaoNova?.funcional
                            ?.codigo || 0
                        ).toString() +
                          ' - ' +
                          dadosAlteracaoOrcamentaria.dotacaoNova?.funcional
                            ?.desc || ''}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Fonte:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.dotacaoNova?.fonte.toString() ||
                          '' +
                            ' - ' +
                            Fontes[
                              dadosAlteracaoOrcamentaria.dotacaoNova?.fonte
                            ]}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Valor:{' '}
                      </span>
                      <span className='border-l-[2px] pl-2 text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(dadosAlteracaoOrcamentaria.valorTotal),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                </div>
              )
            ) : dadosAlteracaoOrcamentaria.tipoAlteracao ==
              tiposAlteracao['Anulação'] ? (
              <div>
                <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                  Copiar Dados
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Secretaria:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {
                        getSecretariaDot(
                          dadosAlteracaoOrcamentaria.dotacaoCopia
                        ).nome
                      }{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Departamento:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {
                        dadosAlteracaoOrcamentaria.dotacaoCopia.departamento
                          ?.nome
                      }{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Despesa:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {dadosAlteracaoOrcamentaria.despesaCopia +
                        ' - ' +
                        dadosAlteracaoOrcamentaria.dotacaoCopia.desc}{' '}
                    </span>
                  </div>
                  <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Dotação:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {currency(
                        toCurrency(
                          dadosAlteracaoOrcamentaria.dotacaoCopia.valorAtual ||
                            0
                        ),
                        currencyOptions
                      ).format()}{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Econômica:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {(
                        dadosAlteracaoOrcamentaria.dotacaoCopia.economica
                          ?.codigo || 0
                      ).toString() +
                        ' - ' +
                        dadosAlteracaoOrcamentaria.dotacaoCopia.economica
                          ?.desc}{' '}
                    </span>
                  </div>
                  <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Cód.Aplicação:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {
                        dadosAlteracaoOrcamentaria.dotacaoCopia.codAplicacao
                      }{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Funcional:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {(
                        dadosAlteracaoOrcamentaria.dotacaoCopia.funcional
                          .codigo || 0
                      ).toString() +
                        ' - ' +
                        dadosAlteracaoOrcamentaria.dotacaoCopia.funcional
                          ?.desc}{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>Fonte: </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {dadosAlteracaoOrcamentaria.dotacaoCopia.fonte.toString() +
                        ' - ' +
                        Fontes[
                          dadosAlteracaoOrcamentaria.dotacaoCopia.fonte
                        ]}{' '}
                    </span>
                  </div>
                </div>
                <div>
                  <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                    Despesa Anular
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Secretaria:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {
                          getSecretariaDot(
                            dadosAlteracaoOrcamentaria.dotacaoAcao
                          ).nome
                        }{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Departamento:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.dotacaoAcao?.departamento
                          ?.nome || ''}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Despesa:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.despesaAcao ||
                          '' +
                            ' - ' +
                            dadosAlteracaoOrcamentaria.dotacaoAcao?.desc ||
                          ''}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Dotação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(
                            dadosAlteracaoOrcamentaria.dotacaoAcao
                              ?.valorAtual || 0
                          ),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Econômica:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.dotacaoAcao?.economica
                            ?.codigo || 0
                        ).toString() +
                          ' - ' +
                          (dadosAlteracaoOrcamentaria.dotacaoAcao?.economica
                            ?.desc || '')}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Cód.Aplicação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {
                          dadosAlteracaoOrcamentaria.dotacaoAcao?.codAplicacao
                        }{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Funcional:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.dotacaoAcao?.funcional
                            ?.codigo || 0
                        ).toString() +
                          ' - ' +
                          (dadosAlteracaoOrcamentaria.dotacaoAcao?.funcional
                            ?.desc || '')}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Fonte:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.dotacaoAcao?.fonte.toString() ||
                          '' +
                            ' - ' +
                            (Fontes[
                              dadosAlteracaoOrcamentaria.dotacaoAcao?.fonte
                            ] || '')}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Valor:{' '}
                      </span>
                      <span className='border-l-[2px] pl-2 text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(dadosAlteracaoOrcamentaria.valorTotal),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                </div>
                <div>
                  <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                    Despesa Nova
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Secretaria:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.secretaria?.nome}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Departamento:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.departamento?.nome}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Despesa:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.despesaNova ||
                          '' +
                            ' - ' +
                            dadosAlteracaoOrcamentaria.descrDespNova ||
                          ''}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Dotação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(
                            dadosAlteracaoOrcamentaria.dotacaoNova
                              ?.valorAtual || 0
                          ),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Econômica:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.economica?.codigo || 0
                        ).toString() +
                          ' - ' +
                          (dadosAlteracaoOrcamentaria.economica?.desc ||
                            '')}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Cód.Aplicação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.codAplicacao}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Funcional:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.funcional?.codigo || 0
                        ).toString() +
                          ' - ' +
                          (dadosAlteracaoOrcamentaria.funcional?.desc ||
                            '')}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Fonte:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.fonte.toString() ||
                          '' +
                            ' - ' +
                            (Fontes[dadosAlteracaoOrcamentaria.fonte] ||
                              '')}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Valor:{' '}
                      </span>
                      <span className='border-l-[2px] pl-2 text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(dadosAlteracaoOrcamentaria.valorTotal),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                  Copiar Dados
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Secretaria:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {
                        getSecretariaDot(
                          dadosAlteracaoOrcamentaria.dotacaoCopia
                        ).nome
                      }{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Departamento:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {
                        dadosAlteracaoOrcamentaria.dotacaoCopia.departamento
                          ?.nome
                      }{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Despesa:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {dadosAlteracaoOrcamentaria.despesaCopia +
                        ' - ' +
                        dadosAlteracaoOrcamentaria.dotacaoCopia.desc}{' '}
                    </span>
                  </div>
                  <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Dotação:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {currency(
                        toCurrency(
                          dadosAlteracaoOrcamentaria.dotacaoCopia.valorAtual ||
                            0
                        ),
                        currencyOptions
                      ).format()}{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Econômica:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {(
                        dadosAlteracaoOrcamentaria.dotacaoCopia.economica
                          ?.codigo || 0
                      ).toString() +
                        ' - ' +
                        dadosAlteracaoOrcamentaria.dotacaoCopia.economica
                          ?.desc}{' '}
                    </span>
                  </div>
                  <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Cód.Aplicação:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {
                        dadosAlteracaoOrcamentaria.dotacaoCopia.codAplicacao
                      }{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>
                      Funcional:{' '}
                    </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {(
                        dadosAlteracaoOrcamentaria.dotacaoCopia.funcional
                          .codigo || 0
                      ).toString() +
                        ' - ' +
                        dadosAlteracaoOrcamentaria.dotacaoCopia.funcional
                          ?.desc}{' '}
                    </span>
                  </div>
                </div>
                <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                  <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                    <span className='mr-2 text-[12pt] font-bold'>Fonte: </span>
                    <span className='text-[10pt]'>
                      {' '}
                      {dadosAlteracaoOrcamentaria.dotacaoCopia.fonte.toString() +
                        ' - ' +
                        Fontes[
                          dadosAlteracaoOrcamentaria.dotacaoCopia.fonte
                        ]}{' '}
                    </span>
                  </div>
                </div>
                <div>
                  <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[12pt] font-bold'>
                    Despesa Nova
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Secretaria:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.secretaria?.nome}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Departamento:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.departamento?.nome}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Despesa:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.despesaNova ||
                          '' +
                            ' - ' +
                            dadosAlteracaoOrcamentaria.descrDespNova ||
                          ''}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Dotação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(
                            dadosAlteracaoOrcamentaria.dotacaoNova
                              ?.valorAtual || 0
                          ),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Econômica:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.economica?.codigo || 0
                        ).toString() +
                          ' - ' +
                          (dadosAlteracaoOrcamentaria.economica?.desc ||
                            '')}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Cód.Aplicação:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.codAplicacao}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-full overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Funcional:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {(
                          dadosAlteracaoOrcamentaria.funcional?.codigo || 0
                        ).toString() +
                          ' - ' +
                          (dadosAlteracaoOrcamentaria.funcional?.desc ||
                            '')}{' '}
                      </span>
                    </div>
                  </div>
                  <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
                    <div className='w-[60%] overflow-hidden pt-[3px] pr-1 pb-0 pl-6 text-left'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Fonte:{' '}
                      </span>
                      <span className='text-[10pt]'>
                        {' '}
                        {dadosAlteracaoOrcamentaria.fonte.toString() ||
                          '' +
                            ' - ' +
                            (Fontes[dadosAlteracaoOrcamentaria.fonte] ||
                              '')}{' '}
                      </span>
                    </div>
                    <div className='w-[40%] border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 whitespace-nowrap'>
                      <span className='mr-2 text-[12pt] font-bold'>
                        Valor:{' '}
                      </span>
                      <span className='border-l-[2px] pl-2 text-[10pt]'>
                        {' '}
                        {currency(
                          toCurrency(dadosAlteracaoOrcamentaria.valorTotal),
                          currencyOptions
                        ).format()}{' '}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className='w-full overflow-hidden border-l-[2px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1'>
              <span className='text-[10pt] font-bold'>
                CRONOGRAMA FINANCEIRO
              </span>
            </div>

            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>JANEIRO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>FEVEREIRO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>MARÇO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>MAIO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>ABRIL</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>JUNHO</span>
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes1),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes2),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes3),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes4),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes5),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes6),
                  currencyOptions
                ).format()}
              </div>
            </div>

            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>JULHO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>AGOSTO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>SETEMBRO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>OUTUBRO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>NOVEMBRO</span>
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>DEZEMBRO</span>
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes7),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes8),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes9),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes10),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes11),
                  currencyOptions
                ).format()}
              </div>
              <div className='flex w-[16%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {currency(
                  toCurrency(dadosAlteracaoOrcamentaria.valorMes12),
                  currencyOptions
                ).format()}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] pt-[4px] pr-1 pb-0 pl-1 text-[10pt] whitespace-nowrap'>
              <span className='mr-2 font-bold'>OBSERVAÇÃO</span>
              <span
                className='overflow-hidden'
                style={{
                  fontSize: '10pt',
                }}
              >
                {dadosAlteracaoOrcamentaria.obs.toUpperCase()}
              </span>
            </div>
            {dadosAlteracaoOrcamentaria.configsExercicio?.habilitaAssinatura2 &&
            dadosAlteracaoOrcamentaria.economica?.codigo.startsWith(
              dadosAlteracaoOrcamentaria.configsExercicio.economicaAssinatura2
            ) ? (
              <div className='flex h-[214px] w-full text-[10pt] whitespace-nowrap'>
                <div className='flex w-[33%] flex-col overflow-hidden pt-[3px] pr-1 pb-0 pl-1 text-center'>
                  <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                    {((dadosAlteracaoOrcamentaria.gerente &&
                      dadosAlteracaoOrcamentaria.incluirGestor) ||
                      !dadosAlteracaoOrcamentaria.gerente) && (
                      <>
                        <span className='font-bold'>
                          {dadosAlteracaoOrcamentaria.gestor?.nome?.toUpperCase()}
                        </span>
                        <span className='text-[10pt]'>
                          GESTOR(A) DE ORÇAMENTO
                        </span>
                      </>
                    )}
                  </div>
                  <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                    <span className='font-bold'>
                      {dadosAlteracaoOrcamentaria.configsExercicio
                        ?.habilitaAssinatura1
                        ? dadosAlteracaoOrcamentaria.configsExercicio.nomeChefeSecretariaFinancas?.toUpperCase()
                        : null}
                    </span>
                    <span className='text-[10pt]'>
                      {dadosAlteracaoOrcamentaria.configsExercicio
                        ?.habilitaAssinatura1
                        ? dadosAlteracaoOrcamentaria.configsExercicio.assinatura1Linha1.toUpperCase()
                        : null}
                    </span>
                  </div>
                </div>
                <div className='w-[36%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center'>
                  <div className='justify-top flex h-[45%] w-full flex-col align-bottom'>
                    <span className='text-[10pt]'>
                      {siteConfig.nomeAbreviado.toUpperCase()},{' '}
                      {dadosAlteracaoOrcamentaria.dataAbertura
                        .toLocaleDateString('pt-br', {
                          day: '2-digit',
                        })
                        .toUpperCase()}{' '}
                      DE{' '}
                      {dadosAlteracaoOrcamentaria.dataAbertura
                        .toLocaleDateString('pt-br', {
                          month: 'long',
                        })
                        .toUpperCase()}{' '}
                      DE{' '}
                      {dadosAlteracaoOrcamentaria.dataAbertura
                        .toLocaleDateString('pt-br', {
                          year: 'numeric',
                        })
                        .toUpperCase()}
                      .
                    </span>
                  </div>
                  <div className='mt-1 flex h-[48%] w-full flex-col justify-end align-bottom'>
                    <span className='font-bold'>
                      {dadosAlteracaoOrcamentaria.secretario?.nome?.toUpperCase() ||
                        ''}
                    </span>
                    <span className='text-[10pt]'>{secretaria.nome}</span>
                  </div>
                </div>
                <div className='w-[30%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center'>
                  <div className='flex h-[96%] w-full flex-col justify-end align-bottom'>
                    <span className='mt-6'>AUTORIZO</span>
                    <span className='font-bold'>
                      {dadosAlteracaoOrcamentaria.configsExercicio.nomePrefeito.toUpperCase()}
                    </span>
                    <span className='text-[10pt]'>
                      {dadosAlteracaoOrcamentaria.configsExercicio.assinatura2Linha1.toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className='flex h-[214px] w-full text-[10pt] whitespace-nowrap'>
                <div className='flex w-[50%] flex-col overflow-hidden pt-[3px] pr-1 pb-0 pl-1 text-center'>
                  <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                    {((dadosAlteracaoOrcamentaria.gerente &&
                      dadosAlteracaoOrcamentaria.incluirGestor) ||
                      !dadosAlteracaoOrcamentaria.gerente) && (
                      <>
                        <span className='font-bold'>
                          {dadosAlteracaoOrcamentaria.gestor?.nome?.toUpperCase()}
                        </span>
                        <span className='text-[10pt]'>
                          GESTOR(A) DE ORÇAMENTO
                        </span>
                      </>
                    )}
                  </div>
                  <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                    <span className='font-bold'>
                      {dadosAlteracaoOrcamentaria.configsExercicio
                        ?.habilitaAssinatura1
                        ? dadosAlteracaoOrcamentaria.configsExercicio.nomeChefeSecretariaFinancas?.toUpperCase()
                        : null}
                    </span>
                    <span className='text-[10pt]'>
                      {dadosAlteracaoOrcamentaria.configsExercicio
                        ?.habilitaAssinatura1
                        ? dadosAlteracaoOrcamentaria.configsExercicio.assinatura1Linha1.toUpperCase()
                        : null}
                    </span>
                  </div>
                </div>
                <div className='w-[50%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center'>
                  <div className='justify-top flex h-[45%] w-full flex-col align-bottom'>
                    <span className='text-[10pt]'>
                      {siteConfig.nomeAbreviado.toUpperCase()},{' '}
                      {dadosAlteracaoOrcamentaria.dataAbertura
                        .toLocaleDateString('pt-br', {
                          day: '2-digit',
                        })
                        .toUpperCase()}{' '}
                      DE{' '}
                      {dadosAlteracaoOrcamentaria.dataAbertura
                        .toLocaleDateString('pt-br', {
                          month: 'long',
                        })
                        .toUpperCase()}{' '}
                      DE{' '}
                      {dadosAlteracaoOrcamentaria.dataAbertura
                        .toLocaleDateString('pt-br', {
                          year: 'numeric',
                        })
                        .toUpperCase()}
                      .
                    </span>
                  </div>
                  <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                    <span className='mt-6 font-bold'>
                      {dadosAlteracaoOrcamentaria.secretario?.nome.toUpperCase() ||
                        ''}
                    </span>
                    <span className='text-[10pt]'>{secretaria.nome}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      }
    </>
  );
};
