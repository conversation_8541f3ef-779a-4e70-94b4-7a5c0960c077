'use server';

import { prisma } from '@/lib/prisma';
import {
  obterIpUsuarioConectado,
  salvarDotacoesUsuarioUuid,
  temPermissao,
} from '../usuarios';
import { Mo<PERSON><PERSON> } from '@/lib/modulos';
import { AuditoriaGerenciamentoUsuarios, Permissoes } from '@/lib/enums';
import {
  auditoriaErroSchema,
  editarUsuarioSchema,
  idSchema,
  permissaoSchema,
  usuarioSchema,
} from '@/lib/validation';
import { createSuperClient } from '@/lib/supabase/server';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { revalidatePath } from 'next/cache';
import { inserirErroAudit } from '../auditoria/erro';
import z from 'zod';

export const listarCargosAtivos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const cargos = await prisma.cargos.findMany({
      where: { ativo: true },
      orderBy: { nome: 'asc' },
    });
    return {
      data: cargos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter cargos.`,
    };
  }
};

export const obterUsuarioECargos = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { id } = parsedParams.data;

  try {
    const user = await prisma.user_profiles.findFirst({
      where: {
        id: id,
      },
      include: {
        user_profiles_cargos: true,
      },
    });
    return {
      data: user,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao obter usuário e cargos.',
    };
  }
};

const convidarUsuario = async (email: string) => {
  //Uso Interno

  try {
    const supabase = createSuperClient();
    const resultado = await supabase.auth.admin.inviteUserByEmail(email);
    if (resultado.error) {
      throw new Error('Erro ao convidar usuário. ' + resultado.error.message);
    }
    return {
      data: resultado.data,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao convidar usuário.`,
    };
  }
};

export const criarUsuario = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = usuarioSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { nome, email, cargos } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    const usuario = await prisma.user_profiles.findFirst({
      where: {
        email: email,
      },
    });
    if (usuario) {
      return {
        error: 'Email já cadastrado.',
      };
    }

    const convite = await convidarUsuario(email);
    if (convite.error || !convite.data) {
      return {
        error: 'Erro ao convidar usuário.',
      };
    }

    await prisma.$transaction(async (tx) => {
      const criarPerfil = await tx.user_profiles.create({
        data: {
          nome,
          email,
          user_id: convite.data.user.id,
          exercicio: new Date().getFullYear(),
        },
      });

      const audit = await tx.gerenciamentoUsuarios_audit.create({
        data: {
          usuarioId: resultPermissao.idUsuario!,
          acao: AuditoriaGerenciamentoUsuarios.CRIAR_USUARIO,
          usuarioModificadoId: criarPerfil.id,
          ip,
        },
      });

      const cargoPromises = cargos.map(async (cargo) => {
        return tx.user_profiles_cargos.create({
          data: {
            cargoId: cargo,
            idUsuario: criarPerfil.id,
          },
        });
      });

      const auditPromises = cargos.map(async (cargo) => {
        return tx.gerenciamentoUsuarios_audit.update({
          where: {
            id: audit.id,
          },
          data: {
            gerenciamentoUsuarios_audit_cargos: {
              create: {
                cargoId: cargo,
              },
            },
          },
        });
      });

      await Promise.all(cargoPromises);
      await Promise.all(auditPromises);
    });

    await salvarDotacoesUsuarioUuid({
      uuid: convite.data.user.id,
    });

    revalidatePath('/gerenciamento/usuarios');
    return {
      data: true,
    };
  } catch (e) {
    console.log(e);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: 'Email já cadastrado.',
        };
      }
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar usuário.`,
    };
  }
};

export const alterarCargosUsuario = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = editarUsuarioSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { nome, cargos, id } = parsedParams.data;
  try {
    let userUuid;
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const deleteResult = await tx.user_profiles.update({
        where: {
          id,
        },
        data: {
          user_profiles_cargos: {
            deleteMany: {},
          },
          nome,
        },
      });
      const audit = await tx.gerenciamentoUsuarios_audit.create({
        data: {
          usuarioId: resultPermissao.idUsuario!,
          acao: AuditoriaGerenciamentoUsuarios.ALTERAR_CARGOS,
          usuarioModificadoId: deleteResult.id,
          ip,
        },
      });

      const cargoPromises = cargos.map(async (cargo) => {
        return tx.user_profiles_cargos.create({
          data: {
            cargoId: cargo,
            idUsuario: id,
          },
        });
      });

      const auditPromises = cargos.map(async (cargo) => {
        return tx.gerenciamentoUsuarios_audit.update({
          where: {
            id: audit.id,
          },
          data: {
            gerenciamentoUsuarios_audit_cargos: {
              create: {
                cargoId: cargo,
              },
            },
          },
        });
      });

      await Promise.all(cargoPromises);
      await Promise.all(auditPromises);
      userUuid = deleteResult.user_id;
    });

    await salvarDotacoesUsuarioUuid({
      uuid: userUuid,
    });

    revalidatePath('/gerenciamento/usuarios');
    return {
      data: true,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao alterar cargos do usuário.`,
    };
  }
};

export const listarPerfisUsuarios = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const usuarios = await prisma.user_profiles.findMany({
      orderBy: { nome: 'asc' },
    });
    return {
      data: usuarios,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter usuários.`,
    };
  }
};

export const ativarUsuario = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.user_profiles.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });
      await tx.gerenciamentoUsuarios_audit.create({
        data: {
          usuarioId: resultPermissao.idUsuario!,
          usuarioModificadoId: result.id,
          acao: AuditoriaGerenciamentoUsuarios.ATIVAR_USUARIO,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/usuarios');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar usuário.`,
    };
  }
};

export const desativarUsuario = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.user_profiles.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });
      await tx.gerenciamentoUsuarios_audit.create({
        data: {
          usuarioId: resultPermissao.idUsuario!,
          usuarioModificadoId: result.id,
          acao: AuditoriaGerenciamentoUsuarios.DESATIVAR_USUARIO,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/usuarios');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar usuário.`,
    };
  }
};
