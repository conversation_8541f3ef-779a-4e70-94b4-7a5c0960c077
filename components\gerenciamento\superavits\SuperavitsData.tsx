import { ErrorAlert } from '@/components/error-alert';
import SuperavitsDatatable from './SuperavitsDataTable';
import { listarSuperavits } from '@/lib/database/gerenciamento/superavit';

export default async function SuperavitsData() {
  const result = await listarSuperavits();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Superavits não encontrados.'} />;

  return <SuperavitsDatatable data={result} />;
}
