import * as z from 'zod';
import {
  AuditoriaAcesso,
  Fontes,
  Permissoes,
  StatusAlteracaoOrcamentaria,
  StatusDocumentoExterno,
  StatusProtocolo,
  StatusAF,
  TipoDocumentoAF,
  tiposAcao,
  TiposAcesso,
  tiposAlteracao,
  TiposAssinatura,
  TiposDecreto,
  TipoOrigemDocumento,
  Unidades,
} from './enums';
import { Modulos } from './modulos';
import { isNumeric } from './utils';

// export const relatorioPedidoCompraSchema = z.object({
//   nReserva: z.coerce.number().min(1).max(9999999).int(),
//   data: z.union([z.coerce.date().optional(), z.literal('')]),
//   imprimirNomeGestor: z.union([z.boolean(), z.literal('true'), z.literal('false')])
//     .transform((value) => value === true || value === 'true')
// });

// export const numeroProtocoloSchema = z.number().int().min(0);

// export const formArquivarProcessoSchema = z.object({
//   secretaria: z.coerce.number().min(0).max(9999999).int().nullable(),
//   tipo: z.coerce.number().min(0).max(9999999).int(),
//   status: z.coerce.number().min(0).max(9999999).int(),
//   ordemPagamento: z.coerce.number().min(1).max(9999999).int().or(z.literal('')).nullable(),
//   empenhos: z.array(z.coerce.number().min(1).max(999999999999).int()).min(1).or(z.literal('')).nullable(),
//   notasFiscais: z.array(z.coerce.number().min(1).max(999999999999999).int()).or(z.literal('')).nullable(),
//   valor: z.number().min(0).or(z.string().transform(moneyUnmask)).nullable(),
//   docCaixa: z.coerce.number().int().min(1),
//   dataPagamento: z.date(),
//   obs: z.string().trim().max(500),
//   fornecedor: z.coerce.number().min(1).int().or(z.literal('nenhum')),
// }).refine((data) => {
//   //False = trigger error
//   if ((data.tipo === TiposProtocolo['Depósito e Retirada']) && (!data.ordemPagamento)) {
//     return true;
//   }
//   if (data.tipo !== TiposProtocolo['Devolução de Adiantamento'] && (data.ordemPagamento === '' ||!data.ordemPagamento)) {
//     return false;
//   }
//   if (data.tipo === TiposProtocolo['Devolução de Adiantamento'] && (data.ordemPagamento !== '' || data.ordemPagamento)) {
//     return false;
//   }
//   return true
// }, {
//   message: 'Informe o número da ordem',
//   path: ['ordemPagamento']
// });

// export const pedidosPlurianuaisShema = z.array(z.object({
//   SECRETARIA: z.string().trim(),
//   ID_SECRETARIA: z.number().int(),
//   COD_SECRET: z.string().trim(),
//   DEPTO: z.string().trim(),
//   COD_DEPTO: z.string().trim(),
//   ECONOMICA: z.string().trim(),
//   ECONOMICA_DESC: z.string().trim(),
//   FUNCIONAL: z.string().trim(),
//   FONTE: z.string().trim(),
//   COD_APL: z.string().trim(),
//   CATEGORIA: z.string().trim(),
//   DESPESA: z.number().int(),
//   NOME_SECRETARIO: z.string().trim(),
//   VL_PLURIANUAL: z.coerce.number(),
//   PLU_JAN: z.coerce.number(),
//   PLU_FEV: z.coerce.number(),
//   PLU_MAR: z.coerce.number(),
//   PLU_ABR: z.coerce.number(),
//   PLU_MAI: z.coerce.number(),
//   PLU_JUN: z.coerce.number(),
//   PLU_JUL: z.coerce.number(),
//   PLU_AGO: z.coerce.number(),
//   PLU_SET: z.coerce.number(),
//   PLU_OUT: z.coerce.number(),
//   PLU_NOV: z.coerce.number(),
//   PLU_DEZ: z.coerce.number(),
//   EXERCICIO: z.number().int(),
//   NUMERORESERVA: z.number().int(),
//   ECONRESERVA: z.string().trim(),
//   ECONRESERVADESCRICAO: z.string().trim(),
// }))

// export const relatorioPedidoPlurianualSchema = z.object({
//   idSecretaria: z.coerce.number().int(),
// });

// export const relatorioEmpenhoSchema = z.object({
//   despesa: z.coerce.number().min(1).max(9999999).int(),
// });

// export const relatorioEmpenhosLiquidadoslSchema = z.object({
//   idSecretaria: z.coerce.number().int(),
//   dataInicial: z.coerce.number().int(),
//   dataFinal: z.coerce.number().int(),
//   empenho: z.coerce.number().int(),
//   despesa: z.coerce.number().int(),
// });

export const formAlterarSenhaSchema = z
  .object({
    senha: z
      .string()
      .min(8, { message: 'A senha deve ter pelo menos 8 caracteres' }),
    confirmarSenha: z
      .string()
      .min(8, { message: 'A senha deve ter pelo menos 8 caracteres' }),
  })
  .refine((data) => data.senha === data.confirmarSenha, {
    message: 'As senhas devem ser iguais',
    path: ['confirmarSenha'],
  });

export const formCadastroSchema = z
  .object({
    nome: z.string().trim().max(100).min(3),
    senha: z
      .string()
      .min(8, { message: 'A senha deve ter pelo menos 8 caracteres' }),
    confirmarSenha: z
      .string()
      .min(8, { message: 'A senha deve ter pelo menos 8 caracteres' }),
  })
  .refine((data) => data.senha === data.confirmarSenha, {
    message: 'As senhas devem ser iguais',
    path: ['confirmarSenha'],
  });

export const mfaSchema = z.object({
  token: z.string().min(6).max(6),
});

export const emailFormSchema = z.object({
  email: z.string().min(2).email(),
});

export const codigoSchema = z.object({
  codigo: z.coerce.number().int().min(1).max(99),
});

export const fornecedorSchema = z.object({
  codigo: z.coerce.number().int().min(1).max(99),
  nome: z.string().min(2),
  cnpjCpf: z.string().max(18).optional().or(z.literal('')),
  email: z
    .string()
    .email({ message: 'E-Mail Inválido' })
    .optional()
    .or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
});

export const editarfornecedorSchema = z.object({
  id: z.number().int(),
  codigo: z.coerce.number().int().min(1).max(99),
  nome: z.string().min(2),
  cnpjCpf: z.string().max(18).optional().or(z.literal('')),
  email: z
    .string()
    .email({ message: 'E-Mail Inválido' })
    .optional()
    .or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
});

export const cargoFormSchema = z.object({
  nome: z.string().min(2).max(100),
  tipoAcesso: z.nativeEnum(TiposAcesso),
  tipoAssinatura: z.nativeEnum(TiposAssinatura),
});

export const cargoSchema = z.object({
  nome: z.string().min(2).max(100),
  permissions: z.array(
    z.object({
      moduloId: z.number().int(),
      read: z.boolean(),
      write: z.boolean(),
      update: z.boolean(),
      delete: z.boolean(),
    })
  ),
  tipoAcesso: z.nativeEnum(TiposAcesso),
  tipoAssinatura: z.nativeEnum(TiposAssinatura),
  acessos: z.array(z.number().int()),
});

export const editarCargoSchema = z.object({
  id: z.number().int(),
  nome: z.string().min(2).max(100),
  permissions: z.array(
    z.object({
      moduloId: z.number().int(),
      read: z.boolean(),
      write: z.boolean(),
      update: z.boolean(),
      delete: z.boolean(),
    })
  ),
  tipoAcesso: z.nativeEnum(TiposAcesso),
  tipoAssinatura: z.nativeEnum(TiposAssinatura),
  acessos: z.array(z.number().int()),
});

export const secretariaFormSchema = z.object({
  codigo: z.coerce.number().int().min(1).max(99),
  nome: z.string().min(2).max(150),
});

export const secretariaSchema = z.object({
  codigo: z.coerce.number().int().min(1).max(99),
  nome: z.string().min(2).max(150),
  departamentos: z.array(
    z.object({
      codigo: z.coerce.number().int().min(1).max(99),
      nome: z.string().min(2).max(150),
      subdepartamentos: z.array(
        z.object({
          codigo: z.coerce.number().int().min(1).max(99),
          nome: z.string().min(2).max(150),
        })
      ),
    })
  ),
});

export const nomeSchema = z.object({
  nome: z.string().min(2).max(150),
});

export const nomeEIdSchema = z.object({
  id: z.coerce.number().int().min(1),
  nome: z.string().min(2).max(150),
});

export const idSchema = z.object({
  id: z.coerce.number().int().min(1),
});

export const idExercicioSchema = z.object({
  id: z.coerce.number().int().min(1),
  exercicio: z.number().int(),
});

export const secretariaEDepartamentoIdsSchema = z.object({
  idSecretaria: z.coerce.number().int().min(1),
  idDepartamento: z.coerce.number().int().min(1),
});

export const novoDepartamentoSchema = z.object({
  secretariaId: z.coerce.number().int().min(1),
  codigo: z.coerce.number().int().min(1).max(99),
  nome: z.string().min(2).max(150),
});

export const novoSubdepartamentoSchema = z.object({
  departamentoId: z.coerce.number().int().min(1),
  codigo: z.coerce.number().int().min(1).max(99),
  nome: z.string().min(2).max(150),
});

export const usuarioFormSchema = z.object({
  nome: z.string().min(2).max(150),
  email: z.string().min(2).max(150).email(),
});

export const usuarioSchema = z.object({
  nome: z.string().min(2).max(150),
  email: z.string().min(2).max(150).email(),
  cargos: z.array(z.coerce.number().int().min(1)),
});

export const editarUsuarioSchema = z.object({
  nome: z.string().min(2).max(150),
  id: z.coerce.number().int().min(1),
  cargos: z.array(z.coerce.number().int().min(1)),
});

export const emailSchema = z.object({
  email: z.string().min(2).max(150).email(),
});

export const uuidSchema = z.object({
  uuid: z.string().uuid(),
});

export const permissaoSchema = z.object({
  modulo: z.nativeEnum(Modulos),
  permissao: z.nativeEnum(Permissoes),
  retornarIdUsuario: z.boolean().default(false).optional(),
  retornarExercicioUsuario: z.boolean().default(false).optional(),
  bearer: z.string().optional(),
});

export const auditoriaAcessoSchema = z.object({
  acao: z.nativeEnum(AuditoriaAcesso),
});

export const auditoriaErroSchema = z.object({
  erro: z.string(),
  modulo: z.nativeEnum(Modulos).optional(),
});

export const criarEconomicaSchema = z
  .object({
    codigo: z.string().min(12).max(12),
    desc: z.string().min(2).max(150),
    info: z.string().min(0).max(200),
    obs: z.string().min(0).max(200),
  })
  .refine(
    (data) => {
      const economicaArr = data.codigo.split('.');
      if (
        economicaArr.length !== 5 ||
        !isNumeric(economicaArr[0]) ||
        !isNumeric(economicaArr[1]) ||
        !isNumeric(economicaArr[2]) ||
        !isNumeric(economicaArr[3]) ||
        !isNumeric(economicaArr[4]) ||
        Number(economicaArr[0]) > 9 ||
        Number(economicaArr[1]) > 9 ||
        Number(economicaArr[2]) > 99 ||
        Number(economicaArr[3]) > 99 ||
        Number(economicaArr[4]) > 99
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'Código de econômica inválido.',
      path: ['codigo'],
    }
  );

export const editarEconomicaSchema = z.object({
  id: z.coerce.number().int().min(1),
  desc: z.string().min(2).max(150),
  info: z.string().min(0).max(200),
  obs: z.string().min(0).max(200),
});

export const criarFuncionalSchema = z
  .object({
    codigo: z.string().min(16).max(16),
    desc: z.string().min(2).max(150),
  })
  .refine(
    (data) => {
      const funcionalArr = data.codigo.split(' ');
      if (
        funcionalArr.length !== 4 ||
        !isNumeric(funcionalArr[0]) ||
        !isNumeric(funcionalArr[1]) ||
        !isNumeric(funcionalArr[2]) ||
        !isNumeric(funcionalArr[3]) ||
        Number(funcionalArr[0]) > 99 ||
        Number(funcionalArr[1]) > 999 ||
        Number(funcionalArr[2]) > 9999 ||
        Number(funcionalArr[3]) > 9999
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'Código de funcional inválido.',
      path: ['codigo'],
    }
  );

export const criarDotacaoSchema = z.object({
  despesa: z.coerce.number().min(1).max(9999999).int(),
  desc: z.string().min(2).max(150),
  fonte: z.coerce
    .number()
    .int()
    .refine((data) => Fontes[data] !== undefined, {
      message: 'Fonte inválida.',
      path: ['fonte'],
    }),
  codAplicacao: z.coerce.number().int().min(1000000).max(9999999),
  funcionalId: z.coerce.number().int().min(1),
  economicaId: z.coerce.number().int().min(1),
  secretariaId: z.coerce.number().int().min(1).optional(),
  departamentoId: z.coerce.number().int().min(1).optional(),
  subdepartamentoId: z.coerce.number().int().min(1).optional(),
  valorInicial: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaReducaoInicial: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  suplementacao: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  anulacao: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
});

export const alterarCotasFormSchema = z.object({
  id: z.number().int(),
  cotaMes1: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes2: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes3: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes4: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes5: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes6: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes7: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes8: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes9: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes10: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes11: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes12: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
});

export const cotaReducaoSchema = z.object({
  id: z.number().int(),
  motivo: z.string().min(2).max(255),
  cotaMes1: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes2: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes3: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes4: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes5: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes6: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes7: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes8: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes9: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes10: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes11: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  cotaMes12: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
});

export const superavitSchema = z.object({
  // id: z.number().int(),
  exercicio: z.number().int(),
  fonte: z.coerce
    .number()
    .int()
    .refine((data) => Fontes[data] !== undefined, {
      message: 'Fonte inválida.',
      path: ['fonte'],
    }),
  codAplicacao: z.coerce.number().int().min(1000000).max(9999999),
  valorReceita: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  dotInicial: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  suplementado: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  valReserva: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  saldoAtual: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  idDotacao: z.number().int().optional(),
});

export const editarSuperavitSchema = z.object({
  id: z.number().int(),
  exercicio: z.number().int(),
  fonte: z.coerce
    .number()
    .int()
    .refine((data) => Fontes[data] !== undefined, {
      message: 'Fonte inválida.',
      path: ['fonte'],
    }),
  codAplicacao: z.coerce.number().int().min(1000000).max(9999999),
  valorReceita: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  dotInicial: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  suplementado: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  valReserva: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
  saldoAtual: z.coerce.number().multipleOf(0.01).or(z.literal(0)).optional(),
});

export const alteracaoOrcamentariaSchema = z.object({
  id: z.number().int(),
  exercicio: z.number().int(),
  tipoAlteracao: z.nativeEnum(tiposAlteracao),
  tipoAcao: z.nativeEnum(tiposAcao),
  status: z.nativeEnum(StatusAlteracaoOrcamentaria),
  valorTotal: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes1: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes2: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes3: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes4: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes5: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes6: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes7: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes8: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes9: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes10: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes11: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  valorMes12: z.coerce.number().multipleOf(0.01).or(z.literal(0)),
  // dataAbertura: z.date(),
  // dataStatus: z.date(),
  obs: z.string().min(2).max(255).optional(),
  fonte: z.coerce
    .number()
    .int()
    .refine((data) => Fontes[data] !== undefined, {
      message: 'Fonte inválida.',
      path: ['fonte'],
    })
    .optional(),
  codAplicacao: z.coerce
    .number()
    .int()
    .min(1000000)
    .max(9999999)
    .optional()
    .or(z.literal(0)),
  idSecretaria: z.coerce.number().min(1).int(),
  idDepto: z.coerce.number().min(1).int(),
  idEconomica: z.coerce.number().int().optional().or(z.literal(0)).nullable(),
  idFuncional: z.coerce.number().int().min(1),
  idSecretario: z.coerce.number().int().optional().or(z.literal(0)).nullable(),
  despesaCopia: z.coerce
    .number()
    .max(9999999)
    .int()
    .optional()
    .or(z.literal(0))
    .nullable(),
  despesaAcao: z.coerce.number().max(9999999).int().optional(),
  // idUsuario: z.coerce.number().int().min(1),
  // dataDecreto: z.date().optional(),
  // tipoDecreto: z.string().min(2).max(30).optional(),
  // numDecreto: z.coerce.number().int().min(1).max(99).optional(),
  despesaNova: z.coerce.number().max(9999999).int().or(z.literal(0)).optional(),
  descrDespNova: z.string().max(150).optional(),
});

// export const reprovarAlteracaoOrcamentariaSchema = z.object({})

export const insereNovaDespesaSchema = z.object({
  id: z.number().int(),
  despesaNova: z.coerce.number().min(1).max(9999999).int(),
  descrDespNova: z.string().min(2).max(150),
});

export const aprovarAlteracaoOrcamentariaSchema = z.object({
  id: z.number().int().optional(),
  dataDecreto: z.date(),
  tipoDecreto: z.nativeEnum(TiposDecreto),
  numDecreto: z.coerce.number().int().min(1).max(99999999999),
});

export const itemFormSchema = z.object({
  quantidade: z.coerce.number().min(0.0001),
  unidade: z.nativeEnum(Unidades),
  desc: z.string().min(2).max(3000),
  valor: z.coerce.number().multipleOf(0.001).or(z.literal(0)),
});

export const criarReservaSchema = z.object({
  despesa: z.coerce.number().min(1).max(9999999).int(),
  economicaItemId: z.coerce.number().int().min(1),
  obs: z.string().min(0).max(255).optional(),
  resumo: z.string().min(2).max(255).optional(),
  itens: z.array(itemFormSchema).optional(),
  idSecretario: z.coerce.number().int().min(1),
  idDiretor: z.coerce.number().int().min(1),
  idPrefeito: z.coerce.number().int().min(1),

  usarMes1: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes2: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes3: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes4: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes5: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes6: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes7: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes8: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes9: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes10: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes11: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes12: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),

  pedidoPlurianual: z.boolean().default(false),

  pluUsarMes1: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes2: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes3: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes4: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes5: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes6: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes7: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes8: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes9: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes10: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes11: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes12: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),

  valorDisponivelCota: z.coerce.number().multipleOf(0.001).or(z.literal(0)),
});

export const redistribuirCotasSchema = z.object({
  dotacaoId: z.coerce.number().int().min(1).optional(),
  despesa: z.coerce.number().min(1).int().optional(),
  exercicio: z.coerce.number().int().min(1),
});

export const editarReservaSchema = z.object({
  id: z.coerce.number().int().min(1),
  economicaItemId: z.coerce.number().int().min(1),
  obs: z.string().min(0).max(255).optional(),
  resumo: z.string().min(2).max(255).optional(),
  itens: z.array(itemFormSchema).optional(),
  idSecretario: z.coerce.number().int().min(1),
  idDiretor: z.coerce.number().int().min(1),
  idPrefeito: z.coerce.number().int().min(1),

  usarMes1: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes2: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes3: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes4: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes5: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes6: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes7: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes8: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes9: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes10: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes11: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  usarMes12: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),

  pedidoPlurianual: z.boolean().default(false),

  pluUsarMes1: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes2: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes3: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes4: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes5: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes6: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes7: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes8: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes9: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes10: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes11: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  pluUsarMes12: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),

  valorDisponivelCota: z.coerce.number().multipleOf(0.001).or(z.literal(0)),
});

export const idBearerSchema = z.object({
  id: z.coerce.number().int().min(1),
  bearer: z.string().optional(),
});

export const bearerSchema = z.object({
  bearer: z.string(),
});

export const optionalBearerSchema = z
  .object({
    bearer: z.string().optional(),
  })
  .optional();

export const cancelarReservaSchema = z.object({
  id: z.coerce.number().int().min(1),
  motivo: z.string().min(2).max(255),
});

export const imprimirReservaSchema = z.object({
  id: z.coerce.number().int().min(1),
  bearer: z.string().optional(),
  incluirGestor: z.boolean().default(true),
});

export const imprimirAlteracaoSchema = z.object({
  id: z.coerce.number().int().min(1),
  bearer: z.string().optional(),
  incluirGestor: z.boolean().default(true),
});

export const assinarPDFReservaSchema = z.object({
  idDotacao: z.coerce.number().int().min(1),
  idReserva: z.coerce.number().int().min(1),
  tipoAssinatura: z.nativeEnum(TiposAssinatura),
  senha: z.string(),
});

export const uploadCertSchema = z.object({
  certificado: z
    .instanceof(File)
    .refine((file) => file.name.endsWith('.pfx'), {
      message: 'Arquivo precisa ter a extensão .pfx',
    })
    .refine((file) => file.type === 'application/x-pkcs12', {
      message: 'Arquivo precisa ser um certificado PFX válido',
    }),
});

export const solicitarAssinaturasReservaSchema = z.object({
  idReserva: z.coerce.number().int().min(1),
  // pdfFile: z.instanceof(File).refine((file) => file.name.endsWith('.pdf'), {
  //   message: 'Arquivo precisa ter a extensão .pdf',
  // }),
  gestor: z.coerce.number().int().min(1).or(z.literal(false)),
  secretario: z.coerce.number().int().min(1).or(z.literal(false)),
  diretor: z.coerce.number().int().min(1).or(z.literal(false)),
  prefeito: z.coerce.number().int().min(1).or(z.literal(false)),
});

export const assinarReservaSchema = z.object({
  idReserva: z.coerce.number().int().min(1),
  idAssinatura: z.coerce.number().int().min(1),
  senha: z.string(),
});

export const criarContratoSchema = z.object({
  id: z.coerce.number().int(),
  // exercicio: z.number().int(),
  idFornecedor: z.coerce.number().int().min(1),
  processo: z.coerce.number().int().min(1),
  processoAno: z.coerce.number().int(),
  valor: z.coerce.number().multipleOf(0.001).positive().or(z.literal(0)),
  nomeGestor: z.string(),
  cpfGestor: z.string(),
  condPagamento: z.string(),
  numAf: z.coerce.number().int().min(1),
  dataInicio: z.date().optional(),
  dataFinal: z.date().optional(),
});

export const imprimirContratoSchema = z.object({
  id: z.coerce.number().int().min(1),
  bearer: z.string().optional(),
  incluirGestor: z.boolean().default(true),
});

export const criarUsuarioExternoSchema = z.object({
  user_id: z.string().uuid(),
  nome: z.string().min(1),
  email: z.string().email(),
});

export const obterUsuarioExternoPorUuidSchema = z.object({
  uuid: z.string().uuid(),
});

export const usuarioExternoSchema = z.object({
  nome: z.string().min(2).max(150),
  email: z.string().min(2).max(150).email(),
});

// Schemas para Sistema de Assinaturas Externas
export const criarDocumentoExternoSchema = z.object({
  idOrigemTipo: z.nativeEnum(TipoOrigemDocumento),
  idOrigemRegistro: z.coerce.number().int().min(1),
  idUsuarioExterno: z.coerce.number().int().min(1),
  nomeDocumento: z.string().min(2).max(255),
  descricaoDocumento: z.string().max(500).optional(),
  instrucoes: z.string().max(1000).optional(),
  idTemplate: z.coerce.number().int().min(1).optional(),
});

export const templateDocumentoSchema = z.object({
  nome: z.string().min(2).max(255),
  descricao: z.string().max(500).optional(),
  conteudoHtml: z.string().min(1),
});

export const editarTemplateDocumentoSchema = templateDocumentoSchema.extend({
  id: z.coerce.number().int().min(1),
});

export const uploadDocumentoAssinadoSchema = z.object({
  idDocumentoExterno: z.coerce.number().int().min(1),
  pathDocumentoAssinado: z.string().min(1),
});

// Schemas adicionais para Documentos Externos
export const solicitarAssinaturaExternaSchema = z.object({
  idOrigemTipo: z.nativeEnum(TipoOrigemDocumento),
  idOrigemRegistro: z.coerce.number().int().min(1),
  idUsuarioExterno: z.coerce.number().int().min(1),
  idTemplate: z.coerce.number().int().min(1),
  nomeDocumento: z.string().min(2).max(255),
  descricaoDocumento: z.string().max(500).optional(),
  instrucoes: z.string().max(1000).optional(),
  variaveisPersonalizadas: z.record(z.string()).optional(),
});

export const validarAssinaturaDigitalSchema = z.object({
  idDocumentoExterno: z.coerce.number().int().min(1),
  forcarValidacao: z.boolean().default(false).optional(),
});

export const cancelarDocumentoExternoSchema = z.object({
  idDocumentoExterno: z.coerce.number().int().min(1),
  motivoCancelamento: z.string().min(2).max(500),
});

export const listarDocumentosExternosSchema = z.object({
  idUsuarioExterno: z.coerce.number().int().min(1).optional(),
  status: z.nativeEnum(StatusDocumentoExterno).optional(),
  idOrigemTipo: z.nativeEnum(TipoOrigemDocumento).optional(),
});

// Schemas para Sistema de Protocolos
export const listarProtocolosSchema = z.object({
  dias: z.coerce.number().int().min(0).optional(),
  status: z.nativeEnum(StatusProtocolo).optional(),
  pedidoDe: z.coerce.number().int().min(0).optional(),
  pedidoAte: z.coerce.number().int().min(0).optional(),
  afDe: z.coerce.number().int().min(0).optional(),
  afAte: z.coerce.number().int().min(0).optional(),
  exercicioAF: z.coerce.number().int().min(0).optional(),
  idSecretaria: z.coerce.number().int().min(0).optional(),
  idDepartamento: z.coerce.number().int().min(0).optional(),
  idDespesa: z.coerce.number().int().min(0).optional(),
});

export const protocoloIdSchema = z.object({
  id: z.coerce.number().int().min(1),
  bearer: z.string().optional(),
});

export const criarProtocoloSchema = z.object({
  idReserva: z.coerce.number().int().min(1),
  resumo: z.string().min(2).max(255).optional(),
  obs: z.string().max(500).optional(),
});

export const alterarStatusProtocoloSchema = z.object({
  id: z.coerce.number().int().min(1),
  novoStatus: z.nativeEnum(StatusProtocolo),
  obs: z.string().min(2).max(255),
});

export const alterarStatusLoteSchema = z.object({
  ids: z.array(z.coerce.number().int().min(1)).min(1).max(100),
  novoStatus: z.nativeEnum(StatusProtocolo),
  obs: z.string().min(2).max(255),
});

export const vincularAFSchema = z.object({
  id: z.coerce.number().int().min(1),
  numeroAF: z.coerce.number().int().min(1),
  exercicioAF: z.coerce.number().int().min(0),
});

export const vincularEmpenhoSchema = z.object({
  id: z.coerce.number().int().min(1),
  numeroEmpenho: z.coerce.number().int().min(1),
  exercicioEmpenho: z.coerce.number().int().min(0),
});

export const consultarProtocolosSchema = z.object({
  protocolo: z.coerce.number().int().min(0).optional(),
  exercicio: z.coerce.number().int().min(0).optional(),
  idReserva: z.coerce.number().int().min(0).optional(),
});

export const imprimirProtocolosSchema = z.object({
  ids: z.array(z.coerce.number().int().min(1)).min(1),
  bearer: z.string().optional(),
});

// ===== EMPENHOS VALIDATION SCHEMAS =====

export const empenhoIdSchema = z.object({
  id: z.coerce.number().int().min(1),
});

export const criarEmpenhoSchema = z
  .object({
    idReserva: z.coerce.number().int().min(1),
    idFornecedor: z.coerce.number().int().min(1),
    resumo: z.string().min(2).max(255).optional(),
    obs: z.string().max(500).optional(),
    valorTotal: z.coerce.number().positive(),
    // Monthly quotas (must sum to valorTotal)
    usarMes1: z.coerce.number().min(0),
    usarMes2: z.coerce.number().min(0),
    usarMes3: z.coerce.number().min(0),
    usarMes4: z.coerce.number().min(0),
    usarMes5: z.coerce.number().min(0),
    usarMes6: z.coerce.number().min(0),
    usarMes7: z.coerce.number().min(0),
    usarMes8: z.coerce.number().min(0),
    usarMes9: z.coerce.number().min(0),
    usarMes10: z.coerce.number().min(0),
    usarMes11: z.coerce.number().min(0),
    usarMes12: z.coerce.number().min(0),
  })
  .refine(
    (data) => {
      const totalCotas =
        data.usarMes1 +
        data.usarMes2 +
        data.usarMes3 +
        data.usarMes4 +
        data.usarMes5 +
        data.usarMes6 +
        data.usarMes7 +
        data.usarMes8 +
        data.usarMes9 +
        data.usarMes10 +
        data.usarMes11 +
        data.usarMes12;
      return Math.abs(totalCotas - data.valorTotal) < 0.01; // Allow for floating point precision
    },
    {
      message:
        'A soma das cotas mensais deve ser igual ao valor total do empenho',
      path: ['valorTotal'],
    }
  );

export const alterarEmpenhoSchema = z
  .object({
    id: z.coerce.number().int().min(1),
    resumo: z.string().min(2).max(255).optional(),
    obs: z.string().max(500).optional(),
    valorTotal: z.coerce.number().positive(),
    // Monthly quotas (must sum to valorTotal)
    usarMes1: z.coerce.number().min(0),
    usarMes2: z.coerce.number().min(0),
    usarMes3: z.coerce.number().min(0),
    usarMes4: z.coerce.number().min(0),
    usarMes5: z.coerce.number().min(0),
    usarMes6: z.coerce.number().min(0),
    usarMes7: z.coerce.number().min(0),
    usarMes8: z.coerce.number().min(0),
    usarMes9: z.coerce.number().min(0),
    usarMes10: z.coerce.number().min(0),
    usarMes11: z.coerce.number().min(0),
    usarMes12: z.coerce.number().min(0),
  })
  .refine(
    (data) => {
      const totalCotas =
        data.usarMes1 +
        data.usarMes2 +
        data.usarMes3 +
        data.usarMes4 +
        data.usarMes5 +
        data.usarMes6 +
        data.usarMes7 +
        data.usarMes8 +
        data.usarMes9 +
        data.usarMes10 +
        data.usarMes11 +
        data.usarMes12;
      return Math.abs(totalCotas - data.valorTotal) < 0.01;
    },
    {
      message:
        'A soma das cotas mensais deve ser igual ao valor total do empenho',
      path: ['valorTotal'],
    }
  );

export const cancelarEmpenhoSchema = z.object({
  id: z.coerce.number().int().min(1),
  obs: z.string().min(5).max(500),
});

export const anularEmpenhoSchema = z.object({
  id: z.coerce.number().int().min(1),
  valorAnulacao: z.coerce.number().positive(),
  obs: z.string().min(5).max(500),
});

export const estornarAnulacaoSchema = z.object({
  id: z.coerce.number().int().min(1),
  motivo: z.string().min(5).max(500),
});

export const consultarSaldoEmpenhoSchema = z.object({
  id: z.coerce.number().int().min(1),
});

export const imprimirEmpenhoSchema = z.object({
  id: z.coerce.number().int().min(1),
  bearer: z.string().optional(),
});

// AFs (Autorizações de Fornecimento) Schemas
export const afIdSchema = z.object({
  id: z.coerce.number().int().min(1),
});

export const criarAFSchema = z
  .object({
    idEmpenho: z.coerce.number().int().min(1),
    resumo: z.string().min(2).max(255).optional(),
    obs: z.string().max(500).optional(),
    valorTotal: z.coerce.number().positive('O valor da AF deve ser positivo.'),
    dataEmissao: z.coerce.date().optional(),
    dataVencimento: z.coerce.date().optional(),
  })
  .refine(
    (data) => {
      if (data.dataEmissao && data.dataVencimento) {
        return data.dataVencimento >= data.dataEmissao;
      }
      return true;
    },
    {
      message: 'Data de vencimento deve ser maior ou igual à data de emissão',
      path: ['dataVencimento'],
    }
  );

export const editarAFSchema = z
  .object({
    id: z.coerce.number().int().min(1),
    resumo: z.string().min(2).max(255).optional(),
    obs: z.string().max(500).optional(),
    valorTotal: z.coerce.number().positive('O valor da AF deve ser positivo.'),
    dataEmissao: z.coerce.date().optional(),
    dataVencimento: z.coerce.date().optional(),
  })
  .refine(
    (data) => {
      if (data.dataEmissao && data.dataVencimento) {
        return data.dataVencimento >= data.dataEmissao;
      }
      return true;
    },
    {
      message: 'Data de vencimento deve ser maior ou igual à data de emissão',
      path: ['dataVencimento'],
    }
  );

export const cancelarAFSchema = z.object({
  id: z.coerce.number().int().min(1),
  motivo: z
    .string()
    .min(5)
    .max(500, 'O motivo deve ter entre 5 e 500 caracteres.'),
});

export const marcarAFComoUtilizadaSchema = z.object({
  id: z.coerce.number().int().min(1),
  observacao: z.string().max(500).optional(),
});

export const reativarAFSchema = z.object({
  id: z.coerce.number().int().min(1),
  motivo: z
    .string()
    .min(5)
    .max(500, 'O motivo deve ter entre 5 e 500 caracteres.'),
});

export const atualizarDatasAFSchema = z
  .object({
    id: z.coerce.number().int().min(1),
    dataEmissao: z.coerce.date().optional(),
    dataVencimento: z.coerce.date().optional(),
    motivo: z
      .string()
      .min(5)
      .max(500, 'O motivo da atualização deve ter entre 5 e 500 caracteres.'),
  })
  .refine(
    (data) => {
      if (data.dataEmissao && data.dataVencimento) {
        return data.dataVencimento >= data.dataEmissao;
      }
      return true;
    },
    {
      message: 'Data de vencimento deve ser maior ou igual à data de emissão',
      path: ['dataVencimento'],
    }
  )
  .refine(
    (data) => {
      return (
        data.dataEmissao !== undefined || data.dataVencimento !== undefined
      );
    },
    {
      message: 'Pelo menos uma data deve ser informada',
      path: ['dataEmissao'],
    }
  );

export const listarAFsSchema = z.object({
  idEmpenho: z.coerce.number().int().min(1).optional(),
  exercicio: z.coerce.number().int().min(2020).optional(),
  status: z.nativeEnum(StatusAF).optional(),
  numeroAF: z.coerce.number().int().min(1).optional(),
  dataInicio: z.coerce.date().optional(),
  dataFim: z.coerce.date().optional(),
});

export const uploadDocumentoAFSchema = z.object({
  idAF: z.coerce.number().int().min(1),
  tipoDocumento: z.nativeEnum(TipoDocumentoAF),
  nomeArquivo: z.string().min(1).max(255),
  caminhoArquivo: z.string().min(1).max(500),
  tamanho: z
    .number()
    .int()
    .min(1)
    .max(10 * 1024 * 1024), // 10MB
});

export const removerDocumentoAFSchema = z.object({
  idAF: z.coerce.number().int().min(1),
  idDocumento: z.coerce.number().int().min(1),
});

export const consultarSaldoAFSchema = z.object({
  id: z.coerce.number().int().min(1),
});

export const numeroAFSchema = z.object({
  numero: z.coerce.number().int().min(1),
  exercicio: z.coerce.number().int().min(2020),
});

// ===== LIQUIDAÇÕES VALIDATION SCHEMAS =====

// Document validation schema (following afs_documentos pattern)
export const documentoLiquidacaoSchema = z
  .object({
    numeroDocumento: z
      .string()
      .min(1, 'Número do documento é obrigatório')
      .max(50, 'Número do documento deve ter no máximo 50 caracteres'),
    dataEmissao: z.coerce.date({
      errorMap: () => ({ message: 'Data de emissão inválida' }),
    }),
    dataRecebimento: z.coerce.date({
      errorMap: () => ({ message: 'Data de recebimento inválida' }),
    }),
    dataMaterialServico: z.coerce.date().optional(),
    valorDocumento: z.coerce
      .number()
      .positive('Valor do documento deve ser positivo')
      .refine((val) => val <= 999999999.999, 'Valor muito alto'),

    // File upload fields (following afs_documentos pattern)
    nomeArquivo: z.string().max(255).optional(),
    caminhoArquivo: z.string().max(500).optional(),
    tamanho: z.coerce.number().int().min(0).optional(),
  })
  .refine(
    (data) => {
      // Validate date logic
      return data.dataEmissao <= data.dataRecebimento;
    },
    {
      message:
        'Data de emissão deve ser anterior ou igual à data de recebimento',
      path: ['dataRecebimento'],
    }
  );

// Monthly distribution schema (optional, for AUDESP)
export const distribuicaoMensalLiquidacaoSchema = z.object({
  usarJan: z.coerce.number().min(0).default(0),
  usarFev: z.coerce.number().min(0).default(0),
  usarMar: z.coerce.number().min(0).default(0),
  usarAbr: z.coerce.number().min(0).default(0),
  usarMai: z.coerce.number().min(0).default(0),
  usarJun: z.coerce.number().min(0).default(0),
  usarJul: z.coerce.number().min(0).default(0),
  usarAgo: z.coerce.number().min(0).default(0),
  usarSet: z.coerce.number().min(0).default(0),
  usarOut: z.coerce.number().min(0).default(0),
  usarNov: z.coerce.number().min(0).default(0),
  usarDez: z.coerce.number().min(0).default(0),
});

// Document metadata schema (without file upload requirements)
export const documentoMetadataLiquidacaoSchema = z
  .object({
    numeroDocumento: z
      .string()
      .min(1, 'Número do documento é obrigatório')
      .max(50, 'Número do documento deve ter no máximo 50 caracteres'),
    dataEmissao: z.coerce.date({
      errorMap: () => ({ message: 'Data de emissão inválida' }),
    }),
    dataRecebimento: z.coerce.date({
      errorMap: () => ({ message: 'Data de recebimento inválida' }),
    }),
    dataMaterialServico: z.coerce.date().optional(),
    valorDocumento: z.coerce
      .number()
      .min(0.01, 'Valor do documento deve ser maior que zero'),
  })
  .refine((data) => data.dataEmissao <= data.dataRecebimento, {
    message: 'Data de emissão deve ser anterior ou igual à data de recebimento',
    path: ['dataRecebimento'],
  });

// Initial creation schema (without documents - documents added later in edit mode)
export const criarLiquidacaoInicialSchema = z.object({
  idEmpenho: z.coerce.number().int().min(1, 'Empenho é obrigatório'),
  resumo: z.string().max(255).optional(),
  obs: z.string().max(1000).optional(),
  mesReferencia: z.string().max(15).optional(),
  dataMaterialServico: z.coerce.date().optional(),

  // AUDESP compliance fields (optional)
  audespDataInicio: z.coerce.date().optional(),
  audespRegimeExecucao: z.coerce.number().int().optional(),
  audespCorrenteImportacao: z.coerce.number().int().optional(),
  audespExecucaoContrato: z.coerce.number().int().optional(),

  // Monthly distribution (optional, for AUDESP reporting only)
  distribuicaoMensal: distribuicaoMensalLiquidacaoSchema.optional(),
});

// Full creation schema (with documents - used for edit mode when documents are added)
export const criarLiquidacaoSchema = z
  .object({
    idEmpenho: z.coerce.number().int().min(1, 'Empenho é obrigatório'),
    resumo: z.string().max(255).optional(),
    obs: z.string().max(1000).optional(),
    mesReferencia: z.string().max(15).optional(),
    dataMaterialServico: z.coerce.date().optional(),

    documentos: z
      .array(documentoMetadataLiquidacaoSchema)
      .min(1, 'Adicione pelo menos um documento')
      .max(50, 'Máximo de 50 documentos por liquidação'),

    // AUDESP compliance fields (optional)
    audespDataInicio: z.coerce.date().optional(),
    audespRegimeExecucao: z.coerce.number().int().optional(),
    audespCorrenteImportacao: z.coerce.number().int().optional(),
    audespExecucaoContrato: z.coerce.number().int().optional(),

    // Monthly distribution (optional, for AUDESP reporting only)
    distribuicaoMensal: distribuicaoMensalLiquidacaoSchema.optional(),
  })
  .refine(
    (data) => {
      // Validate total document values
      const totalDocumentos = data.documentos.reduce(
        (sum, doc) => sum + doc.valorDocumento,
        0
      );
      return totalDocumentos > 0;
    },
    {
      message: 'Valor total dos documentos deve ser maior que zero',
      path: ['documentos'],
    }
  )
  .refine(
    (data) => {
      // Validate unique document numbers within liquidacao
      const numeros = data.documentos.map((doc) => doc.numeroDocumento);
      const numerosUnicos = new Set(numeros);
      return numeros.length === numerosUnicos.size;
    },
    {
      message: 'Números de documentos devem ser únicos dentro da liquidação',
      path: ['documentos'],
    }
  );

export const liquidacaoIdSchema = z.object({
  id: z.coerce.number().int().min(1),
});

export const editarLiquidacaoSchema = z
  .object({
    id: z.coerce.number().int().min(1),
    resumo: z.string().max(255).optional(),
    obs: z.string().max(1000).optional(),
    mesReferencia: z.string().max(15).optional(),
    dataMaterialServico: z.coerce.date().optional(),

    documentos: z
      .array(documentoLiquidacaoSchema)
      .min(1, 'Adicione pelo menos um documento')
      .max(50, 'Máximo de 50 documentos por liquidação'),

    // AUDESP compliance fields (optional)
    audespDataInicio: z.coerce.date().optional(),
    audespRegimeExecucao: z.coerce.number().int().optional(),
    audespCorrenteImportacao: z.coerce.number().int().optional(),
    audespExecucaoContrato: z.coerce.number().int().optional(),

    // Monthly distribution (optional, for AUDESP reporting only)
    distribuicaoMensal: distribuicaoMensalLiquidacaoSchema.optional(),
  })
  .refine(
    (data) => {
      // Validate total document values
      const totalDocumentos = data.documentos.reduce(
        (sum, doc) => sum + doc.valorDocumento,
        0
      );
      return totalDocumentos > 0;
    },
    {
      message: 'Valor total dos documentos deve ser maior que zero',
      path: ['documentos'],
    }
  )
  .refine(
    (data) => {
      // Validate unique document numbers within liquidacao
      const numeros = data.documentos.map((doc) => doc.numeroDocumento);
      const numerosUnicos = new Set(numeros);
      return numeros.length === numerosUnicos.size;
    },
    {
      message: 'Números de documentos devem ser únicos dentro da liquidação',
      path: ['documentos'],
    }
  );

export const estornarLiquidacaoSchema = z.object({
  id: z.coerce.number().int().min(1),
  motivo: z
    .string()
    .min(5)
    .max(500, 'O motivo deve ter entre 5 e 500 caracteres.'),
});

export const consultarSaldoEmpenhoParaLiquidacaoSchema = z.object({
  id: z.coerce.number().int().min(1),
});

export const imprimirLiquidacaoSchema = z.object({
  id: z.coerce.number().int().min(1),
  bearer: z.string().optional(),
});

export const uploadDocumentoLiquidacaoSchema = z.object({
  idLiquidacao: z.coerce
    .number()
    .int()
    .min(1, 'ID da liquidação é obrigatório'),
  numeroDocumento: z.string().min(1, 'Número do documento é obrigatório'),
  nomeArquivo: z.string().min(1, 'Nome do arquivo é obrigatório'),
  caminhoArquivo: z.string().min(1, 'Caminho do arquivo é obrigatório'),
  tamanho: z.coerce.number().int().min(1, 'Tamanho do arquivo é obrigatório'),
  valorDocumento: z.coerce
    .number()
    .min(0.01, 'Valor do documento deve ser maior que zero'),
  dataEmissao: z.coerce.date(),
  dataRecebimento: z.coerce.date(),
  dataMaterialServico: z.coerce.date().optional(),
});

export const removerDocumentoLiquidacaoSchema = z.object({
  id: z.coerce.number().int().min(1, 'ID do documento é obrigatório'),
});

export const relatorioEmpenhosSchema = z
  .object({
    exercicio: z.coerce.number().int().min(2020).optional(),
    idSecretaria: z.coerce.number().int().min(1).optional(),
    idDepartamento: z.coerce.number().int().min(1).optional(),
    idSubdepartamento: z.coerce.number().int().min(1).optional(),
    status: z.array(z.coerce.number().int().min(1)).optional(),
    idFornecedor: z.coerce.number().int().min(1).optional(),
    dataInicio: z.coerce.date().optional(),
    dataFim: z.coerce.date().optional(),
    incluirLiquidados: z.boolean().optional(),
    incluirNaoLiquidados: z.boolean().optional(),
    bearer: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.dataInicio && data.dataFim) {
        return data.dataFim >= data.dataInicio;
      }
      return true;
    },
    {
      message: 'Data fim deve ser maior ou igual à data início',
      path: ['dataFim'],
    }
  );

export const relatorioEmpenhosPorPeriodoSchema = z
  .object({
    exercicio: z.coerce.number().int().min(2020).optional(),
    idSecretaria: z.coerce.number().int().min(1).optional(),
    idDepartamento: z.coerce.number().int().min(1).optional(),
    idSubdepartamento: z.coerce.number().int().min(1).optional(),
    status: z.array(z.coerce.number().int().min(1)).optional(),
    idFornecedor: z.coerce.number().int().min(1).optional(),
    periodoInicio: z.coerce.date(),
    periodoFim: z.coerce.date(),
    agruparPor: z
      .enum(['mes', 'secretaria', 'departamento', 'fornecedor'])
      .default('mes'),
    bearer: z.string().optional(),
  })
  .refine(
    (data) => {
      return data.periodoFim >= data.periodoInicio;
    },
    {
      message: 'Período fim deve ser maior ou igual ao período início',
      path: ['periodoFim'],
    }
  );

// ===== EMPENHOS IMPORT VALIDATION SCHEMAS =====

export interface EmpenhoCSVRow {
  numero?: string;
  exercicio?: string;
  idReserva?: string;
  idDotacao?: string;
  idFornecedor?: string;
  fornecedorNome?: string;
  fornecedorCnpjCpf?: string;
  resumo?: string;
  obs?: string;
  valorTotal?: string;
  usarMes1?: string;
  usarMes2?: string;
  usarMes3?: string;
  usarMes4?: string;
  usarMes5?: string;
  usarMes6?: string;
  usarMes7?: string;
  usarMes8?: string;
  usarMes9?: string;
  usarMes10?: string;
  usarMes11?: string;
  usarMes12?: string;
  data?: string;
  rowNumber?: number;
}

export const empenhoCSVRowSchema = z
  .object({
    numero: z.string().optional(),
    exercicio: z.coerce.number().int().min(2020).max(2030).optional(),
    idReserva: z.coerce.number().int().min(1).optional(),
    idDotacao: z.coerce.number().int().min(1).optional(),
    idFornecedor: z.coerce.number().int().min(1).optional(),
    fornecedorNome: z.string().min(2).max(255).optional(),
    fornecedorCnpjCpf: z.string().min(11).max(18).optional(),
    resumo: z.string().min(2).max(255).optional(),
    obs: z.string().max(500).optional(),
    valorTotal: z.coerce.number().positive().optional(),
    usarMes1: z.coerce.number().min(0).optional(),
    usarMes2: z.coerce.number().min(0).optional(),
    usarMes3: z.coerce.number().min(0).optional(),
    usarMes4: z.coerce.number().min(0).optional(),
    usarMes5: z.coerce.number().min(0).optional(),
    usarMes6: z.coerce.number().min(0).optional(),
    usarMes7: z.coerce.number().min(0).optional(),
    usarMes8: z.coerce.number().min(0).optional(),
    usarMes9: z.coerce.number().min(0).optional(),
    usarMes10: z.coerce.number().min(0).optional(),
    usarMes11: z.coerce.number().min(0).optional(),
    usarMes12: z.coerce.number().min(0).optional(),
    data: z.coerce.date().optional(),
    rowNumber: z.number().min(1).optional(),
  })
  .refine(
    (data) => {
      // Must have either idFornecedor or fornecedorNome + fornecedorCnpjCpf
      if (
        !data.idFornecedor &&
        (!data.fornecedorNome || !data.fornecedorCnpjCpf)
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'É necessário informar ID do fornecedor ou Nome + CNPJ/CPF',
      path: ['fornecedorNome'],
    }
  )
  .refine(
    (data) => {
      // Must have either idDotacao or idReserva
      if (!data.idDotacao && !data.idReserva) {
        return false;
      }
      return true;
    },
    {
      message: 'É necessário informar ID da Dotação ou ID da Reserva',
      path: ['idDotacao'],
    }
  )
  .refine(
    (data) => {
      // Monthly quotas must sum to valorTotal
      if (data.valorTotal) {
        const totalCotas = [
          data.usarMes1 || 0,
          data.usarMes2 || 0,
          data.usarMes3 || 0,
          data.usarMes4 || 0,
          data.usarMes5 || 0,
          data.usarMes6 || 0,
          data.usarMes7 || 0,
          data.usarMes8 || 0,
          data.usarMes9 || 0,
          data.usarMes10 || 0,
          data.usarMes11 || 0,
          data.usarMes12 || 0,
        ].reduce((sum, val) => sum + val, 0);

        return Math.abs(totalCotas - data.valorTotal) < 0.01;
      }
      return true;
    },
    {
      message:
        'A soma das cotas mensais deve ser igual ao valor total do empenho',
      path: ['valorTotal'],
    }
  );

export const importarEmpenhosSchema = z.object({
  arquivo: z
    .instanceof(File)
    .refine((file) => file.name.endsWith('.csv'), {
      message: 'Arquivo precisa ter a extensão .csv',
    })
    .refine(
      (file) => file.size > 0 && file.size <= 10 * 1024 * 1024, // 10MB
      {
        message: 'Arquivo deve ter entre 1 byte e 10MB',
      }
    ),
  opcoes: z
    .object({
      criarFornecedores: z.boolean().default(false),
      ignorarErros: z.boolean().default(false),
      atualizarExistentes: z.boolean().default(false),
      validarDisponibilidade: z.boolean().default(true),
    })
    .optional(),
});
