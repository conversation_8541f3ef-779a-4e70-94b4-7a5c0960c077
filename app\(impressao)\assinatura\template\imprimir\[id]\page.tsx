'use server';

import { ErrorAlert } from '@/components/error-alert';
import { isNumeric } from '@/lib/utils';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permis<PERSON><PERSON> } from '@/lib/enums';
import { z } from 'zod';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';
import { obterTemplateProcessado } from '@/lib/database/gerenciamento/templatesDocumentos';
import { sanitizarHtmlTemplate } from '@/lib/utils/seguranca';

export default async function ImprimirTemplatePage({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.ACESSAR,
  };

  const currSearchParams = await searchParams;
  const { id } = await params;

  if (!isNumeric(id)) return <ErrorAlert error='ID inválido.' />;

  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }

  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();

  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }

  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  if (result.error) {
    return <ErrorAlert error={result.error} />;
  }

  if (!result.temPermissao) {
    return <ErrorAlert error='Usuário sem permissão.' />;
  }

  // Obter variáveis dos searchParams
  const variaveis: Record<string, string> = {};
  Object.entries(currSearchParams).forEach(([key, value]) => {
    if (typeof value === 'string' && key.startsWith('var_')) {
      const nomeVariavel = key.replace('var_', '').toUpperCase();
      variaveis[`{{${nomeVariavel}}}`] = value;
    }
  });

  const templateProcessado = await obterTemplateProcessado({
    id: Number(id),
    variaveis,
    bearer,
  });

  if (templateProcessado.error) {
    return <ErrorAlert error={templateProcessado.error} />;
  }

  if (!templateProcessado.data) {
    return <ErrorAlert error='Falha ao obter template.' />;
  }

  return (
    <>
      <div
        className='print-content w-full'
        dangerouslySetInnerHTML={{
          __html: sanitizarHtmlTemplate(
            templateProcessado.data.conteudoProcessado
          ),
        }}
      />
      <ClientCompletionTrigger />
    </>
  );
}
