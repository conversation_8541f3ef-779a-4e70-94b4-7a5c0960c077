'use server';

import { prisma } from '../prisma';
import { createClient, createClientWithBearer } from '../supabase/server';
import {
  auditoriaAcessoSchema,
  emailSchema,
  nomeSchema,
  optionalBearerSchema,
  permissaoSchema,
  uuidSchema,
} from '../validation';
import { Permissoes, TiposAcesso } from '../enums';
import { Modulos } from '../modulos';
import { headers } from 'next/headers';

const obterUsuarioEPermissoesPorUuid = async (params: unknown) => {
  //Uso Interno

  const parsedParams = uuidSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { uuid } = parsedParams.data;

  try {
    const user = await prisma.user_profiles.findFirst({
      where: {
        user_id: uuid,
      },
      include: {
        user_profiles_cargos: {
          include: {
            cargo: {
              include: {
                cargos_permissoes: true,
              },
            },
          },
        },
      },
    });
    return {
      data: user,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: 'Erro ao obter usuário e cargos.',
    };
  }
};

const obterUsuarioEAcessosPorUuid = async (params: unknown) => {
  //Uso Interno

  const parsedParams = uuidSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { uuid } = parsedParams.data;

  try {
    const user = await prisma.user_profiles.findFirst({
      where: {
        user_id: uuid,
      },
      include: {
        user_profiles_cargos: {
          include: {
            cargo: {
              include: {
                cargos_acessos: true,
              },
            },
          },
        },
      },
    });
    return {
      data: user,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: 'Erro ao obter usuário e acessos.',
    };
  }
};

const obterUsuarioPorUuid = async (params: unknown) => {
  //Uso Interno

  const parsedParams = uuidSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { uuid } = parsedParams.data;

  try {
    const user = await prisma.user_profiles.findFirst({
      where: {
        user_id: uuid,
      },
    });
    return {
      data: user,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: 'Erro ao obter usuário.',
    };
  }
};

const obterUuidUsuarioConectado = async () => {
  //Uso Interno

  const supabase = await createClient();
  try {
    const { data, error } = await supabase.auth.getUser();
    if (error || !data?.user) {
      return null;
    }
    return data.user.id;
  } catch (e: any) {
    console.log(e.message);
    return null;
  }
};

const obterUuidUsuarioConectadoBearer = async (bearer: string) => {
  //Uso Interno

  const supabase = await createClientWithBearer(bearer);
  try {
    const { data, error } = await supabase.auth.getUser();
    if (error || !data?.user) {
      return null;
    }
    return data.user.id;
  } catch (e: any) {
    console.log(e.message);
    return null;
  }
};

const obterEmailUsuarioConectado = async () => {
  //Uso Interno

  const supabase = await createClient();
  try {
    const { data, error } = await supabase.auth.getUser();
    if (error || !data?.user) {
      return null;
    }
    return data.user.email;
  } catch (e: any) {
    console.log(e.message);
    return null;
  }
};

export const atualizarExercicioUsuarioConectado = async (exercicio: number) => {
  try {
    const userIdPromise = obterUuidUsuarioConectado();
    const emailPromise = obterEmailUsuarioConectado();

    const [userId, email] = await Promise.all([userIdPromise, emailPromise]);

    if (!userId || !email) {
      return {
        error: `Erro ao buscar acesso do usuário.`,
      };
    }
    const resultado = await prisma.user_profiles.upsert({
      where: {
        user_id: userId,
      },
      create: {
        exercicio: exercicio,
        user_id: userId,
        nome: '',
        email: email || '',
      },
      update: {
        exercicio: exercicio,
      },
    });
    return {
      data: resultado,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao atualizar exercício do usuário.`,
    };
  }
};

export const atualizarCadastroUsuario = async (params: unknown) => {
  const parsedParams = nomeSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { nome } = parsedParams.data;
  try {
    const userId = await obterUuidUsuarioConectado();
    const email = await obterEmailUsuarioConectado();
    if (!userId || !email) {
      return {
        error: `Erro ao buscar acesso do usuário.`,
      };
    }
    const resultado = await prisma.user_profiles.upsert({
      where: {
        user_id: userId,
      },
      update: {
        nome: nome,
        exercicio: new Date().getFullYear(),
      },
      create: {
        nome: nome,
        exercicio: new Date().getFullYear(),
        user_id: userId,
        email: email || '',
      },
    });
    return {
      data: resultado,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao atualizar cadastro do usuário.`,
    };
  }
};

export const obterExercicioUsuarioConectado = async () => {
  try {
    const userId = await obterUuidUsuarioConectado();
    if (!userId) {
      return {
        error: `Erro ao buscar acesso do usuário.`,
      };
    }
    const resultado = await prisma.user_profiles.findFirst({
      where: {
        user_id: userId,
      },
      select: {
        exercicio: true,
      },
    });
    return {
      data: resultado?.exercicio,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao buscar ano de exercício do usuário.`,
    };
  }
};

export const obterNomeUsuarioConectado = async () => {
  try {
    const userId = await obterUuidUsuarioConectado();
    if (!userId) {
      return {
        error: `Erro ao buscar acesso do usuário.`,
      };
    }
    const resultado = await prisma.user_profiles.findFirst({
      where: {
        user_id: userId,
      },
      select: {
        nome: true,
      },
    });
    return {
      data: resultado?.nome,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao buscar nome do usuário.`,
    };
  }
};

export const verificarSeUsuarioAtivoPorEmail = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.NENHUM, //Só verifica se está conectado
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = emailSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { email } = parsedParams.data;
  try {
    const resultado = await prisma.user_profiles.findFirst({
      where: {
        email: email,
      },
      select: {
        ativo: true,
      },
    });
    if (!resultado) {
      return {
        error: `Usuário não encontrado.`,
      };
    }
    return {
      data: resultado.ativo,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao verificar usuário.`,
    };
  }
};

export const verificarSeUsuarioAtivoPorUuid = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.NENHUM, //Só verifica se está conectado
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = uuidSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { uuid } = parsedParams.data;
  try {
    const resultado = await prisma.user_profiles.findFirst({
      where: {
        user_id: uuid,
      },
      select: {
        ativo: true,
      },
    });
    if (!resultado) {
      return {
        error: `Usuário não encontrado.`,
      };
    }
    return {
      data: resultado.ativo,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao verificar usuário.`,
    };
  }
};

export const temPermissao = async (params: unknown) => {
  const parsedParams = permissaoSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const {
    modulo,
    permissao,
    retornarIdUsuario,
    retornarExercicioUsuario,
    bearer,
  } = parsedParams.data;

  const userUuid = bearer
    ? await obterUuidUsuarioConectadoBearer(bearer)
    : await obterUuidUsuarioConectado();

  if (!userUuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }

  if (modulo === Modulos.NENHUM) {
    return {
      temPermissao: true,
    };
  }

  const usuario = await obterUsuarioEPermissoesPorUuid({
    uuid: userUuid,
  });

  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }

  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }

  if (usuario.data.gerente) {
    return {
      temPermissao: true,
      gerente: true,
      idUsuario: retornarIdUsuario ? usuario.data.id : null,
      exercicio: retornarExercicioUsuario ? usuario.data.exercicio : null,
    };
  }

  const permissoes = usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.cargos_permissoes)
    .flat();

  let temPermissao = false;

  switch (permissao) {
    case Permissoes.ACESSAR:
      temPermissao = !!permissoes.find((permissao) => {
        return permissao.moduloId === modulo && permissao.read;
      });
      break;
    case Permissoes.CRIAR:
      temPermissao = !!permissoes.find((permissao) => {
        return permissao.moduloId === modulo && permissao.write;
      });
      break;
    case Permissoes.ALTERAR:
      temPermissao = !!permissoes.find((permissao) => {
        return permissao.moduloId === modulo && permissao.update;
      });
      break;
    case Permissoes.DELETAR:
      temPermissao = !!permissoes.find((permissao) => {
        return permissao.moduloId === modulo && permissao.delete;
      });
      break;
  }
  return {
    temPermissao,
    idUsuario: retornarIdUsuario ? usuario.data.id : null,
    exercicio: retornarExercicioUsuario ? usuario.data.exercicio : null,
  };
};

export const usuarioEhGerente = async () => {
  const userUuid = await obterUuidUsuarioConectado();

  if (!userUuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }

  const usuario = await obterUsuarioPorUuid({
    uuid: userUuid,
  });

  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }

  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }
  return {
    data: usuario.data.gerente,
  };
};

export const listarPermissoesAcessarUsuarioConectado = async () => {
  const userUuid = await obterUuidUsuarioConectado();
  if (!userUuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }
  const usuario = await obterUsuarioEPermissoesPorUuid({
    uuid: userUuid,
  });
  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }
  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }

  const permissoes = usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.cargos_permissoes)
    .flat()
    .filter((permissao) => permissao.read)
    .map((permissao) => permissao.moduloId);

  return {
    data: permissoes,
  };
};

export const listarAcessosOrgaosUsuarioConectado = async () => {
  const userUuid = await obterUuidUsuarioConectado();
  if (!userUuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }
  const usuario = await obterUsuarioEAcessosPorUuid({
    uuid: userUuid,
  });
  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }
  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }

  const acessoTotal = !!usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.tipoAcesso)
    .find((tipoAcesso) => tipoAcesso === TiposAcesso.TODAS_SECRETARIAS);

  if (acessoTotal) {
    return {
      data: {
        acessoTotal: true,
      },
    };
  }

  const acessos = usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.cargos_acessos)
    .flat();

  const acessosSecretarias: number[] = acessos
    .filter((acesso) => acesso.secretariaId !== null)
    .map((acesso) => acesso.secretariaId || 0);
  const acessosDepartamentos = acessos
    .filter((acesso) => acesso.departamentoId !== null)
    .map((acesso) => acesso.departamentoId || 0);
  const acessosSubDepartamentos = acessos
    .filter((acesso) => acesso.subdepartamentoId !== null)
    .map((acesso) => acesso.subdepartamentoId || 0);

  return {
    data: {
      acessoTotal: false,
      acessosSecretarias,
      acessosDepartamentos,
      acessosSubDepartamentos,
    },
  };
};

export const listarIdsDotacoesUsuarioConectadoTemAcesso = async (
  params?: unknown
) => {
  const parsedParams = optionalBearerSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const bearer = parsedParams?.data?.bearer || null;

  const userUuid = bearer
    ? await obterUuidUsuarioConectadoBearer(bearer)
    : await obterUuidUsuarioConectado();
  if (!userUuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }
  const usuario = await obterUsuarioEAcessosPorUuid({
    uuid: userUuid,
  });
  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }
  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }

  const acessoTotal = !!usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.tipoAcesso)
    .find((tipoAcesso) => tipoAcesso === TiposAcesso.TODAS_SECRETARIAS);

  if (acessoTotal) {
    return {
      data: {
        acessoTotal: true,
      },
    };
  }

  const acessos = usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.cargos_acessos)
    .flat();

  const acessosSecretarias: number[] = acessos
    .filter((acesso) => acesso.secretariaId !== null)
    .map((acesso) => acesso.secretariaId || 0);
  const acessosDepartamentos = acessos
    .filter((acesso) => acesso.departamentoId !== null)
    .map((acesso) => acesso.departamentoId || 0);
  const acessosSubDepartamentos = acessos
    .filter((acesso) => acesso.subdepartamentoId !== null)
    .map((acesso) => acesso.subdepartamentoId || 0);

  const dotacoes = await prisma.dotacoes.findMany({
    select: {
      id: true,
    },
    where: {
      exercicio: usuario.data.exercicio,
      OR: [
        { secretariaId: { in: acessosSecretarias } },
        { departamentoId: { in: acessosDepartamentos } },
        { subdepartamentoId: { in: acessosSubDepartamentos } },
      ],
    },
  });

  return {
    data: {
      acessoTotal: false,
      dotacoes: dotacoes.map((dotacao) => dotacao.id),
    },
  };
};

const listarIdsDotacoesUsuarioUuidTemAcesso = async (uuid: string) => {
  //Usando sem exportar, não precisa de validação de parâmetros
  if (!uuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }
  const usuario = await obterUsuarioEAcessosPorUuid({
    uuid,
  });
  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }
  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }

  const acessoTotal = !!usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.tipoAcesso)
    .find((tipoAcesso) => tipoAcesso === TiposAcesso.TODAS_SECRETARIAS);

  if (acessoTotal) {
    return {
      data: {
        acessoTotal: true,
      },
    };
  }

  const acessos = usuario.data.user_profiles_cargos
    .filter((cargo) => cargo.cargo.ativo)
    .map((cargo) => cargo.cargo.cargos_acessos)
    .flat();

  const acessosSecretarias: number[] = acessos
    .filter((acesso) => acesso.secretariaId !== null)
    .map((acesso) => acesso.secretariaId || 0);
  const acessosDepartamentos = acessos
    .filter((acesso) => acesso.departamentoId !== null)
    .map((acesso) => acesso.departamentoId || 0);
  const acessosSubDepartamentos = acessos
    .filter((acesso) => acesso.subdepartamentoId !== null)
    .map((acesso) => acesso.subdepartamentoId || 0);

  const dotacoes = await prisma.dotacoes.findMany({
    select: {
      id: true,
    },
    where: {
      exercicio: usuario.data.exercicio,
      OR: [
        { secretariaId: { in: acessosSecretarias } },
        { departamentoId: { in: acessosDepartamentos } },
        { subdepartamentoId: { in: acessosSubDepartamentos } },
      ],
    },
  });

  return {
    data: {
      acessoTotal: false,
      dotacoes: dotacoes.map((dotacao) => dotacao.id),
    },
  };
};

export const salvarDotacoesUsuarioConectado = async () => {
  const userUuid = await obterUuidUsuarioConectado();
  if (!userUuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }
  const dotacoesIds = await listarIdsDotacoesUsuarioConectadoTemAcesso();
  if (dotacoesIds.error) {
    return {
      error: dotacoesIds.error,
    };
  }
  if (!dotacoesIds.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }
  try {
    await prisma.user_profiles_dotacoes.upsert({
      where: {
        uuidUsuario: userUuid,
      },
      update: {
        dotacoesIds: dotacoesIds.data.dotacoes,
        acessoTotal: dotacoesIds.data.acessoTotal,
      },
      create: {
        uuidUsuario: userUuid,
        dotacoesIds: dotacoesIds.data.dotacoes,
        acessoTotal: dotacoesIds.data.acessoTotal,
      },
    });
  } catch (error) {
    console.log(error);
  }
};

export const salvarDotacoesUsuarioUuid = async (params: unknown) => {
  const parsedParams = uuidSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { uuid } = parsedParams.data;

  const dotacoesIds = await listarIdsDotacoesUsuarioUuidTemAcesso(uuid);
  if (dotacoesIds.error) {
    return {
      error: dotacoesIds.error,
    };
  }
  if (!dotacoesIds.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }
  try {
    await prisma.user_profiles_dotacoes.upsert({
      where: {
        uuidUsuario: uuid,
      },
      update: {
        dotacoesIds: dotacoesIds.data.dotacoes,
        acessoTotal: dotacoesIds.data.acessoTotal,
      },
      create: {
        uuidUsuario: uuid,
        dotacoesIds: dotacoesIds.data.dotacoes,
        acessoTotal: dotacoesIds.data.acessoTotal,
      },
    });
  } catch (error) {
    console.log(error);
  }
};

export const salvarAuditoriaAcesso = async (params: unknown) => {
  const usuario = await obterUsuarioPorUuid({
    uuid: await obterUuidUsuarioConectado(),
  });

  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }
  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }
  const parsedParams = auditoriaAcessoSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { acao } = parsedParams.data;

  const ip = (await obterIpUsuarioConectado()).data;

  try {
    await prisma.acessos_audit.create({
      data: {
        acao,
        usuarioId: usuario.data.id,
        ip,
      },
    });
    return {
      data: true,
    };
  } catch (e) {
    return {
      error: e,
    };
  }
};

export const obterIdUsuarioConectado = async () => {
  const userUuid = await obterUuidUsuarioConectado();
  if (!userUuid) {
    return {
      error: 'Usuário não autenticado.',
    };
  }
  const usuario = await obterUsuarioPorUuid({
    uuid: userUuid,
  });
  if (usuario.error) {
    return {
      error: usuario.error,
    };
  }
  if (!usuario.data) {
    return {
      error: 'Não foi possível obter dados do usuário.',
    };
  }
  return {
    data: usuario.data.id,
  };
};

export const obterIpUsuarioConectado = async () => {
  const header = await headers();
  const ip = (header.get('x-forwarded-for') ?? '127.0.0.1').split(',')[0];

  return {
    data: ip,
  };
};
