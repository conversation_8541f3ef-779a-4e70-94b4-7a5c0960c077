/*
  Warnings:

  - Added the required column `tipoAcesso` to the `cargos` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "cargos" ADD COLUMN     "tipoAcesso" SMALLINT NOT NULL;

-- CreateTable
CREATE TABLE "cargos_secretarias" (
    "id" SERIAL NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "secretariaId" INTEGER NOT NULL,

    CONSTRAINT "cargos_secretarias_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cargos_departamentos" (
    "id" SERIAL NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "departamentoId" INTEGER NOT NULL,

    CONSTRAINT "cargos_departamentos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cargos_subdepartamentos" (
    "id" SERIAL NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "subdepartamentoId" INTEGER NOT NULL,

    CONSTRAINT "cargos_subdepartamentos_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cargos_secretarias_cargoId_secretariaId_key" ON "cargos_secretarias"("cargoId", "secretariaId");

-- CreateIndex
CREATE UNIQUE INDEX "cargos_departamentos_cargoId_departamentoId_key" ON "cargos_departamentos"("cargoId", "departamentoId");

-- CreateIndex
CREATE UNIQUE INDEX "cargos_subdepartamentos_cargoId_subdepartamentoId_key" ON "cargos_subdepartamentos"("cargoId", "subdepartamentoId");

-- AddForeignKey
ALTER TABLE "cargos_secretarias" ADD CONSTRAINT "cargos_secretarias_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_secretarias" ADD CONSTRAINT "cargos_secretarias_secretariaId_fkey" FOREIGN KEY ("secretariaId") REFERENCES "secretarias"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_departamentos" ADD CONSTRAINT "cargos_departamentos_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_departamentos" ADD CONSTRAINT "cargos_departamentos_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "departamentos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_subdepartamentos" ADD CONSTRAINT "cargos_subdepartamentos_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_subdepartamentos" ADD CONSTRAINT "cargos_subdepartamentos_subdepartamentoId_fkey" FOREIGN KEY ("subdepartamentoId") REFERENCES "subdepartamentos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
