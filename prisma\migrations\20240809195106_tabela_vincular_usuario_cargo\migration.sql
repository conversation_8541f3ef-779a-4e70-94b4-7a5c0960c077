-- CreateTable
CREATE TABLE "user_profiles_cargos" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "cargoId" INTEGER NOT NULL,

    CONSTRAINT "user_profiles_cargos_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_profiles_cargos_idUsuario_cargoId_key" ON "user_profiles_cargos"("idUsuario", "cargoId");

-- AddForeignKey
ALTER TABLE "user_profiles_cargos" ADD CONSTRAINT "user_profiles_cargos_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_profiles_cargos" ADD CONSTRAINT "user_profiles_cargos_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
