import { ErrorAlert } from '@/components/error-alert';
import SubdepartamentosDatatable from './subdepartamentosDataTable';
import { listarSubdepartamentos } from '@/lib/database/gerenciamento/subdepartamentos';
import { obterSecretaria } from '@/lib/database/gerenciamento/secretarias';
import { obterDepartamento } from '@/lib/database/gerenciamento/departamentos';

export default async function SubdepartamentosDatatableWrapper({
  idSecretaria,
  idDepartamento,
}: {
  idSecretaria: number;
  idDepartamento: number;
}) {
  const subdepartamentosData = listarSubdepartamentos({
    id: idDepartamento,
  });

  const secretariaData = obterSecretaria({
    id: idSecretaria,
  });

  const departamentoData = obterDepartamento({
    id: idDepartamento,
  });

  const [subdepartamentos, secretaria, departamento] = await Promise.all([
    subdepartamentosData,
    secretariaData,
    departamentoData,
  ]);

  if (subdepartamentos.error)
    return <ErrorAlert error={subdepartamentos.error} />;
  if (!subdepartamentos.data)
    return <ErrorAlert error={'Subdepartamentos não encontrados.'} />;

  if (secretaria.error) return <ErrorAlert error={secretaria.error} />;
  if (!secretaria.data)
    return <ErrorAlert error={'Secretaria não encontrada.'} />;

  if (departamento.error) return <ErrorAlert error={departamento.error} />;
  if (!departamento.data)
    return <ErrorAlert error={'Departamento não encontrado.'} />;

  return (
    <SubdepartamentosDatatable
      data={subdepartamentos}
      secretaria={secretaria}
      departamento={departamento}
    />
  );
}
