'use client';

import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, Edit, RotateCcw } from 'lucide-react';
import {
  listarLiquidacoes,
  estornarLiquidacao,
} from '@/lib/database/movimento/liquidacoes';
import { StatusLiquidacao } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ReusableDatatableFiltroIdDespesaSecretariaDepartamento } from '@/components/datatable/reusableDatatableFiltroIdDespesaSecretariaDepartamento';
import { useState } from 'react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  DatatableActionsDropdown,
  DatatableAction,
} from '@/components/datatable/DatatableActionsDropdown';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export function LiquidacoesDataTable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarLiquidacoes>>;
}) {
  const [estornandoId, setEstornandoId] = useState<number | null>(null);
  const [motivoEstorno, setMotivoEstorno] = useState('');
  const [estornandoLoading, setEstornandoLoading] = useState(false);
  const router = useRouter();

  if (!data.data?.liquidacoes) return null;

  const handleEstornar = (id: number) => {
    setEstornandoId(id);
    setMotivoEstorno('');
  };

  const confirmarEstorno = async () => {
    if (!estornandoId || !motivoEstorno.trim()) {
      toast.error('Motivo do estorno é obrigatório');
      return;
    }

    try {
      setEstornandoLoading(true);
      const res = await estornarLiquidacao({
        id: estornandoId,
        motivo: motivoEstorno,
      });

      if (res?.error) {
        toast.error(res.error);
      } else {
        toast.success('Liquidação estornada com sucesso!');
        setEstornandoId(null);
        setMotivoEstorno('');
        router.refresh(); // Refresh the page to reload data
      }
    } catch (error) {
      console.error('Erro ao estornar liquidação:', error);
      toast.error('Erro ao estornar liquidação');
    } finally {
      setEstornandoLoading(false);
    }
  };

  const cancelarEstorno = () => {
    setEstornandoId(null);
    setMotivoEstorno('');
  };

  const getStatusBadge = (status: number) => {
    switch (status) {
      case StatusLiquidacao.LIQUIDADA:
        return (
          <Badge variant='default' className='bg-green-100 text-green-800'>
            Liquidada
          </Badge>
        );
      case StatusLiquidacao.ESTORNADA:
        return <Badge variant='destructive'>Estornada</Badge>;
      default:
        return <Badge variant='secondary'>Desconhecido</Badge>;
    }
  };

  const columns: ColumnDef<(typeof data.data.liquidacoes)[0]>[] = [
    {
      accessorKey: 'id',
      header: 'Liquidação',
      cell: ({ row }) => (
        <div className='font-medium'>
          {row.original.numero}/{row.original.exercicio}
        </div>
      ),
    },
    {
      accessorKey: 'data',
      header: 'Data',
      cell: ({ row }) => (
        <div>{format(row.original.data, 'dd/MM/yyyy', { locale: ptBR })}</div>
      ),
    },
    {
      accessorKey: 'empenho',
      header: 'Empenho',
      cell: ({ row }) => (
        <div>
          {row.original.empenho.numero}/{row.original.empenho.exercicio}
        </div>
      ),
    },
    {
      accessorKey: 'fornecedor',
      header: 'Fornecedor',
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='max-w-[200px] truncate font-medium'>
            {row.original.empenho.fornecedor?.nome}
          </span>
          <span className='text-muted-foreground text-xs'>
            {row.original.empenho.fornecedor?.cnpjCpf}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'despesa',
      header: 'Despesa',
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='text-xs font-medium'>
            {row.original.empenho.reserva?.dotacao.despesa}
          </span>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className='text-muted-foreground max-w-[150px] cursor-help truncate text-xs'>
                {row.original.empenho.reserva?.dotacao.desc}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className='max-w-xs'>
                {row.original.empenho.reserva?.dotacao.desc}
              </p>
            </TooltipContent>
          </Tooltip>
        </div>
      ),
    },
    {
      accessorKey: 'valorTotal',
      header: 'Valor',
      cell: ({ row }) => (
        <div className='text-right font-medium'>
          {currency(row.original.valorTotal, currencyOptionsNoSymbol)
            .format()
            .replace(/0$/, '')}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: 'documentos',
      header: 'Documentos',
      cell: ({ row }) => (
        <div className='text-center'>
          <Badge variant='outline'>
            {row.original.liquidacoes_documentos.length}
          </Badge>
        </div>
      ),
    },
    {
      accessorKey: 'secretariaId',
      header: 'Secretaria',
      filterFn: 'equals',
      accessorFn: (row) =>
        row.empenho.reserva?.dotacao.secretariaId ||
        row.empenho.reserva?.dotacao.departamento?.secretariaId,
    },
    {
      accessorKey: 'departamentoId',
      header: 'Departamento',
      filterFn: 'equals',
      accessorFn: (row) => row.empenho.reserva?.dotacao.departamentoId,
    },
    {
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => {
        const actions: DatatableAction[] = [
          {
            label: 'Editar',
            icon: <Edit className='size-4' />,
            href: `/movimento/liquidacoes/editar/${row.original.id}`,
            show: row.original.status === StatusLiquidacao.LIQUIDADA,
          },
          {
            label: 'Estornar',
            icon: <RotateCcw className='size-4' />,
            onClick: () => handleEstornar(row.original.id),
            variant: 'destructive',
            show: row.original.status === StatusLiquidacao.LIQUIDADA,
          },
        ];

        return (
          <div className='flex items-center gap-2'>
            <Link href={`/movimento/liquidacoes/visualizar/${row.original.id}`}>
              <Button variant='ghost' size='sm'>
                <Eye className='size-4' />
              </Button>
            </Link>
            <DatatableActionsDropdown actions={actions} />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <TooltipProvider>
        <div className='container mx-auto'>
          <ReusableDatatableFiltroIdDespesaSecretariaDepartamento
            columns={columns}
            data={data.data.liquidacoes}
            secretarias={data.data.secretarias}
            departamentos={data.data.departamentos}
          />
        </div>
      </TooltipProvider>

      <Dialog
        open={estornandoId !== null}
        onOpenChange={() => !estornandoLoading && cancelarEstorno()}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Estornar Liquidação</DialogTitle>
            <DialogDescription>
              Esta ação irá estornar a liquidação. O empenho voltará ao status
              anterior. Informe o motivo do estorno:
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-2'>
            <Label htmlFor='motivo'>Motivo do Estorno *</Label>
            <Textarea
              id='motivo'
              value={motivoEstorno}
              onChange={(e) => setMotivoEstorno(e.target.value)}
              placeholder='Descreva o motivo do estorno...'
              disabled={estornandoLoading}
            />
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={cancelarEstorno}
              disabled={estornandoLoading}
            >
              Cancelar
            </Button>
            <Button
              variant='destructive'
              onClick={confirmarEstorno}
              disabled={estornandoLoading || !motivoEstorno.trim()}
            >
              {estornandoLoading ? (
                <>
                  <Loader2 className='mr-2 size-4 animate-spin' />
                  Estornando...
                </>
              ) : (
                'Confirmar Estorno'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
