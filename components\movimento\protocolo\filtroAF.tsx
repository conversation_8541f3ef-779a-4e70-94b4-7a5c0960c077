'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { FileText, Filter, X } from 'lucide-react';

interface FiltroAFProps {
  onFilterChange: (filters: AFFilters) => void;
  initialFilters?: Partial<AFFilters>;
}

interface AFFilters {
  afDe?: number;
  afAte?: number;
  exercicioAF?: number;
  apenasSemAF?: boolean;
  apenasComAF?: boolean;
}

export function FiltroAF({ onFilterChange, initialFilters }: FiltroAFProps) {
  const [filters, setFilters] = useState<AFFilters>({
    afDe: initialFilters?.afDe,
    afAte: initialFilters?.afAte,
    exercicioAF: initialFilters?.exercicioAF,
    apenasSemAF: initialFilters?.apenasSemAF,
    apenasComAF: initialFilters?.apenasComAF,
  });

  const handleFilterChange = (key: keyof AFFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };

    // Clear conflicting filters
    if (key === 'apenasSemAF' && value) {
      newFilters.apenasComAF = false;
    }
    if (key === 'apenasComAF' && value) {
      newFilters.apenasSemAF = false;
    }

    setFilters(newFilters);
  };

  const handleApply = () => {
    onFilterChange(filters);
  };

  const handleClear = () => {
    const clearedFilters: AFFilters = {};
    setFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) => value !== undefined && value !== false
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <FileText className='h-5 w-5' />
          Filtro por AF
          {hasActiveFilters && (
            <Badge variant='secondary' className='text-xs'>
              Ativo
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label htmlFor='afDe'>AF De</Label>
            <Input
              id='afDe'
              type='number'
              placeholder='Nº AF'
              value={filters.afDe || ''}
              onChange={(e) =>
                handleFilterChange(
                  'afDe',
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='afAte'>AF Até</Label>
            <Input
              id='afAte'
              type='number'
              placeholder='Nº AF'
              value={filters.afAte || ''}
              onChange={(e) =>
                handleFilterChange(
                  'afAte',
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
            />
          </div>
        </div>

        <div className='space-y-2'>
          <Label htmlFor='exercicioAF'>Exercício da AF</Label>
          <Input
            id='exercicioAF'
            type='number'
            placeholder='Ano'
            value={filters.exercicioAF || ''}
            onChange={(e) =>
              handleFilterChange(
                'exercicioAF',
                e.target.value ? parseInt(e.target.value) : undefined
              )
            }
          />
        </div>

        <div className='space-y-3'>
          <Label className='text-sm font-medium'>Tipo de AF</Label>
          <div className='space-y-2'>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='comAF'
                checked={filters.apenasComAF}
                onCheckedChange={(checked) =>
                  handleFilterChange('apenasComAF', checked)
                }
              />
              <Label htmlFor='comAF' className='text-sm'>
                Apenas com AF vinculada
              </Label>
            </div>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='semAF'
                checked={filters.apenasSemAF}
                onCheckedChange={(checked) =>
                  handleFilterChange('apenasSemAF', checked)
                }
              />
              <Label htmlFor='semAF' className='text-sm'>
                Apenas sem AF vinculada
              </Label>
            </div>
          </div>
        </div>

        <div className='flex gap-2'>
          <Button onClick={handleApply} className='flex-1'>
            <Filter className='mr-2 h-4 w-4' />
            Aplicar Filtro
          </Button>
          <Button variant='outline' onClick={handleClear}>
            <X className='mr-2 h-4 w-4' />
            Limpar
          </Button>
        </div>

        {hasActiveFilters && (
          <div className='mt-4 rounded-lg bg-blue-50 p-3'>
            <div className='text-sm text-blue-800'>
              <div className='mb-1 font-medium'>Filtros ativos:</div>
              <div className='space-y-1'>
                {filters.afDe && <div>• AF De: {filters.afDe}</div>}
                {filters.afAte && <div>• AF Até: {filters.afAte}</div>}
                {filters.exercicioAF && (
                  <div>• Exercício: {filters.exercicioAF}</div>
                )}
                {filters.apenasComAF && <div>• Apenas com AF</div>}
                {filters.apenasSemAF && <div>• Apenas sem AF</div>}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
