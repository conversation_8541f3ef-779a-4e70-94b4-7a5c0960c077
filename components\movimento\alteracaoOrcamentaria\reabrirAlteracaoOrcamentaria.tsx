import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ReabrirAltOrc } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { RotateCcw } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function ReabrirAlteracaoOrcamentaria({
  idAlteracaoOrcamentaria,
}: {
  idAlteracaoOrcamentaria: number;
}) {
  const [loading, setLoading] = useState(false);

  const reabir = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idAlteracaoOrcamentaria,
      };

      const res = await ReabrirAltOrc(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Pedido Reaberto.');
      }
    } catch (error: any) {
      console.log(error);
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline'>
          <RotateCcw className='mr-2 size-4' /> Reabrir
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Reabrir Alteração Orçamentária</AlertDialogTitle>
          <AlertDialogDescription>
            Tem certeza que deseja reabrir a alteração orçamentária?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              reabir();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Reabrir'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
