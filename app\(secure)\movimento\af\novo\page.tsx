import FormCriarAF from '@/components/af/formCriarAF';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';

export default function NovoAFPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Nova AF</PageTitle>
      </PageHeader>
      <PageContent>
        <FormCriarAF />
      </PageContent>
    </PageWrapper>
  );
}
