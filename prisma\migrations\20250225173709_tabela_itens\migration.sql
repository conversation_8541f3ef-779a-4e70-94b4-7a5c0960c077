-- CreateTable
CREATE TABLE "reserva_itens" (
    "id" SERIAL NOT NULL,
    "idReserva" INTEGER NOT NULL,
    "unidadeItem" SMALLINT NOT NULL,
    "quantidade" INTEGER NOT NULL,
    "valor" DECIMAL(18,2) NOT NULL,
    "descricao" TEXT NOT NULL,

    CONSTRAINT "reserva_itens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reserva_itens_audit" (
    "id" SERIAL NOT NULL,
    "idReserva" INTEGER NOT NULL,
    "unidadeItem" SMALLINT NOT NULL,
    "quantidade" INTEGER NOT NULL,
    "valor" DECIMAL(18,2) NOT NULL,
    "descricao" TEXT NOT NULL,

    CONSTRAINT "reserva_itens_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "reserva_itens" ADD CONSTRAINT "reserva_itens_idReserva_fkey" FOREIGN KEY ("idReserva") REFERENCES "reservas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva_itens_audit" ADD CONSTRAINT "reserva_itens_audit_idReserva_fkey" FOREIGN KEY ("idReserva") REFERENCES "reservas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
