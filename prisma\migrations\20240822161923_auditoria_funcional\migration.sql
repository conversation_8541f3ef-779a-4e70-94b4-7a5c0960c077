-- CreateTable
CREATE TABLE "gerenciamentoFuncionais_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idFuncional" INTEGER NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gerenciamentoFuncionais_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "gerenciamentoFuncionais_audit" ADD CONSTRAINT "gerenciamentoFuncionais_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoFuncionais_audit" ADD CONSTRAINT "gerenciamentoFuncionais_audit_idFuncional_fkey" FOREIGN KEY ("idFuncional") REFERENCES "funcionais"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
