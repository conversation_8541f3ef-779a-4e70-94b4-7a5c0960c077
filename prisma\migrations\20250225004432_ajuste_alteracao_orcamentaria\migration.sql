/*
  Warnings:

  - Made the column `despesaCopia` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.
  - Made the column `despesaAcao` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.
  - Made the column `obs` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.
  - Made the column `dataDecreto` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tipoDecreto` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.
  - Made the column `numDecreto` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.
  - Made the column `despesaNova` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.
  - Made the column `descrDespNova` on table `AlteracaoOrcamentaria` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "AlteracaoOrcamentaria" ALTER COLUMN "despesaCopia" SET NOT NULL,
ALTER COLUMN "despesaAcao" SET NOT NULL,
ALTER COLUMN "obs" SET NOT NULL,
ALTER COLUMN "dataDecreto" SET NOT NULL,
ALTER COLUMN "tipoDecreto" SET NOT NULL,
ALTER COLUMN "numDecreto" SET NOT NULL,
ALTER COLUMN "despesaNova" SET NOT NULL,
ALTER COLUMN "descrDespNova" SET NOT NULL;
