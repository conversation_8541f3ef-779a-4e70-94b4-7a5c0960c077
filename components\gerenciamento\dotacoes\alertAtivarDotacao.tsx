'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ativarDotacao } from '@/lib/database/gerenciamento/dotacoes';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Check } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertAtivarDotacao({
  idDotacao,
  despesa,
  descDotacao,
}: {
  idDotacao: number;
  despesa: number;
  descDotacao: string;
}) {
  const [loading, setLoading] = useState(false);

  const ativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idDotacao,
      };

      const res = await ativarDotacao(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Dotacão ativada.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='min-w-[115px] text-green-800'
        >
          <Check className='mr-2 size-4' /> Ativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Ativar Dotação?</AlertDialogTitle>
          <AlertDialogDescription>
            A dotação{' '}
            <span className='font-bold'>
              {despesa} - {descDotacao}
            </span>{' '}
            será ativada.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              ativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Ativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
