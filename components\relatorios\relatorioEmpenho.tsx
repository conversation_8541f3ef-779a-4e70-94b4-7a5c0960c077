import { siteConfig } from '@/config/site';
import {
  StatusEmpenho,
  StatusEmpenhoDesc,
  StatusLiquidacao,
  StatusLiquidacaoDesc,
} from '@/lib/enums';
import { toCurrency } from '@/lib/serverUtils';

interface RelatorioEmpenhoProps {
  empenho: NonNullable<
    Required<
      Awaited<
        ReturnType<
          typeof import('@/lib/database/relatorios/empenhos').obterEmpenhoParaRelatorio
        >
      >
    >
  >['data'];
}

export const RelatorioEmpenho = ({ empenho }: RelatorioEmpenhoProps) => {
  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>Empenho</h2>
        <p className='text-sm text-gray-600'>
          Nº {empenho.numero}/{empenho.exercicio}
        </p>
      </div>

      {/* Informações Principais */}
      <div className='mb-6 grid grid-cols-2 gap-6'>
        <div className='rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Informações do Empenho
          </h3>
          <div className='space-y-2 text-sm'>
            <div className='flex justify-between'>
              <span className='font-medium'>Número:</span>
              <span className='font-mono'>
                {empenho.numero}/{empenho.exercicio}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='font-medium'>Data:</span>
              <span>{new Date(empenho.data).toLocaleDateString('pt-BR')}</span>
            </div>
            <div className='flex justify-between'>
              <span className='font-medium'>Status:</span>
              <span
                className={`rounded px-2 py-1 text-xs ${
                  empenho.status === StatusEmpenho.EMPENHADO
                    ? 'bg-green-100 text-green-800'
                    : empenho.status === StatusEmpenho.CANCELADO
                      ? 'bg-red-100 text-red-800'
                      : empenho.status === StatusEmpenho.ANULADO
                        ? 'bg-orange-100 text-orange-800'
                        : empenho.status === StatusEmpenho.LIQUIDADO
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                }`}
              >
                {StatusEmpenhoDesc[empenho.status as StatusEmpenho] || 'N/A'}
              </span>
            </div>
            {empenho.dataLiquidacao && (
              <div className='flex justify-between'>
                <span className='font-medium'>Data Liquidação:</span>
                <span>
                  {new Date(empenho.dataLiquidacao).toLocaleDateString('pt-BR')}
                </span>
              </div>
            )}
          </div>
        </div>

        <div className='rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Valores</h3>
          <div className='space-y-2 text-sm'>
            <div className='flex justify-between'>
              <span className='font-medium'>Valor Total:</span>
              <span className='font-mono'>
                {toCurrency(empenho.valorTotal).format().replace(/0$/, '')}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='font-medium'>Valor Liquidado:</span>
              <span className='font-mono'>
                {toCurrency(empenho.valorLiquidado).format().replace(/0$/, '')}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='font-medium'>Valor Processado:</span>
              <span className='font-mono'>
                {toCurrency(empenho.valorProcessado).format().replace(/0$/, '')}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='font-medium'>Valor Não Processado:</span>
              <span className='font-mono'>
                {toCurrency(empenho.valorNaoProcessado)
                  .format()
                  .replace(/0$/, '')}
              </span>
            </div>
            <div className='flex justify-between border-t pt-2'>
              <span className='font-medium'>Saldo Disponível:</span>
              <span
                className={`font-mono ${empenho.saldoDisponivel < 0 ? 'text-red-600' : ''}`}
              >
                {toCurrency(empenho.saldoDisponivel).format().replace(/0$/, '')}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Fornecedor */}
      {empenho.fornecedor && (
        <div className='mb-6 rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Fornecedor</h3>
          <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
              <div className='mb-1 flex justify-between'>
                <span className='font-medium'>Nome:</span>
                <span>{empenho.fornecedor.nome}</span>
              </div>
              <div className='mb-1 flex justify-between'>
                <span className='font-medium'>Tipo:</span>
                <span>
                  {empenho.fornecedor.tipo === 'FISICA'
                    ? 'Pessoa Física'
                    : 'Pessoa Jurídica'}
                </span>
              </div>
            </div>
            <div>
              <div className='mb-1 flex justify-between'>
                <span className='font-medium'>Documento:</span>
                <span className='font-mono'>
                  {empenho.fornecedor.documento}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Dotação */}
      <div className='mb-6 rounded-lg bg-gray-50 p-4'>
        <h3 className='mb-3 border-b pb-1 font-semibold'>Dotação</h3>
        <div className='grid grid-cols-2 gap-4 text-sm'>
          <div>
            <div className='mb-1 flex justify-between'>
              <span className='font-medium'>Despesa:</span>
              <span className='font-mono'>{empenho.dotacao.despesa}</span>
            </div>
            <div className='mb-1 flex justify-between'>
              <span className='font-medium'>Descrição:</span>
              <span className='truncate'>{empenho.dotacao.desc}</span>
            </div>
            <div className='mb-1 flex justify-between'>
              <span className='font-medium'>Valor Atual:</span>
              <span className='font-mono'>
                {toCurrency(empenho.dotacao.valorAtual)
                  .format()
                  .replace(/0$/, '')}
              </span>
            </div>
          </div>
          <div>
            <div className='mb-1 flex justify-between'>
              <span className='font-medium'>Secretaria:</span>
              <span>{empenho.dotacao.secretaria?.nome || 'N/A'}</span>
            </div>
            <div className='mb-1 flex justify-between'>
              <span className='font-medium'>Departamento:</span>
              <span>{empenho.dotacao.departamento?.nome || 'N/A'}</span>
            </div>
            <div className='mb-1 flex justify-between'>
              <span className='font-medium'>Subdepartamento:</span>
              <span>{empenho.dotacao.subdepartamento?.nome || 'N/A'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Resumo e Observações */}
      {(empenho.resumo || empenho.obs) && (
        <div className='mb-6 rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Descrições</h3>
          <div className='space-y-2 text-sm'>
            {empenho.resumo && (
              <div>
                <span className='font-medium'>Resumo: </span>
                <span>{empenho.resumo}</span>
              </div>
            )}
            {empenho.obs && (
              <div>
                <span className='font-medium'>Observações: </span>
                <span>{empenho.obs}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Distribuição Mensal */}
      {empenho.valoresMensais.length > 0 && (
        <div className='mb-6 rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Distribuição Mensal
          </h3>
          <div className='overflow-x-auto'>
            <table className='w-full border-collapse text-xs'>
              <thead>
                <tr className='border-b border-gray-800'>
                  <th className='p-1 text-left font-bold'>Mês</th>
                  <th className='p-1 text-right font-bold'>Liquidado</th>
                  <th className='p-1 text-right font-bold'>Processado</th>
                  <th className='p-1 text-right font-bold'>Não Processado</th>
                </tr>
              </thead>
              <tbody>
                {empenho.valoresMensais.map((item, index) => (
                  <tr
                    key={item.mes}
                    className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                  >
                    <td className='border-b border-gray-200 p-1'>{item.mes}</td>
                    <td className='border-b border-gray-200 p-1 text-right font-mono'>
                      {toCurrency(item.valorLiquidado)
                        .format()
                        .replace(/0$/, '')}
                    </td>
                    <td className='border-b border-gray-200 p-1 text-right font-mono'>
                      {toCurrency(item.valorProcessado)
                        .format()
                        .replace(/0$/, '')}
                    </td>
                    <td className='border-b border-gray-200 p-1 text-right font-mono'>
                      {toCurrency(item.valorNaoProcessado)
                        .format()
                        .replace(/0$/, '')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Liquidações */}
      {empenho.liquidacoes.length > 0 && (
        <div className='mb-6 rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Liquidações</h3>
          <div className='overflow-x-auto'>
            <table className='w-full border-collapse text-xs'>
              <thead>
                <tr className='border-b border-gray-800'>
                  <th className='p-1 text-left font-bold'>Nº</th>
                  <th className='p-1 text-left font-bold'>Data</th>
                  <th className='p-1 text-left font-bold'>Mês Ref.</th>
                  <th className='p-1 text-left font-bold'>Status</th>
                  <th className='p-1 text-right font-bold'>Valor</th>
                </tr>
              </thead>
              <tbody>
                {empenho.liquidacoes.map((liq, index) => (
                  <tr
                    key={liq.id}
                    className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                  >
                    <td className='border-b border-gray-200 p-1 font-mono'>
                      {liq.numero}/{liq.exercicio}
                    </td>
                    <td className='border-b border-gray-200 p-1'>
                      {new Date(liq.dataLiquidacao).toLocaleDateString('pt-BR')}
                    </td>
                    <td className='border-b border-gray-200 p-1'>
                      {liq.mesReferencia || 'N/A'}
                    </td>
                    <td className='border-b border-gray-200 p-1'>
                      <span
                        className={`rounded px-1 py-0.5 text-xs ${
                          liq.status === StatusLiquidacao.PROCESSADO
                            ? 'bg-green-100 text-green-800'
                            : liq.status === StatusLiquidacao.NAO_PROCESSADO
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {StatusLiquidacaoDesc[liq.status as StatusLiquidacao] ||
                          'N/A'}
                      </span>
                    </td>
                    <td className='border-b border-gray-200 p-1 text-right font-mono'>
                      {toCurrency(liq.valorTotal).format().replace(/0$/, '')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Rodapé */}
      <div className='mt-8 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p className='mt-1'>{siteConfig.name}</p>
      </div>
    </div>
  );
};
