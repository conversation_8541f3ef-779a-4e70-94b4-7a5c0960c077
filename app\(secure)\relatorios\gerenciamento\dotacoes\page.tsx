'use server';
import { z } from 'zod';
import { ensureAuth } from '@/lib/supabase/actions';
import { ErrorAlert } from '@/components/error-alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { RelatorioDotacaoAtual } from '@/components/relatorios/dotacaoAtual';
import { obterDotacoesParaRelatorio } from '@/lib/database/relatorios/dotacoes';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';

async function RelatorioDotacoesContent() {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const result = await temPermissao({ ...parametrosPermissao, bearer: '' });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const params = {
    exercicio: result.exercicio,
    bearer: '',
  };

  const dotacoes = await obterDotacoesParaRelatorio(params);

  if (dotacoes.error) {
    return <ErrorAlert error={dotacoes.error} />;
  }

  if (!dotacoes.data) {
    return <ErrorAlert error='Falha ao obter dotações.' />;
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Relatório de Dotações
          </h1>
          <p className='text-muted-foreground'>
            Visualize e exporte o relatório de dotações orçamentárias
          </p>
        </div>
        <div className='flex gap-4'>
          <BotaoDownloadPDF
            url='/gerenciamento/dotacoes/imprimir'
            title='Imprimir Relatório'
          />
        </div>
      </div>

      <div className='rounded-lg bg-white p-6 shadow'>
        <RelatorioDotacaoAtual dotacoes={dotacoes.data} />
      </div>
    </div>
  );
}

export default async function RelatorioDotacoesPage() {
  await ensureAuth();

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de Dotações</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <RelatorioDotacoesContent />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
