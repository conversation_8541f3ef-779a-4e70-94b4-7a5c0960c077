/*
  Warnings:

  - You are about to drop the column `userId` on the `gerenciamentoCargos_audit` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `gerenciamentoSecretarias_audit` table. All the data in the column will be lost.
  - Added the required column `usuarioId` to the `gerenciamentoCargos_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usuarioId` to the `gerenciamentoSecretarias_audit` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "gerenciamentoCargos_audit" DROP CONSTRAINT "gerenciamentoCargos_audit_userId_fkey";

-- DropForeignKey
ALTER TABLE "gerenciamentoSecretarias_audit" DROP CONSTRAINT "gerenciamentoSecretarias_audit_userId_fkey";

-- AlterTable
ALTER TABLE "gerenciamentoCargos_audit" DROP COLUMN "userId",
ADD COLUMN     "usuarioId" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "gerenciamentoSecretarias_audit" DROP COLUMN "userId",
ADD COLUMN     "usuarioId" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "gerenciamentoSecretarias_audit" ADD CONSTRAINT "gerenciamentoSecretarias_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoCargos_audit" ADD CONSTRAINT "gerenciamentoCargos_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
