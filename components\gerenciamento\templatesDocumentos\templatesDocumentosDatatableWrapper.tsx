import { listarTemplatesDocumentos } from '@/lib/database/gerenciamento/templatesDocumentos';
import { TemplatesDocumentosDataTable } from './templatesDocumentosDataTable';
import { ErrorAlert } from '@/components/error-alert';

export async function TemplatesDocumentosDatatableWrapper() {
  const result = await listarTemplatesDocumentos();

  if (result.error) {
    return <ErrorAlert error={result.error} />;
  }

  if (!result.data) {
    return <ErrorAlert error='Falha ao obter dados dos templates.' />;
  }

  return <TemplatesDocumentosDataTable data={result.data} />;
}
