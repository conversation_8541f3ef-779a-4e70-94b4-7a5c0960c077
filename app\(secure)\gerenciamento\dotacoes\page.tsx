import DotacoesDatatableWrapper from '@/components/gerenciamento/dotacoes/dotacoesDatatableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusCircle, FileText } from 'lucide-react';
import Link from 'next/link';
import { Suspense } from 'react';
import { ensureAuth } from '@/lib/supabase/actions';
import { temPermissao } from '@/lib/database/usuarios';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';

async function VerRelatorioButton() {
  'use server';

  await ensureAuth();

  const temPermissaoRelatorio = await temPermissao({
    modulo: Modulos.RELATORIOS_GERENCIAMENTO,
    permissao: Permissoes.ACESSAR,
    bearer: '',
  });

  if (!temPermissaoRelatorio) {
    return null;
  }

  return (
    <Link href='/relatorios/gerenciamento/dotacoes'>
      <Button variant='outline'>
        <FileText className='mr-2 h-4 w-4' />
        Ver Relatório
      </Button>
    </Link>
  );
}

export default function GerenciamentoDeCargosPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Gerenciamento de Dotações</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='mb-4 flex w-full justify-between pr-6'>
            <VerRelatorioButton />
            <Link href='/gerenciamento/dotacoes/novo'>
              <Button>
                Nova Dotação <PlusCircle className='ml-1 h-4 w-4' />
              </Button>
            </Link>
          </div>
          <DotacoesDatatableWrapper />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
