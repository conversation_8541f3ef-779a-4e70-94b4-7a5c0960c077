!(function () {
  'use strict';
  var t = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const o = (t, o, e) => {
      t.dom.toggleClass(t.getBody(), 'mce-visualblocks'),
        e.set(!e.get()),
        ((t, o) => {
          t.dispatch('VisualBlocks', { state: o });
        })(t, e.get());
    },
    e = (t) => t.options.get('visualblocks_default_state');
  const s = (t, o) => (e) => {
    e.setActive(o.get());
    const s = (t) => e.setActive(t.state);
    return t.on('VisualBlocks', s), () => t.off('VisualBlocks', s);
  };
  t.add('visualblocks', (t, l) => {
    ((t) => {
      (0, t.options.register)('visualblocks_default_state', {
        processor: 'boolean',
        default: !1,
      });
    })(t);
    const a = (() => {
      let t = !1;
      return {
        get: () => t,
        set: (o) => {
          t = o;
        },
      };
    })();
    ((t, e, s) => {
      t.addCommand('mceVisualBlocks', () => {
        o(t, 0, s);
      });
    })(t, 0, a),
      ((t, o) => {
        const e = () => t.execCommand('mceVisualBlocks');
        t.ui.registry.addToggleButton('visualblocks', {
          icon: 'visualblocks',
          tooltip: 'Show blocks',
          onAction: e,
          onSetup: s(t, o),
          context: 'any',
        }),
          t.ui.registry.addToggleMenuItem('visualblocks', {
            text: 'Show blocks',
            icon: 'visualblocks',
            onAction: e,
            onSetup: s(t, o),
            context: 'any',
          });
      })(t, a),
      ((t, s, l) => {
        t.on('PreviewFormats AfterPreviewFormats', (o) => {
          l.get() &&
            t.dom.toggleClass(
              t.getBody(),
              'mce-visualblocks',
              'afterpreviewformats' === o.type
            );
        }),
          t.on('init', () => {
            e(t) && o(t, 0, l);
          });
      })(t, 0, a);
  });
})();
