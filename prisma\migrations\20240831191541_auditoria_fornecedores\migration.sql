-- CreateTable
CREATE TABLE "gerenciamentoFornecedores_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idFornecedor" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gerenciamentoFornecedores_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "gerenciamentoFornecedores_audit" ADD CONSTRAINT "gerenciamentoFornecedores_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoFornecedores_audit" ADD CONSTRAINT "gerenciamentoFornecedores_audit_idFornecedor_fkey" FOREIGN KEY ("idFornecedor") REFERENCES "fornecedores"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
