'use client';;
import { z } from 'zod';
import { salvarAuditoriaAcesso } from '@/lib/database/usuarios';
import { AuditoriaAcesso } from '@/lib/enums';
import { createClient } from '@/lib/supabase/client';
import { auditoriaAcessoSchema } from '@/lib/validation';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function LogoutPage() {
  const supabase = createClient();
  const router = useRouter();
  useEffect(() => {
    const paramentrosAuditoria: z.infer<typeof auditoriaAcessoSchema> = {
      acao: AuditoriaAcesso.LOGOUT,
    };
    salvarAuditoriaAcesso(paramentrosAuditoria).then(() =>
      supabase.auth.signOut().then(() => {
        router.push('/login');
      })
    );
  }, [router, supabase.auth]);

  return (
    <div className='h-[600px] flex-1 space-y-4 p-8 pt-6'>
      <div className='flex items-center justify-between space-y-2'>
        Saindo...
      </div>
    </div>
  );
}
