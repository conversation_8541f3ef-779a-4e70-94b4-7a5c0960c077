/*
  Warnings:

  - Added the required column `secretariaId` to the `dotacoes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `subdepartamentoId` to the `dotacoes` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "dotacoes" ADD COLUMN     "secretariaId" INTEGER NOT NULL,
ADD COLUMN     "subdepartamentoId" INTEGER NOT NULL;

-- CreateTable
CREATE TABLE "gerenciamentoDotacoes_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idDotacao" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gerenciamentoDotacoes_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "dotacoes" ADD CONSTRAINT "dotacoes_secretariaId_fkey" FOREIGN KEY ("secretariaId") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "dotacoes" ADD CONSTRAINT "dotacoes_subdepartamentoId_fkey" FOREIGN KEY ("subdepartamentoId") REFERENCES "subdepartamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoDotacoes_audit" ADD CONSTRAINT "gerenciamentoDotacoes_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoDotacoes_audit" ADD CONSTRAINT "gerenciamentoDotacoes_audit_idDotacao_fkey" FOREIGN KEY ("idDotacao") REFERENCES "dotacoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
