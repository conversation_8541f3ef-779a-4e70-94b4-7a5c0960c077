'use client';
import { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { EnrollMFA } from '@/lib/supabase/EnrollMFA';
import { createClient } from '@/lib/supabase/client';
import { UnenrollMFA } from '@/lib/supabase/UnenrollMFA';
import { Card, CardContent } from '@/components/ui/card';

export default function LoginDialog() {
  const supabase = createClient();
  const [mfaEnabled, setMfaEnabled] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    (async () => {
      const factors = await supabase.auth.mfa.listFactors();
      const totp = factors.data?.totp?.length;
      if (totp && totp > 0) {
        setMfaEnabled(true);
      }
      setLoading(false);
    })();
  }, [supabase]);

  return (
    <div className='h-auto flex-1 space-y-4 p-0 pt-6 md:p-8'>
      <div className='mb-6 flex items-center justify-center space-y-2'>
        <h2 className='text-xl font-bold tracking-tight'>
          Autenticação Multifator
        </h2>
      </div>
      <div className='w-full justify-center text-center align-middle'>
        <Card className='m-auto max-w-[600px]'>
          <CardContent className='mt-8 flex flex-wrap justify-center gap-2'>
            {loading ? (
              <Skeleton className='h-10 w-32' />
            ) : !mfaEnabled ? (
              <EnrollMFA
                onEnrolled={() => {
                  setMfaEnabled(true);
                }}
              />
            ) : (
              <UnenrollMFA />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
