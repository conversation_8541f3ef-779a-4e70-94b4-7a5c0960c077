'use server';

import {
  AuditoriaMovimentoReservas,
  ErrosReserva,
  Permissoes,
  StatusReserva,
} from '@/lib/enums';
import { Mo<PERSON><PERSON> } from '@/lib/modulos';
import {
  assinarReservaSchema,
  auditoriaErroSchema,
  cancelarReservaSchema,
  permissaoSchema,
} from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { createClient, createSuperClient } from '@/lib/supabase/server';
import { montarPathPdfReserva } from '@/lib/utils';
import { CHAVE_API, URL_SERVER_ASSINATURA } from '@/lib/consts';
import { revalidatePath } from 'next/cache';

export const listarAssinaturasReservasPendentes = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_RESERVA,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter id do usuário conectado.',
    };
  }

  try {
    const assinaturas = await prisma.reservas_assinaturas.findMany({
      where: {
        idUsuario: resultPermissao.idUsuario,
        assinado: false,
      },
      include: {
        reserva: {
          select: {
            id: true,
            dotacao: {
              select: {
                id: true,
                despesa: true,
                desc: true,
                secretariaId: true,
                departamentoId: true,
                departamento: { select: { secretariaId: true } },
              },
            },
            resumo: true,
            obs: true,
            usarTotal: true,
            exercicio: true,
            ultimaModificacao: true,
          },
        },
      },
      orderBy: { id: 'desc' },
    });

    const secretariasIds = assinaturas.map(
      (assinatura) => assinatura.reserva.dotacao.secretariaId || 0
    );
    const departamentosIds = assinaturas.map(
      (assinatura) => assinatura.reserva.dotacao.departamentoId || 0
    );

    const secretariasPromise = prisma.secretarias.findMany({
      where: {
        id: {
          in: secretariasIds,
        },
        ativo: true,
      },
    });
    const departamentosPromise = prisma.departamentos.findMany({
      where: {
        id: {
          in: departamentosIds,
        },
        ativo: true,
      },
      include: { secretaria: true },
    });

    let [secretarias, departamentos] = await Promise.all([
      secretariasPromise,
      departamentosPromise,
    ]);

    if (!secretarias || !secretarias.length) {
      secretarias = departamentos.reduce(
        (acc, departamento) => {
          if (
            acc.find(
              (secretaria) => secretaria.id === departamento.secretaria.id
            )
          ) {
            return acc;
          }
          return [...acc, departamento.secretaria];
        },
        [] as typeof secretarias
      );
    }

    return {
      data: {
        assinaturas: assinaturas.map((assinatura) => ({
          ...assinatura,
          left: assinatura.left.toNumber(),
          top: assinatura.top.toNumber(),
          reserva: {
            ...assinatura.reserva,
            usarTotal: assinatura.reserva.usarTotal.toNumber(),
            secretariaId:
              assinatura.reserva.dotacao.secretariaId ||
              assinatura.reserva.dotacao.departamento?.secretariaId ||
              null,
            departamentoId: assinatura.reserva.dotacao.departamentoId || null,
          },
        })),
        secretarias,
        departamentos,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter assinaturas de reservas pendentes.`,
    };
  }
};

export const assinarReserva = async (params: unknown) => {
  const parsedParams = assinarReservaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { idReserva, idAssinatura, senha } = parsedParams.data;

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_RESERVA,
    permissao: Permissoes.ALTERAR,
    retornarExercicioUsuario: true,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter id do usuário conectado.',
    };
  }

  try {
    const supabase = await createClient();
    const ip = (await obterIpUsuarioConectado()).data;
    const session = await supabase.auth.getSession();
    const bucket = 'reservas';
    const reservaPromise = prisma.reservas.findUnique({
      where: {
        id: idReserva,
      },
      include: {
        dotacao: {
          select: {
            id: true,
          },
        },
      },
    });

    const assinaturasPendentesPromise = prisma.reservas_assinaturas.findMany({
      where: {
        idReserva: idReserva,
        assinado: false,
      },
    });

    const [reserva, assinaturasPendentes] = await Promise.all([
      reservaPromise,
      assinaturasPendentesPromise,
    ]);

    if (!reserva) {
      throw new Error(ErrosReserva.RESERVA_NAO_ENCONTRADA);
    }

    if (reserva.status !== StatusReserva['Aguardando Assinaturas']) {
      throw new Error(ErrosReserva.STATUS_INVALIDO);
    }

    if (!assinaturasPendentes || !assinaturasPendentes.length) {
      throw new Error(ErrosReserva.NENHUMA_ASSINATURA_PENDENTE);
    }

    const assinaturaUsuario = assinaturasPendentes.find(
      (assinatura) => assinatura.id === idAssinatura
    );

    if (!assinaturaUsuario) {
      throw new Error(ErrosReserva.ASSINATURA_PENDENTE_USUARIO_NAO_ENCONTRADA);
    }
    const assinaturaResult = await fetch(URL_SERVER_ASSINATURA, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${session.data.session?.access_token}`,
        'Content-Type': 'application/json',
        chave: CHAVE_API,
      },
      body: JSON.stringify({
        pdfPath: montarPathPdfReserva(
          reserva.id,
          reserva.dotacao.id,
          reserva.exercicio,
          reserva.ultimaModificacao
        ),
        senha,
        bucket,
        top: assinaturaUsuario.top,
        left: assinaturaUsuario.left,
      }),
    });

    if (!assinaturaResult.ok) {
      throw new Error(ErrosReserva.FALHA_AO_ASSINAR);
    }

    await prisma.$transaction(async (tx) => {
      const updateReservaPromise = tx.reservas.update({
        where: {
          id: idReserva,
        },
        data: {
          ultimaModificacao: new Date(),
        },
      });
      const updateAssinaturaPromise = tx.reservas_assinaturas.update({
        where: {
          id: idAssinatura,
        },
        data: {
          assinado: true,
        },
      });

      const reservaAuditPromise = tx.reservas_audit.create({
        data: {
          idReserva: reserva.id,
          ip,
          exercicio: resultPermissao.exercicio!,
          acao: AuditoriaMovimentoReservas.ASSINAR_RESERVA,
          idUsuario: resultPermissao.idUsuario!,
        },
      });

      let atualizarStatusPromise = null;

      if (assinaturasPendentes.length === 1) {
        atualizarStatusPromise = tx.reservas.update({
          where: {
            id: idReserva,
          },
          data: {
            status: StatusReserva.Assinado,
          },
        });
      }

      const [
        updateReservaResult,
        updateResult,
        reservaResult,
        atualizarStatusResult,
      ] = await Promise.all([
        updateReservaPromise,
        updateAssinaturaPromise,
        reservaAuditPromise,
        atualizarStatusPromise,
      ]);

      if (
        !updateReservaResult ||
        !updateResult ||
        !reservaResult ||
        (atualizarStatusPromise && !atualizarStatusResult)
      ) {
        throw new Error(ErrosReserva.FALHA_AO_ASSINAR);
      }
    });
    revalidatePath('/assinatura/reservas');
    revalidatePath('/movimento/reservas');
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosReserva).includes(e.message as ErrosReserva)) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao assinar reserva.`,
    };
  }
};

export const devolverReserva = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_RESERVA,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = cancelarReservaSchema.safeParse(params);

  if (!parsedParams.success) {
    console.log(parsedParams.error);
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, motivo } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    let idReserva: number | null = null;
    let idDotacao: number | null = null;
    let exercicio: number | null = null;
    let modificado: Date | null = null;
    await prisma.$transaction(async (tx) => {
      const reservaAntiga = await tx.reservas.findUnique({
        where: {
          id,
        },
      });

      if (!reservaAntiga) {
        throw new Error(ErrosReserva.RESERVA_NAO_ENCONTRADA);
      }

      if (reservaAntiga.status !== StatusReserva['Aguardando Assinaturas']) {
        throw new Error(ErrosReserva.STATUS_INVALIDO);
      }

      const atualizarReservaPromise = tx.reservas.update({
        where: { id },
        data: {
          status: StatusReserva.Devolvido,
          ultimaModificacao: new Date(),
          motivoCancelamento: motivo,
          reservas_assinaturas: { deleteMany: {} },
        },
      });

      const reservaAuditPromise = tx.reservas_audit.create({
        data: {
          idReserva: reservaAntiga.id,
          ip,
          exercicio: resultPermissao.exercicio!,
          acao: AuditoriaMovimentoReservas.DEVOLVER_RESERVA,
          idUsuario: resultPermissao.idUsuario!,
        },
      });

      await Promise.all([reservaAuditPromise, atualizarReservaPromise]);

      idReserva = reservaAntiga.id;
      idDotacao = reservaAntiga.idDotacao;
      exercicio = reservaAntiga.exercicio;
      modificado = reservaAntiga.ultimaModificacao;
    });
    if (idReserva && idDotacao && exercicio && modificado) {
      const supabase = createSuperClient();
      const deletarPdf = await supabase.storage
        .from('reservas')
        .remove([
          montarPathPdfReserva(idReserva, idDotacao, exercicio, modificado),
        ]);
      console.log(JSON.stringify(deletarPdf));
      //TODO: Deletar outros anexos?
    }
    revalidatePath('/assinatura/reservas');
    revalidatePath('/movimento/reservas');
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosReserva).includes(e.message as ErrosReserva)) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao devolver reserva.`,
    };
  }
};
