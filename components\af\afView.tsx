'use client';
import { But<PERSON> } from '@/components/ui/button';
import { useCallback, useEffect, useState } from 'react';
import {
  ArrowLeft,
  Edit,
  FileText,
  XCircle,
  CheckCircle,
  RotateCcw,
  Calendar,
  User,
  Building2,
  Printer,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  obterAF,
  cancelarAF,
  marcarAFComoUtilizada,
  reativarAF,
} from '@/lib/database/movimento/afs';
import {
  AuditoriaAFs,
  AuditoriaAFsDesc,
  StatusAF,
  StatusAFDesc,
} from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import DocumentUploadAF from './documentUploadAF';

export default function AFView({ id }: { id: number }) {
  const router = useRouter();
  const [af, setAF] = useState<Awaited<ReturnType<typeof obterAF>> | null>(
    null
  );
  const [isProcessing, setIsProcessing] = useState<number | null>(null);

  const loadAF = useCallback(
    async (afId: number) => {
      try {
        const result = await obterAF({ id: afId });
        if (result.error) {
          toast.error(result.error);
          router.push('/movimento/af');
        } else {
          setAF(result);
        }
      } catch (error) {
        toast.error('Erro ao carregar AF');
        router.push('/movimento/af');
      }
    },
    [router]
  );

  useEffect(() => {
    loadAF(id);
  }, [id, loadAF]);

  const handleMarcarComoUtilizada = async () => {
    setIsProcessing(id);
    try {
      const result = await marcarAFComoUtilizada({
        id: id,
        observacao: 'AF marcada como utilizada via interface',
      });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF marcada como utilizada com sucesso!');
        loadAF(id);
      }
    } catch (error) {
      toast.error('Erro ao marcar AF como utilizada');
    } finally {
      setIsProcessing(null);
    }
  };

  const handleCancelarAF = async () => {
    setIsProcessing(id);
    try {
      const result = await cancelarAF({
        id: id,
        motivo: 'AF cancelada via interface do usuário',
      });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF cancelada com sucesso!');
        loadAF(id);
      }
    } catch (error) {
      toast.error('Erro ao cancelar AF');
    } finally {
      setIsProcessing(null);
    }
  };

  const handleReativarAF = async () => {
    setIsProcessing(id);
    try {
      const result = await reativarAF({
        id: id,
        motivo: 'AF reativada via interface do usuário',
      });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF reativada com sucesso!');
        loadAF(id);
      }
    } catch (error) {
      toast.error('Erro ao reativar AF');
    } finally {
      setIsProcessing(null);
    }
  };

  const getStatusColor = (status: StatusAF) => {
    switch (status) {
      case StatusAF.ATIVA:
        return 'bg-green-100 text-green-800';
      case StatusAF.UTILIZADA:
        return 'bg-blue-100 text-blue-800';
      case StatusAF.CANCELADA:
        return 'bg-red-100 text-red-800';
      case StatusAF.VENCIDA:
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!af?.data) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <div className='border-primary mx-auto h-8 w-8 animate-spin rounded-full border-2 border-t-transparent'></div>
          <p className='text-muted-foreground mt-2 text-sm'>Carregando AF...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Button variant='ghost' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='mr-1 h-4 w-4' />
            Voltar
          </Button>
          <h1 className='text-2xl font-bold'>AF #{af.data.numero}</h1>
          <Badge className={getStatusColor(af.data.status)}>
            {StatusAFDesc[af.data.status as StatusAF]}
          </Badge>
        </div>

        <div className='flex gap-2'>
          <Link href={`/relatorios/movimento/af?af=${af.data.id}`}>
            <Button variant='outline' size='sm'>
              <Printer className='mr-1 h-4 w-4' />
              Ver Relatório
            </Button>
          </Link>
          {af.data.status === StatusAF.ATIVA && (
            <>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  router.push(`/movimento/af/editar/${af.data.id}`)
                }
              >
                <Edit className='mr-1 h-4 w-4' />
                Editar
              </Button>
              <Button
                variant='outline'
                size='sm'
                onClick={handleMarcarComoUtilizada}
                disabled={isProcessing === af.data.id}
              >
                <CheckCircle className='mr-1 h-4 w-4' />
                {isProcessing === af.data.id
                  ? 'Processando...'
                  : 'Marcar Utilizada'}
              </Button>
              <Button
                variant='outline'
                size='sm'
                onClick={handleCancelarAF}
                disabled={isProcessing === af.data.id}
              >
                <XCircle className='mr-1 h-4 w-4' />
                {isProcessing === af.data.id ? 'Cancelando...' : 'Cancelar'}
              </Button>
            </>
          )}
          {af.data.status === StatusAF.CANCELADA && (
            <Button
              variant='outline'
              size='sm'
              onClick={handleReativarAF}
              disabled={isProcessing === af.data.id}
            >
              <RotateCcw className='mr-1 h-4 w-4' />
              {isProcessing === af.data.id ? 'Reativando...' : 'Reativar'}
            </Button>
          )}
        </div>
      </div>

      <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <FileText className='h-5 w-5' />
              Informações da AF
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Número AF
                </label>
                <p className='font-semibold'>{af.data.numero}</p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Status
                </label>
                <p className='font-semibold'>
                  {StatusAFDesc[af.data.status as StatusAF]}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Data
                </label>
                <p className='font-semibold'>
                  {new Date(af.data.data).toLocaleDateString('pt-BR')}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Data Emissão
                </label>
                <p className='font-semibold'>
                  {af.data.dataEmissao
                    ? new Date(af.data.dataEmissao).toLocaleDateString('pt-BR')
                    : '-'}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Data Vencimento
                </label>
                <p className='font-semibold'>
                  {af.data.dataVencimento
                    ? new Date(af.data.dataVencimento).toLocaleDateString(
                        'pt-BR'
                      )
                    : '-'}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Valor Total
                </label>
                <p className='font-semibold'>
                  {currency(af.data.valorTotal, currencyOptionsNoSymbol)
                    .format()
                    .replace(/0$/, '')}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Valor Utilizado
                </label>
                <p className='font-semibold'>
                  {currency(
                    af.data.valorUtilizado || 0,
                    currencyOptionsNoSymbol
                  )
                    .format()
                    .replace(/0$/, '')}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Saldo
                </label>
                <p className='font-semibold'>
                  {currency(af.data.saldo || 0, currencyOptionsNoSymbol)
                    .format()
                    .replace(/0$/, '')}
                </p>
              </div>
            </div>

            {/* Status-related dates */}
            {af.data.dataUtilizacao && (
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Data de Utilização
                </label>
                <p className='font-semibold'>
                  {new Date(af.data.dataUtilizacao).toLocaleDateString('pt-BR')}
                </p>
              </div>
            )}
            {af.data.dataCancelamento && (
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Data de Cancelamento
                </label>
                <p className='font-semibold'>
                  {new Date(af.data.dataCancelamento).toLocaleDateString(
                    'pt-BR'
                  )}
                </p>
              </div>
            )}
            {af.data.dataReativacao && (
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Data de Reativação
                </label>
                <p className='font-semibold'>
                  {new Date(af.data.dataReativacao).toLocaleDateString('pt-BR')}
                </p>
              </div>
            )}

            {af.data.resumo && (
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Resumo
                </label>
                <p className='mt-1'>{af.data.resumo}</p>
              </div>
            )}

            {af.data.obs && (
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Observações
                </label>
                <p className='mt-1'>{af.data.obs}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Building2 className='h-5 w-5' />
              Informações do Empenho
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Número do Empenho
                </label>
                <p className='font-semibold'>#{af.data.empenho.id}</p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Data do Empenho
                </label>
                <p className='font-semibold'>
                  {new Date(af.data.empenho.data).toLocaleDateString('pt-BR')}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Fornecedor
                </label>
                <p className='font-semibold'>
                  {af.data.empenho.fornecedor?.nome}
                </p>
              </div>
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Saldo do Empenho
                </label>
                <p className='font-semibold'>
                  {currency(af.data.saldo || 0, currencyOptionsNoSymbol)
                    .format()
                    .replace(/0$/, '')}
                </p>
              </div>
            </div>

            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Despesa
              </label>
              <p className='mt-1 font-semibold'>
                {af.data.empenho.dotacao?.despesa}
              </p>
            </div>

            {af.data.empenho.resumo && (
              <div>
                <label className='text-muted-foreground text-sm font-medium'>
                  Resumo do Empenho
                </label>
                <p className='mt-1'>{af.data.empenho.resumo}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {af.data.auditoria && af.data.auditoria.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Calendar className='h-5 w-5' />
              Histórico de Alterações
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {af.data.auditoria.map((auditoria) => (
                <div
                  key={auditoria.id}
                  className='border-muted border-l-2 pl-4'
                >
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-2'>
                      <User className='h-4 w-4' />
                      <span className='font-medium'>
                        {auditoria.usuario?.nome || 'Sistema'}
                      </span>
                      <Badge variant='outline' className='text-xs'>
                        {AuditoriaAFsDesc[auditoria.acao as AuditoriaAFs]}
                      </Badge>
                    </div>
                    <span className='text-muted-foreground text-sm'>
                      {new Date(auditoria.data).toLocaleString('pt-BR')}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <DocumentUploadAF
        idAF={id}
        exercicio={af.data?.exercicio}
        idDotacao={af.data?.empenho?.dotacao?.id}
        idEmpenho={af.data?.empenho?.id}
        documentosExistentes={af.data?.afs_documentos || []}
        readOnly={
          af.data?.status === StatusAF.CANCELADA ||
          af.data?.status === StatusAF.UTILIZADA
        }
      />
    </div>
  );
}
