import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  ativarDepartamento,
  revalidateDepartamentos,
} from '@/lib/database/gerenciamento/departamentos';
import { codigoSecretariaMask, toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Check } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertAtivarDepartamento({
  idSecretaria,
  idDepartamento,
  nomeDepartamento,
  codigoDepartamento,
  codigoSecretaria,
}: {
  idDepartamento: number;
  idSecretaria: number;
  nomeDepartamento: string;
  codigoDepartamento: number;
  codigoSecretaria: number;
}) {
  const [loading, setLoading] = useState(false);

  const ativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idDepartamento,
      };

      const res = await ativarDepartamento(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        await revalidateDepartamentos({
          id: idSecretaria,
        });
        toast.success('Departamento Ativado.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='min-w-[115px] text-green-800'
        >
          <Check className='mr-2 size-4' /> Ativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Ativar Departamento?</AlertDialogTitle>
          <AlertDialogDescription>
            O Departamento{' '}
            <span className='font-bold'>
              {codigoSecretariaMask(codigoSecretaria.toString())}.
              {codigoSecretariaMask(codigoDepartamento.toString())}.00 -{' '}
              {nomeDepartamento}
            </span>{' '}
            será ativado.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              ativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Ativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
