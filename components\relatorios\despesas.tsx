import { siteConfig } from '@/config/site';
import { toCurrency } from '@/lib/serverUtils';
import { DotacaoAtualReport } from '@/lib/database/relatorios/dotacoes';
import {
  RelatorioDespesaItem,
  DespesaPessoalItem,
} from '@/lib/database/relatorios/despesas';

interface RelatorioDespesasProps {
  dotacoes?: DotacaoAtualReport[];
  despesas?: RelatorioDespesaItem[];
  despesaPessoal?: DespesaPessoalItem[];
  titulo?: string;
  filtros?: {
    exercicio?: number;
    secretaria?: string;
    departamento?: string;
    subdepartamento?: string;
  };
  tipo?: 'detalhado' | 'resumido' | 'valores' | 'pessoal' | 'despesa-detalhado';
}

export const RelatorioDespesas = ({
  dotacoes,
  despesas,
  despesaPessoal,
  titulo = 'Relatório de Despesas',
  filtros,
  tipo = 'detalhado',
}: RelatorioDespesasProps) => {
  // Handle different report types
  if (tipo === 'despesa-detalhado' && despesas) {
    return renderRelatorioDespesaDetalhado(despesas, titulo, filtros);
  }

  if (tipo === 'valores' && despesas) {
    return renderRelatorioValores(despesas, titulo, filtros);
  }

  if (tipo === 'pessoal' && despesaPessoal) {
    return renderRelatorioDespesaPessoal(despesaPessoal, titulo, filtros);
  }

  // Fallback to original dotacoes-based reports
  if (!dotacoes) {
    return (
      <div className='text-center text-red-500'>
        Nenhum dado disponível para este relatório.
      </div>
    );
  }

  // Agrupar por secretaria
  const agrupadoPorSecretaria = dotacoes.reduce(
    (acc, dotacao) => {
      const secretariaNome = dotacao.secretaria?.nome || 'Sem Secretaria';
      if (!acc[secretariaNome]) {
        acc[secretariaNome] = {
          secretaria: dotacao.secretaria,
          dotacoes: [],
          total: 0,
        };
      }
      acc[secretariaNome].dotacoes.push(dotacao);
      acc[secretariaNome].total += dotacao.valorAtual;
      return acc;
    },
    {} as Record<
      string,
      {
        secretaria: DotacaoAtualReport['secretaria'];
        dotacoes: DotacaoAtualReport[];
        total: number;
      }
    >
  );

  const valorTotal = dotacoes.reduce(
    (sum, dotacao) => sum + dotacao.valorAtual,
    0
  );

  if (tipo === 'resumido') {
    return (
      <div className='mx-auto max-w-[800px] p-4'>
        {/* Cabeçalho */}
        <div className='mb-6 text-center'>
          <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
          <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
          {filtros && (
            <div className='space-y-1 text-sm text-gray-600'>
              {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
              {filtros.secretaria && <p>Secretaria: {filtros.secretaria}</p>}
              {filtros.departamento && (
                <p>Departamento: {filtros.departamento}</p>
              )}
              {filtros.subdepartamento && (
                <p>Subdepartamento: {filtros.subdepartamento}</p>
              )}
            </div>
          )}
        </div>

        {/* Tabela Resumida */}
        <div className='overflow-x-auto'>
          <table className='w-full border-collapse text-xs'>
            <thead>
              <tr className='border-b-2 border-gray-800'>
                <th className='p-1 text-left font-bold'>Secretaria</th>
                <th className='p-1 text-right font-bold'>Total Dotações</th>
                <th className='p-1 text-right font-bold'>Valor Total</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(agrupadoPorSecretaria).map(
                ([secretariaNome, grupo], index) => (
                  <tr
                    key={secretariaNome}
                    className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                  >
                    <td className='border-b border-gray-200 p-1'>
                      {secretariaNome}
                    </td>
                    <td className='border-b border-gray-200 p-1 text-right font-mono'>
                      {grupo.dotacoes.length}
                    </td>
                    <td className='border-b border-gray-200 p-1 text-right font-mono'>
                      {toCurrency(grupo.total).format().replace(/0$/, '')}
                    </td>
                  </tr>
                )
              )}
              {/* Linha do Total */}
              <tr className='border-t-2 border-gray-800 bg-gray-100 font-bold'>
                <td className='p-1 text-right'>TOTAL GERAL:</td>
                <td className='p-1 text-right font-mono'>{dotacoes.length}</td>
                <td className='p-1 text-right font-mono'>
                  {toCurrency(valorTotal).format().replace(/0$/, '')}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Rodapé */}
        <div className='mt-6 text-center text-xs text-gray-500'>
          <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
          <p>
            Total de secretarias: {Object.keys(agrupadoPorSecretaria).length}
          </p>
          <p>Total de dotações: {dotacoes.length}</p>
        </div>
      </div>
    );
  }

  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
            {filtros.secretaria && <p>Secretaria: {filtros.secretaria}</p>}
            {filtros.departamento && (
              <p>Departamento: {filtros.departamento}</p>
            )}
            {filtros.subdepartamento && (
              <p>Subdepartamento: {filtros.subdepartamento}</p>
            )}
          </div>
        )}
      </div>

      {/* Tabela Detalhada */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Secretaria</th>
              <th className='p-1 text-left font-bold'>Departamento</th>
              <th className='p-1 text-left font-bold'>Despesa</th>
              <th className='p-1 text-left font-bold'>Econômica</th>
              <th className='p-1 text-left font-bold'>Funcional</th>
              <th className='p-1 text-left font-bold'>Descrição</th>
              <th className='p-1 text-right font-bold'>Valor Atual</th>
            </tr>
          </thead>
          <tbody>
            {dotacoes.map((dotacao, index) => (
              <tr
                key={dotacao.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1'>
                  {dotacao.secretaria?.nome || 'N/A'}
                </td>
                <td className='border-b border-gray-200 p-1'>
                  {dotacao.departamento?.nome || 'N/A'}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {dotacao.despesa}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {dotacao.economica.codigo}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {dotacao.funcional.codigo}
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div className='truncate' title={dotacao.desc}>
                    {dotacao.desc}
                  </div>
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(dotacao.valorAtual).format().replace(/0$/, '')}
                </td>
              </tr>
            ))}
            {/* Linha do Total */}
            <tr className='border-t-2 border-gray-800 bg-gray-100 font-bold'>
              <td colSpan={6} className='p-1 text-right'>
                TOTAL GERAL:
              </td>
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorTotal).format().replace(/0$/, '')}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Resumo por Secretaria */}
      <div className='mt-6'>
        <h3 className='mb-2 text-sm font-semibold'>Resumo por Secretaria</h3>
        <div className='grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3'>
          {Object.entries(agrupadoPorSecretaria).map(
            ([secretariaNome, grupo]) => (
              <div
                key={secretariaNome}
                className='rounded border bg-gray-50 p-2 text-xs'
              >
                <p className='font-semibold'>{secretariaNome}</p>
                <p className='text-gray-600'>
                  {grupo.dotacoes.length} dotações
                </p>
                <p className='font-mono'>
                  {toCurrency(grupo.total).format().replace(/0$/, '')}
                </p>
              </div>
            )
          )}
        </div>
      </div>

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de dotações: {dotacoes.length}</p>
        <p>Valor total: {toCurrency(valorTotal).format().replace(/0$/, '')}</p>
      </div>
    </div>
  );
};

// Helper function to render detailed expense report
function renderRelatorioDespesaDetalhado(
  despesas: RelatorioDespesaItem[],
  titulo: string,
  filtros?: any
) {
  const valorTotal = despesas.reduce((sum, item) => sum + item.valor, 0);

  return (
    <div className='mx-auto max-w-[1400px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
            {filtros.despesa && <p>Despesa: {filtros.despesa}</p>}
          </div>
        )}
      </div>

      {/* Tabela Detalhada */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Data</th>
              <th className='p-1 text-left font-bold'>Reserva</th>
              <th className='p-1 text-left font-bold'>Empenho</th>
              <th className='p-1 text-left font-bold'>Fornecedor</th>
              <th className='p-1 text-left font-bold'>Histórico</th>
              <th className='p-1 text-right font-bold'>Valor</th>
              <th className='p-1 text-right font-bold'>Jan</th>
              <th className='p-1 text-right font-bold'>Fev</th>
              <th className='p-1 text-right font-bold'>Mar</th>
              <th className='p-1 text-right font-bold'>Abr</th>
              <th className='p-1 text-right font-bold'>Mai</th>
              <th className='p-1 text-right font-bold'>Jun</th>
              <th className='p-1 text-right font-bold'>Jul</th>
              <th className='p-1 text-right font-bold'>Ago</th>
              <th className='p-1 text-right font-bold'>Set</th>
              <th className='p-1 text-right font-bold'>Out</th>
              <th className='p-1 text-right font-bold'>Nov</th>
              <th className='p-1 text-right font-bold'>Dez</th>
              <th className='p-1 text-right font-bold'>Total</th>
              <th className='p-1 text-right font-bold'>Saldo</th>
              <th className='p-1 text-right font-bold'>% Utilizado</th>
            </tr>
          </thead>
          <tbody>
            {despesas.map((item, index) => (
              <tr
                key={item.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1'>{item.data}</td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {item.reserva || '-'}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {item.empenho || '-'}
                </td>
                <td className='border-b border-gray-200 p-1'>
                  {item.fornecedor?.nome || '-'}
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div className='truncate' title={item.historico}>
                    {item.historico}
                  </div>
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(item.valor).format().replace(/0$/, '')}
                </td>
                {Object.values(item.valoresMensais).map((valor, idx) => (
                  <td
                    key={idx}
                    className={`border-b border-gray-200 p-1 text-right font-mono ${
                      item.tipo === 'estorno' &&
                      item.valoresMensaisEstorno &&
                      Object.values(item.valoresMensaisEstorno)[idx] !== 0
                        ? 'font-semibold text-red-600'
                        : ''
                    }`}
                  >
                    {item.tipo === 'estorno' &&
                    item.valoresMensaisEstorno &&
                    Object.values(item.valoresMensaisEstorno)[idx] !== 0
                      ? `${toCurrency(valor).format().replace(/0$/, '')} (${toCurrency(Object.values(item.valoresMensaisEstorno)[idx]).format().replace(/0$/, '')})`
                      : toCurrency(valor).format().replace(/0$/, '')}
                  </td>
                ))}
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(item.valorTotalMensal).format().replace(/0$/, '')}
                </td>
                <td
                  className={`border-b border-gray-200 p-1 text-right font-mono ${item.saldoReserva && item.saldoReserva < 0 ? 'text-red-600' : ''}`}
                >
                  {item.saldoReserva !== undefined
                    ? toCurrency(item.saldoReserva).format().replace(/0$/, '')
                    : '-'}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {item.percentualUtilizado !== undefined
                    ? `${item.percentualUtilizado.toFixed(1)}%`
                    : '-'}
                </td>
              </tr>
            ))}
            {/* Linha do Total */}
            <tr className='border-t-2 border-gray-800 bg-gray-100 font-bold'>
              <td colSpan={6} className='p-1 text-right'>
                TOTAL GERAL:
              </td>
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorTotal).format().replace(/0$/, '')}
              </td>
              {[...Array(12)].map((_, idx) => (
                <td key={idx} className='p-1 text-right font-mono'>
                  {toCurrency(
                    despesas.reduce((sum, item) => {
                      const meses = Object.values(item.valoresMensais);
                      return sum + meses[idx];
                    }, 0)
                  )
                    .format()
                    .replace(/0$/, '')}
                </td>
              ))}
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorTotal).format().replace(/0$/, '')}
              </td>
              <td className='p-1 text-right font-mono'>-</td>
              <td className='p-1 text-right font-mono'>-</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de registros: {despesas.length}</p>
        <p>Valor total: {toCurrency(valorTotal).format().replace(/0$/, '')}</p>
      </div>
    </div>
  );
}

// Helper function to render values report
function renderRelatorioValores(
  despesas: RelatorioDespesaItem[],
  titulo: string,
  filtros?: any
) {
  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
          </div>
        )}
      </div>

      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Tipo</th>
              <th className='p-1 text-left font-bold'>Data</th>
              <th className='p-1 text-right font-bold'>Valor</th>
              <th className='p-1 text-right font-bold'>Jan</th>
              <th className='p-1 text-right font-bold'>Fev</th>
              <th className='p-1 text-right font-bold'>Mar</th>
              <th className='p-1 text-right font-bold'>Abr</th>
              <th className='p-1 text-right font-bold'>Mai</th>
              <th className='p-1 text-right font-bold'>Jun</th>
              <th className='p-1 text-right font-bold'>Jul</th>
              <th className='p-1 text-right font-bold'>Ago</th>
              <th className='p-1 text-right font-bold'>Set</th>
              <th className='p-1 text-right font-bold'>Out</th>
              <th className='p-1 text-right font-bold'>Nov</th>
              <th className='p-1 text-right font-bold'>Dez</th>
            </tr>
          </thead>
          <tbody>
            {despesas.map((item, index) => (
              <tr
                key={item.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1'>{item.tipo}</td>
                <td className='border-b border-gray-200 p-1'>{item.data}</td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {item.valor.toFixed(2)}
                </td>
                {Object.values(item.valoresMensais).map((valor, idx) => (
                  <td
                    key={idx}
                    className={`border-b border-gray-200 p-1 text-right font-mono ${
                      item.tipo === 'estorno' &&
                      item.valoresMensaisEstorno &&
                      Object.values(item.valoresMensaisEstorno)[idx] !== 0
                        ? 'font-semibold text-red-600'
                        : ''
                    }`}
                  >
                    {item.tipo === 'estorno' &&
                    item.valoresMensaisEstorno &&
                    Object.values(item.valoresMensaisEstorno)[idx] !== 0
                      ? `${valor.toFixed(2)} (${Object.values(item.valoresMensaisEstorno)[idx].toFixed(2)})`
                      : valor.toFixed(2)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Helper function to render personal expense report
function renderRelatorioDespesaPessoal(
  despesaPessoal: DespesaPessoalItem[],
  titulo: string,
  filtros?: any
) {
  const valorTotalDotado = despesaPessoal.reduce(
    (sum, item) => sum + item.dotacaoAtual,
    0
  );
  const valorTotalLiquidado = despesaPessoal.reduce(
    (sum, item) => sum + item.totalLiquidado,
    0
  );

  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
          </div>
        )}
      </div>

      {/* Tabela de Despesa Pessoal */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Departamento</th>
              <th className='p-1 text-left font-bold'>Código</th>
              <th className='p-1 text-left font-bold'>Despesa</th>
              <th className='p-1 text-right font-bold'>Dotação Inicial</th>
              <th className='p-1 text-right font-bold'>Suplementações</th>
              <th className='p-1 text-right font-bold'>Anulações</th>
              <th className='p-1 text-right font-bold'>Dotação Atual</th>
              <th className='p-1 text-right font-bold'>Jan</th>
              <th className='p-1 text-right font-bold'>Fev</th>
              <th className='p-1 text-right font-bold'>Mar</th>
              <th className='p-1 text-right font-bold'>Abr</th>
              <th className='p-1 text-right font-bold'>Mai</th>
              <th className='p-1 text-right font-bold'>Jun</th>
              <th className='p-1 text-right font-bold'>Jul</th>
              <th className='p-1 text-right font-bold'>Ago</th>
              <th className='p-1 text-right font-bold'>Set</th>
              <th className='p-1 text-right font-bold'>Out</th>
              <th className='p-1 text-right font-bold'>Nov</th>
              <th className='p-1 text-right font-bold'>Dez</th>
              <th className='p-1 text-right font-bold'>Total Liquidado</th>
              <th className='p-1 text-right font-bold'>Saldo</th>
            </tr>
          </thead>
          <tbody>
            {despesaPessoal.map((item, index) => {
              return (
                <tr
                  key={`${item.departamento}-${item.despesa}`}
                  className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                >
                  <td className='border-b border-gray-200 p-1'>
                    {item.departamento}
                  </td>
                  <td className='border-b border-gray-200 p-1 font-mono'>
                    {item.codigoDepartamento}
                  </td>
                  <td className='border-b border-gray-200 p-1 font-mono'>
                    {item.despesa}
                  </td>
                  <td className='border-b border-gray-200 p-1 text-right font-mono'>
                    {toCurrency(item.dotacaoInicial).format().replace(/0$/, '')}
                  </td>
                  <td className='border-b border-gray-200 p-1 text-right font-mono'>
                    {toCurrency(item.suplementacoes).format().replace(/0$/, '')}
                  </td>
                  <td className='border-b border-gray-200 p-1 text-right font-mono'>
                    {toCurrency(item.anulações).format().replace(/0$/, '')}
                  </td>
                  <td className='border-b border-gray-200 p-1 text-right font-mono'>
                    {toCurrency(item.dotacaoAtual).format().replace(/0$/, '')}
                  </td>
                  {Object.values(item.valoresLiquidados).map((valor, idx) => (
                    <td
                      key={idx}
                      className='border-b border-gray-200 p-1 text-right font-mono'
                    >
                      {toCurrency(valor).format().replace(/0$/, '')}
                    </td>
                  ))}
                  <td className='border-b border-gray-200 p-1 text-right font-mono'>
                    {toCurrency(item.totalLiquidado).format().replace(/0$/, '')}
                  </td>
                  <td
                    className={`border-b border-gray-200 p-1 text-right font-mono ${item.saldo < 0 ? 'text-red-600' : ''}`}
                  >
                    {toCurrency(item.saldo).format().replace(/0$/, '')}
                  </td>
                </tr>
              );
            })}
            {/* Linha do Total */}
            <tr className='border-t-2 border-gray-800 bg-gray-100 font-bold'>
              <td colSpan={7} className='p-1 text-right'>
                TOTAL GERAL:
              </td>
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorTotalDotado).format().replace(/0$/, '')}
              </td>
              {[...Array(12)].map((_, idx) => (
                <td key={idx} className='p-1 text-right font-mono'>
                  {toCurrency(
                    despesaPessoal.reduce((sum, item) => {
                      const meses = Object.values(item.valoresLiquidados);
                      return sum + meses[idx];
                    }, 0)
                  )
                    .format()
                    .replace(/0$/, '')}
                </td>
              ))}
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorTotalLiquidado).format().replace(/0$/, '')}
              </td>
              <td
                className={`p-1 text-right font-mono ${valorTotalDotado - valorTotalLiquidado < 0 ? 'text-red-600' : ''}`}
              >
                {toCurrency(valorTotalDotado - valorTotalLiquidado)
                  .format()
                  .replace(/0$/, '')}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>
          Total de departamentos:{' '}
          {new Set(despesaPessoal.map((item) => item.departamento)).size}
        </p>
        <p>
          Valor total dotado:{' '}
          {toCurrency(valorTotalDotado).format().replace(/0$/, '')}
        </p>
        <p>
          Valor total liquidado:{' '}
          {toCurrency(valorTotalLiquidado).format().replace(/0$/, '')}
        </p>
        <p>
          Saldo geral:{' '}
          {toCurrency(valorTotalDotado - valorTotalLiquidado)
            .format()
            .replace(/0$/, '')}
        </p>
      </div>
    </div>
  );
}
