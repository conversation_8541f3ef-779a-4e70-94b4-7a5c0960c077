'use server';
import { z } from 'zod';
import { obterAlteracaoParaRelatorio } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioAlteracaoOrcamentaria } from '@/components/movimento/alteracaoOrcamentaria/relatorioAlteracaoOrcamentaria';
import { isNumeric } from '@/lib/utils';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function ImprimirAlteracaoOrcamentariaPage({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
  };
  const currSearchParams = await searchParams;

  const { id } = await params;
  if (!isNumeric(id)) return <ErrorAlert error='ID inválido.' />;
  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }
  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();
  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }
  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }
  const incluirGestor = currSearchParams.incluirGestor !== 'false';
  const alteracaoOrcamentaria = await obterAlteracaoParaRelatorio({
    id: Number(id),
    bearer,
    incluirGestor,
  });
  if (alteracaoOrcamentaria.error) {
    return <ErrorAlert error={alteracaoOrcamentaria.error} />;
  }
  if (!alteracaoOrcamentaria.data) {
    return <ErrorAlert error='Falha ao obter alteração orçamentária.' />;
  }

  return (
    <>
      <RelatorioAlteracaoOrcamentaria
        alteracaoOrcamentaria={alteracaoOrcamentaria}
      />
      <ClientCompletionTrigger />
    </>
  );
}
