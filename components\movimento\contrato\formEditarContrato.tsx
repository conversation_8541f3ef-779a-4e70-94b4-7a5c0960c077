'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { ArrowLeft, CalendarIcon, Save } from 'lucide-react';
import {
  cn,
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  obterAnoAtual,
  obterDataAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import currency from 'currency.js';
import { criarContratoSchema } from '@/lib/validation';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  editarContrato,
  obterContrato,
} from '@/lib/database/movimento/contratos';
import { ComboboxSelecionaFornecedor } from './comboboxSelecionarFornecedor';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { ptBR } from 'date-fns/locale';
import { Icons } from '@/components/icons';
import { listarFornecedores } from '@/lib/database/gerenciamento/fornecedores';

export default function FormEditarContrato({
  contrato,
}: {
  contrato: Awaited<ReturnType<typeof obterContrato>>;
}) {
  const contratoData = contrato.data;

  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [fornecedores, setFornecedores] = useState([]);

  const anoAtual = obterAnoAtual();
  const [date, setDate] = useState<Date | null>(
    contratoData?.dataInicio || null
  );
  const [dateFinal, setDateFinal] = useState<Date | null>(
    contratoData?.dataFinal || null
  );

  const [valor, setValor] = useState(
    currency(contratoData?.valor || 0, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );

  const form = useForm<z.infer<typeof criarContratoSchema>>({
    resolver: zodResolver(criarContratoSchema),
    defaultValues: {
      //@ts-ignore
      id: contratoData?.id,
      idFornecedor: contratoData?.idFornecedor,
      processo: contratoData?.processo,
      processoAno: contratoData?.processoAno,
      valor: contratoData?.valor,
      nomeGestor: contratoData?.nomeGestor,
      cpfGestor: contratoData?.cpfGestor,
      condPagamento: contratoData?.condPagamento,
      numAf: contratoData?.numAf,
      dataInicio: contratoData?.dataInicio,
      dataFinal: contratoData?.dataFinal,
    },
  });

  useEffect(() => {
    // form.setFocus('processo');

    const buscarFornecedores = async () => {
      try {
        setLoading(true);
        const res: any = await listarFornecedores();
        if (res?.error) {
          setLoading(false);
          toast.error(res.error);
          setFornecedores([]);
        } else {
          setLoading(false);
          setFornecedores(res.data);
        }
      } catch (error) {
        setLoading(false);
        console.log(JSON.stringify(error));
        toast.error(toastAlgoDeuErrado);
      }
    };

    buscarFornecedores();
  }, []);

  const onSubmit = async (values: z.infer<typeof criarContratoSchema>) => {
    try {
      setLoading(true);
      const res = await editarContrato({
        ...values,
        id: contratoData?.id,
        processoAno: values.processoAno || anoAtual,
      });
      if (res?.error) {
        setLoading(false);
        toast(res.error, { duration: 10000 });
      } else {
        toast.success('Contrato Alterado', {
          description: 'Número do contrato: ' + res.data?.idContrato,
          action: {
            label: 'Visualizar',
            onClick: () =>
              router.push(
                '/movimento/contratos/visualizar/' + res?.data?.idContrato
              ),
          },
        });
        router.push('/movimento/contratos');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const onInvalid = (errors: any) => {
    console.log(errors);
    toast.error(JSON.stringify(errors));
  };

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit, onInvalid)}>
            <div className='flex'>
              <div className='w-[33%]'>
                <FormField
                  control={form.control}
                  name='processo'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='flex w-full text-left'>
                        Processo Nº
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className='max-w-[250px] text-left text-base'
                          onChange={(e) =>
                            form.setValue('processo', Number(e.target.value))
                          }
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className='flex w-[33%] flex-wrap gap-2'>
                <FormField
                  name='processoAno'
                  render={({ field }) => (
                    <FormItem className='w-full'>
                      <FormLabel className='flex w-full text-left'>
                        Ano
                      </FormLabel>
                      <Input
                        {...field}
                        pattern='[0-99]*'
                        className='max-w-[250px] text-left text-base'
                        value={form.getValues('processoAno') || anoAtual}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('processoAno', Number(value));
                        }}
                      />
                    </FormItem>
                  )}
                />
              </div>

              <div className='flex w-[33%] flex-wrap gap-2'>
                <FormField
                  name='numAf'
                  render={({ field }) => (
                    <FormItem className='w-full'>
                      <FormLabel className='flex w-full text-left'>
                        Numero AF
                      </FormLabel>
                      <Input
                        {...field}
                        pattern='[0-99]*'
                        className='max-w-[400px] text-left text-base'
                        value={`${field.value}`}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('numAf', Number(value));
                        }}
                      />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className='mt-4 flex gap-4'>
              <div className='flex w-[50%] flex-wrap gap-2'>
                <Label className='text-left'>Fornecedor</Label>
                <ComboboxSelecionaFornecedor
                  fornecedores={fornecedores}
                  form={form}
                />
              </div>

              <div className='flex w-[50%] gap-2'>
                <FormField
                  name='valor'
                  render={({ field }) => (
                    <FormItem className='w-full'>
                      <FormLabel className='flex w-full text-left'>
                        Valor
                      </FormLabel>
                      <Input
                        {...field}
                        pattern='[0-99]*'
                        className='max-w-full text-right text-base'
                        value={valor}
                        onChange={(event) => {
                          const { value } = event.target;
                          setValor(moneyMask(value, 2));
                          form.setValue('valor', Number(moneyUnmask(value)));
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className='flex gap-4 py-4'>
              <FormField
                control={form.control}
                name='dataInicio'
                render={({ field }) => (
                  <FormItem className='w-[50%]'>
                    <FormLabel className='flex w-full text-left'>
                      Data Inicial
                    </FormLabel>
                    <FormControl>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-[240px] justify-start text-left font-normal',
                              !date && 'text-muted-foreground'
                            )}
                          >
                            <CalendarIcon className='mr-2 h-4 w-4' />
                            {date ? (
                              format(date, 'PPP', { locale: ptBR })
                            ) : (
                              <span>Escolha a Data</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className='w-auto p-0' align='start'>
                          <Calendar
                            {...field}
                            locale={ptBR}
                            mode='single'
                            // selected={date}
                            onSelect={(value) => {
                              form.setValue(
                                'dataInicio',
                                value || obterDataAtual(),
                                {
                                  shouldDirty: true,
                                }
                              );
                              setDate(value || null);
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='dataFinal'
                render={({ field }) => (
                  <FormItem className='w-[50%]'>
                    <FormLabel className='flex w-full text-left'>
                      Data Final
                    </FormLabel>
                    <FormControl>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-[240px] justify-start text-left font-normal',
                              !date && 'text-muted-foreground'
                            )}
                          >
                            <CalendarIcon className='mr-2 h-4 w-4' />
                            {dateFinal ? (
                              format(dateFinal, 'PPP', { locale: ptBR })
                            ) : (
                              <span>Escolha a Data</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className='w-auto p-0' align='start'>
                          <Calendar
                            {...field}
                            locale={ptBR}
                            mode='single'
                            // selected={date}
                            onSelect={(value) => {
                              form.setValue(
                                'dataFinal',
                                value || obterDataAtual(),
                                {
                                  shouldDirty: true,
                                }
                              );
                              setDateFinal(value || null);
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='nomeGestor'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Nome do Gestor do Contrato
                    </FormLabel>
                    <Input
                      {...field}
                      className='max-w-[400px] text-left text-base'
                      value={`${field.value}`}
                      onChange={(event) => {
                        const { value } = event.target;
                        form.setValue('nomeGestor', value);
                      }}
                    />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='cpfGestor'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>CPF</FormLabel>
                    <Input
                      {...field}
                      className='max-w-[400px] text-left text-base'
                      value={`${field.value}`}
                      onChange={(event) => {
                        const { value } = event.target;
                        form.setValue('cpfGestor', value);
                      }}
                    />
                  </FormItem>
                )}
              />
            </div>

            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='condPagamento'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Condição de Pagamento
                    </FormLabel>
                    <Input
                      {...field}
                      className='max-w-full text-left text-base'
                      value={`${field.value}`}
                      onChange={(event) => {
                        const { value } = event.target;
                        form.setValue('condPagamento', value);
                      }}
                    />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <div className='mt-12 flex w-full justify-between'>
          <Button
            variant={'destructive'}
            disabled={loading}
            onClick={(e) => {
              e.preventDefault();
              router.push('/movimento/contratos');
            }}
          >
            <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit, onInvalid)}
            disabled={loading}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <Save className='mr-2 h-4 w-4' /> Salvar Contrato
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
}
