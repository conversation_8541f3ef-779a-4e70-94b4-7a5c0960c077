import { NextResponse } from 'next/server';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';

export async function GET() {
  try {
    const headers_ = await headers();
    if (!headers_.get('Authorization')) {
      return NextResponse.json({ error: 'Sem autenticação' }, { status: 401 });
    }

    const bearer = headers_.get('Authorization') || '';
    const supabase = await createClient<PERSON>ith<PERSON>earer(bearer);
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Import the function to get protocolos for combobox
    const { listarProtocolosParaCombobox } = await import(
      '@/lib/database/relatorios/protocolos'
    );

    const result = await listarProtocolosParaCombobox();

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error('Erro ao listar protocolos:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
