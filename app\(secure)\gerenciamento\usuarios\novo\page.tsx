'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { listarCargosAtivos } from '@/lib/database/gerenciamento/usuarios';
import CriarUsuarioForm from '@/components/gerenciamento/usuarios/criarUsuarioForm';

export default async function NovoUsuarioPage() {
  const cargos = await listarCargosAtivos();

  if (cargos.error) return <ErrorAlert error={cargos.error} />;
  if (!cargos.data) return <ErrorAlert error={'Cargos não encontrados.'} />;

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Novo Usuário</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <CriarUsuarioForm cargos={cargos} />
        </div>
      </PageContent>
    </PageWrapper>
  );
}
