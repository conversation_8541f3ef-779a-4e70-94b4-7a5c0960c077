'use client';
import { ColumnDef } from '@tanstack/react-table';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { AcessoCargo } from '@/types/app';
import { Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dispatch } from 'react';

export default function ConfigAcessosDatatable({
  data,
  setAcessosConfigurados,
}: {
  data: AcessoCargo[];
  setAcessosConfigurados: Dispatch<React.SetStateAction<AcessoCargo[]>>;
}) {
  if (!data) return null;
  const columns: ColumnDef<(typeof data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Código',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <Button
          type='button'
          variant='ghost'
          aria-description='Remover Tipo de Acesso'
          onClick={() => {
            setAcessosConfigurados(
              data.filter((acesso) => acesso.id !== row.getValue('id'))
            );
          }}
        >
          <Trash className='size-4' />
        </Button>
      ),
    },
  ];
  return (
    <div className='overflow-x-scroll'>
      <ReusableDatatable columns={columns} data={data} />
    </div>
  );
}
