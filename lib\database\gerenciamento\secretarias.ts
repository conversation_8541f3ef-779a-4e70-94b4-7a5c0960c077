'use server';
import { revalidatePath } from 'next/cache';
import { prisma } from '../../prisma';
import {
  auditoriaErroSchema,
  nomeEIdSchema,
  idSchema,
  permissaoSchema,
  secretariaSchema,
} from '../../validation';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { Modulos } from '@/lib/modulos';
import { AuditoriaGerenciamentoSecretarias, Permissoes } from '@/lib/enums';
import { inserirErroAudit } from '../auditoria/erro';
import z from 'zod';

export const listarSecretarias = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const secretarias = await prisma.secretarias.findMany({
      orderBy: { codigo: 'asc' },
    });
    return {
      data: secretarias,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter secretarias.`,
    };
  }
};

export const obterSecretaria = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const secretaria = await prisma.secretarias.findFirstOrThrow({
      where: { id: id },
    });
    return {
      data: secretaria,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter secretaria.`,
    };
  }
};

export const criarSecretaria = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!result.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = secretariaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { codigo, nome, departamentos } = parsedParams.data;

  const ip = (await obterIpUsuarioConectado()).data;

  try {
    await prisma.$transaction(async (tx) => {
      const secretariaResult = await tx.secretarias.create({
        data: {
          nome: nome,
          codigo: codigo,
        },
      });

      const auditPromises = [
        tx.gerenciamentoSecretarias_audit.create({
          data: {
            secretariaId: secretariaResult.id,
            acao: AuditoriaGerenciamentoSecretarias.CRIAR_SECRETARIA,
            usuarioId: result.idUsuario!,
            ip,
          },
        }),
      ];

      const subdepsPromises: Promise<void>[] = [];
      const depsPromises = departamentos.map(async (dep) => {
        const criarDep = async () => {
          const departamentoResult = await tx.departamentos.create({
            data: {
              secretariaId: secretariaResult.id,
              codigo: dep.codigo,
              nome: dep.nome,
            },
          });

          const auditPromise = tx.gerenciamentoSecretarias_audit.create({
            data: {
              departamentoId: departamentoResult.id,
              acao: AuditoriaGerenciamentoSecretarias.CRIAR_DEPARTAMENTO,
              usuarioId: result.idUsuario!,
              ip,
            },
          });

          auditPromises.push(auditPromise);

          dep.subdepartamentos.forEach(async (subdep) => {
            const criarSubDep = async () => {
              const subdepartamentoResult = await tx.subdepartamentos.create({
                data: {
                  departamentoId: departamentoResult.id,
                  codigo: subdep.codigo,
                  nome: subdep.nome,
                },
              });

              const auditPromise = tx.gerenciamentoSecretarias_audit.create({
                data: {
                  subdepartamentoId: subdepartamentoResult.id,
                  acao: AuditoriaGerenciamentoSecretarias.CRIAR_SUBDEPARTAMENTO,
                  usuarioId: result.idUsuario!,
                  ip,
                },
              });

              auditPromises.push(auditPromise);
            };

            subdepsPromises.push(criarSubDep());
          });
        };

        return criarDep();
      });

      await Promise.all(depsPromises);
      await Promise.all(subdepsPromises);
      await Promise.all(auditPromises);

      return secretariaResult;
    });

    revalidatePath('/gerenciamento/secretarias');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Secretaria já existe.`,
        };
      }
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar secretaria.`,
    };
  }
};

export const renomearSecretaria = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultadoPermissao = await temPermissao(parametrosPermissao);

  if (!resultadoPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultadoPermissao.error) {
    return {
      error: resultadoPermissao.error,
    };
  }

  if (!resultadoPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultadoPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = nomeEIdSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { nome, id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    const result = await prisma.$transaction(async (tx) => {
      const nomeAntigo = await tx.secretarias.findFirstOrThrow({
        where: {
          id: id,
        },
        select: {
          nome: true,
        },
      });

      const secretariaPromise = tx.secretarias.update({
        where: {
          id: id,
        },
        data: {
          nome: nome,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          secretariaId: id,
          acao: AuditoriaGerenciamentoSecretarias.RENOMEAR_SECRETARIA,
          usuarioId: resultadoPermissao.idUsuario!,
          ip,
          de: nomeAntigo.nome,
          para: nome,
        },
      });

      await Promise.all([secretariaPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/secretarias');
    return {
      data: result,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao renomear secretaria.`,
    };
  }
};

export const desativarSecretaria = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const secretariaPromise = tx.secretarias.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          secretariaId: id,
          acao: AuditoriaGerenciamentoSecretarias.DESATIVAR_SECRETARIA,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([secretariaPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/secretarias');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar secretaria.`,
    };
  }
};

export const ativarSecretaria = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const secretariaPromise = tx.secretarias.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          secretariaId: id,
          acao: AuditoriaGerenciamentoSecretarias.ATIVAR_SECRETARIA,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([secretariaPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/secretarias');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar secretaria.`,
    };
  }
};
