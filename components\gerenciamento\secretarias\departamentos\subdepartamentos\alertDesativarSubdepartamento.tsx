import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  desativarSubdepartamento,
  revalidateSubdepartamentos,
} from '@/lib/database/gerenciamento/subdepartamentos';
import { codigoSecretariaMask, toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema, secretariaEDepartamentoIdsSchema } from '@/lib/validation';
import { Ban } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertDesativarSubdepartamento({
  idSubdepartamento,
  nomeSubdepartamento,
  codigoSubdepartamento,
  codigoSecretaria,
  idSecretaria,
  idDepartamento,
  codigoDepartamento,
}: {
  idSubdepartamento: number;
  nomeSubdepartamento: string;
  codigoSubdepartamento: number;
  codigoSecretaria: number;
  idSecretaria: number;
  idDepartamento: number;
  codigoDepartamento: number;
}) {
  const [loading, setLoading] = useState(false);

  const desativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idSubdepartamento,
      };

      const res = await desativarSubdepartamento(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        const data: z.infer<typeof secretariaEDepartamentoIdsSchema> = {
          idDepartamento: idDepartamento,
          idSecretaria: idSecretaria,
        };
        await revalidateSubdepartamentos(data);
        toast.success('Subdepartamento Desativado.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='min-w-[115px] text-red-800'
        >
          <Ban className='mr-2 size-4' /> Desativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Desativar Subdepartamento?</AlertDialogTitle>
          <AlertDialogDescription>
            O Subdepartamento{' '}
            <span className='font-bold'>
              {codigoSecretariaMask(codigoSecretaria.toString())}.
              {codigoSecretariaMask(codigoDepartamento.toString())}.
              {codigoSecretariaMask(codigoSubdepartamento.toString())} -{' '}
              {nomeSubdepartamento}
            </span>{' '}
            será desativado.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              desativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Desativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
