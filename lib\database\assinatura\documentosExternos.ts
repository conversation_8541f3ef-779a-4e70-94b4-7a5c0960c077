'use server';

import {
  AuditoriaAssinaturaExterna,
  Per<PERSON><PERSON><PERSON>,
  StatusDocumentoExterno,
} from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  solicitarAssinaturaExternaSchema,
  uploadDocumentoAssinadoSchema,
  validarAssinaturaDigitalSchema,
  cancelarDocumentoExternoSchema,
  listarDocumentosExternosSchema,
  permissaoSchema,
} from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { createClient, createSuperClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { converterTemplatePdf } from '@/lib/utils/conversorPdf';
import { montarPathDocumentoExterno } from '@/lib/utils';
import type { z as Zod } from 'zod';
import { z } from 'zod';

export const criarDocumentoExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = solicitarAssinaturaExternaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const {
    idOrigemTipo,
    idOrigemRegistro,
    idUsuarioExterno,
    idTemplate,
    nomeDocumento,
    descricaoDocumento,
    instrucoes,
    variaveisPersonalizadas,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Verificar se o usuário externo existe e está ativo
    const usuarioExterno = await prisma.external_users.findUnique({
      where: {
        id: idUsuarioExterno,
        ativo: true,
      },
    });

    if (!usuarioExterno) {
      return {
        error: 'Usuário externo não encontrado ou inativo.',
      };
    }

    // Obter template
    const template = await prisma.templates_documentos.findUnique({
      where: {
        id: idTemplate,
        ativo: true,
      },
    });

    if (!template) {
      return {
        error: 'Template não encontrado ou inativo.',
      };
    }

    // Processar variáveis do template
    const variaveisBase = {
      '{{NOME_USUARIO}}': usuarioExterno.nome,
      '{{EMAIL_USUARIO}}': usuarioExterno.email,
      '{{NOME_DOCUMENTO}}': nomeDocumento,
      '{{DESCRICAO_DOCUMENTO}}': descricaoDocumento || '',
      ...variaveisPersonalizadas,
    };

    // Converter template para PDF usando o sistema de impressão
    const pdfResult = await converterTemplatePdf(
      idTemplate,
      variaveisBase,
      nomeDocumento
    );

    if (pdfResult.error) {
      return {
        error: `Erro ao gerar PDF: ${pdfResult.error}`,
      };
    }

    const novoDocumento = await prisma.$transaction(async (tx) => {
      // Criar documento externo
      const documento = await tx.documentos_externos.create({
        data: {
          idOrigemTipo,
          idOrigemRegistro,
          idUsuarioExterno,
          idUsuarioSolicitante: resultPermissao.idUsuario!,
          nomeDocumento,
          descricaoDocumento,
          instrucoes,
          pathDocumentoOriginal: '', // Será preenchido após upload
          status: StatusDocumentoExterno['Aguardando Assinatura'],
          ativo: true,
        },
      });

      // Criar controle de acesso
      await tx.external_users_documentos.create({
        data: {
          idUsuarioExterno,
          idDocumentoExterno: documento.id,
        },
      });

      // Auditoria
      await tx.documentos_externos_audit.create({
        data: {
          idDocumentoExterno: documento.id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAssinaturaExterna.CRIAR_DOCUMENTO_EXTERNO,
          ip,
        },
      });

      return documento;
    });

    // Upload do PDF para Supabase usando createSuperClient para operações administrativas
    const supabase = createSuperClient();
    const pathDocumento = montarPathDocumentoExterno(
      idUsuarioExterno,
      novoDocumento.id,
      'original',
      nomeDocumento
    );

    const { error: uploadError } = await supabase.storage
      .from('documentos-externos')
      .upload(pathDocumento, pdfResult.data!, {
        contentType: 'application/pdf',
        upsert: false,
      });

    if (uploadError) {
      // Reverter criação do documento se upload falhar
      await prisma.documentos_externos.delete({
        where: { id: novoDocumento.id },
      });

      return {
        error: `Erro ao fazer upload do documento: ${uploadError.message}`,
      };
    }

    // Atualizar path do documento
    await prisma.documentos_externos.update({
      where: { id: novoDocumento.id },
      data: {
        pathDocumentoOriginal: pathDocumento,
      },
    });

    revalidatePath('/assinatura/usuarios-externos');
    revalidatePath(`/movimento/reservas/${idOrigemRegistro}`);

    return {
      data: novoDocumento,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_EXTERNA,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao criar documento externo.',
    };
  }
};

export const listarDocumentosExternosPendentes = async (_params: unknown) => {
  // Verificar se é usuário externo
  const supabase = await createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    return {
      error: 'Usuário não autenticado.',
    };
  }

  // Verificar se é usuário externo
  const usuarioExterno = await prisma.external_users.findUnique({
    where: {
      user_id: userData.user.id,
      ativo: true,
    },
  });

  if (!usuarioExterno) {
    return {
      error: 'Usuário externo não encontrado.',
    };
  }

  try {
    const documentos = await prisma.documentos_externos.findMany({
      where: {
        idUsuarioExterno: usuarioExterno.id,
        ativo: true,
        status: {
          in: [
            StatusDocumentoExterno['Aguardando Assinatura'],
            StatusDocumentoExterno['Processando'],
          ],
        },
      },
      include: {
        usuarioSolicitante: {
          select: {
            nome: true,
          },
        },
      },
      orderBy: {
        dataCriacao: 'desc',
      },
    });

    return {
      data: { documentos },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_EXTERNA,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao listar documentos externos pendentes.',
    };
  }
};

export const listarDocumentosExternosGestor = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.ACESSAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = listarDocumentosExternosSchema.safeParse(params || {});

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { idUsuarioExterno, status, idOrigemTipo } = parsedParams.data;

  try {
    const whereClause: any = {
      ativo: true,
    };

    if (idUsuarioExterno) {
      whereClause.idUsuarioExterno = idUsuarioExterno;
    }

    if (status !== undefined) {
      whereClause.status = status;
    }

    if (idOrigemTipo !== undefined) {
      whereClause.idOrigemTipo = idOrigemTipo;
    }

    const documentos = await prisma.documentos_externos.findMany({
      where: whereClause,
      include: {
        usuarioExterno: {
          select: {
            nome: true,
            email: true,
          },
        },
        usuarioSolicitante: {
          select: {
            nome: true,
          },
        },
      },
      orderBy: {
        dataCriacao: 'desc',
      },
    });

    return {
      data: { documentos },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_EXTERNA,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao listar documentos externos.',
    };
  }
};

export const uploadDocumentoAssinado = async (params: unknown) => {
  // Verificar se é usuário externo
  const supabase = await createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    return {
      error: 'Usuário não autenticado.',
    };
  }

  const parsedParams = uploadDocumentoAssinadoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { idDocumentoExterno, pathDocumentoAssinado } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Verificar se é usuário externo e se tem acesso ao documento
    const usuarioExterno = await prisma.external_users.findUnique({
      where: {
        user_id: userData.user.id,
        ativo: true,
      },
    });

    if (!usuarioExterno) {
      return {
        error: 'Usuário externo não encontrado.',
      };
    }

    // Verificar acesso ao documento
    const acessoDocumento = await prisma.external_users_documentos.findFirst({
      where: {
        idUsuarioExterno: usuarioExterno.id,
        idDocumentoExterno,
      },
      include: {
        documentoExterno: true,
      },
    });

    if (!acessoDocumento) {
      return {
        error: 'Acesso negado ao documento.',
      };
    }

    if (
      acessoDocumento.documentoExterno.status !==
      StatusDocumentoExterno['Aguardando Assinatura']
    ) {
      return {
        error: 'Documento não está aguardando assinatura.',
      };
    }

    await prisma.$transaction(async (tx) => {
      // Atualizar documento
      await tx.documentos_externos.update({
        where: { id: idDocumentoExterno },
        data: {
          pathDocumentoAssinado,
          status: StatusDocumentoExterno['Processando'],
          dataAssinatura: new Date(),
        },
      });

      // Auditoria
      await tx.documentos_externos_audit.create({
        data: {
          idDocumentoExterno,
          idUsuario: usuarioExterno.id,
          acao: AuditoriaAssinaturaExterna.UPLOAD_DOCUMENTO_ASSINADO,
          ip,
        },
      });
    });

    revalidatePath('/assinatura/usuarios-externos');

    return {
      data: true,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_EXTERNA,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao fazer upload do documento assinado.',
    };
  }
};

export const validarAssinaturaDigital = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = validarAssinaturaDigitalSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { idDocumentoExterno, forcarValidacao } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Verificar se o documento existe e está no status correto
    const documento = await prisma.documentos_externos.findUnique({
      where: { id: idDocumentoExterno },
    });

    if (!documento) {
      return {
        error: 'Documento não encontrado.',
      };
    }

    if (
      documento.status !== StatusDocumentoExterno['Processando'] &&
      !forcarValidacao
    ) {
      return {
        error: 'Documento não está em processamento.',
      };
    }

    if (!documento.pathDocumentoAssinado) {
      return {
        error: 'Documento assinado não encontrado.',
      };
    }

    // TODO: Implementar validação real da assinatura digital
    // Por enquanto, simular validação bem-sucedida
    const validacaoSucesso = true;

    await prisma.$transaction(async (tx) => {
      if (validacaoSucesso) {
        // Aprovar documento
        await tx.documentos_externos.update({
          where: { id: idDocumentoExterno },
          data: {
            status: StatusDocumentoExterno['Assinado'],
          },
        });

        // Auditoria de aprovação
        await tx.documentos_externos_audit.create({
          data: {
            idDocumentoExterno,
            idUsuario: resultPermissao.idUsuario!,
            acao: AuditoriaAssinaturaExterna.APROVAR_ASSINATURA_EXTERNA,
            ip,
          },
        });
      } else {
        // Rejeitar documento
        await tx.documentos_externos.update({
          where: { id: idDocumentoExterno },
          data: {
            status: StatusDocumentoExterno['Rejeitado'],
            motivoCancelamento: 'Assinatura digital inválida',
          },
        });

        // Auditoria de rejeição
        await tx.documentos_externos_audit.create({
          data: {
            idDocumentoExterno,
            idUsuario: resultPermissao.idUsuario!,
            acao: AuditoriaAssinaturaExterna.REJEITAR_ASSINATURA_EXTERNA,
            ip,
          },
        });
      }
    });

    revalidatePath('/assinatura/usuarios-externos');

    return {
      data: { validacaoSucesso },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_EXTERNA,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao validar assinatura digital.',
    };
  }
};

export const listarDocumentosExternosReserva = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.ACESSAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = z
    .object({
      idReserva: z.coerce.number().int().min(1),
    })
    .safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { idReserva } = parsedParams.data;

  try {
    const documentos = await prisma.documentos_externos.findMany({
      where: {
        idOrigemTipo: 1, // TipoOrigemDocumento.Reserva
        idOrigemRegistro: idReserva,
        ativo: true,
      },
      include: {
        usuarioExterno: {
          select: {
            nome: true,
            email: true,
          },
        },
        usuarioSolicitante: {
          select: {
            nome: true,
          },
        },
      },
      orderBy: {
        dataCriacao: 'desc',
      },
    });

    return {
      data: { documentos },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_EXTERNA,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao listar documentos externos da reserva.',
    };
  }
};

export const cancelarDocumentoExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = cancelarDocumentoExternoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { idDocumentoExterno, motivoCancelamento } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Verificar se o documento existe
    const documento = await prisma.documentos_externos.findUnique({
      where: { id: idDocumentoExterno },
    });

    if (!documento) {
      return {
        error: 'Documento não encontrado.',
      };
    }

    if (documento.status === StatusDocumentoExterno['Cancelado']) {
      return {
        error: 'Documento já está cancelado.',
      };
    }

    await prisma.$transaction(async (tx) => {
      // Cancelar documento
      await tx.documentos_externos.update({
        where: { id: idDocumentoExterno },
        data: {
          status: StatusDocumentoExterno['Cancelado'],
          motivoCancelamento,
        },
      });

      // Auditoria
      await tx.documentos_externos_audit.create({
        data: {
          idDocumentoExterno,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAssinaturaExterna.CANCELAR_ASSINATURA_EXTERNA,
          ip,
        },
      });
    });

    revalidatePath('/assinatura/usuarios-externos');

    return {
      data: true,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.ASSINATURA_EXTERNA,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao cancelar documento externo.',
    };
  }
};
