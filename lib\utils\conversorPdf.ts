'use server';

import { URL_SERVER_IMPRESSAO, CHAVE_API } from '@/lib/consts';
import { createClient } from '@/lib/supabase/server';
import { siteConfig } from '@/config/site';

/**
 * Converte template HTML para PDF usando o sistema de impressão existente
 */
export const converterTemplatePdf = async (
  idTemplate: number,
  variaveis: Record<string, string>,
  _nomeArquivo: string
) => {
  try {
    const supabase = await createClient();
    const session = await supabase.auth.getSession();

    if (!session.data.session) {
      return {
        error: 'Sessão não encontrada.',
      };
    }

    // Construir URL da página de impressão com variáveis como query params
    const searchParams = new URLSearchParams();
    Object.entries(variaveis).forEach(([chave, valor]) => {
      const nomeParam = chave.replace(/[{}]/g, '').toLowerCase();
      searchParams.append(`var_${nomeParam}`, valor);
    });

    const urlPaginaImpressao = `/assinatura/template/imprimir/${idTemplate}?${searchParams.toString()}`;

    // Usar o sistema de impressão existente do projeto
    const response = await fetch(URL_SERVER_IMPRESSAO, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${session.data.session.access_token}`,
        url: siteConfig.url + urlPaginaImpressao,
        'Content-Type': 'application/json',
        chave: CHAVE_API,
      },
      body: JSON.stringify({
        landscape: false, // A4 portrait para documentos
      }),
    });

    if (!response.ok) {
      throw new Error('Erro na conversão para PDF');
    }

    const pdfBlob = await response.blob();

    return {
      data: pdfBlob,
    };
  } catch (e) {
    console.log(e);
    return {
      error: 'Erro ao converter HTML para PDF.',
    };
  }
};

/**
 * Gera preview de template em PDF
 */
export const gerarPreviewTemplate = async (
  idTemplate: number,
  variaveis: Record<string, string> = {}
) => {
  // Adicionar variáveis padrão para preview
  const variaveisPreview = {
    '{{NOME_USUARIO}}': 'João da Silva',
    '{{NUMERO_DOCUMENTO}}': '001/2024',
    ...variaveis,
  };

  return await converterTemplatePdf(
    idTemplate,
    variaveisPreview,
    `preview-template-${idTemplate}.pdf`
  );
};
