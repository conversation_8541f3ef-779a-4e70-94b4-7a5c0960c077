'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ChevronsUpDown, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { listarUsuariosExternos } from '@/lib/database/gerenciamento/usuariosExternos';

interface ComboboxSelecionarUsuarioExternoProps {
  value?: number;
  onChange: (value: number) => void;
}

export function ComboboxSelecionarUsuarioExterno({
  value,
  onChange,
}: ComboboxSelecionarUsuarioExternoProps) {
  const [open, setOpen] = useState(false);
  const [usuarios, setUsuarios] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const carregarUsuarios = async () => {
      try {
        const resultado = await listarUsuariosExternos();
        if (resultado.data) {
          // Filtrar apenas usuários ativos
          const usuariosAtivos = resultado.data.filter(
            (usuario: any) => usuario.ativo
          );
          setUsuarios(usuariosAtivos);
        }
      } catch (error) {
        console.error('Erro ao carregar usuários externos:', error);
      } finally {
        setLoading(false);
      }
    };

    carregarUsuarios();
  }, []);

  const usuarioSelecionado = usuarios.find((usuario) => usuario.id === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
          disabled={loading}
        >
          {loading
            ? 'Carregando...'
            : usuarioSelecionado
              ? `${usuarioSelecionado.nome} (${usuarioSelecionado.email})`
              : 'Selecione um usuário externo...'}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-full p-0'>
        <Command>
          <CommandInput placeholder='Buscar usuário externo...' />
          <CommandEmpty>Usuário externo não encontrado.</CommandEmpty>
          <CommandList>
            {usuarios.map((usuario) => (
              <CommandItem
                key={usuario.id}
                value={`${usuario.nome} ${usuario.email}`}
                onSelect={() => {
                  onChange(usuario.id);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    value === usuario.id ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className='flex flex-col'>
                  <span className='font-medium'>{usuario.nome}</span>
                  <span className='text-muted-foreground text-sm'>
                    {usuario.email}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
