import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { CriarTemplateForm } from '@/components/gerenciamento/templatesDocumentos/criarTemplateForm';

export default function NovoTemplatePage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Criar Novo Template de Documento</PageTitle>
      </PageHeader>
      <PageContent>
        <CriarTemplateForm />
      </PageContent>
    </PageWrapper>
  );
}
