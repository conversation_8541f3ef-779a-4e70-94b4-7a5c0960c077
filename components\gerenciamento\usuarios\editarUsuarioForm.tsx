'use client';
import { But<PERSON> } from '@/components/ui/button';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save } from 'lucide-react';
import { listarCargosAtivos } from '@/lib/database/gerenciamento/usuarios';
import { ConfigCargo } from '@/types/app';
import { editarUsuarioSchema, usuarioFormSchema } from '@/lib/validation';
import { ComboboxSelecionarCargo } from './comboboxSelecionarCargo';
import ConfigCargosDatatable from './configCargosDatatable';
import {
  alterarCargosUsuario,
  obterUsuarioECargos,
} from '@/lib/database/gerenciamento/usuarios';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { Input } from '@/components/ui/input';

export default function EditarUsuarioForm({
  cargos,
  usuario,
}: {
  cargos: Awaited<ReturnType<typeof listarCargosAtivos>>;
  usuario: Awaited<ReturnType<typeof obterUsuarioECargos>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);

  const cargosUsuario = cargos.data!.filter((cargo) => {
    return (
      usuario.data!.user_profiles_cargos.filter(
        (cargoUsuario) => cargoUsuario.cargoId === cargo.id
      ).length > 0
    );
  });

  const [cargosConfigurados, setCargosConfigurados] = useState<ConfigCargo[]>(
    cargosUsuario.map((cargo) => {
      return {
        id: cargo.id,
        nome: cargo.nome,
      };
    })
  );

  const form = useForm<z.infer<typeof usuarioFormSchema>>({
    resolver: zodResolver(usuarioFormSchema),
    defaultValues: {
      nome: usuario.data!.nome,
      email: usuario.data!.email,
    },
  });

  const onSubmit = async (values: z.infer<typeof usuarioFormSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof editarUsuarioSchema> = {
        nome: values.nome,
        id: usuario.data!.id,
        cargos: cargosConfigurados.map((cargo) => {
          return Number(cargo.id);
        }),
      };
      const res = await alterarCargosUsuario(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Usuario alterado.');
        router.push('/gerenciamento/usuarios');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='grid gap-4 py-8'>
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Nome Completo
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={2}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <span className='mt-4 text-lg'>Cargos</span>
          <ComboboxSelecionarCargo
            cargos={cargos}
            cargosConfigurados={cargosConfigurados}
            setCargosConfigurados={setCargosConfigurados}
          />
          <ConfigCargosDatatable
            data={cargosConfigurados}
            setCargosConfigurados={setCargosConfigurados}
          />
        </div>
        <div className='mt-12 flex justify-between'>
          <Button
            variant={'destructive'}
            disabled={loading}
            onClick={(e) => {
              e.preventDefault();
              router.push('/gerenciamento/usuarios');
            }}
          >
            <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
          </Button>
          <Button
            type='submit'
            disabled={loading || cargosConfigurados.length === 0}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <Save className='mr-2 h-4 w-4' /> Salvar Usuário
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
