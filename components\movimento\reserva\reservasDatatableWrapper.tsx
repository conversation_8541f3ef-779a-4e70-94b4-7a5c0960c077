import { ErrorAlert } from '@/components/error-alert';
import ReservasDatatable from './reservasDataTable';
import { listarReservas } from '@/lib/database/movimento/reservas';

export default async function ReservasDatatableWrapper() {
  const result = await listarReservas();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Reservas não encontradas.'} />;

  return <ReservasDatatable data={result} />;
}
