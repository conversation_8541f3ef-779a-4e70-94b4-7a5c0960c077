import { ErrorAlert } from '@/components/error-alert';
import EconomicasDatatable from './economicasDataTable';
import { listarEconomicas } from '@/lib/database/gerenciamento/economicas';

export default async function EconomicasDatatableWrapper() {
  const result = await listarEconomicas();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Econômicas não encontradas.'} />;

  return <EconomicasDatatable data={result} />;
}
