'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { StatusProtocolo } from '@/lib/enums';
import { toast } from 'sonner';

interface DialogoAlterarStatusLoteProps {
  isOpen: boolean;
  onClose: () => void;
  selectedIds: number[];
  onConfirm: (ids: number[], novoStatus: number) => Promise<void>;
  currentStatuses?: { [id: number]: number }; // Optional: Current status of selected protocols
}

export function DialogoAlterarStatusLote({
  isOpen,
  onClose,
  selectedIds,
  onConfirm,
  currentStatuses = {},
}: DialogoAlterarStatusLoteProps) {
  const [novoStatus, setNovoStatus] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);

  // Define valid status transitions for safer bulk operations
  const _validTransitions = useMemo(
    (): Record<number, number[]> => ({
      [StatusProtocolo.Nenhum]: [
        StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
        StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
        StatusProtocolo.CONTROLE_INTERNO,
      ],
      [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS]: [
        StatusProtocolo.RECEBIMENTO_EM_COMPRAS,
        StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS,
      ],
      [StatusProtocolo.RECEBIMENTO_EM_COMPRAS]: [
        StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS,
        StatusProtocolo.AF_ENCAMINHADA,
        StatusProtocolo.ENTRADANOCOMPRAS,
      ],
      [StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS]: [
        StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
        StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
      ],
      [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE]: [
        StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE,
        StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS,
      ],
      [StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE]: [
        StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS,
        StatusProtocolo.CONTROLE_INTERNO,
      ],
      [StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS]: [
        StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
        StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
      ],
      [StatusProtocolo.AF_ENCAMINHADA]: [
        StatusProtocolo.RECEBIMENTO_EM_COMPRAS,
        StatusProtocolo.CONTROLE_INTERNO,
      ],
      [StatusProtocolo.CONTROLE_INTERNO]: [
        StatusProtocolo.ENVIADODEFINANCASPARACONTROLEINTERNO,
        StatusProtocolo.ENCAMINHADOSECRETARIODEFINANCAS,
      ],
    }),
    []
  );

  // Get unique current statuses from selected protocols
  const _uniqueCurrentStatuses = useMemo(() => {
    return Object.values(currentStatuses).filter(
      (status, index, self) => self.indexOf(status) === index
    );
  }, [currentStatuses]);

  const handleConfirm = async () => {
    if (!novoStatus) {
      toast.error('Selecione um novo status');
      return;
    }

    setIsLoading(true);
    try {
      await onConfirm(selectedIds, novoStatus);
      toast.success(`Status alterado para ${selectedIds.length} protocolo(s)`);
      onClose();
    } catch (error) {
      toast.error('Erro ao alterar status dos protocolos');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Alterar Status em Lote</DialogTitle>
        </DialogHeader>
        <div className='space-y-4'>
          <div className='space-y-2'>
            <p className='text-sm text-gray-600'>
              Você está alterando o status de{' '}
              <strong>{selectedIds.length}</strong> protocolo(s).
            </p>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Novo Status</label>
              <Select
                value={novoStatus.toString()}
                onValueChange={(value) => setNovoStatus(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Selecione o novo status' />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(StatusProtocolo)
                    .filter(([key]) => isNaN(Number(key)))
                    .map(([key, value]) => (
                      <SelectItem key={value} value={value.toString()}>
                        {key.replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className='flex justify-end gap-2'>
            <Button variant='outline' onClick={onClose} disabled={isLoading}>
              Cancelar
            </Button>
            <Button onClick={handleConfirm} disabled={isLoading || !novoStatus}>
              {isLoading ? 'Alterando...' : 'Confirmar'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
