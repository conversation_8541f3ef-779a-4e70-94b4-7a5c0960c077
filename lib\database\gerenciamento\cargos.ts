'use server';

import { revalidatePath } from 'next/cache';
import { prisma } from '../../prisma';
import {
  auditoriaErroSchema,
  cargoSchema,
  editarCargoSchema,
  idSchema,
  permissaoSchema,
} from '../../validation';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import {
  AuditoriaGerenciamentoCargos,
  Permissoes,
  TiposAcesso,
} from '../../enums';
import { Modulos } from '../../modulos';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { inserirErroAudit } from '../auditoria/erro';
import z from 'zod';

export const listarCargos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const cargos = await prisma.cargos.findMany({ orderBy: { nome: 'asc' } });
    return {
      data: cargos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter cargos.`,
    };
  }
};

export const obterPermissoesEAcessosCargo = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { id: cargoId } = parsedParams.data;

  try {
    const cargo = await prisma.cargos.findFirst({
      where: {
        id: cargoId,
      },
      include: {
        cargos_permissoes: true,
        cargos_acessos: {
          include: {
            secretaria: true,
            subdepartamento: {
              include: {
                departamento: {
                  include: {
                    secretaria: true,
                  },
                },
              },
            },
            departamento: {
              include: {
                secretaria: true,
              },
            },
          },
        },
      },
    });
    if (!cargo) {
      return {
        error: `Cargo não encontrado.`,
      };
    }
    return {
      data: cargo,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter cargo e permissões.`,
    };
  }
};

interface CargosAcessos {
  secretariaId: number | null;
  subdepartamentoId: number | null;
  departamentoId: number | null;
}

export const criarCargo = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = cargoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { nome, permissions, tipoAcesso, tipoAssinatura, acessos } =
    parsedParams.data;

  let acessosCargo: CargosAcessos[] = [];

  switch (tipoAcesso) {
    case TiposAcesso.SECRETARIAS:
      acessosCargo = acessos.map((id) => {
        return {
          secretariaId: id,
          departamentoId: null,
          subdepartamentoId: null,
        };
      });
      break;

    case TiposAcesso.DEPARTAMENTOS:
      acessosCargo = acessos.map((id) => {
        return {
          departamentoId: id,
          subdepartamentoId: null,
          secretariaId: null,
        };
      });
      break;

    case TiposAcesso.SUBDEPARTAMENTOS:
      acessosCargo = acessos.map((id) => {
        return {
          subdepartamentoId: id,
          secretariaId: null,
          departamentoId: null,
        };
      });
      break;
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const cargo = await tx.cargos.create({
        data: {
          nome: nome,
          cargos_permissoes: {
            create: permissions,
          },
          tipoAcesso: tipoAcesso,
          tipoAssinatura: tipoAssinatura,
          cargos_acessos: {
            create: acessosCargo,
          },
        },
      });
      const cargoAuditPromise = tx.gerenciamentoCargos_audit.create({
        data: {
          cargoId: cargo.id,
          acao: AuditoriaGerenciamentoCargos.CRIAR_CARGO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });
      const permissoesAuditPromise = tx.cargos_permissoes_audit.createMany({
        data: permissions.map((permissao) => {
          return {
            cargoId: cargo.id,
            read: permissao.read,
            moduloId: permissao.moduloId,
            write: permissao.write,
            delete: permissao.delete,
            update: permissao.update,
            usuarioId: resultPermissao.idUsuario!,
            ip,
          };
        }),
      });
      const acessosAuditPromise = tx.cargos_acessos_audit.createMany({
        data: acessosCargo.map((acesso) => {
          return {
            cargoId: cargo.id,
            secretariaId: acesso.secretariaId,
            subdepartamentoId: acesso.subdepartamentoId,
            departamentoId: acesso.departamentoId,
            usuarioId: resultPermissao.idUsuario!,
            ip,
          };
        }),
      });
      await Promise.all([
        cargoAuditPromise,
        permissoesAuditPromise,
        acessosAuditPromise,
      ]);
    });

    revalidatePath('/gerenciamento/cargos');
    return {
      data: true,
    };
  } catch (e: unknown) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Cargo já existe.`,
        };
      }
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar cargo.`,
    };
  }
};

export const editarCargo = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = editarCargoSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id, nome, permissions, tipoAcesso, tipoAssinatura, acessos } =
    parsedParams.data;

  let acessosCargo: CargosAcessos[] = [];

  switch (tipoAcesso) {
    case TiposAcesso.SECRETARIAS:
      acessosCargo = acessos.map((id) => {
        return {
          secretariaId: id,
          departamentoId: null,
          subdepartamentoId: null,
        };
      });
      break;

    case TiposAcesso.DEPARTAMENTOS:
      acessosCargo = acessos.map((id) => {
        return {
          departamentoId: id,
          subdepartamentoId: null,
          secretariaId: null,
        };
      });
      break;

    case TiposAcesso.SUBDEPARTAMENTOS:
      acessosCargo = acessos.map((id) => {
        return {
          subdepartamentoId: id,
          secretariaId: null,
          departamentoId: null,
        };
      });
      break;
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const deletarPermissoesPromise = tx.cargos_permissoes.deleteMany({
        where: {
          cargoId: id,
        },
      });
      const deletarAcessosPromise = tx.cargos_acessos.deleteMany({
        where: {
          cargoId: id,
        },
      });

      const valoresAntigosPromise = tx.cargos.findUniqueOrThrow({
        where: {
          id: id,
        },
        select: {
          nome: true,
          tipoAcesso: true,
          tipoAssinatura: true,
        },
      });

      await Promise.all([deletarPermissoesPromise, deletarAcessosPromise]);

      const valoresAntigos = await valoresAntigosPromise;

      const editarCargoPromise = tx.cargos.update({
        where: {
          id,
        },
        data: {
          nome: nome,
          cargos_permissoes: {
            create: permissions,
          },
          tipoAcesso: tipoAcesso,
          tipoAssinatura: tipoAssinatura,
          cargos_acessos: {
            create: acessosCargo,
          },
        },
      });

      const auditRenomearPromise =
        valoresAntigos?.nome !== nome
          ? tx.gerenciamentoCargos_audit.create({
              data: {
                cargoId: id,
                acao: AuditoriaGerenciamentoCargos.RENOMEAR_CARGO,
                de: valoresAntigos?.nome,
                para: nome,
                usuarioId: resultPermissao.idUsuario!,
                ip,
              },
            })
          : null;

      const auditAlterarTipoAcessoPromise =
        valoresAntigos?.tipoAcesso !== tipoAcesso
          ? tx.gerenciamentoCargos_audit.create({
              data: {
                cargoId: id,
                acao: AuditoriaGerenciamentoCargos.ALTERAR_TIPO_ACESSO,
                de: valoresAntigos?.tipoAcesso.toString(),
                para: tipoAcesso.toString(),
                usuarioId: resultPermissao.idUsuario!,
                ip,
              },
            })
          : null;

      const auditAlterarTipoAssinaturaPromise =
        valoresAntigos?.tipoAssinatura !== tipoAssinatura
          ? tx.gerenciamentoCargos_audit.create({
              data: {
                cargoId: id,
                acao: AuditoriaGerenciamentoCargos.ALTERAR_TIPO_ASSINATURA,
                de: valoresAntigos?.tipoAssinatura.toString(),
                para: tipoAssinatura.toString(),
                usuarioId: resultPermissao.idUsuario!,
                ip,
              },
            })
          : null;

      const auditPromise = tx.gerenciamentoCargos_audit.create({
        data: {
          cargoId: id,
          acao: AuditoriaGerenciamentoCargos.ALTERAR_PERMISSOES_ACESSOS,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      const permissoesAuditPromise = tx.cargos_permissoes_audit.createMany({
        data: permissions.map((permissao) => {
          return {
            cargoId: id,
            moduloId: permissao.moduloId,
            read: permissao.read,
            write: permissao.write,
            delete: permissao.delete,
            update: permissao.update,
            usuarioId: resultPermissao.idUsuario!,
            ip,
          };
        }),
      });

      const acessosAuditPromise = tx.cargos_acessos_audit.createMany({
        data: acessosCargo.map((acesso) => {
          return {
            cargoId: id,
            secretariaId: acesso.secretariaId,
            subdepartamentoId: acesso.subdepartamentoId,
            departamentoId: acesso.departamentoId,
            usuarioId: resultPermissao.idUsuario!,
            ip,
          };
        }),
      });

      await Promise.all([
        editarCargoPromise,
        auditRenomearPromise,
        auditAlterarTipoAcessoPromise,
        auditAlterarTipoAssinaturaPromise,
        auditPromise,
        permissoesAuditPromise,
        acessosAuditPromise,
      ]);
    });

    revalidatePath('/gerenciamento/cargos');
    return {
      data: true,
    };
  } catch (e: unknown) {
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao editar cargo.`,
    };
  }
};

export const desativarCargo = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const atualizarCargoPromise = tx.cargos.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });

      const auditPromise = tx.gerenciamentoCargos_audit.create({
        data: {
          cargoId: id,
          acao: AuditoriaGerenciamentoCargos.DESATIVAR_CARGO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([atualizarCargoPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/cargos');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar cargo.`,
    };
  }
};

export const ativarCargo = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const atualizarCargoPromise = tx.cargos.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.gerenciamentoCargos_audit.create({
        data: {
          cargoId: id,
          acao: AuditoriaGerenciamentoCargos.ATIVAR_CARGO,
          usuarioId: result.idUsuario!,
          ip,
        },
      });

      await Promise.all([atualizarCargoPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/cargos');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar cargo.`,
    };
  }
};

export const listarDepartamentosAtivos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const departamentos = await prisma.departamentos.findMany({
      orderBy: [{ secretaria: { codigo: 'asc' } }, { codigo: 'asc' }],
      where: { ativo: true },
      include: {
        secretaria: {
          select: {
            codigo: true,
          },
        },
      },
    });
    return {
      data: departamentos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter departamentos.`,
    };
  }
};

export const listarSecretariasAtivas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const secretarias = await prisma.secretarias.findMany({
      where: { ativo: true },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: secretarias,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter secretarias.`,
    };
  }
};

export const listarSubdepartamentosAtivos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const subdepartamentos = await prisma.subdepartamentos.findMany({
      where: { ativo: true },
      orderBy: [
        { departamento: { secretaria: { codigo: 'asc' } } },
        { departamento: { codigo: 'asc' } },
        { codigo: 'asc' },
      ],
      include: {
        departamento: {
          select: {
            codigo: true,
            secretaria: {
              select: {
                codigo: true,
              },
            },
          },
        },
      },
    });
    return {
      data: subdepartamentos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter subdepartamentos.`,
    };
  }
};
