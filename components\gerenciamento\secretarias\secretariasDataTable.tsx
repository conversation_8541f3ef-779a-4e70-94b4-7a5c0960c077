'use client';
import { ColumnDef } from '@tanstack/react-table';
import { Network } from 'lucide-react';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { listarSecretarias } from '@/lib/database/gerenciamento/secretarias';
import { codigoSecretariaMask } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import { DialogRenomearSecretaria } from './dialogRenomearSecretaria';
import { AlertDesativarSecretaria } from './alertDesativarSecretaria';
import { AlertAtivarSecretaria } from './alertAtivarSecretaria';

export default function SecretariasDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarSecretarias>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Código',
      filterFn: 'includesString',
      cell: ({ row }) =>
        `${codigoSecretariaMask(row.getValue('codigo'))}.00.00`,
    },
    {
      accessorKey: 'nome',
      header: 'Secretaria',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link
            href={`/gerenciamento/secretarias/${row.original.id}/departamentos`}
          >
            <Button type='button' variant='secondary'>
              <Network className='mr-2 size-4' /> Departamentos
            </Button>
          </Link>
          <Separator orientation='vertical' />
          <DialogRenomearSecretaria
            idSecretaria={row.original.id}
            nomeSecretaria={row.original.nome}
            codigoSecretaria={row.original.codigo}
          />
          {row.original.ativo ? (
            <AlertDesativarSecretaria
              idSecretaria={row.original.id}
              nomeSecretaria={row.original.nome}
              codigoSecretaria={row.original.codigo}
            />
          ) : (
            <AlertAtivarSecretaria
              idSecretaria={row.original.id}
              nomeSecretaria={row.original.nome}
              codigoSecretaria={row.original.codigo}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <>
      <div className='container mx-auto'>
        <ReusableDatatable columns={columns} data={data.data} />
      </div>
    </>
  );
}
