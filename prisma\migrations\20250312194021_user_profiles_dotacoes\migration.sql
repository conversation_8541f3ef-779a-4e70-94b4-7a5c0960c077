-- CreateTable
CREATE TABLE "user_profiles_dotacoes" (
    "id" SERIAL NOT NULL,
    "uuidUsuario" UUID NOT NULL,
    "dotacoesIds" INTEGER[] DEFAULT ARRAY[]::INTEGER[],

    CONSTRAINT "user_profiles_dotacoes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_profiles_dotacoes_uuidUsuario_key" ON "user_profiles_dotacoes"("uuidUsuario");

-- AddForeignKey
ALTER TABLE "user_profiles_dotacoes" ADD CONSTRAINT "user_profiles_dotacoes_uuidUsuario_fkey" FOREIGN KEY ("uuidUsuario") REFERENCES "user_profiles"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION;
