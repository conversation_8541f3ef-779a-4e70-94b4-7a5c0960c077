'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { StatusProtocolo } from '@/lib/enums';

interface AlterarStatusFormProps {
  currentStatus: number;
  onSubmit: (data: { novoStatus: number; obs: string }) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const TRANSICOES_STATUS_VALIDAS: Record<number, number[]> = {
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS]: [
    StatusProtocolo.RECEBIMENTO_EM_COMPRAS,
    StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_COMPRAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
    StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE]: [
    StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE,
    StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE,
    StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE]: [
    StatusProtocolo.RECEBIMENTO_EM_GABINETE,
    StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_GABINETE]: [
    StatusProtocolo.AF_ENCAMINHADA,
    StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS,
  ],
  [StatusProtocolo.AF_ENCAMINHADA]: [
    StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM,
  ],
  [StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
  ],
  [StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
  ],
  [StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE,
  ],
  [StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM]: [
    StatusProtocolo.AF_ENCAMINHADA,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_SAUDE]: [
    StatusProtocolo.RECEBIMENTO_EM_SAUDE,
    StatusProtocolo.DEVOLUCAO_DE_SAUDE_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_SAUDE]: [
    StatusProtocolo.DEVOLUCAO_DE_SAUDE_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_ESPORTES]: [
    StatusProtocolo.RECEBIMENTO_EM_ESPORTES,
    StatusProtocolo.DEVOLUCAO_DE_ESPORTES_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_ESPORTES]: [
    StatusProtocolo.DEVOLUCAO_DE_ESPORTES_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_EDUCACAO]: [
    StatusProtocolo.RECEBIMENTO_EM_EDUCACAO,
    StatusProtocolo.DEVOLUCAO_DE_EDUCACAO_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_EDUCACAO]: [
    StatusProtocolo.DEVOLUCAO_DE_EDUCACAO_PARA_FINANCAS,
  ],
};

export function AlterarStatusForm({
  currentStatus,
  onSubmit,
  onCancel,
  isLoading = false,
}: AlterarStatusFormProps) {
  const [novoStatus, setNovoStatus] = useState<number | undefined>();
  const [obs, setObs] = useState<string>('');

  const statusPermitidos = TRANSICOES_STATUS_VALIDAS[currentStatus] || [];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (novoStatus !== undefined) {
      onSubmit({ novoStatus, obs });
    }
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-4'>
      <div>
        <label htmlFor='novoStatus' className='mb-1 block text-sm font-medium'>
          Novo Status *
        </label>
        <Select
          value={novoStatus?.toString() || ''}
          onValueChange={(value) =>
            setNovoStatus(value ? parseInt(value) : undefined)
          }
          required
        >
          <SelectTrigger>
            <SelectValue placeholder='Selecione um status' />
          </SelectTrigger>
          <SelectContent>
            {statusPermitidos.map((status) => (
              <SelectItem key={status} value={status.toString()}>
                {StatusProtocolo[status]?.replace(/_/g, ' ')}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <label htmlFor='obs' className='mb-1 block text-sm font-medium'>
          Observações
        </label>
        <Textarea
          id='obs'
          value={obs}
          onChange={(e) => setObs(e.target.value)}
          placeholder='Observações sobre a mudança de status (opcional)'
          rows={3}
        />
      </div>

      <div className='flex gap-2 pt-4'>
        <Button type='submit' disabled={isLoading || novoStatus === undefined}>
          {isLoading ? 'Alterando...' : 'Alterar Status'}
        </Button>
        <Button type='button' variant='outline' onClick={onCancel}>
          Cancelar
        </Button>
      </div>
    </form>
  );
}
