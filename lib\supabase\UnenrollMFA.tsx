'use client';
import { useState } from 'react';
import { unenrollMFA, validateToptp } from '@/lib/supabase/actions';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { mfaSchema } from '../validation';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { AlertDialogFooter } from '@/components/ui/alert-dialog';
import { Icons } from '@/components/icons';

/**
 * UnenrollMFA shows a simple table with the list of factors together with a button to unenroll.
 * When a user types in the factorId of the factor that they wish to unenroll and clicks unenroll
 * the corresponding factor will be unenrolled.
 */
export function UnenrollMFA() {
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const mfaForm = useForm<z.infer<typeof mfaSchema>>({
    resolver: zodResolver(mfaSchema),
  });
  const desativar = async (values: z.infer<typeof mfaSchema>) => {
    try {
      setLoading(true);
      const res = await validateToptp(`${values.token}`);
      if (res?.error || !res.data) {
        setLoading(false);
        if (res.error.status === 400) {
          toast.error('Token inválido.');
        } else {
          toast.error('Algo deu errado, por favor tente novamente mais tarde.');
        }
        return;
      }
      await unenrollMFA();
      toast.success('Autenticação Multifator Desativada.');
      router.push('/dashboard');
    } catch (e) {
      console.log(e);
      setLoading(false);
      setError('Erro, tente novamente mais tarde');
    }
  };

  return (
    <>
      {error && <div className='error'>{error}</div>}
      <Form {...mfaForm}>
        <form onSubmit={mfaForm.handleSubmit(desativar)}>
          <div className='grid gap-8 py-4'>
            <FormField
              control={mfaForm.control}
              name='token'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Código</FormLabel>
                  <FormControl>
                    <Input className='col-span-3' {...field} type='number' />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <AlertDialogFooter>
            <Button type='submit' disabled={loading}>
              {loading ? (
                <>
                  <Icons.loader className='mr-2 h-4 w-4 animate-spin' />{' '}
                  Aguarde...
                </>
              ) : (
                <>
                  <Icons.logIn className='mr-2 h-4 w-4' /> Desativar
                  Autenticação Multifator
                </>
              )}
            </Button>
          </AlertDialogFooter>
        </form>
      </Form>
    </>
  );
}
