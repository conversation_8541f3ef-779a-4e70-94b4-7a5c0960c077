'use client';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Eye, Pencil, XCircle, CheckCircle, RotateCcw } from 'lucide-react';
import { listarAFs } from '@/lib/database/movimento/afs';
import { StatusAF, StatusAFDesc } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import {
  cancelarAF,
  marcarAFComoUtilizada,
  reativarAF,
} from '@/lib/database/movimento/afs';
import { toast } from 'sonner';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ReusableDatatableFiltroNumeroDespesaSecretariaDepartamento } from '../datatable/reusableDatatableFiltroNumeroDespesaSecretariaDepartamento';
import {
  DatatableActionsDropdown,
  DatatableAction,
} from '@/components/datatable/DatatableActionsDropdown';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export default function AfsDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarAFs>>;
}) {
  const [isProcessing, setIsProcessing] = useState<number | null>(null);
  const router = useRouter();

  if (!data.data?.afs) return null;

  const handleMarcarComoUtilizada = async (afId: number) => {
    setIsProcessing(afId);
    try {
      const result = await marcarAFComoUtilizada({
        id: afId,
        observacao: 'AF marcada como utilizada via tabela',
      });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF marcada como utilizada com sucesso!');
        router.refresh();
      }
    } catch (error) {
      toast.error('Erro ao marcar AF como utilizada');
    } finally {
      setIsProcessing(null);
    }
  };

  const handleCancelarAF = async (afId: number) => {
    setIsProcessing(afId);
    try {
      const result = await cancelarAF({
        id: afId,
        motivo: 'AF cancelada via tabela de listagem',
      });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF cancelada com sucesso!');
        router.refresh();
      }
    } catch (error) {
      toast.error('Erro ao cancelar AF');
    } finally {
      setIsProcessing(null);
    }
  };

  const handleReativarAF = async (afId: number) => {
    setIsProcessing(afId);
    try {
      const result = await reativarAF({
        id: afId,
        motivo: 'AF reativada via tabela de listagem',
      });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF reativada com sucesso!');
        router.refresh();
      }
    } catch (error) {
      toast.error('Erro ao reativar AF');
    } finally {
      setIsProcessing(null);
    }
  };

  const columns: ColumnDef<(typeof data.data.afs)[0]>[] = [
    {
      accessorKey: 'numero',
      header: 'Número AF',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'empenho.id',
      header: 'Empenho',
      filterFn: 'includesString',
      cell: ({ row }) => {
        const empenho = row.original.empenho;
        return (
          <Link
            href={`/movimento/empenhos/visualizar/${empenho.id}`}
            className='text-blue-600 hover:underline'
          >
            #{empenho.id}
          </Link>
        );
      },
    },
    {
      accessorKey: 'empenho.dotacao.despesa',
      id: 'despesa',
      header: 'Despesa',
      filterFn: 'includesString',
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='text-xs font-medium'>
            {row.original.empenho.dotacao.despesa}
          </span>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className='text-muted-foreground max-w-[150px] cursor-help truncate text-xs'>
                {row.original.empenho.dotacao.desc}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className='max-w-xs'>{row.original.empenho.dotacao.desc}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      ),
    },
    {
      accessorKey: 'empenho.fornecedor.nome',
      header: 'Fornecedor',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'resumo',
      header: 'Resumo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'obs',
      header: 'Obs',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      filterFn: 'includesString',
      cell: ({ row }) => {
        const afStatus = row.original.status;
        return StatusAFDesc[afStatus as StatusAF];
      },
    },
    {
      accessorKey: 'valorTotal',
      header: 'Valor Total',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'saldo',
      header: 'Saldo',
      cell: ({ row }) => {
        const af = row.original;
        const saldo =
          af.saldo ??
          currency(af.valorTotal, currencyOptionsNoSymbol).subtract(
            currency(af.valorUtilizado ?? 0, currencyOptionsNoSymbol)
          ).value;
        return currency(saldo, currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'data',
      header: 'Data',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        const date = getValue<Date>();
        return date.toLocaleDateString('pt-BR');
      },
    },
    {
      accessorKey: 'dataEmissao',
      header: 'Data Emissão',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        const date = getValue<Date | null>();
        return date ? date.toLocaleDateString('pt-BR') : '-';
      },
    },
    {
      accessorKey: 'dataVencimento',
      header: 'Data Vencimento',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        const date = getValue<Date | null>();
        return date ? date.toLocaleDateString('pt-BR') : '-';
      },
    },
    {
      accessorKey: 'dataUtilizacao',
      header: 'Data Utilização',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        const date = getValue<Date | null>();
        return date ? date.toLocaleDateString('pt-BR') : '-';
      },
    },
    {
      accessorKey: 'dataCancelamento',
      header: 'Data Cancelamento',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        const date = getValue<Date | null>();
        return date ? date.toLocaleDateString('pt-BR') : '-';
      },
    },
    {
      accessorKey: 'dataReativacao',
      header: 'Data Reativação',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        const date = getValue<Date | null>();
        return date ? date.toLocaleDateString('pt-BR') : '-';
      },
    },
    {
      accessorKey: 'secretariaId',
      header: 'Secretaria',
      filterFn: 'equals',
    },
    {
      accessorKey: 'departamentoId',
      header: 'Departamento',
      filterFn: 'equals',
    },
    {
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => {
        const actions: DatatableAction[] = [
          {
            label: 'Editar',
            icon: <Pencil className='size-4' />,
            href: `/movimento/af/editar/${row.original.id}`,
            show: row.original.status === StatusAF.ATIVA,
          },
          {
            label: 'Marcar Utilizada',
            icon: <CheckCircle className='size-4' />,
            onClick: () => handleMarcarComoUtilizada(row.original.id),
            disabled: isProcessing === row.original.id,
            loading: isProcessing === row.original.id,
            show: row.original.status === StatusAF.ATIVA,
          },
          {
            label: 'Cancelar',
            icon: <XCircle className='size-4' />,
            onClick: () => handleCancelarAF(row.original.id),
            disabled: isProcessing === row.original.id,
            loading: isProcessing === row.original.id,
            variant: 'destructive',
            show: row.original.status === StatusAF.ATIVA,
          },
          {
            label: 'Reativar',
            icon: <RotateCcw className='size-4' />,
            onClick: () => handleReativarAF(row.original.id),
            disabled: isProcessing === row.original.id,
            loading: isProcessing === row.original.id,
            show: row.original.status === StatusAF.CANCELADA,
          },
        ];

        return (
          <div className='flex items-center gap-2'>
            <Link href={`/movimento/af/visualizar/${row.original.id}`}>
              <Button variant='ghost' size='sm'>
                <Eye className='size-4' />
              </Button>
            </Link>
            <DatatableActionsDropdown actions={actions} />
          </div>
        );
      },
    },
  ];

  return (
    <TooltipProvider>
      <div className='container mx-auto'>
        <ReusableDatatableFiltroNumeroDespesaSecretariaDepartamento
          columns={columns}
          data={data.data.afs}
          secretarias={data.data.secretarias}
          departamentos={data.data.departamentos}
        />
      </div>
    </TooltipProvider>
  );
}
