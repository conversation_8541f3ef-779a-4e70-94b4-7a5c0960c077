/*
  Warnings:

  - You are about to drop the column `name` on the `cargos` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `user_profiles` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[nome]` on the table `cargos` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `nome` to the `cargos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `nome` to the `user_profiles` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "cargos_name_key";

-- AlterTable
ALTER TABLE "cargos" DROP COLUMN "name",
ADD COLUMN     "nome" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "user_profiles" DROP COLUMN "name",
ADD COLUMN     "nome" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "cargos_nome_key" ON "cargos"("nome");
