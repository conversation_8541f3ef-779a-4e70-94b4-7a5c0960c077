'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn, montarCodigoDepartamento } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { listarReservasParaProtocolo } from '@/lib/database/movimento/protocolos';

interface ComboboxSelecionarReservaProps {
  reservaId: number;
  setReservaId: (id: number) => void;
  filtrarSomenteAssinadas?: boolean;
}

export function ComboboxSelecionarReserva({
  reservaId,
  setReservaId,
  filtrarSomenteAssinadas = false,
}: ComboboxSelecionarReservaProps) {
  const [open, setOpen] = React.useState(false);
  const [reservas, setReservas] = React.useState<
    | Required<
        Awaited<ReturnType<typeof listarReservasParaProtocolo>>
      >['data']['reservas']
    | []
  >([]);
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    const carregarReservas = async () => {
      setLoading(true);
      try {
        const result = await listarReservasParaProtocolo();
        if (result.data && result.data.reservas) {
          setReservas(result.data.reservas);
        }
      } catch (error) {
        console.error('Erro ao carregar reservas:', error);
      } finally {
        setLoading(false);
      }
    };

    carregarReservas();
  }, [filtrarSomenteAssinadas]);

  const reservaSelecionada = reservas.find(
    (reserva) => reserva.id === reservaId
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
          disabled={loading}
        >
          {loading
            ? 'Carregando...'
            : reservaSelecionada
              ? `${reservaSelecionada.id} - ${reservaSelecionada.resumo} (${currency(reservaSelecionada.usarTotal, currencyOptionsNoSymbol).format().replace(/0$/, '')})`
              : 'Selecione uma reserva...'}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[400px] p-0'>
        <Command>
          <CommandInput placeholder='Buscar reserva...' />
          <CommandList>
            <CommandEmpty>Nenhuma reserva encontrada.</CommandEmpty>
            {reservas.map((reserva) => (
              <CommandItem
                key={reserva.id}
                onSelect={() => {
                  setReservaId(reserva.id);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    reserva.id === reservaId ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className='flex flex-col'>
                  <span className='font-medium'>
                    Reserva {reserva.id} - {reserva.resumo}
                  </span>
                  <span className='text-sm text-gray-500'>
                    Departamento:{' '}
                    {montarCodigoDepartamento(
                      reserva.secretariaId || 0,
                      reserva.departamentoId || 0
                    )}
                  </span>
                  <span className='text-sm text-gray-500'>
                    Valor:{' '}
                    {reserva.usarTotal
                      ? currency(
                          Number(reserva.usarTotal),
                          currencyOptionsNoSymbol
                        )
                          .format()
                          .replace(/0$/, '')
                      : 'Valor não definido'}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
