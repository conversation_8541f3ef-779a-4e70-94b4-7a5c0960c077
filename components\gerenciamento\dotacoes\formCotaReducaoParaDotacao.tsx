'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save } from 'lucide-react';
import {
  cn,
  currencyOptions,
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  obterAnoAtual,
  obterMesAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { cotaReducaoSchema } from '@/lib/validation';
import {
  obterCotasPorId,
  reducaoParaDotacao,
} from '@/lib/database/gerenciamento/dotacoes';
import currency from 'currency.js';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
export default function FormCotaReducaoParaDotacao({
  dotacao,
}: {
  dotacao: Awaited<ReturnType<typeof obterCotasPorId>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);

  const {
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
  } = dotacao.data!;

  const [mes1BRL, setMes1BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes2BRL, setMes2BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes3BRL, setMes3BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes4BRL, setMes4BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes5BRL, setMes5BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes6BRL, setMes6BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes7BRL, setMes7BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes8BRL, setMes8BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes9BRL, setMes9BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes10BRL, setMes10BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes11BRL, setMes11BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes12BRL, setMes12BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );

  const form = useForm<z.infer<typeof cotaReducaoSchema>>({
    resolver: zodResolver(cotaReducaoSchema),
    defaultValues: {
      id: dotacao.data!.id,
      motivo: '',
      cotaMes1: 0,
      cotaMes2: 0,
      cotaMes3: 0,
      cotaMes4: 0,
      cotaMes5: 0,
      cotaMes6: 0,
      cotaMes7: 0,
      cotaMes8: 0,
      cotaMes9: 0,
      cotaMes10: 0,
      cotaMes11: 0,
      cotaMes12: 0,
    },
  });

  const total = currency(mes1BRL, currencyOptionsNoSymbol)
    .add(mes2BRL)
    .add(mes3BRL)
    .add(mes4BRL)
    .add(mes5BRL)
    .add(mes6BRL)
    .add(mes7BRL)
    .add(mes8BRL)
    .add(mes9BRL)
    .add(mes10BRL)
    .add(mes11BRL)
    .add(mes12BRL);

  const restante = currency(
    dotacao.data!.cotaReducao,
    currencyOptionsNoSymbol
  ).subtract(total);

  const onSubmit = async (values: z.infer<typeof cotaReducaoSchema>) => {
    try {
      setLoading(true);
      const res = await reducaoParaDotacao(values);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Cota de redução enviada para dotação.');
        router.push(
          `/gerenciamento/dotacoes/${dotacao.data!.id}/cotas-de-reducao`
        );
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };
  const anoAtual = obterAnoAtual();
  const mesAtual = dotacao.data!.exercicio < anoAtual ? 12 : obterMesAtual();

  const somaMes1 = currency(cotaMes1, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes1'))
    .format()
    .replace(/0$/, '');
  const somaMes2 = currency(cotaMes2, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes2'))
    .format()
    .replace(/0$/, '');
  const somaMes3 = currency(cotaMes3, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes3'))
    .format()
    .replace(/0$/, '');
  const somaMes4 = currency(cotaMes4, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes4'))
    .format()
    .replace(/0$/, '');
  const somaMes5 = currency(cotaMes5, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes5'))
    .format()
    .replace(/0$/, '');
  const somaMes6 = currency(cotaMes6, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes6'))
    .format()
    .replace(/0$/, '');
  const somaMes7 = currency(cotaMes7, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes7'))
    .format()
    .replace(/0$/, '');
  const somaMes8 = currency(cotaMes8, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes8'))
    .format()
    .replace(/0$/, '');
  const somaMes9 = currency(cotaMes9, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes9'))
    .format()
    .replace(/0$/, '');
  const somaMes10 = currency(cotaMes10, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes10'))
    .format()
    .replace(/0$/, '');
  const somaMes11 = currency(cotaMes11, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes11'))
    .format()
    .replace(/0$/, '');
  const somaMes12 = currency(cotaMes12, currencyOptionsNoSymbol)
    .add(form.getValues('cotaMes12'))
    .format()
    .replace(/0$/, '');

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='mt-8 flex flex-wrap justify-between gap-6 text-left'>
              <span>
                <Label>Valor Inicial:</Label>{' '}
                {currency(dotacao.data!.valorInicial, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Cota Redução Inicial:</Label>{' '}
                {currency(dotacao.data!.cotaReducaoInicial, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Cota Redução:</Label>{' '}
                {currency(dotacao.data!.cotaReducao, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Suplementação:</Label>{' '}
                {currency(dotacao.data!.suplementacao, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Anulação:</Label>{' '}
                {currency(dotacao.data!.anulacao, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span className='w-full'>
                <Label>Valor Disponível:</Label>{' '}
                {currency(dotacao.data!.valorAtual, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
            </div>
            <Separator className='my-12' />
            <span>Informe os valores que serão somados às cotas atuais:</span>
            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              <FormField
                name='janeiro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Janeiro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes1BRL}
                        onChange={(e) => {
                          setMes1BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes1',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 1}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='fevereiro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Fevereiro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes2BRL}
                        onChange={(e) => {
                          setMes2BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes2',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 2}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Março'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Março</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes3BRL}
                        onChange={(e) => {
                          setMes3BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes3',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 3}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Abril'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Abril</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes4BRL}
                        onChange={(e) => {
                          setMes4BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes4',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 4}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Maio'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Maio</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes5BRL}
                        onChange={(e) => {
                          setMes5BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes5',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 5}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Junho'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Junho</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes6BRL}
                        onChange={(e) => {
                          setMes6BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes6',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 6}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Julho'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Julho</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes7BRL}
                        onChange={(e) => {
                          setMes7BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes7',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 7}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Agosto'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Agosto</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes8BRL}
                        onChange={(e) => {
                          setMes8BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes8',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 8}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Setembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Setembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes9BRL}
                        onChange={(e) => {
                          setMes9BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes9',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 9}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Outubro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Outubro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes10BRL}
                        onChange={(e) => {
                          setMes10BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes10',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 10}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Novembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Novembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes11BRL}
                        onChange={(e) => {
                          setMes11BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes11',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 11}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Dezembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Dezembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes12BRL}
                        onChange={(e) => {
                          setMes12BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes12',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-12 flex flex-wrap justify-between gap-6'>
              <FormField
                name='total'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Total</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'text-right text-base disabled:opacity-100',
                          total.value === 0 ||
                            total.value > dotacao.data!.cotaReducao
                            ? 'text-red-600'
                            : 'text-green-600'
                        )}
                        value={total.format().replace(/0$/, '')}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='restante'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Cota de Redução Restante
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'text-right text-base disabled:opacity-100',
                          restante.value < 0 ||
                            restante.value === dotacao.data!.cotaReducao
                            ? 'text-red-600'
                            : 'text-green-600'
                        )}
                        value={restante.format().replace(/0$/, '')}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <Separator className='my-12' />
            <span>Valores finais das cotas:</span>
            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              <FormField
                name='janeiro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Janeiro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes1') > 0 && 'text-green-600'
                        )}
                        value={somaMes1}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='fevereiro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Fevereiro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes2') > 0 && 'text-green-600'
                        )}
                        value={somaMes2}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Março'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Março</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes3') > 0 && 'text-green-600'
                        )}
                        value={somaMes3}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Abril'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Abril</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes4') > 0 && 'text-green-600'
                        )}
                        value={somaMes4}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Maio'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Maio</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes5') > 0 && 'text-green-600'
                        )}
                        value={somaMes5}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Junho'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Junho</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes6') > 0 && 'text-green-600'
                        )}
                        value={somaMes6}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Julho'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Julho</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes7') > 0 && 'text-green-600'
                        )}
                        value={somaMes7}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Agosto'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Agosto</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes8') > 0 && 'text-green-600'
                        )}
                        value={somaMes8}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Setembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Setembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes9') > 0 && 'text-green-600'
                        )}
                        value={somaMes9}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Outubro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Outubro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes10') > 0 && 'text-green-600'
                        )}
                        value={somaMes10}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Novembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Novembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes11') > 0 && 'text-green-600'
                        )}
                        value={somaMes11}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Dezembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Dezembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right text-base disabled:opacity-100',
                          form.getValues('cotaMes12') > 0 && 'text-green-600'
                        )}
                        value={somaMes12}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <Separator className='my-12' />
            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              <FormField
                control={form.control}
                name='motivo'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Motivo
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Digite...'
                        className='resize-none'
                        {...field}
                        maxLength={255}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <div className='mt-12 flex w-full justify-between'>
        <Button
          variant={'destructive'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/gerenciamento/dotacoes');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={
            loading ||
            total.value === 0 ||
            restante.value < 0 ||
            restante.value > dotacao.data!.cotaReducao ||
            Object.keys(form.formState.dirtyFields).length === 0
          }
        >
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <Save className='mr-2 h-4 w-4' /> Alterar Cotas
            </>
          )}
        </Button>
      </div>
    </>
  );
}
