'use server';

import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { obterUsuarioExterno } from '@/lib/database/gerenciamento/usuariosExternos';
import EditarUsuarioExternoForm from '@/components/gerenciamento/usuariosExternos/EditarUsuarioExternoForm';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { isNumeric } from '@/lib/utils';

export default async function EditarUsuarioExternoPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  if (!isNumeric(id)) {
    return <ErrorAlert error='ID inválido.' />;
  }

  const usuario = await obterUsuarioExterno({
    id: parseInt(id),
  });

  if (usuario.error) {
    return <ErrorAlert error={usuario.error} />;
  }

  if (!usuario.data) {
    return <ErrorAlert error='Falha ao obter usuário externo.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Editar Usuário Externo</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <EditarUsuarioExternoForm usuario={usuario.data} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
