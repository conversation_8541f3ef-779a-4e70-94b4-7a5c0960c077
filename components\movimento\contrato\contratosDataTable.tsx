'use client';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';
import {
  listarContratos,
  listarFornecedores,
} from '@/lib/database/movimento/contratos';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { ReusableDatatableFiltroIdExercicioFornecedor } from '@/components/datatable/reusableDatatableFiltroIdExercicioFornecedor';
import { AlertDesativarContrato } from './alertDesativarContrato';
import { AlertAtivarContrato } from './alertAtivarContrato';

export default function ContratosDatatable({
  data,
  fornecedores,
}: {
  data: Awaited<ReturnType<typeof listarContratos>>;
  fornecedores: Awaited<ReturnType<typeof listarFornecedores>>;
}) {
  if (!data.data?.contratos) return null;
  const columns: ColumnDef<(typeof data.data.contratos)[0]>[] = [
    {
      accessorKey: 'id',
      header: 'Contrato',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'exercicio',
      id: 'exercicio',
      header: 'Exercicio',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'processo',
      id: 'processo',
      header: 'Processo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'processoAno',
      id: 'processoAno',
      header: 'Ano',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'idFornecedor',
      header: 'Fornecedor',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'valor',
      header: 'Valor Total',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'nomeGestor',
      header: 'Gestor do Contrato',
      filterFn: 'equals',
    },
    {
      accessorKey: 'dataInicio',
      header: 'Inicio',
      filterFn: 'equals',
    },
    {
      accessorKey: 'dataFinal',
      header: 'Fim',
      filterFn: 'equals',
    },
    {
      accessorKey: '',
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex items-center gap-2'>
          {row.original.ativo && (
            <Link href={`/movimento/contratos/editar/${row.original.id}`}>
              <Button variant='ghost' size='sm'>
                <Pencil className='size-4' />
              </Button>
            </Link>
          )}
          <div className='flex items-center gap-2'>
            {row.original.ativo ? (
              <AlertDesativarContrato idContrato={row.original.id} />
            ) : (
              <AlertAtivarContrato idContrato={row.original.id} />
            )}
          </div>
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatableFiltroIdExercicioFornecedor
        columns={columns}
        data={data.data.contratos}
        fornecedores={fornecedores.data?.fornecedores!}
      />
    </div>
  );
}
