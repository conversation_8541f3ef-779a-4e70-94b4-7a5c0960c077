'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Pencil, Eye, Trash2 } from 'lucide-react';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { listarTemplatesDocumentos } from '@/lib/database/gerenciamento/templatesDocumentos';
import { DialogEditarTemplate } from './dialogEditarTemplate';
import { DialogVisualizarTemplate } from './dialogVisualizarTemplate';
import { AlertDesativarTemplate } from './alertDesativarTemplate';
import { formatDataHora } from '@/lib/utils';

type TemplateDocumento = Required<
  Awaited<ReturnType<typeof listarTemplatesDocumentos>>
>['data']['templates'][0];

const columns: ColumnDef<TemplateDocumento>[] = [
  {
    accessorKey: 'nome',
    header: 'Nome',
    filterFn: 'includesString',
  },
  {
    accessorKey: 'descricao',
    header: 'Descrição',
    cell: ({ row }) => {
      const descricao = row.getValue('descricao') as string;
      return descricao || '-';
    },
  },
  {
    accessorKey: 'dataCriacao',
    header: 'Data de Criação',
    cell: ({ row }) => {
      const data = row.getValue('dataCriacao') as Date;
      return formatDataHora(data);
    },
  },
  {
    id: 'acoes',
    header: 'Ações',
    cell: ({ row }) => {
      const template = row.original;
      return (
        <div className='flex gap-2'>
          <DialogVisualizarTemplate template={template}>
            <Button variant='outline' size='sm' title='Visualizar template'>
              <Eye className='h-4 w-4' />
            </Button>
          </DialogVisualizarTemplate>

          <DialogEditarTemplate template={template}>
            <Button variant='outline' size='sm' title='Editar template'>
              <Pencil className='h-4 w-4' />
            </Button>
          </DialogEditarTemplate>

          <AlertDesativarTemplate
            idTemplate={template.id}
            nomeTemplate={template.nome}
          >
            <Button variant='outline' size='sm' title='Desativar template'>
              <Trash2 className='h-4 w-4' />
            </Button>
          </AlertDesativarTemplate>
        </div>
      );
    },
  },
];

interface TemplatesDocumentosDataTableProps {
  data: Required<Awaited<ReturnType<typeof listarTemplatesDocumentos>>>['data'];
}

export function TemplatesDocumentosDataTable({
  data,
}: TemplatesDocumentosDataTableProps) {
  return (
    <div className='container mx-auto'>
      <ReusableDatatable columns={columns} data={data.templates} />
    </div>
  );
}
