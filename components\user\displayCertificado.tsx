import forge from 'node-forge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

export function CertificateDisplay({
  subjectAttributes,
  issuerAttributes,
  validity,
}: {
  subjectAttributes: forge.pki.CertificateField[];
  issuerAttributes: forge.pki.CertificateField[];
  validity: { notBefore: Date; notAfter: Date };
}) {
  const attributeLabels: Record<string, string> = {
    CN: 'Nome Comum',
    O: 'Organização',
    OU: 'Unidade Organizacional',
    C: 'País',
    ST: 'Estado/Província',
    L: 'Localidade',
    E: 'E-mail',
    GN: 'Nome',
    SN: 'Sobrenome',
  };

  return (
    <Card className='mx-auto w-full max-w-2xl'>
      <CardHeader>
        <CardTitle>Detalhes do Certificado</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Subject Section */}
        <h3 className='mb-2 text-lg font-semibold'>Sujeito</h3>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='w-1/3'>Campo</TableHead>
              <TableHead>Valor</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subjectAttributes.map((attr, index) => (
              <TableRow key={index}>
                <TableCell className='font-medium'>
                  {attributeLabels[attr.shortName || 0] || attr.shortName}
                </TableCell>
                <TableCell>{attr.value}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Issuer Section */}
        {issuerAttributes && (
          <>
            <h3 className='mt-4 mb-2 text-lg font-semibold'>Emissor</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-1/3'>Campo</TableHead>
                  <TableHead>Valor</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {issuerAttributes.map((attr, index) => (
                  <TableRow key={index}>
                    <TableCell className='font-medium'>
                      {attributeLabels[attr.shortName || 0] || attr.shortName}
                    </TableCell>
                    <TableCell>{attr.value}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </>
        )}

        {validity && (
          <>
            <h3 className='mt-4 mb-2 text-lg font-semibold'>Validade</h3>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell className='font-medium'>
                    Válido a Partir de
                  </TableCell>
                  <TableCell>
                    {validity.notBefore.toLocaleDateString('pt-BR')}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-medium'>Expira em</TableCell>
                  <TableCell>
                    {validity.notAfter.toLocaleDateString('pt-BR')}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </>
        )}
      </CardContent>
    </Card>
  );
}
