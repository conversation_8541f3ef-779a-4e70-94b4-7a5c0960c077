'use server';
import { revalidatePath } from 'next/cache';
import {
  auditoriaErroSchema,
  nomeEIdSchema,
  idSchema,
  novoDepartamentoSchema,
  permissaoSchema,
} from '../../validation';
import { prisma } from '../../prisma';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { Modulos } from '@/lib/modulos';
import { AuditoriaGerenciamentoSecretarias, Permissoes } from '@/lib/enums';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { inserirErroAudit } from '../auditoria/erro';

export const listarDepartamentos = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permisso<PERSON>.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id: idSecretaria } = parsedParams.data;

  try {
    const departamentos = await prisma.departamentos.findMany({
      where: { secretariaId: idSecretaria },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: departamentos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter departamentos.`,
    };
  }
};

export const obterDepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const departamento = await prisma.departamentos.findFirstOrThrow({
      where: { id: id },
    });
    return {
      data: departamento,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter departamento.`,
    };
  }
};

export const revalidateDepartamentos = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;
  revalidatePath(`/secretarias/${id}/departamentos`);
};

export const desativarDepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const departamento = tx.departamentos.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          departamentoId: id,
          acao: AuditoriaGerenciamentoSecretarias.DESATIVAR_DEPARTAMENTO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([departamento, auditPromise]);
    });

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar departamento.`,
    };
  }
};

export const ativarDepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const departamentoPromise = tx.departamentos.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          departamentoId: id,
          acao: AuditoriaGerenciamentoSecretarias.ATIVAR_DEPARTAMENTO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([departamentoPromise, auditPromise]);
    });

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar departamento.`,
    };
  }
};

export const renomearDepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = nomeEIdSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { nome, id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const nomeAntigo = await tx.departamentos.findUniqueOrThrow({
        where: {
          id: id,
        },
        select: {
          nome: true,
        },
      });
      const departamentoPromise = tx.departamentos.update({
        where: {
          id: id,
        },
        data: {
          nome: nome,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          departamentoId: id,
          acao: AuditoriaGerenciamentoSecretarias.RENOMEAR_DEPARTAMENTO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
          de: nomeAntigo.nome,
          para: nome,
        },
      });

      await Promise.all([departamentoPromise, auditPromise]);
    });

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao renomear departamento.`,
    };
  }
};

export const novoDepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = novoDepartamentoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { nome, secretariaId, codigo } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const departamentoPromise = tx.departamentos.create({
        data: {
          codigo: codigo,
          nome: nome,
          secretariaId: secretariaId,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          acao: AuditoriaGerenciamentoSecretarias.CRIAR_DEPARTAMENTO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([departamentoPromise, auditPromise]);
    });

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Departamento já existe.`,
        };
      }
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar departamento.`,
    };
  }
};
