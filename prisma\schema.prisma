generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model user_profiles {
  id                                              Int                                   @id @default(autoincrement())
  user_id                                         String                                @unique @db.Uuid
  exercicio                                       Int                                   @db.SmallInt
  gerente                                         Bo<PERSON>an                               @default(false)
  nome                                            String
  email                                           String                                @unique
  ativo                                           Boolean                               @default(true)
  acessos_audit                                   acessos_audit[]
  afs_audit                                       afs_audit[]
  alteracaoOrcamentaria_secretario                alteracaoOrcamentaria[]               @relation("secretario")
  alteracaoOrcamentaria                           alteracaoOrcamentaria[]
  cargos_acessos_audit                            cargos_acessos_audit[]
  cargos_permissoes_audit                         cargos_permissoes_audit[]
  contratos_audit                                 contratos_audit[]
  controleAlteracaoOrcamentaria_audit             controleAlteracaoOrcamentaria_audit[]
  controleSuperavitsDetalhes_audit                controleSuperavitsDetalhes_audit[]
  controleSuperavits_audit                        controleSuperavits_audit[]
  cotasReducao_audit                              cotasReducao_audit[]
  departamentos_gestores                          departamentos_gestores?
  documentos_externos                             documentos_externos[]
  documentos_externos_audit                       documentos_externos_audit[]
  empenhos_anulacoes                              empenhos_anulacoes[]
  empenhos_audit                                  empenhos_audit[]
  erros_audit                                     erros_audit[]
  gerenciamentoCargos_audit                       gerenciamentoCargos_audit[]
  gerenciamentoDotacoes_audit                     gerenciamentoDotacoes_audit[]
  gerenciamentoEconomicas_audit                   gerenciamentoEconomicas_audit[]
  gerenciamentoFornecedores_audit                 gerenciamentoFornecedores_audit[]
  gerenciamentoFuncionais_audit                   gerenciamentoFuncionais_audit[]
  gerenciamentoSecretarias_audit                  gerenciamentoSecretarias_audit[]
  gerenciamentoUsuarios_audit_usuarios            gerenciamentoUsuarios_audit[]         @relation("usuario")
  gerenciamentoUsuarios_audit_usuariosModificados gerenciamentoUsuarios_audit[]         @relation("usuarioModificado")
  liquidacoes_audit                               liquidacoes_audit[]
  protocolos_audit                                protocolos_audit[]
  reserva_itens_audit                             reserva_itens_audit[]
  reserva_diretor                                 reservas[]                            @relation("diretor")
  reserva_gestor                                  reservas[]                            @relation("gestor")
  reserva_prefeito                                reservas[]                            @relation("prefeito")
  reserva_secretario                              reservas[]                            @relation("secretario")
  reservas_assinaturas                            reservas_assinaturas[]
  diretor_reservas_audit                          reservas_audit[]                      @relation("diretor")
  prefeito_reservas_audit                         reservas_audit[]                      @relation("prefeito")
  secretario_reservas_audit                       reservas_audit[]                      @relation("secretario")
  reservas_audit                                  reservas_audit[]                      @relation("usuario")
  secretarias_secretarios                         secretarias_secretarios?
  subdepartamentos_gestores                       subdepartamentos_gestores?
  templates_documentos_audit                      templates_documentos_audit[]
  user_profiles_cargos                            user_profiles_cargos[]
  user_profiles_dotacoes                          user_profiles_dotacoes?

  @@index([ativo])
  @@index([user_id])
}

model user_profiles_cargos {
  id        Int           @id @default(autoincrement())
  idUsuario Int
  cargoId   Int
  cargo     cargos        @relation(fields: [cargoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario   user_profiles @relation(fields: [idUsuario], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([idUsuario, cargoId])
}

model user_profiles_dotacoes {
  id          Int           @id @default(autoincrement())
  uuidUsuario String        @unique @db.Uuid
  dotacoesIds Int[]         @default([])
  acessoTotal Boolean       @default(false)
  usuario     user_profiles @relation(fields: [uuidUsuario], references: [user_id], onDelete: Cascade, onUpdate: NoAction)

  @@index([uuidUsuario], map: "idx_user_profiles_dotacoes_uuidusuario")
}

/**
 * Supabase RLS policy para acessar arquivos na pasta private/
 * bucket_id = 'reservas'
 * AND auth.role() = 'authenticated'
 * AND (
 * EXISTS (
 *   SELECT 1 FROM user_profiles_dotacoes
 *   WHERE "uuidUsuario" = auth.uid() AND "acessoTotal" = true
 * )
 * OR
 * EXISTS (
 *   SELECT 1 FROM user_profiles_dotacoes
 *   WHERE "uuidUsuario" = auth.uid()
 *   AND split_part(storage.objects.name, '/', 4)::integer = ANY("dotacoesIds")
 * )
 * )
 */

//  -- Allow authenticated users to SELECT their own dotacoes
// CREATE POLICY "Allow select own user_profiles_dotacoes" ON public.user_profiles_dotacoes
//   FOR SELECT
//   TO authenticated
//   USING ((SELECT auth.uid()) = "uuidUsuario");

// -- Allow authenticated users to INSERT (if needed) ensuring uuidUsuario matches auth.uid()
// CREATE POLICY "Allow insert own user_profiles_dotacoes" ON public.user_profiles_dotacoes
//   FOR INSERT
//   TO authenticated
//   WITH CHECK ((SELECT auth.uid()) = "uuidUsuario");

// -- Create an index to speed up lookups by uuidUsuario
// CREATE INDEX IF NOT EXISTS idx_user_profiles_dotacoes_uuidUsuario ON public.user_profiles_dotacoes("uuidUsuario");

model external_users {
  id                        Int                         @id @default(autoincrement())
  user_id                   String                      @unique @db.Uuid
  nome                      String
  email                     String                      @unique
  ativo                     Boolean                     @default(true)
  documentos_externos       documentos_externos[]
  external_users_audit      external_users_audit[]
  external_users_documentos external_users_documentos[]
}

model external_users_audit {
  id        Int            @id @default(autoincrement())
  usuarioId Int
  acao      Int            @db.SmallInt
  ip        String         @db.Inet
  data      DateTime       @default(now()) @db.Timestamp(6)
  usuario   external_users @relation(fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model parametros {
  id    Int    @id @default(autoincrement())
  key   String @unique
  value String
}

model parametros_exercicio {
  id        Int    @id @default(autoincrement())
  exercicio Int    @db.SmallInt
  key       String
  value     String

  @@unique([exercicio, key])
}

model cargos {
  id                                 Int                                  @id @default(autoincrement())
  ativo                              Boolean                              @default(true)
  nome                               String                               @unique
  tipoAcesso                         Int                                  @db.SmallInt
  tipoAssinatura                     Int                                  @default(0) @db.SmallInt
  cargos_acessos                     cargos_acessos[]
  cargos_acessos_audit               cargos_acessos_audit[]
  cargos_permissoes                  cargos_permissoes[]
  cargos_permissoes_audit            cargos_permissoes_audit[]
  gerenciamentoCargos_audit          gerenciamentoCargos_audit[]
  gerenciamentoUsuarios_audit_cargos gerenciamentoUsuarios_audit_cargos[]
  user_profiles_cargos               user_profiles_cargos[]

  @@index([ativo])
}

model cargos_permissoes {
  id       Int     @id @default(autoincrement())
  cargoId  Int
  moduloId Int
  read     Boolean
  write    Boolean
  update   Boolean
  delete   Boolean
  cargo    cargos  @relation(fields: [cargoId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([cargoId, moduloId])
}

model cargos_acessos {
  id                Int               @id @default(autoincrement())
  cargoId           Int
  secretariaId      Int?
  departamentoId    Int?
  subdepartamentoId Int?
  cargo             cargos            @relation(fields: [cargoId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  departamento      departamentos?    @relation(fields: [departamentoId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  secretaria        secretarias?      @relation(fields: [secretariaId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  subdepartamento   subdepartamentos? @relation(fields: [subdepartamentoId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([cargoId, secretariaId])
  @@unique([cargoId, departamentoId])
  @@unique([cargoId, subdepartamentoId])
}

model secretarias {
  id                             Int                              @id @default(autoincrement())
  codigo                         Int                              @unique @db.SmallInt
  nome                           String
  ativo                          Boolean                          @default(true)
  alteracaoOrcamentaria          alteracaoOrcamentaria[]
  cargos_secretarias             cargos_acessos[]
  cargos_acessos_audit           cargos_acessos_audit[]
  departamentos                  departamentos[]
  dotacoes                       dotacoes[]
  gerenciamentoSecretarias_audit gerenciamentoSecretarias_audit[]
  secretarias_secretarios        secretarias_secretarios[]

  @@index([ativo])
}

model departamentos {
  id                             Int                              @id @default(autoincrement())
  codigo                         Int                              @db.SmallInt
  nome                           String
  secretariaId                   Int
  ativo                          Boolean                          @default(true)
  alteracaoOrcamentaria          alteracaoOrcamentaria[]
  cargos_acessos                 cargos_acessos[]
  cargos_acessos_audit           cargos_acessos_audit[]
  secretaria                     secretarias                      @relation(fields: [secretariaId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  departamentos_gestores         departamentos_gestores[]
  dotacoes                       dotacoes[]
  gerenciamentoSecretarias_audit gerenciamentoSecretarias_audit[]
  subdepartamentos               subdepartamentos[]

  @@unique([secretariaId, codigo])
  @@index([ativo])
}

model subdepartamentos {
  id                             Int                              @id @default(autoincrement())
  codigo                         Int                              @db.SmallInt
  nome                           String
  departamentoId                 Int
  ativo                          Boolean                          @default(true)
  cargos_acessos                 cargos_acessos[]
  cargos_acessos_audit           cargos_acessos_audit[]
  dotacoes                       dotacoes[]
  gerenciamentoSecretarias_audit gerenciamentoSecretarias_audit[]
  departamento                   departamentos                    @relation(fields: [departamentoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subdepartamentos_gestores      subdepartamentos_gestores[]

  @@unique([departamentoId, codigo])
  @@index([ativo])
}

model secretarias_secretarios {
  id              Int
  secretariaId    Int
  secretarioId    Int           @id
  acessoTotalDeps Boolean       @default(false)
  secretaria      secretarias   @relation(fields: [secretariaId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  secretario      user_profiles @relation(fields: [secretarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([secretariaId, secretarioId])
  @@index([secretariaId, secretarioId])
}

model departamentos_gestores {
  id                 Int
  departamentoId     Int
  gestorId           Int           @id
  acessoTotalSubDeps Boolean       @default(false)
  departamento       departamentos @relation(fields: [departamentoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  gestor             user_profiles @relation(fields: [gestorId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([departamentoId, gestorId])
  @@index([departamentoId, gestorId])
}

model subdepartamentos_gestores {
  id                Int
  subdepartamentoId Int
  gestorId          Int              @id
  gestor            user_profiles    @relation(fields: [gestorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subdepartamento   subdepartamentos @relation(fields: [subdepartamentoId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([subdepartamentoId, gestorId])
  @@index([subdepartamentoId, gestorId])
}

model fornecedores {
  id                              Int                               @id @default(autoincrement())
  codigo                          Int                               @unique
  nome                            String
  email                           String
  phone                           String
  ativo                           Boolean                           @default(true)
  cnpjCpf                         String                            @db.VarChar(14)
  contratos                       contratos[]
  empenhos                        empenhos[]
  gerenciamentoFornecedores_audit gerenciamentoFornecedores_audit[]

  @@index([ativo])
}

model funcoes {
  id         Int          @id @default(autoincrement())
  codigo     Int          @unique @db.SmallInt
  desc       String
  funcionais funcionais[]
}

model subfuncoes {
  id         Int          @id @default(autoincrement())
  codigo     Int          @unique @db.SmallInt
  desc       String
  funcionais funcionais[]
}

model programas {
  id         Int          @id @default(autoincrement())
  codigo     Int          @unique @db.SmallInt
  desc       String
  funcionais funcionais[]
}

model acoes {
  id         Int          @id @default(autoincrement())
  codigo     Int          @unique @db.SmallInt
  desc       String
  funcionais funcionais[]
}

model funcionais {
  id                            Int                             @id @default(autoincrement())
  desc                          String
  ativo                         Boolean                         @default(true)
  codigo                        String                          @db.VarChar(16)
  codigoAcao                    Int
  codigoFuncao                  Int
  codigoPrograma                Int
  codigoSubfuncao               Int
  alteracaoOrcamentaria         alteracaoOrcamentaria[]
  dotacoes                      dotacoes[]
  acao                          acoes                           @relation(fields: [codigoAcao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  funcao                        funcoes                         @relation(fields: [codigoFuncao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  programa                      programas                       @relation(fields: [codigoPrograma], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subfuncao                     subfuncoes                      @relation(fields: [codigoSubfuncao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  gerenciamentoFuncionais_audit gerenciamentoFuncionais_audit[]

  @@unique([codigoFuncao, codigoSubfuncao, codigoPrograma, codigoAcao])
  @@index([ativo])
}

model gerenciamentoFuncionais_audit {
  id          Int           @id @default(autoincrement())
  idUsuario   Int
  idFuncional Int
  de          String?
  para        String?
  acao        Int           @db.SmallInt
  ip          String        @db.Inet
  data        DateTime      @default(now()) @db.Timestamp(6)
  funcional   funcionais    @relation(fields: [idFuncional], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario     user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model economicas {
  id                            Int                             @id @default(autoincrement())
  codigo                        String                          @unique @db.VarChar(12)
  desc                          String                          @default("")
  ativo                         Boolean                         @default(true)
  categoria                     Int                             @db.SmallInt
  elemento                      Int                             @db.SmallInt
  grupo                         Int                             @db.SmallInt
  modalidade                    Int                             @db.SmallInt
  subelemento                   Int                             @db.SmallInt
  info                          String                          @default("")
  obs                           String                          @default("")
  alteracaoOrcamentaria         alteracaoOrcamentaria[]
  dotacoes                      dotacoes[]
  gerenciamentoEconomicas_audit gerenciamentoEconomicas_audit[]
  reserva                       reservas[]
  reservas_audit                reservas_audit[]

  @@unique([categoria, grupo, modalidade, elemento, subelemento])
  @@index([ativo])
}

model gerenciamentoEconomicas_audit {
  id          Int           @id @default(autoincrement())
  idUsuario   Int
  idEconomica Int
  acao        Int           @db.SmallInt
  ip          String        @db.Inet
  data        DateTime      @default(now()) @db.Timestamp(6)
  de          String?
  para        String?
  economica   economicas    @relation(fields: [idEconomica], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario     user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model dotacoes {
  id                          Int                           @id @default(autoincrement())
  exercicio                   Int                           @db.SmallInt
  despesa                     Int
  funcionalId                 Int
  economicaId                 Int
  departamentoId              Int?
  valorInicial                Decimal                       @db.Decimal(18, 3)
  cotaReducaoInicial          Decimal                       @db.Decimal(18, 3)
  valorLiberado               Decimal                       @db.Decimal(18, 3)
  cotaReducao                 Decimal                       @db.Decimal(18, 3)
  suplementacao               Decimal                       @db.Decimal(18, 3)
  anulacao                    Decimal                       @db.Decimal(18, 3)
  valorAtual                  Decimal                       @db.Decimal(18, 3)
  cotaMes1                    Decimal                       @db.Decimal(18, 3)
  cotaMes2                    Decimal                       @db.Decimal(18, 3)
  cotaMes3                    Decimal                       @db.Decimal(18, 3)
  cotaMes4                    Decimal                       @db.Decimal(18, 3)
  cotaMes5                    Decimal                       @db.Decimal(18, 3)
  cotaMes6                    Decimal                       @db.Decimal(18, 3)
  cotaMes7                    Decimal                       @db.Decimal(18, 3)
  cotaMes8                    Decimal                       @db.Decimal(18, 3)
  cotaMes9                    Decimal                       @db.Decimal(18, 3)
  cotaMes10                   Decimal                       @db.Decimal(18, 3)
  cotaMes11                   Decimal                       @db.Decimal(18, 3)
  cotaMes12                   Decimal                       @db.Decimal(18, 3)
  ativo                       Boolean                       @default(true)
  secretariaId                Int?
  subdepartamentoId           Int?
  desc                        String
  codAplicacao                Int
  fonte                       Int                           @db.SmallInt
  controleSuperavits          controleSuperavits[]
  cotasReducao                cotasReducao[]
  departamento                departamentos?                @relation(fields: [departamentoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  economica                   economicas                    @relation(fields: [economicaId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  funcional                   funcionais                    @relation(fields: [funcionalId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  secretaria                  secretarias?                  @relation(fields: [secretariaId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subdepartamento             subdepartamentos?             @relation(fields: [subdepartamentoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  empenhos                    empenhos[]
  gerenciamentoDotacoes_audit gerenciamentoDotacoes_audit[]
  reserva                     reservas[]

  @@unique([exercicio, despesa])
  @@index([exercicio, secretariaId, departamentoId, subdepartamentoId])
}

model cotasReducao {
  id                 Int                  @id @default(autoincrement())
  idDotacao          Int
  motivo             String
  valorTotal         Decimal              @db.Decimal(18, 3)
  valorMes1          Decimal              @db.Decimal(18, 3)
  valorMes2          Decimal              @db.Decimal(18, 3)
  valorMes3          Decimal              @db.Decimal(18, 3)
  valorMes4          Decimal              @db.Decimal(18, 3)
  valorMes5          Decimal              @db.Decimal(18, 3)
  valorMes6          Decimal              @db.Decimal(18, 3)
  valorMes7          Decimal              @db.Decimal(18, 3)
  valorMes8          Decimal              @db.Decimal(18, 3)
  valorMes9          Decimal              @db.Decimal(18, 3)
  valorMes10         Decimal              @db.Decimal(18, 3)
  valorMes11         Decimal              @db.Decimal(18, 3)
  valorMes12         Decimal              @db.Decimal(18, 3)
  reducao            Boolean
  data               DateTime             @default(now()) @db.Timestamp(6)
  dotacao            dotacoes             @relation(fields: [idDotacao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  cotasReducao_audit cotasReducao_audit[]
}

model cotasReducao_audit {
  id                         Int                          @id @default(autoincrement())
  idCotaReducao              Int
  idUsuario                  Int
  acao                       Int                          @db.SmallInt
  ip                         String                       @db.Inet
  data                       DateTime                     @default(now()) @db.Timestamp(6)
  cotaReducao                cotasReducao                 @relation(fields: [idCotaReducao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario                    user_profiles                @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
  cotasReducao_audit_valores cotasReducao_audit_valores[]
}

model cotasReducao_audit_valores {
  id                 Int                @id @default(autoincrement())
  idCotaReducaoAudit Int
  mes                Int                @db.SmallInt
  de                 Decimal            @db.Decimal(18, 3)
  para               Decimal            @db.Decimal(18, 3)
  cotaReducaoAudit   cotasReducao_audit @relation(fields: [idCotaReducaoAudit], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model gerenciamentoDotacoes_audit {
  id        Int           @id @default(autoincrement())
  idUsuario Int
  idDotacao Int
  acao      Int           @db.SmallInt
  de        String?
  para      String?
  ip        String        @db.Inet
  data      DateTime      @default(now()) @db.Timestamp(6)
  dotacao   dotacoes      @relation(fields: [idDotacao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario   user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model acessos_audit {
  id        Int           @id @default(autoincrement())
  usuarioId Int
  acao      Int           @db.SmallInt
  ip        String        @db.Inet
  data      DateTime      @default(now()) @db.Timestamp(6)
  usuario   user_profiles @relation(fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model gerenciamentoSecretarias_audit {
  id                Int               @id @default(autoincrement())
  acao              Int               @db.SmallInt
  secretariaId      Int?
  departamentoId    Int?
  subdepartamentoId Int?
  de                String?
  para              String?
  ip                String            @db.Inet
  data              DateTime          @default(now()) @db.Timestamp(6)
  usuarioId         Int
  departamento      departamentos?    @relation(fields: [departamentoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  secretaria        secretarias?      @relation(fields: [secretariaId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subdepartamento   subdepartamentos? @relation(fields: [subdepartamentoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario           user_profiles     @relation(fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model gerenciamentoCargos_audit {
  id        Int           @id @default(autoincrement())
  acao      Int           @db.SmallInt
  cargoId   Int
  de        String?
  para      String?
  ip        String        @db.Inet
  data      DateTime      @default(now()) @db.Timestamp(6)
  usuarioId Int
  cargo     cargos        @relation(fields: [cargoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario   user_profiles @relation(fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model cargos_permissoes_audit {
  id        Int           @id @default(autoincrement())
  usuarioId Int
  cargoId   Int
  read      Boolean
  write     Boolean
  update    Boolean
  delete    Boolean
  ip        String        @db.Inet
  data      DateTime      @default(now()) @db.Timestamp(6)
  moduloId  Int
  cargo     cargos        @relation(fields: [cargoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario   user_profiles @relation(fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model cargos_acessos_audit {
  id                Int               @id @default(autoincrement())
  usuarioId         Int
  cargoId           Int
  secretariaId      Int?
  departamentoId    Int?
  subdepartamentoId Int?
  ip                String            @db.Inet
  data              DateTime          @default(now()) @db.Timestamp(6)
  cargo             cargos            @relation(fields: [cargoId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  departamento      departamentos?    @relation(fields: [departamentoId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  secretaria        secretarias?      @relation(fields: [secretariaId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  subdepartamento   subdepartamentos? @relation(fields: [subdepartamentoId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  usuario           user_profiles     @relation(fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model erros_audit {
  id        Int            @id @default(autoincrement())
  usuarioId Int?
  moduloId  Int?
  erro      String
  ip        String?        @db.Inet
  data      DateTime       @default(now()) @db.Timestamp(6)
  usuario   user_profiles? @relation(fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model gerenciamentoUsuarios_audit {
  id                                 Int                                  @id @default(autoincrement())
  usuarioId                          Int
  usuarioModificadoId                Int
  acao                               Int                                  @db.SmallInt
  ip                                 String                               @db.Inet
  data                               DateTime                             @default(now()) @db.Timestamp(6)
  usuario                            user_profiles                        @relation("usuario", fields: [usuarioId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuarioModificado                  user_profiles                        @relation("usuarioModificado", fields: [usuarioModificadoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  gerenciamentoUsuarios_audit_cargos gerenciamentoUsuarios_audit_cargos[]
}

model gerenciamentoUsuarios_audit_cargos {
  id                          Int                         @id @default(autoincrement())
  auditId                     Int
  cargoId                     Int
  gerenciamentoUsuarios_audit gerenciamentoUsuarios_audit @relation(fields: [auditId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  cargo                       cargos                      @relation(fields: [cargoId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model gerenciamentoFornecedores_audit {
  id           Int           @id @default(autoincrement())
  idUsuario    Int
  idFornecedor Int
  acao         Int           @db.SmallInt
  de           String?
  para         String?
  ip           String        @db.Inet
  data         DateTime      @default(now()) @db.Timestamp(6)
  fornecedor   fornecedores  @relation(fields: [idFornecedor], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario      user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model configuracoesExercicio {
  id                          Int       @id @default(autoincrement())
  travarDotacoes              Boolean   @default(false)
  travarDataPedidos           Boolean   @default(false)
  dataPedidos                 DateTime?
  habilitaAssinatura1         Boolean   @default(false)
  habilitaAssinatura2         Boolean   @default(false)
  economicaAssinatura1        String    @default("")
  economicaAssinatura2        String    @default("")
  assinatura1Linha1           String    @default("")
  assinatura1Linha2           String    @default("")
  assinatura2Linha1           String    @default("")
  assinatura2Linha2           String    @default("")
  nomePrefeito                String    @default("")
  nomeChefeSecretariaFinancas String    @default("")
  exercicio                   Int       @unique @db.SmallInt
}

model controleSuperavits {
  id                         Int                          @id @default(autoincrement())
  exercicio                  Int                          @db.SmallInt
  fonte                      Int                          @db.SmallInt
  codAplicacao               Int
  valorReceita               Decimal                      @db.Decimal(18, 3)
  ativo                      Boolean                      @default(true)
  idDotacao                  Int?
  dotacao                    dotacoes?                    @relation(fields: [idDotacao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  controleSuperavitsDetalhes controleSuperavitsDetalhes[]
  controleSuperavits_audit   controleSuperavits_audit[]

  @@index([ativo])
}

model controleSuperavits_audit {
  id          Int                @id @default(autoincrement())
  idUsuario   Int
  idSuperavit Int
  acao        Int                @db.SmallInt
  de          String?
  para        String?
  ip          String             @db.Inet
  data        DateTime           @default(now()) @db.Timestamp(6)
  superavit   controleSuperavits @relation(fields: [idSuperavit], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario     user_profiles      @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model controleSuperavitsDetalhes {
  id                               Int                                @id @default(autoincrement())
  exercicio                        Int                                @db.SmallInt
  despesa                          Int
  fonte                            Int                                @db.SmallInt
  codAplicacao                     Int
  idAltOrcament                    Int
  data                             DateTime                           @db.Timestamp(6)
  tela                             String
  valor                            Decimal                            @db.Decimal(18, 3)
  valorReservado                   Decimal                            @db.Decimal(18, 3)
  ativo                            Boolean                            @default(true)
  idSuperavit                      Int
  tipoAcao                         Int                                @db.SmallInt
  valorSuplementado                Decimal                            @db.Decimal(18, 3)
  status                           Int                                @default(1) @db.SmallInt
  alteracaoOrcamentaria            alteracaoOrcamentaria              @relation(fields: [idAltOrcament], references: [id], onDelete: NoAction, onUpdate: NoAction)
  superavit                        controleSuperavits                 @relation(fields: [idSuperavit], references: [id], onDelete: NoAction, onUpdate: NoAction)
  controleSuperavitsDetalhes_audit controleSuperavitsDetalhes_audit[]

  @@index([ativo])
}

model controleSuperavitsDetalhes_audit {
  id          Int                        @id @default(autoincrement())
  idUsuario   Int
  idSuperDeta Int
  acao        Int                        @db.SmallInt
  de          String?
  para        String?
  ip          String                     @db.Inet
  data        DateTime                   @default(now()) @db.Timestamp(6)
  superDeta   controleSuperavitsDetalhes @relation(fields: [idSuperDeta], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario     user_profiles              @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model alteracaoOrcamentaria {
  id                                  Int                                   @id @default(autoincrement())
  exercicio                           Int                                   @db.SmallInt
  tipoAlteracao                       Int                                   @db.SmallInt
  tipoAcao                            Int                                   @db.SmallInt
  status                              Int                                   @db.SmallInt
  valorTotal                          Decimal                               @db.Decimal(18, 3)
  valorMes1                           Decimal                               @db.Decimal(18, 3)
  valorMes2                           Decimal                               @db.Decimal(18, 3)
  valorMes3                           Decimal                               @db.Decimal(18, 3)
  valorMes4                           Decimal                               @db.Decimal(18, 3)
  valorMes5                           Decimal                               @db.Decimal(18, 3)
  valorMes6                           Decimal                               @db.Decimal(18, 3)
  valorMes7                           Decimal                               @db.Decimal(18, 3)
  valorMes8                           Decimal                               @db.Decimal(18, 3)
  valorMes9                           Decimal                               @db.Decimal(18, 3)
  valorMes10                          Decimal                               @db.Decimal(18, 3)
  valorMes11                          Decimal                               @db.Decimal(18, 3)
  valorMes12                          Decimal                               @db.Decimal(18, 3)
  dataAbertura                        DateTime                              @default(now()) @db.Timestamp(6)
  dataStatus                          DateTime
  fonte                               Int                                   @db.SmallInt
  codAplicacao                        Int
  idSecretaria                        Int?
  idDepto                             Int?
  idEconomica                         Int?
  idFuncional                         Int
  despesaCopia                        Int
  despesaAcao                         Int
  obs                                 String
  idUsuario                           Int?
  dataDecreto                         DateTime?                             @default(now()) @db.Timestamp(6)
  tipoDecreto                         Int?
  numDecreto                          Int?
  despesaNova                         Int
  descrDespNova                       String
  ativo                               Boolean                               @default(true)
  idSecretario                        Int?
  departamento                        departamentos?                        @relation(fields: [idDepto], references: [id], onDelete: NoAction, onUpdate: NoAction)
  economica                           economicas?                           @relation(fields: [idEconomica], references: [id], onDelete: NoAction, onUpdate: NoAction)
  funcional                           funcionais                            @relation(fields: [idFuncional], references: [id], onDelete: NoAction, onUpdate: NoAction)
  secretaria                          secretarias?                          @relation(fields: [idSecretaria], references: [id], onDelete: NoAction, onUpdate: NoAction)
  secretario                          user_profiles?                        @relation("secretario", fields: [idSecretario], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario                             user_profiles?                        @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
  controleAlteracaoOrcamentaria_audit controleAlteracaoOrcamentaria_audit[]
  controleSuperavitsDetalhes          controleSuperavitsDetalhes[]

  @@unique([exercicio, id])
  @@index([ativo])
}

model controleAlteracaoOrcamentaria_audit {
  id          Int                   @id @default(autoincrement())
  idUsuario   Int
  idAlterOrca Int
  acao        Int                   @db.SmallInt
  de          String?
  para        String?
  ip          String                @db.Inet
  data        DateTime              @default(now()) @db.Timestamp(6)
  alterOrca   alteracaoOrcamentaria @relation(fields: [idAlterOrca], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario     user_profiles         @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model reservas {
  id                   Int                    @id @default(autoincrement())
  idGestor             Int
  idDotacao            Int
  exercicio            Int                    @db.SmallInt
  idSecretario         Int
  idEconomicaItem      Int
  resumo               String
  obs                  String
  data                 DateTime               @default(now()) @db.Timestamp(6)
  usarMes1             Decimal                @db.Decimal(18, 3)
  usarMes2             Decimal                @db.Decimal(18, 3)
  usarMes3             Decimal                @db.Decimal(18, 3)
  usarMes4             Decimal                @db.Decimal(18, 3)
  usarMes5             Decimal                @db.Decimal(18, 3)
  usarMes6             Decimal                @db.Decimal(18, 3)
  usarMes7             Decimal                @db.Decimal(18, 3)
  usarMes8             Decimal                @db.Decimal(18, 3)
  usarMes9             Decimal                @db.Decimal(18, 3)
  usarMes10            Decimal                @db.Decimal(18, 3)
  usarMes11            Decimal                @db.Decimal(18, 3)
  usarMes12            Decimal                @db.Decimal(18, 3)
  usarTotal            Decimal                @db.Decimal(18, 3)
  pedidoPlurianual     Boolean                @default(false)
  pluUsarMes1          Decimal                @db.Decimal(18, 3)
  pluUsarMes2          Decimal                @db.Decimal(18, 3)
  pluUsarMes3          Decimal                @db.Decimal(18, 3)
  pluUsarMes4          Decimal                @db.Decimal(18, 3)
  pluUsarMes5          Decimal                @db.Decimal(18, 3)
  pluUsarMes6          Decimal                @db.Decimal(18, 3)
  pluUsarMes7          Decimal                @db.Decimal(18, 3)
  pluUsarMes8          Decimal                @db.Decimal(18, 3)
  pluUsarMes9          Decimal                @db.Decimal(18, 3)
  pluUsarMes10         Decimal                @db.Decimal(18, 3)
  pluUsarMes11         Decimal                @db.Decimal(18, 3)
  pluUsarMes12         Decimal                @db.Decimal(18, 3)
  pluUsarTotal         Decimal                @db.Decimal(18, 3)
  motivoCancelamento   String?
  ultimaModificacao    DateTime               @default(now()) @db.Timestamp(6)
  status               Int                    @default(0) @db.SmallInt
  idDiretor            Int
  idPrefeito           Int
  empenhos             empenhos[]
  protocolos           protocolos[]
  reserva_itens        reserva_itens[]
  reserva_itens_audit  reserva_itens_audit[]
  diretor              user_profiles          @relation("diretor", fields: [idDiretor], references: [id], onDelete: NoAction, onUpdate: NoAction)
  dotacao              dotacoes               @relation(fields: [idDotacao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  economicaItem        economicas             @relation(fields: [idEconomicaItem], references: [id], onDelete: NoAction, onUpdate: NoAction)
  gestor               user_profiles          @relation("gestor", fields: [idGestor], references: [id], onDelete: NoAction, onUpdate: NoAction)
  prefeito             user_profiles          @relation("prefeito", fields: [idPrefeito], references: [id], onDelete: NoAction, onUpdate: NoAction)
  secretario           user_profiles          @relation("secretario", fields: [idSecretario], references: [id], onDelete: NoAction, onUpdate: NoAction)
  reservas_assinaturas reservas_assinaturas[]
  reservas_audit       reservas_audit[]

  @@index([exercicio, idDotacao])
}

model reservas_audit {
  id               Int            @id @default(autoincrement())
  idReserva        Int
  idSecretario     Int?
  idEconomicaItem  Int?
  resumo           String?
  obs              String?
  data             DateTime?      @default(now()) @db.Timestamp(6)
  usarMes1         Decimal?       @db.Decimal(18, 3)
  usarMes2         Decimal?       @db.Decimal(18, 3)
  usarMes3         Decimal?       @db.Decimal(18, 3)
  usarMes4         Decimal?       @db.Decimal(18, 3)
  usarMes5         Decimal?       @db.Decimal(18, 3)
  usarMes6         Decimal?       @db.Decimal(18, 3)
  usarMes7         Decimal?       @db.Decimal(18, 3)
  usarMes8         Decimal?       @db.Decimal(18, 3)
  usarMes9         Decimal?       @db.Decimal(18, 3)
  usarMes10        Decimal?       @db.Decimal(18, 3)
  usarMes11        Decimal?       @db.Decimal(18, 3)
  usarMes12        Decimal?       @db.Decimal(18, 3)
  usarTotal        Decimal?       @db.Decimal(18, 3)
  pedidoPlurianual Boolean?       @default(false)
  pluUsarMes1      Decimal?       @db.Decimal(18, 3)
  pluUsarMes2      Decimal?       @db.Decimal(18, 3)
  pluUsarMes3      Decimal?       @db.Decimal(18, 3)
  pluUsarMes4      Decimal?       @db.Decimal(18, 3)
  pluUsarMes5      Decimal?       @db.Decimal(18, 3)
  pluUsarMes6      Decimal?       @db.Decimal(18, 3)
  pluUsarMes7      Decimal?       @db.Decimal(18, 3)
  pluUsarMes8      Decimal?       @db.Decimal(18, 3)
  pluUsarMes9      Decimal?       @db.Decimal(18, 3)
  pluUsarMes10     Decimal?       @db.Decimal(18, 3)
  pluUsarMes11     Decimal?       @db.Decimal(18, 3)
  pluUsarMes12     Decimal?       @db.Decimal(18, 3)
  pluUsarTotal     Decimal?       @db.Decimal(18, 3)
  acao             Int            @db.SmallInt
  cotaMes1         Decimal?       @db.Decimal(18, 3)
  cotaMes10        Decimal?       @db.Decimal(18, 3)
  cotaMes11        Decimal?       @db.Decimal(18, 3)
  cotaMes12        Decimal?       @db.Decimal(18, 3)
  cotaMes2         Decimal?       @db.Decimal(18, 3)
  cotaMes3         Decimal?       @db.Decimal(18, 3)
  cotaMes4         Decimal?       @db.Decimal(18, 3)
  cotaMes5         Decimal?       @db.Decimal(18, 3)
  cotaMes6         Decimal?       @db.Decimal(18, 3)
  cotaMes7         Decimal?       @db.Decimal(18, 3)
  cotaMes8         Decimal?       @db.Decimal(18, 3)
  cotaMes9         Decimal?       @db.Decimal(18, 3)
  exercicio        Int            @db.SmallInt
  idUsuario        Int
  ip               String         @db.Inet
  valorTotalCota   Decimal?       @db.Decimal(18, 3)
  idDiretor        Int?
  idPrefeito       Int?
  diretor          user_profiles? @relation("diretor", fields: [idDiretor], references: [id], onDelete: NoAction, onUpdate: NoAction)
  economicaItem    economicas?    @relation(fields: [idEconomicaItem], references: [id], onDelete: NoAction, onUpdate: NoAction)
  prefeito         user_profiles? @relation("prefeito", fields: [idPrefeito], references: [id], onDelete: NoAction, onUpdate: NoAction)
  reserva          reservas       @relation(fields: [idReserva], references: [id], onDelete: NoAction, onUpdate: NoAction)
  secretario       user_profiles? @relation("secretario", fields: [idSecretario], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario          user_profiles  @relation("usuario", fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model reserva_itens {
  id         Int      @id @default(autoincrement())
  idReserva  Int
  quantidade Decimal  @db.Decimal(18, 4)
  valor      Decimal  @db.Decimal(18, 3)
  desc       String
  unidade    Int      @db.SmallInt
  reserva    reservas @relation(fields: [idReserva], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model reserva_itens_audit {
  id         Int           @id @default(autoincrement())
  idReserva  Int
  quantidade Decimal       @db.Decimal(18, 4)
  valor      Decimal       @db.Decimal(18, 3)
  acao       Int           @db.SmallInt
  desc       String
  idUsuario  Int
  ip         String        @db.Inet
  unidade    Int           @db.SmallInt
  reserva    reservas      @relation(fields: [idReserva], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario    user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model reservas_assinaturas {
  id             Int           @id @default(autoincrement())
  idReserva      Int
  idUsuario      Int
  left           Decimal       @db.Decimal(8, 2)
  top            Decimal       @db.Decimal(8, 2)
  assinado       Boolean       @default(false)
  dataAssinatura DateTime?     @default(now()) @db.Timestamp(6)
  reserva        reservas      @relation(fields: [idReserva], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario        user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model contratos {
  id              Int               @id @default(autoincrement())
  exercicio       Int
  idFornecedor    Int
  processo        Int
  processoAno     Int
  valor           Decimal           @db.Decimal(18, 3)
  nomeGestor      String
  cpfGestor       String            @db.VarChar(14)
  condPagamento   String
  numAf           Int
  dataInicio      DateTime
  dataFinal       DateTime
  ativo           Boolean           @default(true)
  fornecedor      fornecedores      @relation(fields: [idFornecedor], references: [id], onDelete: NoAction, onUpdate: NoAction)
  empenhos        empenhos[]
  contratos_audit contratos_audit[]

  @@index([ativo])
}

model contratos_audit {
  id            Int           @id @default(autoincrement())
  idContrato    Int
  idFornecedor  Int
  processo      Int
  processoAno   Int
  nomeGestor    String
  cpfGestor     String        @db.VarChar(14)
  condPagamento String
  numAf         Int
  dataInicio    DateTime
  dataFinal     DateTime
  valor         Decimal       @db.Decimal(18, 3)
  idUsuario     Int
  ip            String        @db.Inet
  acao          Int           @db.SmallInt
  contrato      contratos     @relation(fields: [idContrato], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario       user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model documentos_externos {
  id                        Int                         @id @default(autoincrement())
  idOrigemTipo              Int
  idOrigemRegistro          Int
  idUsuarioExterno          Int
  idUsuarioSolicitante      Int
  nomeDocumento             String
  descricaoDocumento        String?
  instrucoes                String?
  pathDocumentoOriginal     String
  pathDocumentoAssinado     String?
  status                    Int                         @db.SmallInt
  motivoCancelamento        String?
  dataCriacao               DateTime                    @default(now()) @db.Timestamp(6)
  dataAssinatura            DateTime?                   @db.Timestamp(6)
  ativo                     Boolean                     @default(true)
  usuarioExterno            external_users              @relation(fields: [idUsuarioExterno], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuarioSolicitante        user_profiles               @relation(fields: [idUsuarioSolicitante], references: [id], onDelete: NoAction, onUpdate: NoAction)
  documentos_externos_audit documentos_externos_audit[]
  external_users_documentos external_users_documentos[]
}

model documentos_externos_audit {
  id                 Int                 @id @default(autoincrement())
  idDocumentoExterno Int
  idUsuario          Int?
  acao               Int                 @db.SmallInt
  ip                 String              @db.Inet
  data               DateTime            @default(now()) @db.Timestamp(6)
  documentoExterno   documentos_externos @relation(fields: [idDocumentoExterno], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario            user_profiles?      @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model templates_documentos {
  id                         Int                          @id @default(autoincrement())
  nome                       String
  descricao                  String?
  conteudoHtml               String
  ativo                      Boolean                      @default(true)
  dataCriacao                DateTime                     @default(now()) @db.Timestamp(6)
  templates_documentos_audit templates_documentos_audit[]
}

model templates_documentos_audit {
  id         Int                  @id @default(autoincrement())
  idTemplate Int
  idUsuario  Int
  acao       Int                  @db.SmallInt
  ip         String               @db.Inet
  data       DateTime             @default(now()) @db.Timestamp(6)
  template   templates_documentos @relation(fields: [idTemplate], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario    user_profiles        @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model external_users_documentos {
  id                 Int                 @id @default(autoincrement())
  idUsuarioExterno   Int
  idDocumentoExterno Int
  documentoExterno   documentos_externos @relation(fields: [idDocumentoExterno], references: [id], onDelete: Cascade, onUpdate: NoAction)
  usuarioExterno     external_users      @relation(fields: [idUsuarioExterno], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([idUsuarioExterno, idDocumentoExterno])
}

model empenhos {
  id                 Int                  @id @default(autoincrement())
  exercicio          Int                  @db.SmallInt
  numero             Int
  idReserva          Int?
  idDotacao          Int
  idFornecedor       Int?
  idContrato         Int?
  resumo             String?
  obs                String?
  valorTotal         Decimal              @db.Decimal(18, 3)
  status             Int                  @db.SmallInt
  data               DateTime             @default(now()) @db.Timestamp(6)
  ativo              Boolean              @default(true)
  usarMes1           Decimal              @db.Decimal(18, 3)
  usarMes10          Decimal              @db.Decimal(18, 3)
  usarMes11          Decimal              @db.Decimal(18, 3)
  usarMes12          Decimal              @db.Decimal(18, 3)
  usarMes2           Decimal              @db.Decimal(18, 3)
  usarMes3           Decimal              @db.Decimal(18, 3)
  usarMes4           Decimal              @db.Decimal(18, 3)
  usarMes5           Decimal              @db.Decimal(18, 3)
  usarMes6           Decimal              @db.Decimal(18, 3)
  usarMes7           Decimal              @db.Decimal(18, 3)
  usarMes8           Decimal              @db.Decimal(18, 3)
  usarMes9           Decimal              @db.Decimal(18, 3)
  afs                afs[]
  contrato           contratos?           @relation(fields: [idContrato], references: [id], onDelete: NoAction, onUpdate: NoAction)
  dotacao            dotacoes             @relation(fields: [idDotacao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  fornecedor         fornecedores?        @relation(fields: [idFornecedor], references: [id], onDelete: NoAction, onUpdate: NoAction)
  reserva            reservas?            @relation(fields: [idReserva], references: [id], onDelete: NoAction, onUpdate: NoAction)
  empenhos_anulacoes empenhos_anulacoes[]
  empenhos_audit     empenhos_audit[]
  liquidacoes        liquidacoes[]

  @@unique([exercicio, numero])
  @@index([exercicio, idDotacao])
  @@index([idFornecedor, exercicio])
  @@index([idContrato])
  @@index([status, data])
}

model empenhos_audit {
  id        Int           @id @default(autoincrement())
  idEmpenho Int
  idUsuario Int
  acao      Int           @db.SmallInt
  ip        String        @db.Inet
  data      DateTime      @default(now()) @db.Timestamp(6)
  obs       String?
  empenho   empenhos      @relation(fields: [idEmpenho], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario   user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model empenhos_anulacoes {
  id           Int           @id @default(autoincrement())
  idEmpenho    Int
  valorAnulado Decimal       @db.Decimal(18, 3)
  motivo       String
  data         DateTime      @default(now()) @db.Timestamp(6)
  ativo        Boolean       @default(true)
  idUsuario    Int
  ip           String        @db.Inet
  empenho      empenhos      @relation(fields: [idEmpenho], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario      user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([idEmpenho, ativo])
}

model afs {
  id             Int              @id @default(autoincrement())
  exercicio      Int              @db.SmallInt
  numero         Int
  idEmpenho      Int
  resumo         String?
  obs            String?
  valorTotal     Decimal          @db.Decimal(18, 3)
  status         Int              @db.SmallInt
  data           DateTime         @default(now()) @db.Timestamp(6)

  // NOVOS: Campos de rastreamento de datas
  dataEmissao     DateTime?        @db.Timestamp(6) // Data de emissão oficial
  dataVencimento  DateTime?        @db.Timestamp(6) // Data de vencimento
  dataUtilizacao  DateTime?        @db.Timestamp(6) // Data de utilização/uso
  dataCancelamento DateTime?       @db.Timestamp(6) // Data de cancelamento
  dataReativacao  DateTime?        @db.Timestamp(6) // Data de reativação

  ativo          Boolean          @default(true)
  empenho        empenhos         @relation(fields: [idEmpenho], references: [id], onDelete: NoAction, onUpdate: NoAction)
  afs_audit      afs_audit[]
  afs_documentos afs_documentos[]

  // Índices para performance
  @@index([idEmpenho, numero])
  @@index([dataEmissao])
  @@index([dataVencimento])
  @@index([status, ativo])
}

model afs_audit {
  id        Int           @id @default(autoincrement())
  idAF      Int
  idUsuario Int
  acao      Int           @db.SmallInt
  ip        String        @db.Inet
  data      DateTime      @default(now()) @db.Timestamp(6)
  obs       String?
  af        afs           @relation(fields: [idAF], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario   user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model afs_documentos {
  id             Int      @id @default(autoincrement())
  idAF           Int
  tipoDocumento  Int      @db.SmallInt
  nomeArquivo    String   @db.VarChar(255)
  caminhoArquivo String   @db.VarChar(500)
  tamanho        Int
  dataUpload     DateTime @default(now()) @db.Timestamp(6)
  ativo          Boolean  @default(true)
  af             afs      @relation(fields: [idAF], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([idAF, tipoDocumento])
  @@index([ativo])
}

model liquidacoes {
  id                     Int                      @id @default(autoincrement())
  exercicio              Int                      @db.SmallInt
  numero                 Int
  idEmpenho              Int
  valorTotal             Decimal                  @db.Decimal(18, 3)
  data                   DateTime                 @default(now()) @db.Timestamp(6)
  status                 Int                      @db.SmallInt
  ativo                  Boolean                  @default(true)
  resumo                 String?
  obs                    String?
  mesReferencia          String?                  @db.VarChar(15)
  dataMaterialServico    DateTime?                @db.Date
  marca_documento        String?                  @db.VarChar(50)
  // AUDESP compliance fields (optional)
  audespDataInicio       DateTime?                @db.Date
  audespRegimeExecucao   Int?                     @db.SmallInt
  audespCorrenteImportacao Int?                   @db.SmallInt
  audespExecucaoContrato Int?                     @db.SmallInt
  empenho                empenhos                 @relation(fields: [idEmpenho], references: [id], onDelete: NoAction, onUpdate: NoAction)
  liquidacoes_audit      liquidacoes_audit[]
  liquidacoes_documentos liquidacoes_documentos[]
  liquidacoes_itens      liquidacoes_itens?

  @@unique([exercicio, numero])
  @@index([idEmpenho])
  @@index([marca_documento])
}

model liquidacoes_documentos {
  id                  Int         @id @default(autoincrement())
  idLiquidacao        Int
  numeroDocumento     String      @db.VarChar(50)
  dataEmissao         DateTime    @db.Date
  dataRecebimento     DateTime    @db.Date
  dataMaterialServico DateTime?   @db.Date
  valorDocumento      Decimal     @db.Decimal(18, 3)
  nomeArquivo         String?     @db.VarChar(255)
  caminhoArquivo      String?     @db.VarChar(500)
  tamanho             Int?
  dataUpload          DateTime?   @default(now()) @db.Timestamp(6)
  ativo               Boolean     @default(true)
  liquidacao          liquidacoes @relation(fields: [idLiquidacao], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([idLiquidacao, numeroDocumento])
  @@index([ativo])
}

model liquidacoes_itens {
  id           Int         @id @default(autoincrement())
  idLiquidacao Int         @unique
  // Empenho monthly quotas (for reference/validation only)
  empenhoJan   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoFev   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoMar   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoAbr   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoMai   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoJun   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoJul   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoAgo   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoSet   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoOut   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoNov   Decimal     @default(0) @db.Decimal(18, 4)
  empenhoDez   Decimal     @default(0) @db.Decimal(18, 4)
  // Liquidation monthly distribution (for AUDESP compliance)
  usarJan      Decimal     @default(0) @db.Decimal(18, 4)
  usarFev      Decimal     @default(0) @db.Decimal(18, 4)
  usarMar      Decimal     @default(0) @db.Decimal(18, 4)
  usarAbr      Decimal     @default(0) @db.Decimal(18, 4)
  usarMai      Decimal     @default(0) @db.Decimal(18, 4)
  usarJun      Decimal     @default(0) @db.Decimal(18, 4)
  usarJul      Decimal     @default(0) @db.Decimal(18, 4)
  usarAgo      Decimal     @default(0) @db.Decimal(18, 4)
  usarSet      Decimal     @default(0) @db.Decimal(18, 4)
  usarOut      Decimal     @default(0) @db.Decimal(18, 4)
  usarNov      Decimal     @default(0) @db.Decimal(18, 4)
  usarDez      Decimal     @default(0) @db.Decimal(18, 4)
  liquidacao   liquidacoes @relation(fields: [idLiquidacao], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([idLiquidacao])
}

model liquidacoes_audit {
  id           Int           @id @default(autoincrement())
  idLiquidacao Int
  idUsuario    Int
  acao         Int           @db.SmallInt
  ip           String        @db.Inet
  data         DateTime      @default(now()) @db.Timestamp(6)
  obs          String?
  liquidacao   liquidacoes   @relation(fields: [idLiquidacao], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario      user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model protocolos {
  id               Int                @id @default(autoincrement())
  exercicio        Int                @db.SmallInt
  numero           Int
  status           Int                @db.SmallInt
  tipo             Int                @db.SmallInt
  resumo           String?
  obs              String?
  idReserva        Int?
  numeroEmpenho    Int?
  exercicioEmpenho Int?
  numeroAF         Int?
  exercicioAF      Int?
  dataAbertura     DateTime           @default(now()) @db.Timestamp(6)
  dataStatus       DateTime           @default(now()) @db.Timestamp(6)
  ativo            Boolean            @default(true)
  reserva          reservas?          @relation(fields: [idReserva], references: [id], onDelete: NoAction, onUpdate: NoAction)
  protocolos_audit protocolos_audit[]

  @@unique([exercicio, numero])
  @@index([idReserva])
}

model protocolos_audit {
  id          Int           @id @default(autoincrement())
  idProtocolo Int
  idUsuario   Int
  deStatus    Int           @db.SmallInt
  paraStatus  Int           @db.SmallInt
  ip          String        @db.Inet
  data        DateTime      @default(now()) @db.Timestamp(6)
  obs         String?
  protocolo   protocolos    @relation(fields: [idProtocolo], references: [id], onDelete: NoAction, onUpdate: NoAction)
  usuario     user_profiles @relation(fields: [idUsuario], references: [id], onDelete: NoAction, onUpdate: NoAction)
}
