'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Filter, X, FileText } from 'lucide-react';
import { StatusEmpenho } from '@/lib/enums';

interface FiltrosEmpenhoProps {
  onFilterChange: (filters: EmpenhoFilters) => void;
  initialFilters?: Partial<EmpenhoFilters>;
}

interface EmpenhoFilters {
  empenhoDe?: number;
  empenhoAte?: number;
  dataDe?: string;
  dataAte?: string;
  fornecedor?: string;
  status?: keyof typeof StatusEmpenho;
  apenasSemProtocolo?: boolean;
  apenasComProtocolo?: boolean;
  apenasComSaldo?: boolean;
}

export function FiltrosEmpenho({
  onFilterChange,
  initialFilters,
}: FiltrosEmpenhoProps) {
  const [filters, setFilters] = useState<EmpenhoFilters>({
    empenhoDe: initialFilters?.empenhoDe,
    empenhoAte: initialFilters?.empenhoAte,
    dataDe: initialFilters?.dataDe,
    dataAte: initialFilters?.dataAte,
    fornecedor: initialFilters?.fornecedor,
    status: initialFilters?.status,
    apenasSemProtocolo: initialFilters?.apenasSemProtocolo,
    apenasComProtocolo: initialFilters?.apenasComProtocolo,
    apenasComSaldo: initialFilters?.apenasComSaldo,
  });

  const handleFilterChange = (key: keyof EmpenhoFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };

    // Clear conflicting filters
    if (key === 'apenasSemProtocolo' && value) {
      newFilters.apenasComProtocolo = false;
    }
    if (key === 'apenasComProtocolo' && value) {
      newFilters.apenasSemProtocolo = false;
    }

    setFilters(newFilters);
  };

  const handleApply = () => {
    onFilterChange(filters);
  };

  const handleClear = () => {
    const clearedFilters: EmpenhoFilters = {};
    setFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) => value !== undefined && value !== false
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <FileText className='h-5 w-5' />
          Filtros de Empenhos
          {hasActiveFilters && (
            <Badge variant='secondary' className='text-xs'>
              Ativos
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label htmlFor='empenhoDe'>Empenho De</Label>
            <Input
              id='empenhoDe'
              type='number'
              placeholder='Nº Empenho'
              value={filters.empenhoDe || ''}
              onChange={(e) =>
                handleFilterChange(
                  'empenhoDe',
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='empenhoAte'>Empenho Até</Label>
            <Input
              id='empenhoAte'
              type='number'
              placeholder='Nº Empenho'
              value={filters.empenhoAte || ''}
              onChange={(e) =>
                handleFilterChange(
                  'empenhoAte',
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
            />
          </div>
        </div>

        <div className='grid grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label htmlFor='dataDe'>Data De</Label>
            <Input
              id='dataDe'
              type='date'
              value={filters.dataDe || ''}
              onChange={(e) =>
                handleFilterChange('dataDe', e.target.value || undefined)
              }
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='dataAte'>Data Até</Label>
            <Input
              id='dataAte'
              type='date'
              value={filters.dataAte || ''}
              onChange={(e) =>
                handleFilterChange('dataAte', e.target.value || undefined)
              }
            />
          </div>
        </div>

        <div className='space-y-2'>
          <Label htmlFor='fornecedor'>Fornecedor</Label>
          <Input
            id='fornecedor'
            type='text'
            placeholder='Nome do fornecedor'
            value={filters.fornecedor || ''}
            onChange={(e) =>
              handleFilterChange('fornecedor', e.target.value || undefined)
            }
          />
        </div>

        <div className='space-y-2'>
          <Label htmlFor='status'>Status</Label>
          <select
            id='status'
            className='w-full rounded-md border border-gray-300 p-2'
            value={filters.status || ''}
            onChange={(e) =>
              handleFilterChange(
                'status',
                (e.target.value as keyof typeof StatusEmpenho) || undefined
              )
            }
          >
            <option value=''>Todos os status</option>
            {Object.entries(StatusEmpenho).map(([key, value]) => (
              <option key={key} value={key}>
                {value}
              </option>
            ))}
          </select>
        </div>

        <div className='space-y-3'>
          <Label className='text-sm font-medium'>Filtros Específicos</Label>
          <div className='space-y-2'>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='comProtocolo'
                checked={filters.apenasComProtocolo}
                onCheckedChange={(checked) =>
                  handleFilterChange('apenasComProtocolo', checked)
                }
              />
              <Label htmlFor='comProtocolo' className='text-sm'>
                Apenas com protocolo
              </Label>
            </div>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='semProtocolo'
                checked={filters.apenasSemProtocolo}
                onCheckedChange={(checked) =>
                  handleFilterChange('apenasSemProtocolo', checked)
                }
              />
              <Label htmlFor='semProtocolo' className='text-sm'>
                Apenas sem protocolo
              </Label>
            </div>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='comSaldo'
                checked={filters.apenasComSaldo}
                onCheckedChange={(checked) =>
                  handleFilterChange('apenasComSaldo', checked)
                }
              />
              <Label htmlFor='comSaldo' className='text-sm'>
                Apenas com saldo disponível
              </Label>
            </div>
          </div>
        </div>

        <div className='flex gap-2'>
          <Button onClick={handleApply} className='flex-1'>
            <Filter className='mr-2 h-4 w-4' />
            Aplicar Filtros
          </Button>
          <Button variant='outline' onClick={handleClear}>
            <X className='mr-2 h-4 w-4' />
            Limpar
          </Button>
        </div>

        {hasActiveFilters && (
          <div className='mt-4 rounded-lg bg-blue-50 p-3'>
            <div className='text-sm text-blue-800'>
              <div className='mb-1 font-medium'>Filtros ativos:</div>
              <div className='space-y-1'>
                {filters.empenhoDe && (
                  <div>• Empenho De: {filters.empenhoDe}</div>
                )}
                {filters.empenhoAte && (
                  <div>• Empenho Até: {filters.empenhoAte}</div>
                )}
                {filters.dataDe && (
                  <div>
                    • Data De:{' '}
                    {new Date(filters.dataDe).toLocaleDateString('pt-BR')}
                  </div>
                )}
                {filters.dataAte && (
                  <div>
                    • Data Até:{' '}
                    {new Date(filters.dataAte).toLocaleDateString('pt-BR')}
                  </div>
                )}
                {filters.fornecedor && (
                  <div>• Fornecedor: {filters.fornecedor}</div>
                )}
                {filters.status && (
                  <div>• Status: {StatusEmpenho[filters.status]}</div>
                )}
                {filters.apenasComProtocolo && (
                  <div>• Apenas com protocolo</div>
                )}
                {filters.apenasSemProtocolo && (
                  <div>• Apenas sem protocolo</div>
                )}
                {filters.apenasComSaldo && (
                  <div>• Apenas com saldo disponível</div>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
