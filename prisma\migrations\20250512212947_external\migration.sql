-- CreateTable
CREATE TABLE "external_users_audit" (
    "id" SERIAL NOT NULL,
    "usuarioId" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "external_users_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "external_users_audit" ADD CONSTRAINT "external_users_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "external_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
