import { ErrorAlert } from '@/components/error-alert';
import AfsDatatable from './afsDataTable';
import { listarAFs } from '@/lib/database/movimento/afs';

export default async function AfsDatatableWrapper() {
  const result = await listarAFs();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'AFs não encontradas.'} />;

  return <AfsDatatable data={result} />;
}
