-- CreateTable
CREATE TABLE "acessos_audit" (
    "id" SERIAL NOT NULL,
    "usuarioId" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "acessos_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "acessos_audit" ADD CONSTRAINT "acessos_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
