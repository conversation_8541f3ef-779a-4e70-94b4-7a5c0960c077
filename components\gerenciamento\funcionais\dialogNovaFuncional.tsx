'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { criarFuncional } from '@/lib/database/gerenciamento/funcionais';
import { uppercaseMask, toastAlgoDeuErrado, funcionalMask } from '@/lib/utils';
import { criarFuncionalSchema } from '@/lib/validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, PlusCircle } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

export function DialogNovaFuncional() {
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof criarFuncionalSchema>>({
    resolver: zodResolver(criarFuncionalSchema),
    defaultValues: {
      codigo: '',
      desc: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof criarFuncionalSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof criarFuncionalSchema> = {
        codigo: values.codigo,
        desc: values.desc,
      };
      const res = await criarFuncional(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Funcional criada.');
        setOpen(false);
        setLoading(false);
        form.reset();
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          Nova Funcional <PlusCircle className='ml-2 h-4 w-4' />
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Nova Funcional</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex flex-wrap gap-4 py-4'>
              <FormField
                control={form.control}
                name='codigo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Código
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='00 000 0000 0000'
                        maxLength={16}
                        minLength={16}
                        className='w-[135px]'
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('codigo', funcionalMask(value));
                        }}
                        onKeyDown={(event) => {
                          if (event.key === 'Enter') {
                            event.preventDefault();
                            form.handleSubmit(onSubmit)();
                          }
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='desc'
                render={({ field }) => (
                  <FormItem className='w-[315px]'>
                    <FormLabel className='flex w-full text-left'>
                      Descrição
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={150}
                        minLength={2}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('desc', uppercaseMask(value));
                        }}
                        onKeyDown={(event) => {
                          if (event.key === 'Enter') {
                            event.preventDefault();
                            form.handleSubmit(onSubmit)();
                          }
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogFooter>
          <Button
            type='submit'
            disabled={loading}
            onClick={form.handleSubmit(onSubmit)}
          >
            {loading ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' /> Criar
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
