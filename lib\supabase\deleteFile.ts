// "use server"
// import { isUserGerente } from "../database/usuarios";
// import { createSuperClient } from "./server";

// export const deleteFileFromSupabase = async (path: string) => {
//     try {
//         const isGerente = await isUserGerente();
//         if (isGerente.error || !isGerente.data) {
//           return {
//             error: `Usuário sem permissão para essa ação.`,
//           };
//         }
//         const supabase = createSuperClient();
//         const resultado = await supabase.storage
//         .from('dev-arquivos')
//         .remove([path]);
//         if (resultado.error) {
//           console.log(resultado.error)
//           return {
//             error: `Erro ao deletar arquivo. Por favor tente novamente mais tarde.`,
//           };
//         }
//         return {
//           data: resultado.data,
//         };
//       } catch (e) {
//         console.log(e);
//         return {
//           error: `Erro ao deletar arquivo. Por favor tente novamente mais tarde.`,
//         };
//       }
// }
