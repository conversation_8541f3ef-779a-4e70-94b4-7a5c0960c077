import { ErrorAlert } from '@/components/error-alert';
import { listarAssinaturasReservasPendentes } from '@/lib/database/assinatura/reservas';
import AssinaturaReservasDatatable from './assinaturaReservasDataTable';

export default async function AssinaturaReservasDatatableWrapper() {
  const result = await listarAssinaturasReservasPendentes();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Assinaturas pendentes não encontradas.'} />;

  return <AssinaturaReservasDatatable data={result} />;
}
