'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Printer } from 'lucide-react';
import { toast } from 'sonner';

interface BotaoImprimirProtocolosProps {
  selectedIds: number[];
  onPrint: (ids: number[], options: PrintOptions) => void;
}

interface PrintOptions {
  tipo: 'individual' | 'consolidado';
  incluirHistorico: boolean;
  incluirDetalhes: boolean;
}

export function BotaoImprimirProtocolos({
  selectedIds,
  onPrint,
}: BotaoImprimirProtocolosProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [printOptions, setPrintOptions] = useState<PrintOptions>({
    tipo: 'individual',
    incluirHistorico: true,
    incluirDetalhes: true,
  });

  const handlePrint = async () => {
    if (selectedIds.length === 0) {
      toast.error('Selecione pelo menos um protocolo para imprimir');
      return;
    }

    setIsLoading(true);
    try {
      await onPrint(selectedIds, printOptions);
      setIsOpen(false);
      toast.success('Solicitação de impressão enviada');
    } catch (error) {
      toast.error('Erro ao gerar impressão');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        variant='outline'
        size='sm'
        onClick={() => setIsOpen(true)}
        disabled={selectedIds.length === 0}
      >
        <Printer className='mr-2 h-4 w-4' />
        Imprimir ({selectedIds.length})
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>Opções de Impressão</DialogTitle>
          </DialogHeader>
          <div className='space-y-4'>
            <div className='space-y-3'>
              <div>
                <Label className='text-sm font-medium'>Tipo de Impressão</Label>
                <RadioGroup
                  value={printOptions.tipo}
                  onValueChange={(value) =>
                    setPrintOptions((prev) => ({
                      ...prev,
                      tipo: value as 'individual' | 'consolidado',
                    }))
                  }
                  className='mt-2'
                >
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='individual' id='individual' />
                    <Label htmlFor='individual'>Relatórios individuais</Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='consolidado' id='consolidado' />
                    <Label htmlFor='consolidado'>Relatório consolidado</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className='space-y-2'>
                <Label className='text-sm font-medium'>
                  Conteúdo do Relatório
                </Label>
                <div className='space-y-2'>
                  <div className='flex items-center space-x-2'>
                    <Checkbox
                      id='historico'
                      checked={printOptions.incluirHistorico}
                      onCheckedChange={(checked) =>
                        setPrintOptions((prev) => ({
                          ...prev,
                          incluirHistorico: checked as boolean,
                        }))
                      }
                    />
                    <Label htmlFor='historico' className='text-sm'>
                      Incluir histórico
                    </Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Checkbox
                      id='detalhes'
                      checked={printOptions.incluirDetalhes}
                      onCheckedChange={(checked) =>
                        setPrintOptions((prev) => ({
                          ...prev,
                          incluirDetalhes: checked as boolean,
                        }))
                      }
                    />
                    <Label htmlFor='detalhes' className='text-sm'>
                      Incluir detalhes completos
                    </Label>
                  </div>
                </div>
              </div>
            </div>

            <div className='rounded-lg bg-gray-50 p-3'>
              <div className='text-sm text-gray-600'>
                <p className='mb-1 font-medium'>Resumo da impressão:</p>
                <ul className='space-y-1 text-xs'>
                  <li>• {selectedIds.length} protocolo(s) selecionado(s)</li>
                  <li>
                    • Tipo:{' '}
                    {printOptions.tipo === 'individual'
                      ? 'Relatórios individuais'
                      : 'Relatório consolidado'}
                  </li>
                  <li>
                    • Histórico:{' '}
                    {printOptions.incluirHistorico
                      ? 'Incluído'
                      : 'Não incluído'}
                  </li>
                  <li>
                    • Detalhes:{' '}
                    {printOptions.incluirDetalhes ? 'Completos' : 'Básicos'}
                  </li>
                </ul>
              </div>
            </div>

            <div className='flex justify-end gap-2'>
              <Button
                variant='outline'
                onClick={() => setIsOpen(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button
                onClick={handlePrint}
                disabled={isLoading || selectedIds.length === 0}
              >
                <Printer className='mr-2 h-4 w-4' />
                {isLoading ? 'Gerando...' : 'Imprimir'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
