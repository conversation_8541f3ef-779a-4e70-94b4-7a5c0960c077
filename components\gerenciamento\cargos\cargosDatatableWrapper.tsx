import { ErrorAlert } from '@/components/error-alert';
import { listarCargos } from '@/lib/database/gerenciamento/cargos';
import CargosDatatable from './cargosDataTable';

export default async function CargosDatatableWrapper() {
  const result = await listarCargos();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Cargos não encontrados.'} />;

  return <CargosDatatable data={result} />;
}
