'use server';

import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { temPermissao } from '../usuarios';
import { Modulos } from '@/lib/modulos';
import { Permisso<PERSON> } from '@/lib/enums';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { DespesaReportError } from '@/lib/errors/despesa-error';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';

// Function to calculate reservation balance (HAVING clause equivalent)
async function calcularSaldoReserva(
  reservaId: number,
  exercicio: number
): Promise<number> {
  const empenhos = await prisma.empenhos.findMany({
    where: {
      idReserva: reservaId,
      exercicio,
      ativo: true,
    },
    select: {
      valorTotal: true,
    },
  });

  const totalUsado = empenhos.reduce(
    (sum, emp) =>
      currency(sum, currencyOptionsNoSymbol)
        .add(parseFloat(emp.valorTotal.toString()))
        .value,
    0
  );
  return totalUsado;
}

// Function to process reservations with balance filtering (HAVING clause equivalent)
async function processarReservasComFiltroSaldo(
  exercicio: number,
  despesa?: number
): Promise<RelatorioDespesaItem[]> {
  // Get all active reservations
  const reservas = await prisma.reservas.findMany({
    where: {
      exercicio,
      ...(despesa && {
        dotacao: {
          despesa: despesa,
        },
      }),
      status: { in: [1, 2, 3] }, // Active statuses
    },
    include: {
      dotacao: {
        include: {
          secretaria: true,
          departamento: true,
          subdepartamento: true,
          economica: true,
        },
      },
    },
    orderBy: { data: 'asc' },
  });

  // Filter reservations with positive balance (HAVING clause equivalent)
  const reservasComSaldo = await Promise.all(
    reservas.map(async (reserva) => {
      const valorTotalReserva = currency(
        calcularValorTotalMensalReserva(reserva),
        currencyOptionsNoSymbol
      );
      const totalUsado = await calcularSaldoReserva(reserva.id, exercicio);
      const saldo = valorTotalReserva.subtract(totalUsado).value;

      return { reserva, saldo };
    })
  );

  // Apply the HAVING clause filter: saldo > 0
  const reservasFiltradas = reservasComSaldo.filter(({ saldo }) => saldo > 0);

  // Convert to RelatorioDespesaItem format
  return reservasFiltradas.map(({ reserva, saldo }) => {
    const valorTotal = currency(
      calcularValorTotalMensalReserva(reserva),
      currencyOptionsNoSymbol
    );
    const percentualUtilizado = valorTotal.value > 0
      ? valorTotal.subtract(saldo).divide(valorTotal).multiply(100).value
      : 0;

    return {
      id: reserva.id,
      data: formatarData(reserva.data),
      dataOrdem: formatarDataOrdem(reserva.data),
      reserva: reserva.id,
      historico: reserva.resumo || '',
      valor: valorTotal.value,
      valoresMensais: {
        jan: currency(parseFloat(reserva.usarMes1.toString()), currencyOptionsNoSymbol).value,
        fev: currency(parseFloat(reserva.usarMes2.toString()), currencyOptionsNoSymbol).value,
        mar: currency(parseFloat(reserva.usarMes3.toString()), currencyOptionsNoSymbol).value,
        abr: currency(parseFloat(reserva.usarMes4.toString()), currencyOptionsNoSymbol).value,
        mai: currency(parseFloat(reserva.usarMes5.toString()), currencyOptionsNoSymbol).value,
        jun: currency(parseFloat(reserva.usarMes6.toString()), currencyOptionsNoSymbol).value,
        jul: currency(parseFloat(reserva.usarMes7.toString()), currencyOptionsNoSymbol).value,
        ago: currency(parseFloat(reserva.usarMes8.toString()), currencyOptionsNoSymbol).value,
        set: currency(parseFloat(reserva.usarMes9.toString()), currencyOptionsNoSymbol).value,
        out: currency(parseFloat(reserva.usarMes10.toString()), currencyOptionsNoSymbol).value,
        nov: currency(parseFloat(reserva.usarMes11.toString()), currencyOptionsNoSymbol).value,
        dez: currency(parseFloat(reserva.usarMes12.toString()), currencyOptionsNoSymbol).value,
      },
      valorTotalMensal: valorTotal.value,
      saldoReserva: currency(saldo, currencyOptionsNoSymbol).value,
      percentualUtilizado: currency(percentualUtilizado, currencyOptionsNoSymbol).value,
      tipo: 'reserva' as const,
    };
  });
}

// Interface para alterações orçamentárias
interface AlteracaoOrcamentariaData {
  id: number;
  tipoAlteracao: number; // 1=suplementação, 2=anulação
  valorTotal: number;
  status: number; // 1=pending, 2=approved
  dataDecreto?: Date;
  valoresMensais: {
    jan: number;
    fev: number;
    mar: number;
    abr: number;
    mai: number;
    jun: number;
    jul: number;
    ago: number;
    set: number;
    out: number;
    nov: number;
    dez: number;
  };
}

// Schema para validação dos parâmetros do relatório
const relatorioDespesaSchema = z.object({
  exercicio: z.number().int().positive(),
  despesa: z.number().int().positive().optional(),
  tipo: z.enum(['1', '2', '3']).default('1'), // 1=detalhado, 2=valores, 3=resumo
  bearer: z.string().optional(),
});

// Schema para validação do relatório de despesa pessoal
const relatorioDespesaPessoalSchema = z.object({
  exercicio: z.number().int().positive(),
  bearer: z.string().optional(),
});

// Interface para o item do relatório de despesas
export interface RelatorioDespesaItem {
  id: number;
  data: string;
  dataOrdem: string;
  reserva?: number;
  empenho?: number;
  fornecedor?: {
    id: number;
    nome: string;
  };
  historico: string;
  valor: number;
  valoresMensais: {
    jan: number;
    fev: number;
    mar: number;
    abr: number;
    mai: number;
    jun: number;
    jul: number;
    ago: number;
    set: number;
    out: number;
    nov: number;
    dez: number;
  };
  valorTotalMensal: number;
  saldoReserva?: number; // Saldo restante para reservas parcialmente utilizadas
  percentualUtilizado?: number; // Percentual utilizado da reserva
  valoresMensaisEstorno?: {
    jan: number;
    fev: number;
    mar: number;
    abr: number;
    mai: number;
    jun: number;
    jul: number;
    ago: number;
    set: number;
    out: number;
    nov: number;
    dez: number;
  };
  tipo:
    | 'reserva'
    | 'empenho'
    | 'estorno'
    | 'transferencia'
    | 'decreto'
    | 'emenda';
}

// Interface para o resumo de despesa pessoal
export interface DespesaPessoalItem {
  departamento: string;
  codigoDepartamento: string;
  despesa: number;
  despesaDescricao: string; // Descrição completa da despesa
  dotacaoInicial: number; // Dotação inicial sem alterações
  suplementacoes: number; // Total de suplementações
  anulações: number; // Total de anulações
  dotacaoAtual: number; // Dotação atual (inicial - anulações + suplementações)
  valoresLiquidados: {
    jan: number;
    fev: number;
    mar: number;
    abr: number;
    mai: number;
    jun: number;
    jul: number;
    ago: number;
    set: number;
    out: number;
    nov: number;
    dez: number;
  };
  totalLiquidado: number;
  saldo: number; // Saldo disponível
}

// Função principal para obter relatório de despesas
export async function obterRelatorioDespesa(params: unknown) {
  try {
    const parsedParams = relatorioDespesaSchema.safeParse(params);
    if (!parsedParams.success) {
      throw new DespesaReportError(
        'Parâmetros inválidos.',
        'INVALID_PARAMETERS',
        parsedParams.error
      );
    }

    const { exercicio, despesa, tipo, bearer } = parsedParams.data;

    // Validar exercício
    if (!exercicio || exercicio < 2000 || exercicio > 2100) {
      throw new DespesaReportError('Exercício inválido.', 'INVALID_EXERCICIO', {
        exercicio,
      });
    }

    // Verificar permissões
    const parametrosPermissao = {
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
      permissao: Permissoes.ACESSAR,
      retornarExercicioUsuario: true,
    };

    const result = await temPermissao({
      ...parametrosPermissao,
      bearer: bearer || '',
    });
    if (!result) {
      throw new DespesaReportError(
        'Usuário sem permissão para acessar relatórios.',
        'PERMISSION_DENIED'
      );
    }

    switch (tipo) {
      case '1': // Relatório detalhado
        return await obterRelatorioDespesaDetalhado(exercicio, despesa);
      case '2': // Valores brutos
        return await obterRelatorioDespesaValores(exercicio, despesa);
      case '3': // Resumo por tipo
        return await obterRelatorioDespesaResumo(exercicio, despesa);
      default:
        throw new DespesaReportError(
          'Tipo de relatório inválido.',
          'INVALID_REPORT_TYPE',
          { tipo }
        );
    }
  } catch (error: any) {
    console.error('Erro ao gerar relatório de despesas:', error);

    if (error instanceof DespesaReportError) {
      return {
        error: error.message,
        code: error.code,
        details:
          process.env.NODE_ENV === 'development' ? error.details : undefined,
      };
    }

    return {
      error: toastAlgoDeuErrado,
      code: 'INTERNAL_ERROR',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    };
  }
}

// Função para obter relatório detalhado de despesas
async function obterRelatorioDespesaDetalhado(
  exercicio: number,
  despesa?: number
) {
  try {
    const itens: RelatorioDespesaItem[] = [];

    // 1. Buscar todos os dados necessários em paralelo com tratamento de erro
    const [
      empenhosComReservas,
      transferenciasCotas,
      estornos,
      empenhosPorReserva,
    ] = await Promise.all([
      // Empenhos vinculados a reservas
      prisma.empenhos.findMany({
        where: {
          exercicio,
          ...(despesa && {
            dotacao: {
              despesa: despesa,
            },
          }),
          idReserva: { not: null },
          ativo: true,
        },
        include: {
          reserva: true,
          fornecedor: true,
          dotacao: {
            include: {
              secretaria: true,
              departamento: true,
              subdepartamento: true,
              economica: true,
            },
          },
        },
        orderBy: { data: 'asc' },
      }),
      // Transferências de cotas
      prisma.cotasReducao.findMany({
        where: {
          dotacao: {
            exercicio,
            ...(despesa && { despesa: despesa }),
          },
        },
        include: {
          dotacao: {
            include: {
              secretaria: true,
              departamento: true,
              subdepartamento: true,
              economica: true,
            },
          },
        },
        orderBy: { data: 'asc' },
      }),
      // Estornos de empenhos
      prisma.empenhos_anulacoes.findMany({
        where: {
          empenho: {
            exercicio,
            ...(despesa && {
              dotacao: {
                despesa: despesa,
              },
            }),
            ativo: true,
          },
          ativo: true,
        },
        include: {
          empenho: {
            include: {
              fornecedor: true,
              dotacao: {
                include: {
                  secretaria: true,
                  departamento: true,
                  subdepartamento: true,
                },
              },
            },
          },
        },
        orderBy: { data: 'asc' },
      }),
      // Todos os empenhos para cálculo de saldos
      prisma.empenhos.findMany({
        where: {
          exercicio,
          ...(despesa && {
            dotacao: {
              despesa: despesa,
            },
          }),
          ativo: true,
        },
        select: {
          idReserva: true,
          valorTotal: true,
        },
      }),
    ]);

    // Calcular valores utilizados por reserva
    const usadosPorReserva = empenhosPorReserva.reduce(
      (acc, emp) => {
        if (emp.idReserva) {
          acc[emp.idReserva] = currency(
            acc[emp.idReserva] || 0,
            currencyOptionsNoSymbol
          )
            .add(parseFloat(emp.valorTotal.toString()))
            .value;
        }
        return acc;
      },
      {} as Record<number, number>
    );

    // Processar empenhos vinculados a reservas
    for (const empenho of empenhosComReservas) {
      const valorUsado = usadosPorReserva[empenho.idReserva!] || 0;
      const valorReserva = calcularValorTotalMensalReserva(empenho.reserva!);
      const saldoReserva = currency(valorReserva, currencyOptionsNoSymbol)
        .subtract(valorUsado)
        .value;
      const percentualUtilizado = valorReserva > 0
        ? currency(valorUsado, currencyOptionsNoSymbol)
            .divide(valorReserva)
            .multiply(100)
            .value
        : 0;

      itens.push({
        id: empenho.id,
        data: formatarData(empenho.data),
        dataOrdem: formatarDataOrdem(empenho.data),
        reserva: empenho.idReserva || undefined,
        empenho: empenho.numero,
        fornecedor: empenho.fornecedor
          ? { id: empenho.fornecedor.id, nome: empenho.fornecedor.nome }
          : undefined,
        historico: empenho.resumo || '',
        valor: currency(parseFloat(empenho.valorTotal.toString()), currencyOptionsNoSymbol).value,
        valoresMensais: {
          jan: currency(parseFloat(empenho.usarMes1.toString()), currencyOptionsNoSymbol).value,
          fev: currency(parseFloat(empenho.usarMes2.toString()), currencyOptionsNoSymbol).value,
          mar: currency(parseFloat(empenho.usarMes3.toString()), currencyOptionsNoSymbol).value,
          abr: currency(parseFloat(empenho.usarMes4.toString()), currencyOptionsNoSymbol).value,
          mai: currency(parseFloat(empenho.usarMes5.toString()), currencyOptionsNoSymbol).value,
          jun: currency(parseFloat(empenho.usarMes6.toString()), currencyOptionsNoSymbol).value,
          jul: currency(parseFloat(empenho.usarMes7.toString()), currencyOptionsNoSymbol).value,
          ago: currency(parseFloat(empenho.usarMes8.toString()), currencyOptionsNoSymbol).value,
          set: currency(parseFloat(empenho.usarMes9.toString()), currencyOptionsNoSymbol).value,
          out: currency(parseFloat(empenho.usarMes10.toString()), currencyOptionsNoSymbol).value,
          nov: currency(parseFloat(empenho.usarMes11.toString()), currencyOptionsNoSymbol).value,
          dez: currency(parseFloat(empenho.usarMes12.toString()), currencyOptionsNoSymbol).value,
        },
        valorTotalMensal: currency(calcularValorTotalMensal(empenho), currencyOptionsNoSymbol).value,
        saldoReserva: currency(saldoReserva, currencyOptionsNoSymbol).value,
        percentualUtilizado: currency(percentualUtilizado, currencyOptionsNoSymbol).value,
        tipo: 'empenho',
      });
    }

    // Processar reservas com filtro de saldo (HAVING clause)
    const reservasFiltradas = await processarReservasComFiltroSaldo(
      exercicio,
      despesa
    );
    itens.push(...reservasFiltradas);

    // Processar transferências de cotas (processado apenas uma vez)
    for (const transferencia of transferenciasCotas) {
      itens.push({
        id: transferencia.id,
        data: formatarData(transferencia.data),
        dataOrdem: formatarDataOrdem(transferencia.data),
        historico: transferencia.motivo,
        valor: currency(parseFloat(transferencia.valorTotal.toString()), currencyOptionsNoSymbol).value,
        valoresMensais: {
          jan: currency(parseFloat(transferencia.valorMes1.toString()), currencyOptionsNoSymbol).value,
          fev: currency(parseFloat(transferencia.valorMes2.toString()), currencyOptionsNoSymbol).value,
          mar: currency(parseFloat(transferencia.valorMes3.toString()), currencyOptionsNoSymbol).value,
          abr: currency(parseFloat(transferencia.valorMes4.toString()), currencyOptionsNoSymbol).value,
          mai: currency(parseFloat(transferencia.valorMes5.toString()), currencyOptionsNoSymbol).value,
          jun: currency(parseFloat(transferencia.valorMes6.toString()), currencyOptionsNoSymbol).value,
          jul: currency(parseFloat(transferencia.valorMes7.toString()), currencyOptionsNoSymbol).value,
          ago: currency(parseFloat(transferencia.valorMes8.toString()), currencyOptionsNoSymbol).value,
          set: currency(parseFloat(transferencia.valorMes9.toString()), currencyOptionsNoSymbol).value,
          out: currency(parseFloat(transferencia.valorMes10.toString()), currencyOptionsNoSymbol).value,
          nov: currency(parseFloat(transferencia.valorMes11.toString()), currencyOptionsNoSymbol).value,
          dez: currency(parseFloat(transferencia.valorMes12.toString()), currencyOptionsNoSymbol).value,
        },
        valorTotalMensal: currency(parseFloat(transferencia.valorTotal.toString()), currencyOptionsNoSymbol).value,
        tipo: transferencia.reducao ? 'transferencia' : 'emenda',
      });
    }

    // Processar estornos de empenhos (processado apenas uma vez)
    for (const estorno of estornos) {
      const valorAnulado = currency(
        parseFloat(estorno.valorAnulado.toString()),
        currencyOptionsNoSymbol
      ).value;
      const valoresMensaisEstorno = calcularDistribuicaoMensalEstorno(
        estorno.empenho,
        valorAnulado
      );

      itens.push({
        id: estorno.id,
        data: formatarData(estorno.data),
        dataOrdem: formatarDataOrdem(estorno.data),
        empenho: estorno.empenho.numero,
        fornecedor: estorno.empenho.fornecedor
          ? {
              id: estorno.empenho.fornecedor.id,
              nome: estorno.empenho.fornecedor.nome,
            }
          : undefined,
        historico: `ESTORNO: ${estorno.motivo}`,
        valor: -valorAnulado,
        valoresMensais: {
          jan: 0,
          fev: 0,
          mar: 0,
          abr: 0,
          mai: 0,
          jun: 0,
          jul: 0,
          ago: 0,
          set: 0,
          out: 0,
          nov: 0,
          dez: 0,
        },
        valorTotalMensal: -valorAnulado,
        valoresMensaisEstorno: {
          jan: -valoresMensaisEstorno.jan,
          fev: -valoresMensaisEstorno.fev,
          mar: -valoresMensaisEstorno.mar,
          abr: -valoresMensaisEstorno.abr,
          mai: -valoresMensaisEstorno.mai,
          jun: -valoresMensaisEstorno.jun,
          jul: -valoresMensaisEstorno.jul,
          ago: -valoresMensaisEstorno.ago,
          set: -valoresMensaisEstorno.set,
          out: -valoresMensaisEstorno.out,
          nov: -valoresMensaisEstorno.nov,
          dez: -valoresMensaisEstorno.dez,
        },
        tipo: 'estorno',
      });
    }

    // Obter alterações orçamentárias
    const alteracoes = await obterAlteracoesOrcamentarias(exercicio, despesa);

    // Aplicar alterações orçamentárias aos itens
    const itensComAlteracoes = aplicarAlteracoesOrcamentarias(
      itens,
      alteracoes
    );

    // Ordenar itens por data
    itensComAlteracoes.sort((a, b) => a.dataOrdem.localeCompare(b.dataOrdem));

    return { data: itensComAlteracoes };
  } catch (error: any) {
    console.error('Erro ao gerar relatório detalhado de despesas:', error);

    if (error instanceof DespesaReportError) {
      throw error; // Re-throw custom errors
    }

    throw new DespesaReportError(
      'Erro ao processar dados do relatório detalhado.',
      'DETAILED_REPORT_ERROR',
      error
    );
  }
}

// Função para obter relatório de valores brutos
async function obterRelatorioDespesaValores(
  exercicio: number,
  despesa?: number
) {
  // Similar ao detalhado, mas retorna valores brutos sem formatação
  const resultado = await obterRelatorioDespesaDetalhado(exercicio, despesa);

  // Transformar para formato de valores brutos (union approach como no legacy)
  const valoresBrutos = resultado.data.map((item) => ({
    tipo: item.tipo,
    data: item.data,
    valor: item.valor,
    ...item.valoresMensais,
  }));

  return { data: valoresBrutos };
}

// Função para obter resumo por tipo
async function obterRelatorioDespesaResumo(
  exercicio: number,
  despesa?: number
) {
  const resultado = await obterRelatorioDespesaDetalhado(exercicio, despesa);

  // Agrupar por tipo e somar valores
  const resumoPorTipo = resultado.data.reduce(
    (acc, item) => {
      if (!acc[item.tipo]) {
        acc[item.tipo] = {
          quantidade: 0,
          valorTotal: 0,
          valoresMensais: {
            jan: 0,
            fev: 0,
            mar: 0,
            abr: 0,
            mai: 0,
            jun: 0,
            jul: 0,
            ago: 0,
            set: 0,
            out: 0,
            nov: 0,
            dez: 0,
          },
        };
      }

      acc[item.tipo].quantidade++;
      acc[item.tipo].valorTotal = currency(acc[item.tipo].valorTotal, currencyOptionsNoSymbol)
        .add(item.valor)
        .value;

      // Somar valores mensais
      Object.keys(item.valoresMensais).forEach((mes) => {
        const mesKey = mes as keyof typeof item.valoresMensais;
        acc[item.tipo].valoresMensais[mesKey] = currency(
          acc[item.tipo].valoresMensais[mesKey],
          currencyOptionsNoSymbol
        )
          .add(item.valoresMensais[mesKey])
          .value;
      });

      return acc;
    },
    {} as Record<
      string,
      {
        quantidade: number;
        valorTotal: number;
        valoresMensais: {
          jan: number;
          fev: number;
          mar: number;
          abr: number;
          mai: number;
          jun: number;
          jul: number;
          ago: number;
          set: number;
          out: number;
          nov: number;
          dez: number;
        };
      }
    >
  );

  return { data: resumoPorTipo };
}

// Função para obter relatório de despesa pessoal
export async function obterRelatorioDespesaPessoal(params: unknown) {
  try {
    const parsedParams = relatorioDespesaPessoalSchema.safeParse(params);
    if (!parsedParams.success) {
      throw new DespesaReportError(
        'Parâmetros inválidos.',
        'INVALID_PARAMETERS',
        parsedParams.error
      );
    }

    const { exercicio, bearer } = parsedParams.data;

    // Validar exercício
    if (!exercicio || exercicio < 2000 || exercicio > 2100) {
      throw new DespesaReportError('Exercício inválido.', 'INVALID_EXERCICIO', {
        exercicio,
      });
    }

    // Verificar permissões
    const parametrosPermissao = {
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
      permissao: Permissoes.ACESSAR,
      retornarExercicioUsuario: true,
    };

    const result = await temPermissao({
      ...parametrosPermissao,
      bearer: bearer || '',
    });
    if (!result) {
      throw new DespesaReportError(
        'Usuário sem permissão para acessar relatórios.',
        'PERMISSION_DENIED'
      );
    }
    // Buscar dotações com códigos econômicos 3.1.90% e 3.1.91% (como no legacy)
    const dotacoes = await prisma.dotacoes.findMany({
      where: {
        exercicio,
        economica: {
          OR: [
            { codigo: { startsWith: '3.1.90' } },
            { codigo: { startsWith: '3.1.91' } },
          ],
        },
        ativo: true,
      },
      include: {
        secretaria: true,
        departamento: true,
        subdepartamento: true,
        economica: true,
      },
      orderBy: {
        departamento: {
          codigo: 'asc',
        },
      },
    });

    // Buscar todas as alterações orçamentárias para este exercício
    const alteracoesOrcamentarias = await prisma.alteracaoOrcamentaria.findMany(
      {
        where: {
          exercicio,
          ativo: true,
          status: { in: [1, 2] },
        },
        select: {
          despesaAcao: true,
          tipoAlteracao: true, // 1=suplementação, 2=anulação
          valorTotal: true,
        },
      }
    );

    // Calcular totais de suplementações e anulações por despesa
    const alteracoesPorDespesa = alteracoesOrcamentarias.reduce(
      (acc, alt) => {
        if (!acc[alt.despesaAcao]) {
          acc[alt.despesaAcao] = { suplementacoes: 0, anulações: 0 };
        }

        const valor = currency(parseFloat(alt.valorTotal.toString()), currencyOptionsNoSymbol).value;
        if (alt.tipoAlteracao === 1) {
          acc[alt.despesaAcao].suplementacoes = currency(
            acc[alt.despesaAcao].suplementacoes,
            currencyOptionsNoSymbol
          )
            .add(valor)
            .value;
        } else if (alt.tipoAlteracao === 2) {
          acc[alt.despesaAcao].anulações = currency(
            acc[alt.despesaAcao].anulações,
            currencyOptionsNoSymbol
          )
            .add(valor)
            .value;
        }
        return acc;
      },
      {} as Record<number, { suplementacoes: number; anulações: number }>
    );

    const resultado: DespesaPessoalItem[] = [];

    for (const dotacao of dotacoes) {
      // Calcular valores conforme legacy: DOT.V_ATUAL - anulações + suplementações
      const alteracoes = alteracoesPorDespesa[dotacao.despesa] || {
        suplementacoes: 0,
        anulações: 0,
      };
      const dotacaoInicial = currency(
        parseFloat(dotacao.valorInicial.toString()),
        currencyOptionsNoSymbol
      ).value;
      const dotacaoAtual = currency(dotacaoInicial, currencyOptionsNoSymbol)
        .subtract(alteracoes.anulações)
        .add(alteracoes.suplementacoes)
        .value;

      // Buscar valores liquidados por mês (como no legacy)
      const liquidacoes = await prisma.liquidacoes_itens.findMany({
        where: {
          liquidacao: {
            empenho: {
              idDotacao: dotacao.id,
              exercicio,
              ativo: true,
            },
            status: 1, // Apenas liquidações com status 1
          },
        },
      });

      // Somar valores liquidados por mês com precisão
      const valoresLiquidados = {
        jan: 0,
        fev: 0,
        mar: 0,
        abr: 0,
        mai: 0,
        jun: 0,
        jul: 0,
        ago: 0,
        set: 0,
        out: 0,
        nov: 0,
        dez: 0,
      };

      liquidacoes.forEach((liquidacao) => {
        valoresLiquidados.jan = currency(valoresLiquidados.jan, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarJan.toString()))
          .value;
        valoresLiquidados.fev = currency(valoresLiquidados.fev, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarFev.toString()))
          .value;
        valoresLiquidados.mar = currency(valoresLiquidados.mar, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarMar.toString()))
          .value;
        valoresLiquidados.abr = currency(valoresLiquidados.abr, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarAbr.toString()))
          .value;
        valoresLiquidados.mai = currency(valoresLiquidados.mai, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarMai.toString()))
          .value;
        valoresLiquidados.jun = currency(valoresLiquidados.jun, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarJun.toString()))
          .value;
        valoresLiquidados.jul = currency(valoresLiquidados.jul, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarJul.toString()))
          .value;
        valoresLiquidados.ago = currency(valoresLiquidados.ago, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarAgo.toString()))
          .value;
        valoresLiquidados.set = currency(valoresLiquidados.set, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarSet.toString()))
          .value;
        valoresLiquidados.out = currency(valoresLiquidados.out, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarOut.toString()))
          .value;
        valoresLiquidados.nov = currency(valoresLiquidados.nov, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarNov.toString()))
          .value;
        valoresLiquidados.dez = currency(valoresLiquidados.dez, currencyOptionsNoSymbol)
          .add(parseFloat(liquidacao.usarDez.toString()))
          .value;
      });

      // Aplicar precisão decimal aos valores mensais
      Object.keys(valoresLiquidados).forEach((mes) => {
        valoresLiquidados[mes as keyof typeof valoresLiquidados] = currency(
          valoresLiquidados[mes as keyof typeof valoresLiquidados],
          currencyOptionsNoSymbol
        ).value;
      });

      const totalLiquidado = Object.values(valoresLiquidados).reduce(
        (sum, val) => currency(sum, currencyOptionsNoSymbol).add(val).value,
        0
      );

      const saldo = currency(dotacaoAtual, currencyOptionsNoSymbol)
        .subtract(totalLiquidado)
        .value;

      resultado.push({
        departamento: dotacao.departamento?.nome || 'Sem Departamento',
        codigoDepartamento: dotacao.departamento?.codigo?.toString() || '',
        despesa: dotacao.despesa,
        despesaDescricao: dotacao.economica?.desc || '',
        dotacaoInicial,
        suplementacoes: alteracoes.suplementacoes,
        anulações: alteracoes.anulações,
        dotacaoAtual,
        valoresLiquidados,
        totalLiquidado,
        saldo,
      });
    }

    return { data: resultado };
  } catch (error: any) {
    console.error('Erro ao gerar relatório de despesa pessoal:', error);

    if (error instanceof DespesaReportError) {
      return {
        error: error.message,
        code: error.code,
        details:
          process.env.NODE_ENV === 'development' ? error.details : undefined,
      };
    }

    return {
      error: toastAlgoDeuErrado,
      code: 'PERSONAL_REPORT_ERROR',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    };
  }
}

// Funções auxiliares
function formatarData(data: Date): string {
  return data.toLocaleDateString('pt-BR');
}

function formatarDataOrdem(data: Date): string {
  const ano = data.getFullYear();
  const mes = String(data.getMonth() + 1).padStart(2, '0');
  const dia = String(data.getDate()).padStart(2, '0');
  return `${ano}${mes}${dia}`;
}

function calcularValorTotalMensal(empenho: any): number {
  return currency(0, currencyOptionsNoSymbol)
    .add(parseFloat(empenho.usarMes1.toString()))
    .add(parseFloat(empenho.usarMes2.toString()))
    .add(parseFloat(empenho.usarMes3.toString()))
    .add(parseFloat(empenho.usarMes4.toString()))
    .add(parseFloat(empenho.usarMes5.toString()))
    .add(parseFloat(empenho.usarMes6.toString()))
    .add(parseFloat(empenho.usarMes7.toString()))
    .add(parseFloat(empenho.usarMes8.toString()))
    .add(parseFloat(empenho.usarMes9.toString()))
    .add(parseFloat(empenho.usarMes10.toString()))
    .add(parseFloat(empenho.usarMes11.toString()))
    .add(parseFloat(empenho.usarMes12.toString()))
    .value;
}

function calcularValorTotalMensalReserva(reserva: any): number {
  return currency(0, currencyOptionsNoSymbol)
    .add(parseFloat(reserva.usarMes1.toString()))
    .add(parseFloat(reserva.usarMes2.toString()))
    .add(parseFloat(reserva.usarMes3.toString()))
    .add(parseFloat(reserva.usarMes4.toString()))
    .add(parseFloat(reserva.usarMes5.toString()))
    .add(parseFloat(reserva.usarMes6.toString()))
    .add(parseFloat(reserva.usarMes7.toString()))
    .add(parseFloat(reserva.usarMes8.toString()))
    .add(parseFloat(reserva.usarMes9.toString()))
    .add(parseFloat(reserva.usarMes10.toString()))
    .add(parseFloat(reserva.usarMes11.toString()))
    .add(parseFloat(reserva.usarMes12.toString()))
    .value;
}

// Função para obter alterações orçamentárias
async function obterAlteracoesOrcamentarias(
  exercicio: number,
  despesa?: number
): Promise<AlteracaoOrcamentariaData[]> {
  // Buscar suplementações e anulações da tabela alteracaoOrcamentaria
  const alteracoes = await prisma.alteracaoOrcamentaria.findMany({
    where: {
      exercicio,
      ...(despesa && { despesaAcao: despesa }),
      ativo: true,
      status: { in: [1, 2] }, // pending and approved
    },
    select: {
      id: true,
      tipoAlteracao: true,
      valorTotal: true,
      status: true,
      dataDecreto: true,
      valorMes1: true,
      valorMes2: true,
      valorMes3: true,
      valorMes4: true,
      valorMes5: true,
      valorMes6: true,
      valorMes7: true,
      valorMes8: true,
      valorMes9: true,
      valorMes10: true,
      valorMes11: true,
      valorMes12: true,
    },
  });

  return alteracoes.map((alt) => ({
    id: alt.id,
    tipoAlteracao: alt.tipoAlteracao,
    valorTotal: currency(parseFloat(alt.valorTotal.toString()), currencyOptionsNoSymbol).value,
    status: alt.status,
    dataDecreto: alt.dataDecreto || undefined,
    valoresMensais: {
      jan: currency(parseFloat(alt.valorMes1.toString()), currencyOptionsNoSymbol).value,
      fev: currency(parseFloat(alt.valorMes2.toString()), currencyOptionsNoSymbol).value,
      mar: currency(parseFloat(alt.valorMes3.toString()), currencyOptionsNoSymbol).value,
      abr: currency(parseFloat(alt.valorMes4.toString()), currencyOptionsNoSymbol).value,
      mai: currency(parseFloat(alt.valorMes5.toString()), currencyOptionsNoSymbol).value,
      jun: currency(parseFloat(alt.valorMes6.toString()), currencyOptionsNoSymbol).value,
      jul: currency(parseFloat(alt.valorMes7.toString()), currencyOptionsNoSymbol).value,
      ago: currency(parseFloat(alt.valorMes8.toString()), currencyOptionsNoSymbol).value,
      set: currency(parseFloat(alt.valorMes9.toString()), currencyOptionsNoSymbol).value,
      out: currency(parseFloat(alt.valorMes10.toString()), currencyOptionsNoSymbol).value,
      nov: currency(parseFloat(alt.valorMes11.toString()), currencyOptionsNoSymbol).value,
      dez: currency(parseFloat(alt.valorMes12.toString()), currencyOptionsNoSymbol).value,
    },
  }));
}

// Função para aplicar alterações orçamentárias
function aplicarAlteracoesOrcamentarias(
  itens: RelatorioDespesaItem[],
  alteracoes: AlteracaoOrcamentariaData[]
): RelatorioDespesaItem[] {
  // Para cada alteração, criar um item no relatório
  return [
    ...itens,
    ...alteracoes.map(
      (alteracao) =>
        ({
          id: alteracao.id,
          data: alteracao.dataDecreto
            ? formatarData(alteracao.dataDecreto)
            : '',
          dataOrdem: alteracao.dataDecreto
            ? formatarDataOrdem(alteracao.dataDecreto)
            : '',
          historico:
            alteracao.tipoAlteracao === 1
              ? 'SUPLEMENTAÇÃO ORÇAMENTÁRIA'
              : 'ANULAÇÃO ORÇAMENTÁRIA',
          valor:
            alteracao.tipoAlteracao === 1
              ? alteracao.valorTotal
              : -alteracao.valorTotal,
          valoresMensais: alteracao.valoresMensais,
          valorTotalMensal:
            alteracao.tipoAlteracao === 1
              ? alteracao.valorTotal
              : -alteracao.valorTotal,
          tipo: alteracao.tipoAlteracao === 1 ? 'decreto' : 'emenda',
        }) as RelatorioDespesaItem
    ),
  ];
}

// Função para calcular distribuição mensal de estornos baseada no empenho original
function calcularDistribuicaoMensalEstorno(
  empenhoOriginal: any,
  valorAnulado: number
): {
  jan: number;
  fev: number;
  mar: number;
  abr: number;
  mai: number;
  jun: number;
  jul: number;
  ago: number;
  set: number;
  out: number;
  nov: number;
  dez: number;
} {
  const totalOriginal = calcularValorTotalMensal(empenhoOriginal);

  // Se o total original for zero, distribuir igualmente
  if (totalOriginal === 0) {
    const valorMensal = currency(valorAnulado, currencyOptionsNoSymbol)
      .divide(12)
      .value;
    return {
      jan: valorMensal,
      fev: valorMensal,
      mar: valorMensal,
      abr: valorMensal,
      mai: valorMensal,
      jun: valorMensal,
      jul: valorMensal,
      ago: valorMensal,
      set: valorMensal,
      out: valorMensal,
      nov: valorMensal,
      dez: valorMensal,
    };
  }

  // Calcular percentual anulado e distribuir proporcionalmente
  const percentualAnulado = currency(valorAnulado, currencyOptionsNoSymbol)
    .divide(totalOriginal)
    .value;

  return {
    jan: currency(parseFloat(empenhoOriginal.usarMes1.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    fev: currency(parseFloat(empenhoOriginal.usarMes2.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    mar: currency(parseFloat(empenhoOriginal.usarMes3.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    abr: currency(parseFloat(empenhoOriginal.usarMes4.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    mai: currency(parseFloat(empenhoOriginal.usarMes5.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    jun: currency(parseFloat(empenhoOriginal.usarMes6.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    jul: currency(parseFloat(empenhoOriginal.usarMes7.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    ago: currency(parseFloat(empenhoOriginal.usarMes8.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    set: currency(parseFloat(empenhoOriginal.usarMes9.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    out: currency(parseFloat(empenhoOriginal.usarMes10.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    nov: currency(parseFloat(empenhoOriginal.usarMes11.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
    dez: currency(parseFloat(empenhoOriginal.usarMes12.toString()), currencyOptionsNoSymbol)
      .multiply(percentualAnulado)
      .value,
  };
}

// Função de limpeza de dados inconsistentes para manter integridade
export async function limparDadosInconsistentes(exercicio: number) {
  try {
    const resultado = await prisma.$transaction(async (tx) => {
      // Remover estornos sem empenhos correspondentes
      const estornosOrfaos = await tx.empenhos_anulacoes.deleteMany({
        where: {
          empenho: {
            exercicio: { not: exercicio },
          },
        },
      });

      // Limpar alterações orçamentárias incompletas (mais de 30 dias)
      const alteracoesIncompletas = await tx.alteracaoOrcamentaria.updateMany({
        where: {
          exercicio,
          status: 0, // Status incompleto
          dataAbertura: {
            lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Mais de 30 dias
          },
        },
        data: {
          status: 3, // Marcado como expirado/cancelado
        },
      });

      // Verificar e limpar reservas sem itens e muito antigas
      const reservasInativas = await tx.reservas.updateMany({
        where: {
          exercicio,
          status: 1,
          data: {
            lt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // Mais de 1 ano
          },
          reserva_itens: {
            none: {},
          },
        },
        data: {
          status: 2, // Marcado como inativo
        },
      });

      // Limpar empenhos sem liquidação e muito antigos
      const empenhosAntigos = await tx.empenhos.updateMany({
        where: {
          exercicio,
          ativo: true,
          data: {
            lt: new Date(Date.now() - 730 * 24 * 60 * 60 * 1000), // Mais de 2 anos
          },
          liquidacoes: {
            none: {},
          },
        },
        data: {
          ativo: false, // Marcado como inativo
        },
      });

      return {
        estornosLimpos: estornosOrfaos.count,
        alteracoesLimpas: alteracoesIncompletas.count,
        reservasInativadas: reservasInativas.count,
        empenhosInativados: empenhosAntigos.count,
      };
    });

    console.log('Limpeza de dados inconsistentes concluída:', resultado);
    return resultado;
  } catch (error: any) {
    console.error('Erro ao limpar dados inconsistentes:', error);
    throw new DespesaReportError(
      'Erro ao executar limpeza de dados.',
      'CLEANUP_ERROR',
      error
    );
  }
}

// Função de manutenção periódica para despesas
export async function manutencaoDespesas() {
  try {
    const exercicioAtual = new Date().getFullYear();
    const resultado = await limparDadosInconsistentes(exercicioAtual);

    return {
      sucesso: true,
      mensagem: 'Manutenção concluída com sucesso.',
      ...resultado,
    };
  } catch (error: any) {
    console.error('Erro na manutenção de despesas:', error);

    return {
      sucesso: false,
      mensagem: 'Erro durante a manutenção.',
      erro: error.message,
    };
  }
}
