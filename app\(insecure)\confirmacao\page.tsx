'use client';
import { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { EmailOtpType } from '@supabase/supabase-js';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { confirmOtp } from '@/lib/supabase/otp';

export default function CadastroPage() {
  const [isMounted, setIsMounted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const token_hash = searchParams?.get('token_hash') || null;
  const type = searchParams?.get('type') as EmailOtpType | null;
  const router = useRouter();
  //arquivositu.gestaoweb.digital/auth/confirm?token_hash=pkce_4fce337432e1dd770be586b6dcbfbd9e5527d03bf28c912e3c3d158c&type=recovery&next=/cadastro/alterar-senha

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted && (!token_hash || !type)) {
      router.replace('/login');
    }
  }, [isMounted, token_hash, type, router]);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted && !!token_hash && !!type}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmação</AlertDialogTitle>
          </AlertDialogHeader>
          {type === 'invite' && (
            <>Clique no botão abaixo e conclua seu cadastro.</>
          )}
          {/* {type === 'recovery' && <>Clique no botão abaixo para redefinir sua senha.</>} */}
          {type === 'magiclink' && (
            <>Clique no botão abaixo para redefinir sua senha.</>
          )}

          <AlertDialogFooter>
            <Button
              disabled={isLoading}
              onClick={async () => {
                setIsLoading(true);
                const res = await confirmOtp(token_hash, type);
                if (res.error) {
                  return router.replace('/confirmacao/expirado');
                }
                if (type === 'magiclink') {
                  return router.replace('/cadastro/alterar-senha');
                }
                if (type === 'invite') {
                  return router.replace('/cadastro/novo');
                }
                return router.replace('/dashboard');
              }}
            >
              {isLoading ? 'Carregando...' : 'Confirmar'}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
