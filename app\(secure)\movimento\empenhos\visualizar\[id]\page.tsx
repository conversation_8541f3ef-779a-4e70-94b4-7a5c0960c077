'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { obterEmpenho } from '@/lib/database/movimento/empenhos';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft, Pencil, XCircle, Eye, Plus, Receipt } from 'lucide-react';
import { BotaoVincularEmpenhoProtocolo } from '@/components/movimento/botaoVincularEmpenhoProtocolo';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';
import { StatusEmpenho, StatusProtocoloDesc } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from '@/components/ui/table';

export default async function VisualizarEmpenhoPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const empenho = await obterEmpenho({
    id: Number(id),
  });

  if (empenho.error) {
    return <ErrorAlert error={empenho.error} />;
  }
  if (!empenho.data) {
    return <ErrorAlert error='Falha ao obter empenho.' />;
  }

  const statusEmpenho = empenho.data.status;
  const protocolo = empenho.data.protocolo;

  const valorAnulado = currency(
    empenho.data.valorAnulado || 0,
    currencyOptionsNoSymbol
  );
  const valorLiquidado = currency(
    empenho.data.valorLiquidado || 0,
    currencyOptionsNoSymbol
  );

  // FIXED: Use sum of monthly quotas instead of valorTotal for consistency with DataTable
  const valorOriginal = currency(0, currencyOptionsNoSymbol)
    .add(empenho.data.usarMes1 || 0)
    .add(empenho.data.usarMes2 || 0)
    .add(empenho.data.usarMes3 || 0)
    .add(empenho.data.usarMes4 || 0)
    .add(empenho.data.usarMes5 || 0)
    .add(empenho.data.usarMes6 || 0)
    .add(empenho.data.usarMes7 || 0)
    .add(empenho.data.usarMes8 || 0)
    .add(empenho.data.usarMes9 || 0)
    .add(empenho.data.usarMes10 || 0)
    .add(empenho.data.usarMes11 || 0)
    .add(empenho.data.usarMes12 || 0);

  const saldo = valorOriginal.subtract(valorAnulado).subtract(valorLiquidado);

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Visualizar Empenho N° {empenho.data.id}</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='mx-6 flex w-full flex-wrap justify-between gap-4'>
          <Link href='/movimento/empenhos'>
            <Button className='mb-4' variant='secondary'>
              <ArrowLeft className='mr-2 size-4' /> Voltar
            </Button>
          </Link>
          <div className='flex flex-wrap gap-4'>
            {(statusEmpenho === StatusEmpenho.EMPENHADO ||
              statusEmpenho === StatusEmpenho.ANULADO_PARCIAL) && (
              <Link href={`/movimento/empenhos/editar/${id}`}>
                <Button type='button' variant='outline'>
                  <Pencil className='mr-1 size-4' /> Editar
                </Button>
              </Link>
            )}
            {(statusEmpenho === StatusEmpenho.EMPENHADO ||
              statusEmpenho === StatusEmpenho.ANULADO_PARCIAL) && (
              <Link href={`/movimento/empenhos/anular/${id}`}>
                <Button type='button' variant='outline'>
                  <XCircle className='mr-1 size-4' /> Anular
                </Button>
              </Link>
            )}
            {statusEmpenho === StatusEmpenho.EMPENHADO && (
              <Link href={`/movimento/af/novo?empenho=${id}`}>
                <Button type='button' variant='outline'>
                  <Plus className='mr-1 size-4' /> Criar AF
                </Button>
              </Link>
            )}
            {(statusEmpenho === StatusEmpenho.EMPENHADO ||
              statusEmpenho === StatusEmpenho.LIQUIDADO) && (
              <Link href={`/movimento/liquidacoes/novo?empenho=${id}`}>
                <Button type='button' variant='outline'>
                  <Receipt className='mr-1 size-4' /> Liquidar
                </Button>
              </Link>
            )}
            {statusEmpenho === StatusEmpenho.EMPENHADO && !protocolo && (
              <BotaoVincularEmpenhoProtocolo
                empenhoId={Number(id)}
                exercicio={empenho.data.exercicio}
              />
            )}
            {protocolo && (
              <Link href={`/movimento/protocolo/visualizar/${protocolo.id}`}>
                <Button type='button' variant='outline'>
                  <Eye className='mr-1 size-4' /> Ver Protocolo #
                  {protocolo.numero}
                </Button>
              </Link>
            )}
            <BotaoDownloadPDF
              url={`/movimento/empenhos/imprimir/${id}`}
              title='Imprimir'
            />
          </div>
        </div>

        <div className='flex w-full flex-wrap justify-center'>
          <div className='mt-8 flex w-full max-w-4xl flex-col items-center justify-center space-y-2 text-sm'>
            <div>
              <span className='font-bold'>Status:</span>&nbsp;
              <span className='font-bold'>{StatusEmpenho[statusEmpenho]}</span>
            </div>
            {protocolo && (
              <div className='text-blue-600'>
                <span className='font-bold'>Protocolo:</span>&nbsp;
                <span className='font-bold'>
                  #{protocolo.numero} -{' '}
                  {
                    StatusProtocoloDesc[
                      protocolo.status as keyof typeof StatusProtocoloDesc
                    ]
                  }
                </span>
              </div>
            )}
          </div>

          {/* Informações Principais */}
          <Card className='mt-8 w-full max-w-4xl'>
            <CardHeader>
              <CardTitle>Informações do Empenho</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <strong>Despesa:</strong> {empenho.data.dotacao?.despesa}
                </div>
                <div>
                  <strong>Fornecedor:</strong> {empenho.data.fornecedor?.nome}
                </div>
                <div>
                  <strong>Data:</strong>{' '}
                  {new Date(empenho.data.data).toLocaleDateString('pt-BR')}
                </div>
                <div>
                  <strong>Exercício:</strong> {empenho.data.exercicio}
                </div>
                <div>
                  <strong>Resumo:</strong> {empenho.data.resumo}
                </div>
                {empenho.data.obs && (
                  <div>
                    <strong>Observação:</strong> {empenho.data.obs}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Detalhes Financeiros */}
          <Card className='mt-6 w-full max-w-4xl'>
            <CardHeader>
              <CardTitle>Detalhes Financeiros</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='mb-4 grid grid-cols-2 gap-4'>
                <div>
                  <strong>Valor Total:</strong>{' '}
                  {currency(
                    empenho.data.valorTotal,
                    currencyOptionsNoSymbol
                  ).format()}
                </div>
                <div>
                  <strong>Valor Anulado:</strong> {valorAnulado.format()}
                </div>
                <div>
                  <strong>Valor Liquidado:</strong> {valorLiquidado.format()}
                </div>
                <div>
                  <strong>Saldo Disponível:</strong> {saldo.format()}
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Mês</TableHead>
                    <TableHead className='text-right'>Valor Original</TableHead>
                    <TableHead className='text-right'>Valor Usado</TableHead>
                    <TableHead className='text-right'>Saldo</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array.from({ length: 12 }, (_, i) => {
                    const mesNum = i + 1;
                    const mesNome = [
                      'Janeiro',
                      'Fevereiro',
                      'Março',
                      'Abril',
                      'Maio',
                      'Junho',
                      'Julho',
                      'Agosto',
                      'Setembro',
                      'Outubro',
                      'Novembro',
                      'Dezembro',
                    ][i];

                    const valorOriginal = currency(
                      (empenho.data[
                        `usarMes${mesNum}` as keyof typeof empenho.data
                      ] as number) || 0,
                      currencyOptionsNoSymbol
                    );
                    const valorUsado = currency(
                      (empenho.data[
                        `valorUsadoMes${mesNum}` as keyof typeof empenho.data
                      ] as number) || 0,
                      currencyOptionsNoSymbol
                    );
                    const saldoMes = valorOriginal.subtract(valorUsado);

                    return (
                      <TableRow key={mesNum}>
                        <TableCell>{mesNome}</TableCell>
                        <TableCell className='text-right'>
                          {valorOriginal.format()}
                        </TableCell>
                        <TableCell className='text-right'>
                          {valorUsado.format()}
                        </TableCell>
                        <TableCell className='text-right'>
                          {saldoMes.format()}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Histórico de Anulações */}
          {empenho.data.empenhos_anulacoes &&
            empenho.data.empenhos_anulacoes.length > 0 && (
              <Card className='mt-6 w-full max-w-4xl'>
                <CardHeader>
                  <CardTitle>Histórico de Anulações</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Data</TableHead>
                        <TableHead>Valor Anulado</TableHead>
                        <TableHead>Motivo</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {empenho.data.empenhos_anulacoes.map((anulacao: any) => (
                        <TableRow key={anulacao.id}>
                          <TableCell>
                            {new Date(anulacao.data).toLocaleDateString(
                              'pt-BR'
                            )}
                          </TableCell>
                          <TableCell className='text-right'>
                            {currency(
                              anulacao.valorAnulado,
                              currencyOptionsNoSymbol
                            ).format()}
                          </TableCell>
                          <TableCell>{anulacao.motivo}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
        </div>
      </PageContent>
    </PageWrapper>
  );
}
