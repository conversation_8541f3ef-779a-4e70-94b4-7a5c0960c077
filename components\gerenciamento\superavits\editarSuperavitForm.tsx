'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { editarSuperavitSchema, superavitSchema } from '@/lib/validation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import {
  currencyOptions,
  moneyMask,
  moneyUnmask,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import {
  editarSuperavit,
  obterSuperavit,
} from '@/lib/database/gerenciamento/superavit';
import currency from 'currency.js';

export default function EditarSuperavitForm({
  superavit,
}: {
  superavit: Awaited<ReturnType<typeof obterSuperavit>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);

  const dotInicial = currency(
    superavit?.data?.dotInicial || 0,
    currencyOptions
  ).format();

  const valorSuplementado = currency(
    superavit?.data?.suplementado || 0,
    currencyOptions
  ).format();
  const valorReserva = currency(
    superavit?.data?.valReserva || 0,
    currencyOptions
  ).format();
  const saldoAtual = currency(
    superavit?.data?.saldoAtual || 0,
    currencyOptions
  ).format();
  const despesas = superavit?.data?.despesas || '';

  const [valReceita, setValReceita] = useState(
    currency(superavit?.data?.valorReceita || 0, currencyOptions).format()
  );

  const form = useForm<z.infer<typeof superavitSchema>>({
    resolver: zodResolver(superavitSchema),
    defaultValues: {
      // id: superavit.data!.id,
      exercicio: superavit.data!.exercicio,
      fonte: superavit.data!.fonte,
      codAplicacao: superavit.data!.codAplicacao,
      valorReceita: superavit.data!.valorReceita,
      dotInicial: superavit.data!.dotInicial, // Ajustar para não nulo no superavit.ts controle database
      suplementado: superavit.data!.suplementado,
      valReserva: superavit.data!.valReserva,
      saldoAtual: superavit.data!.saldoAtual,
    },
  });

  const onSubmit = async (values: z.infer<typeof superavitSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof editarSuperavitSchema> = {
        id: superavit.data!.id,
        exercicio: superavit.data!.exercicio,
        fonte: superavit.data!.fonte,
        codAplicacao: superavit.data!.codAplicacao,
        valorReceita: values.valorReceita,
      };
      const res = await editarSuperavit(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Superavit alterado.');
        router.push('/gerenciamento/superavits');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const onInvalid = (errors: any) => {
    console.error(errors);
    toast.error(JSON.stringify(errors));
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit, onInvalid)}>
        <div className='grid gap-8 py-4'>
          <FormField
            control={form.control}
            name='exercicio'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Exercicio
                </FormLabel>
                <FormControl>
                  <Input {...field} disabled />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='fonte'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Fonte</FormLabel>
                <FormControl>
                  <Input {...field} disabled />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='codAplicacao'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Código Aplicação
                </FormLabel>
                <FormControl>
                  <Input {...field} disabled />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='valorReceita'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Receita</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={valReceita}
                    onChange={(e) => {
                      setValReceita(moneyMask(e.target.value));
                      form.setValue(
                        'valorReceita',
                        Number(moneyUnmask(e.target.value)),
                        { shouldDirty: true }
                      );
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='dotInicial'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Dotação Inicial
                </FormLabel>
                <FormControl>
                  <Input {...field} disabled value={dotInicial} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='suplementado'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Suplementado
                </FormLabel>
                <FormControl>
                  <Input {...field} value={valorSuplementado} disabled />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='valReserva'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Valor Reservado
                </FormLabel>
                <FormControl>
                  <Input {...field} value={valorReserva} disabled />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='saldoAtual'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Saldo Atual
                </FormLabel>
                <FormControl>
                  <Input {...field} value={saldoAtual} disabled />
                </FormControl>
              </FormItem>
            )}
          />
          <FormLabel className='flex w-full text-left'>
            Despesas Agrupadas: {despesas}
          </FormLabel>
        </div>

        <div></div>
        <div className='mt-12 flex justify-between'>
          <Button
            variant={'destructive'}
            disabled={loading}
            onClick={(e) => {
              e.preventDefault();
              router.push('/gerenciamento/superavits');
            }}
          >
            <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
          </Button>
          <Button
            type='submit'
            disabled={loading} // || FornecedoresConfigurados.length === 0}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' /> Alterar Superavit
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
