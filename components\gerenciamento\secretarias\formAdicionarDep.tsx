'use client';

import * as React from 'react';
import { ArrowDown, Loader2 } from 'lucide-react';

import { uppercaseMask } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Departamento } from '@/types/app';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { secretariaFormSchema } from '@/lib/validation';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { z } from 'zod';

export function FormAdicionarDep({
  data,
  setData,
}: {
  data: Departamento[];
  setData: React.Dispatch<React.SetStateAction<Departamento[]>>;
}) {
  const [loading, setLoading] = React.useState(false);

  const form = useForm<z.infer<typeof secretariaFormSchema>>({
    resolver: zodResolver(secretariaFormSchema),
    defaultValues: {
      //@ts-ignore
      codigo: '',
      nome: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof secretariaFormSchema>) => {
    setLoading(true);

    const deps = data.filter((m) => m.codigo !== Number(values.codigo));
    setData([
      {
        nome: values.nome,
        codigo: Number(values.codigo),
        subdepartamentos: [],
      },
      ...deps,
    ]);

    form.reset();

    setLoading(false);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='flex gap-4 py-4'>
          <FormField
            control={form.control}
            name='codigo'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Código</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    pattern='[0-99]*'
                    placeholder='00'
                    maxLength={2}
                    className='w-[44px]'
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Nome do Departamento
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={2}
                    onChange={(event) => {
                      const { value } = event.target;
                      form.setValue('nome', uppercaseMask(value));
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className='flex w-full'>
          <Button
            type='submit'
            disabled={loading}
            variant={'secondary'}
            className='w-full'
          >
            {loading ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <ArrowDown className='mr-2 h-4 w-4' /> Adicionar Departamento
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
