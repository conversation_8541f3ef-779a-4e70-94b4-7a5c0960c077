'use client';

import { siteConfig } from '@/config/site';
import { CHAVE_API, URL_SERVER_IMPRESSAO } from '@/lib/consts';
import { createClient } from '@/lib/supabase/client';
import { useEffect } from 'react';
import { toast } from 'sonner';

export const IframePDF = ({ url }: { url: string }) => {
  useEffect(() => {
    const carregarPDF = async () => {
      const supabase = createClient();
      const session = await supabase.auth.getSession();
      const incluirGestorCheckbox = document.getElementById(
        'incluirGestor'
      ) as HTMLInputElement;
      const incluirGestor = incluirGestorCheckbox
        ? incluirGestorCheckbox.getAttribute('data-state') !== 'unchecked'
        : true;
      const pdf = await fetch(URL_SERVER_IMPRESSAO, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.data.session?.access_token}`,
          url: siteConfig.url + url + `?incluirGestor=${incluirGestor}`,
          chave: CHAVE_API,
        },
        body: JSON.stringify({ landscape: false }),
      });
      if (!pdf.ok) {
        return toast.error(
          'Falha ao carregar o documento, por favor recarregue a página.'
        );
      }
      const fileURL = URL.createObjectURL(await pdf.blob());

      const iframe = document.getElementById('documento') as HTMLIFrameElement;
      iframe.src = fileURL;
    };
    carregarPDF();
  }, [url]);
  return (
    <iframe id='documento' src={`/carregando`} className='h-[800px] w-full' />
  );
};
