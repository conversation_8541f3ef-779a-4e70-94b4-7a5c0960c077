import AssinaturaReservasDatatableWrapper from '@/components/assinatura/reserva/assinaturaReservasDatatableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';

export default function ReservaPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Assinatura de Reservas</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <AssinaturaReservasDatatableWrapper />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
