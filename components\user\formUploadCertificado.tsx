'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import { Check, PlusCircle } from 'lucide-react';
import forge from 'node-forge';
import { uploadCertSchema } from '@/lib/validation';
import { uploadPFX } from '@/lib/supabase/clientUtils';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { validarCertificadoUsuarioConectado } from '@/lib/supabase/serverUtils';
import { CertificateDisplay } from './displayCertificado';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

export default function FormCertificadoDigital() {
  const [loading, setLoading] = useState(false);
  const [dadosCertificado, setDadosCertificado] = useState<{
    subjectAttributes: forge.pki.CertificateField[];
    issuerAttributes: forge.pki.CertificateField[];
    validity: { notBefore: Date; notAfter: Date };
  } | null>(null);
  const [password, setPassword] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogAction, setDialogAction] = useState<
    'upload' | 'validate' | null
  >(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const triggerFileInput = () => {
    const fileInput = document.getElementById(
      'certificado'
    ) as HTMLInputElement;
    fileInput?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files ? event.target.files[0] : null;
    const result = uploadCertSchema.safeParse({ certificado: file });

    if (!result.success) {
      toast.error(result.error.errors[0].message);
      return;
    }
    if (file) {
      setSelectedFile(file);
      setDialogAction('upload');
      setIsDialogOpen(true);
    }
  };

  const handleDialogSubmit = async () => {
    if (!password || !dialogAction) return;

    setLoading(true);

    if (dialogAction === 'upload' && selectedFile) {
      try {
        const fileBuffer = await selectedFile.arrayBuffer();
        const pfxAsn1 = forge.asn1.fromDer(forge.util.createBuffer(fileBuffer));
        const pfx = forge.pkcs12.pkcs12FromAsn1(pfxAsn1, password);

        if (!pfx) {
          toast.error('Senha incorreta ou arquivo inválido.');
          setLoading(false);
          return;
        }

        const bags = pfx.getBags({ bagType: forge.pki.oids.certBag });
        const certBag = bags[forge.pki.oids.certBag];

        if (certBag && certBag.length > 0) {
          const cert = certBag[0].cert;
          if (cert) {
            const validity = cert.validity;
            const currentDate = new Date();
            const expirationDate = validity.notAfter;
            const isExpired = currentDate > expirationDate;

            if (isExpired) {
              toast.error('O certificado está expirado.');
              setLoading(false);
              return;
            } else {
              await uploadPFX(selectedFile, password);
              toast.success('Certificado cadastrado com sucesso.');
              setIsDialogOpen(false);
            }
          } else {
            toast.error('Certificado não encontrado no PFX.');
            setLoading(false);
            return;
          }
        } else {
          toast.error('Certificado não encontrado no PFX.');
          setLoading(false);
          return;
        }
      } catch (error) {
        console.error(error);
        toast.error('Falha ao processar o arquivo PFX.');
        setLoading(false);
        return;
      } finally {
        if (!loading) {
          setSelectedFile(null);
        }
      }
    } else if (dialogAction === 'validate') {
      try {
        const validacao = await validarCertificadoUsuarioConectado({
          senha: password,
        });
        if (validacao?.error) {
          toast.error(validacao.error);
          setLoading(false);
          return;
        } else {
          toast.success('Certificado válido.');
          if (!validacao.data) throw new Error('Erro ao validar certificado.');
          setDadosCertificado({
            subjectAttributes: validacao.data.subjectAttributes,
            issuerAttributes: validacao.data.issuerAttributes,
            validity: validacao.data.validity,
          });
          setIsDialogOpen(false);
        }
      } catch (error) {
        console.error(error);
        toast.error(toastAlgoDeuErrado);
        setLoading(false);
        return;
      }
    }

    setLoading(false);
    setPassword('');
    setDialogAction(null);
  };

  const handleValidateClick = () => {
    setDialogAction('validate');
    setIsDialogOpen(true);
  };

  return (
    <div className='grid gap-8 py-4'>
      {dadosCertificado && (
        <CertificateDisplay
          subjectAttributes={dadosCertificado.subjectAttributes}
          issuerAttributes={dadosCertificado.issuerAttributes}
          validity={dadosCertificado.validity}
        />
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button
            type='button'
            disabled={loading}
            onClick={handleValidateClick}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <Check className='mr-2 h-4 w-4' />
                Validar Certificado Cadastrado
              </>
            )}
          </Button>
        </DialogTrigger>

        <Input
          placeholder='Certificado PFX'
          type='file'
          accept='.pfx, application/x-pkcs12'
          onChange={handleFileChange}
          id='certificado'
          className='hidden'
        />
        <label htmlFor='certificado'>
          <Button type='button' onClick={triggerFileInput} disabled={loading}>
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' />
                Cadastrar Certificado A1 PFX
              </>
            )}
          </Button>
        </label>

        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {dialogAction === 'upload'
                ? 'Cadastrar Certificado'
                : 'Validar Certificado'}
            </DialogTitle>
            <DialogDescription>
              Insira a senha do certificado para{' '}
              {dialogAction === 'upload'
                ? 'prosseguir com o cadastro'
                : 'validar'}
              .
            </DialogDescription>
          </DialogHeader>
          <Input
            type='password'
            placeholder='Senha do certificado'
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleDialogSubmit()}
            autoFocus
            disabled={loading}
          />
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => {
                setIsDialogOpen(false);
                setPassword('');
                setSelectedFile(null);
                setDialogAction(null);
              }}
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleDialogSubmit}
              disabled={loading || !password}
            >
              {loading ? (
                <>
                  <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                  {dialogAction === 'upload'
                    ? 'Cadastrando...'
                    : 'Validando...'}
                </>
              ) : dialogAction === 'upload' ? (
                'Cadastrar'
              ) : (
                'Validar'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
