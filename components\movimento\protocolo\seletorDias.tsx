'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from 'lucide-react';

interface SeletorDiasProps {
  value: number | undefined;
  onChange: (dias: number | undefined) => void;
  label?: string;
  placeholder?: string;
}

export function SeletorDias({
  value,
  onChange,
  label = 'Dias atrás',
  placeholder = 'Ex: 7',
}: SeletorDiasProps) {
  const predefinedOptions = [
    { label: '7 dias', value: 7 },
    { label: '15 dias', value: 15 },
    { label: '30 dias', value: 30 },
    { label: '60 dias', value: 60 },
    { label: '90 dias', value: 90 },
  ];

  const handlePredefinedClick = (dias: number) => {
    onChange(dias);
  };

  const handleCustomChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '') {
      onChange(undefined);
    } else {
      const numValue = parseInt(value);
      if (!isNaN(numValue) && numValue > 0) {
        onChange(numValue);
      }
    }
  };

  const handleClear = () => {
    onChange(undefined);
  };

  return (
    <div className='space-y-2'>
      <div className='flex items-center gap-2'>
        <Calendar className='h-4 w-4 text-gray-500' />
        <Label className='text-sm font-medium'>{label}</Label>
      </div>

      <div className='flex gap-2'>
        <Input
          type='number'
          value={value || ''}
          onChange={handleCustomChange}
          placeholder={placeholder}
          min='1'
          className='w-20'
        />
        <Button
          type='button'
          variant='outline'
          size='sm'
          onClick={handleClear}
          disabled={!value}
        >
          Limpar
        </Button>
      </div>

      <div className='flex flex-wrap gap-1'>
        {predefinedOptions.map((option) => (
          <Button
            key={option.value}
            type='button'
            variant={value === option.value ? 'default' : 'outline'}
            size='sm'
            onClick={() => handlePredefinedClick(option.value)}
            className='text-xs'
          >
            {option.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
