'use client';

import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { desativarTemplateDocumento } from '@/lib/database/gerenciamento/templatesDocumentos';
import { toast } from 'sonner';
import { Icons } from '@/components/icons';

interface AlertDesativarTemplateProps {
  idTemplate: number;
  nomeTemplate: string;
  children: React.ReactNode;
}

export function AlertDesativarTemplate({
  idTemplate,
  nomeTemplate,
  children,
}: AlertDesativarTemplateProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleDesativar = async () => {
    setIsLoading(true);
    try {
      const result = await desativarTemplateDocumento({ id: idTemplate });

      if (result.error) {
        toast.error(result.error);
        return;
      }

      toast.success('Template desativado com sucesso!');
    } catch (error) {
      console.error('Erro ao desativar template:', error);
      toast.error('Erro inesperado ao desativar template');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Desativar Template</AlertDialogTitle>
          <AlertDialogDescription>
            Tem certeza que deseja desativar o template{' '}
            <strong>&quot;{nomeTemplate}&quot;</strong>?
            <br />
            <br />
            Esta ação não pode ser desfeita. O template ficará indisponível para
            uso, mas os documentos já criados com este template não serão
            afetados.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDesativar}
            disabled={isLoading}
            className='bg-destructive text-destructive-foreground hover:bg-destructive/90'
          >
            {isLoading && (
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
            )}
            Desativar
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
