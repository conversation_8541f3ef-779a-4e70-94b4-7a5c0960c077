'use client';

import { Button } from '@/components/ui/button';
import { siteConfig } from '@/config/site';
import { CHAVE_API, URL_SERVER_IMPRESSAO } from '@/lib/consts';
import {
  obterReservaParaRelatorio,
  solicitarAssinaturasReserva,
} from '@/lib/database/movimento/reservas';
import { createClient } from '@/lib/supabase/client';
import { uploadPDFLegacy } from '@/lib/supabase/clientUtils';
import { montarPathPdfReserva, toastAlgoDeuErrado } from '@/lib/utils';
import { Loader2, Signature } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export const ComponentesPDF = ({
  url,
  reserva,
}: {
  url: string;
  reserva: Awaited<ReturnType<typeof obterReservaParaRelatorio>>['data'];
}) => {
  const [loading, setLoading] = useState(true);
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [incluirGestor, setIncluirGestor] = useState(true);

  const router = useRouter();

  useEffect(() => {
    const checkbox = document.getElementById('incluirGestor');

    if (checkbox) {
      const observer = new MutationObserver(() => {
        const state = checkbox.getAttribute('data-state');
        const isChecked = state === 'checked';
        setIncluirGestor(isChecked);
      });

      observer.observe(checkbox, {
        attributes: true,
        attributeFilter: ['data-state'],
      });

      const initialState = checkbox.getAttribute('data-state');
      setIncluirGestor(initialState === 'checked');

      return () => {
        observer.disconnect();
      };
    }
  }, []);

  useEffect(() => {
    const carregarPDF = async () => {
      try {
        const supabase = createClient();
        const session = await supabase.auth.getSession();
        const pdf = await fetch(URL_SERVER_IMPRESSAO, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${session.data.session?.access_token}`,
            url:
              siteConfig.url +
              url +
              `?incluirGestor=${incluirGestor}&assinaturaDigital=true`,
            'Content-Type': 'application/json',
            chave: CHAVE_API,
          },
          body: JSON.stringify({ assinaturaDigital: true, landscape: true }),
        });
        if (!pdf.ok) {
          return toast.error(
            'Falha ao carregar o documento, por favor recarregue a página.'
          );
        }
        const pdfBlob = await pdf.blob();
        setPdfBlob(pdfBlob);

        const fileURL = URL.createObjectURL(pdfBlob);
        const iframe = document.getElementById(
          'documento'
        ) as HTMLIFrameElement;
        iframe.src = fileURL;
        setLoading(false);
      } catch (error) {
        toast.error(toastAlgoDeuErrado);
      }
    };
    carregarPDF();
  }, [url, incluirGestor]);

  const solicitarAssinaturas = async () => {
    try {
      if (!pdfBlob) {
        return toast.error('Documento não encontrado.');
      }
      setLoading(true);

      //Acesso controlado pelo ID das dotações - deve ser a primeira pasta dentro de private
      const idDotacao = reserva!.dotacao.id;
      const exercicio = reserva!.exercicio;
      const pdfPath = montarPathPdfReserva(reserva!.id, idDotacao, exercicio);

      const pdfFile = new File([pdfBlob], 'reserva.pdf', {
        type: 'application/pdf',
      });
      await uploadPDFLegacy('reservas', pdfPath, pdfFile);
      await solicitarAssinaturasReserva({
        idReserva: reserva!.id,
        gestor: incluirGestor ? reserva!.gestor.id : false,
        secretario: reserva!.secretario.id,
        diretor: reserva!.diretor.id,
        prefeito:
          reserva!.configsExercicio.habilitaAssinatura2 &&
          reserva!.economicaItem.codigo.startsWith(
            reserva!.configsExercicio.economicaAssinatura2
          )
            ? reserva!.prefeito.id
            : false,
      });

      toast.success('Assinaturas solicitadas com sucesso.');
      router.push('/movimento/reservas');
    } catch (error) {
      toast.error(toastAlgoDeuErrado);
      console.log(error);
      setLoading(false);
    }
  };

  return (
    <>
      <iframe id='documento' src={`/carregando`} className='h-[800px] w-full' />
      <Button
        className='mt-4'
        variant='secondary'
        disabled={loading}
        onClick={solicitarAssinaturas}
      >
        {loading ? (
          <Loader2 className='size-4 animate-spin' />
        ) : (
          <Signature className='size-4' />
        )}
        Solicitar Assinaturas
      </Button>
    </>
  );
};
