import { ensureAuth } from '@/lib/supabase/actions';
import { Skeleton } from '@/components/ui/skeleton';
import React, { Suspense } from 'react';

export default async function Home({}: {}) {
  await ensureAuth();
  return (
    <div className='flex-1 space-y-4 p-0 pt-6 md:p-8'>
      <div className='mb-6 flex items-center justify-center space-y-2'>
        <h2 className='text-xl font-bold tracking-tight'>Dashboard</h2>
      </div>
      <Suspense fallback={<Skeleton className='h-[800px] w-full' />}></Suspense>
    </div>
  );
}
