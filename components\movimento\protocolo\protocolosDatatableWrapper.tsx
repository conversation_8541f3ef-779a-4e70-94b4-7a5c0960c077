import { ErrorAlert } from '@/components/error-alert';
import ProtocolosDataTable from './protocolosDataTable';
import { listarProtocolos } from '@/lib/database/movimento/protocolos';

export default async function ProtocolosDatatableWrapper() {
  const result = await listarProtocolos();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Protocolos não encontrados.'} />;

  return <ProtocolosDataTable data={result} />;
}
