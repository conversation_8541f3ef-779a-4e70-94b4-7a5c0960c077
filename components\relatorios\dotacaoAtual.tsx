import { siteConfig } from '@/config/site';
import { toCurrency } from '@/lib/serverUtils';
import { DotacaoAtualReport } from '@/lib/database/relatorios/dotacoes';

interface RelatorioDotacaoAtualProps {
  dotacoes: DotacaoAtualReport[];
  titulo?: string;
  filtros?: {
    exercicio?: number;
    secretaria?: string;
    departamento?: string;
    subdepartamento?: string;
    fonte?: number;
    codAplicacaoInicial?: string;
    codAplicacaoFinal?: string;
    economicaInicial?: string;
    economicaFinal?: string;
  };
}

export const RelatorioDotacaoAtual = ({
  dotacoes,
  titulo = 'Relatório de Dotações Atuais',
  filtros,
}: RelatorioDotacaoAtualProps) => {
  const valorTotal = dotacoes.reduce(
    (sum, dotacao) => sum + dotacao.valorAtual,
    0
  );

  return (
    <div className='mx-auto max-w-[1000px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
            {filtros.secretaria && <p>Secretaria: {filtros.secretaria}</p>}
            {filtros.departamento && (
              <p>Departamento: {filtros.departamento}</p>
            )}
            {filtros.subdepartamento && (
              <p>Subdepartamento: {filtros.subdepartamento}</p>
            )}
            {filtros.fonte && <p>Fonte: {filtros.fonte}</p>}
            {filtros.codAplicacaoInicial && filtros.codAplicacaoFinal && (
              <p>
                Cod. Aplicação: {filtros.codAplicacaoInicial} a{' '}
                {filtros.codAplicacaoFinal}
              </p>
            )}
            {filtros.economicaInicial && filtros.economicaFinal && (
              <p>
                Econômica: {filtros.economicaInicial} a {filtros.economicaFinal}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Tabela */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Secretaria</th>
              <th className='p-1 text-left font-bold'>Econômica</th>
              <th className='p-1 text-left font-bold'>Fonte</th>
              <th className='p-1 text-left font-bold'>Cod.Apl.</th>
              <th className='p-1 text-left font-bold'>Desp.</th>
              <th className='p-1 text-left font-bold'>Descrição</th>
              <th className='p-1 text-right font-bold'>Valor Atual</th>
            </tr>
          </thead>
          <tbody>
            {dotacoes.map((dotacao, index) => (
              <tr
                key={dotacao.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1'>
                  {dotacao.secretaria?.nome || 'N/A'}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {dotacao.economica.codigo}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {dotacao.fonte.toString().padStart(2, '0')}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {dotacao.codAplicacao}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {dotacao.despesa}
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div className='truncate' title={dotacao.desc}>
                    {dotacao.desc}
                  </div>
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(dotacao.valorAtual).format().replace(/0$/, '')}
                </td>
              </tr>
            ))}
            {/* Linha do Total */}
            <tr className='border-t-2 border-gray-800 bg-gray-100 font-bold'>
              <td colSpan={6} className='p-1 text-right'>
                TOTAL:
              </td>
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorTotal).format().replace(/0$/, '')}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de registros: {dotacoes.length}</p>
      </div>
    </div>
  );
};
