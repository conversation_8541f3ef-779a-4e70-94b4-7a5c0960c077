import { Metadata } from 'next';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import FormCriarLiquidacao from '@/components/movimento/liquidacoes/formCriarLiquidacao';

export const metadata: Metadata = {
  title: 'Nova Liquidação',
  description: 'Criar nova liquidação de empenho',
};

export default async function NovaLiquidacaoPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Nova Liquidação</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormCriarLiquidacao />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
