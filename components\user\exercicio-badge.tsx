import { createClient } from '@/lib/supabase/server';
import { Badge } from '../ui/badge';
import { Skeleton } from '../ui/skeleton';
import { obterExercicioUsuarioConectado } from '@/lib/database/usuarios';

export async function ExercicioBadge() {
  const supabase = await createClient();

  const { data: authData, error } = await supabase.auth.getUser();
  if (error || !authData?.user) {
    return <Skeleton className='h-[40px] w-[170px]' />;
  }
  const { data: exercicio } = await obterExercicioUsuarioConectado();

  if (!exercicio) {
    return <Skeleton className='h-[40px] w-[170px]' />;
  }
  return (
    <div className='my-4 md:my-0'>
      <span className='inline'>Exercício: </span>
      <Badge className='mr-2 md:mr-4'>{exercicio}</Badge>
    </div>
  );
}
