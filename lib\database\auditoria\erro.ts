'use server';

import { auditoriaErroSchema } from '@/lib/validation';
import { obterIdUsuarioConectado, obterIpUsuarioConectado } from '../usuarios';
import { prisma } from '@/lib/prisma';

export const inserirErroAudit = async (params: unknown) => {
  const idUsuario = await obterIdUsuarioConectado();

  if (!idUsuario) {
    return {
      error: 'Não foi possível obter o id do usuário conectado.',
    };
  }

  if (idUsuario.error) {
    return {
      error: idUsuario.error,
    };
  }

  const parsedParams = auditoriaErroSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { erro, modulo } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.erros_audit.create({
      data: {
        erro,
        moduloId: modulo,
        usuarioId: idUsuario.data,
        ip,
      },
    });
    return {
      data: true,
    };
  } catch (e) {
    console.log(e);
    return {
      error: 'Erro ao inserir auditoria de erro.',
    };
  }
};
