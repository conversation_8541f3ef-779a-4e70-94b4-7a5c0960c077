'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { RelatorioProtocolo } from '@/components/relatorios/relatorioProtocolo';
import { obterProtocolo } from '@/lib/database/movimento/protocolos';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';
import { ComboboxSelecionarProtocolo } from '@/components/relatorios/comboboxSelecionarProtocolo';
import { ProtocoloWithRelations } from '@/lib/database/movimento/protocolos';

interface ProtocoloOption {
  id: number;
  numero: number;
  exercicio: number;
  dataAbertura: string;
  resumo: string | null;
  status: number;
}

export default function RelatorioProtocoloPage() {
  const [protocolo, setProtocolo] = useState<ProtocoloWithRelations | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const protocoloId = searchParams?.get('protocolo');

  useEffect(() => {
    const fetchProtocolo = async (id: number) => {
      setLoading(true);
      setError(null);

      try {
        const result = await obterProtocolo({ id });

        if (result.error) {
          setError(result.error);
        } else if (result.data) {
          // Convert Prisma Decimal objects to numbers for React rendering
          const processedData = {
            ...result.data,
            reserva: result.data.reserva
              ? {
                  ...result.data.reserva,
                  usarTotal: Number(result.data.reserva.usarTotal),
                }
              : null,
          };
          setProtocolo(processedData);
        } else {
          setProtocolo(null);
        }
      } catch (err) {
        setError('Erro ao carregar protocolo.');
      } finally {
        setLoading(false);
      }
    };

    if (protocoloId) {
      fetchProtocolo(parseInt(protocoloId));
    } else {
      setProtocolo(null);
      setError(null);
    }
  }, [protocoloId]);

  const handleProtocoloChange = async (
    selectedProtocolo: ProtocoloOption | null
  ) => {
    if (selectedProtocolo) {
      // Update URL without navigation
      const newParams = new URLSearchParams(searchParams?.toString() || '');
      newParams.set('protocolo', selectedProtocolo.id.toString());
      window.history.pushState({}, '', `?${newParams.toString()}`);
    } else {
      // Clear protocolo from URL
      const newParams = new URLSearchParams(searchParams?.toString() || '');
      newParams.delete('protocolo');
      window.history.pushState({}, '', `?${newParams.toString()}`);
    }
  };

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de Protocolo</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='space-y-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold tracking-tight'>
                Relatório de Protocolo
              </h1>
              <p className='text-muted-foreground'>
                Visualize e exporte o relatório de protocolos
              </p>
            </div>
            <div className='flex gap-4'>
              {protocolo && (
                <BotaoDownloadPDF
                  url={`/movimento/protocolo/imprimir/${protocolo.id}`}
                  title='Imprimir Relatório'
                />
              )}
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow'>
            <div className='space-y-6'>
              <div>
                <label className='mb-2 block text-sm font-medium text-gray-700'>
                  Selecione um Protocolo
                </label>
                <ComboboxSelecionarProtocolo
                  onProtocoloChange={handleProtocoloChange}
                />
              </div>

              {loading ? (
                <div className='flex justify-center py-8'>
                  <Skeleton className='h-[400px] w-full max-w-4xl' />
                </div>
              ) : error ? (
                <div className='py-8 text-center'>
                  <p className='text-red-500'>{error}</p>
                </div>
              ) : protocolo ? (
                <div className='mt-6'>
                  <RelatorioProtocolo protocolo={protocolo} />
                </div>
              ) : protocoloId ? (
                <div className='py-8 text-center'>
                  <p className='text-gray-500'>
                    Protocolo não encontrado ou não há dados disponíveis.
                  </p>
                </div>
              ) : (
                <div className='py-8 text-center'>
                  <p className='text-gray-500'>
                    Selecione um protocolo acima para visualizar o relatório.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
