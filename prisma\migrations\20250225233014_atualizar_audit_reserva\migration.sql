/*
  Warnings:

  - You are about to drop the column `descricao` on the `reserva_itens` table. All the data in the column will be lost.
  - You are about to drop the column `unidadeItem` on the `reserva_itens` table. All the data in the column will be lost.
  - You are about to drop the column `descricao` on the `reserva_itens_audit` table. All the data in the column will be lost.
  - You are about to drop the column `unidadeItem` on the `reserva_itens_audit` table. All the data in the column will be lost.
  - Added the required column `desc` to the `reserva_itens` table without a default value. This is not possible if the table is not empty.
  - Added the required column `unidade` to the `reserva_itens` table without a default value. This is not possible if the table is not empty.
  - Added the required column `acao` to the `reserva_itens_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `desc` to the `reserva_itens_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `idUsuario` to the `reserva_itens_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `ip` to the `reserva_itens_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `unidade` to the `reserva_itens_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `acao` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes1` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes10` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes11` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes12` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes2` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes3` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes4` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes5` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes6` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes7` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes8` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `cotaMes9` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `exercicio` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `idUsuario` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `ip` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `valorTotalCota` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "reserva_itens" DROP COLUMN "descricao",
DROP COLUMN "unidadeItem",
ADD COLUMN     "desc" TEXT NOT NULL,
ADD COLUMN     "unidade" SMALLINT NOT NULL;

-- AlterTable
ALTER TABLE "reserva_itens_audit" DROP COLUMN "descricao",
DROP COLUMN "unidadeItem",
ADD COLUMN     "acao" SMALLINT NOT NULL,
ADD COLUMN     "desc" TEXT NOT NULL,
ADD COLUMN     "idUsuario" INTEGER NOT NULL,
ADD COLUMN     "ip" INET NOT NULL,
ADD COLUMN     "unidade" SMALLINT NOT NULL;

-- AlterTable
ALTER TABLE "reservas_audit" ADD COLUMN     "acao" SMALLINT NOT NULL,
ADD COLUMN     "cotaMes1" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes10" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes11" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes12" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes2" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes3" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes4" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes5" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes6" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes7" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes8" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "cotaMes9" DECIMAL(18,2) NOT NULL,
ADD COLUMN     "exercicio" SMALLINT NOT NULL,
ADD COLUMN     "idUsuario" INTEGER NOT NULL,
ADD COLUMN     "ip" INET NOT NULL,
ADD COLUMN     "valorTotalCota" DECIMAL(18,2) NOT NULL;

-- AddForeignKey
ALTER TABLE "reservas_audit" ADD CONSTRAINT "reservas_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva_itens_audit" ADD CONSTRAINT "reserva_itens_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
