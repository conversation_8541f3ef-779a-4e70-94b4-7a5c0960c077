'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AuditoriaMovimentoProtocolos } from '@/lib/enums';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface HistoricoEntry {
  id: number;
  uuidUsuario: string;
  acao: AuditoriaMovimentoProtocolos;
  ip: string;
  dataHora: Date;
  usuario?: {
    nome: string;
  };
  details?: {
    novoStatus?: number;
    statusAnterior?: number;
    observacoes?: string;
    [key: string]: any;
  };
}

interface HistoricoProtocoloProps {
  protocoloId: number;
}

export function HistoricoProtocolo({ protocoloId }: HistoricoProtocoloProps) {
  const [historico, setHistorico] = useState<HistoricoEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadHistorico = async () => {
      try {
        const response = await fetch(
          `/api/movimento/protocolo/${protocoloId}/historico`
        );
        if (response.ok) {
          const data = await response.json();
          setHistorico(data);
        }
      } catch (error) {
        console.error('Erro ao carregar histórico:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadHistorico();
  }, [protocoloId]);

  const getAcaoDescription = (acao: AuditoriaMovimentoProtocolos) => {
    switch (acao) {
      case AuditoriaMovimentoProtocolos.CRIAR_PROTOCOLO:
        return 'Criação de Protocolo';
      case AuditoriaMovimentoProtocolos.ALTERAR_STATUS:
        return 'Alteração de Status';
      case AuditoriaMovimentoProtocolos.ALTERAR_STATUS_LOTE:
        return 'Alteração de Status em Lote';
      case AuditoriaMovimentoProtocolos.CONSULTAR_HISTORICO:
        return 'Consulta de Histórico';
      case AuditoriaMovimentoProtocolos.VINCULAR_AF:
        return 'Vinculação de AF';
      case AuditoriaMovimentoProtocolos.VINCULAR_EMPENHO:
        return 'Vinculação de Empenho';
      default:
        return 'Ação Desconhecida';
    }
  };

  const getAcaoColor = (acao: AuditoriaMovimentoProtocolos) => {
    switch (acao) {
      case AuditoriaMovimentoProtocolos.CRIAR_PROTOCOLO:
        return 'bg-green-100 text-green-800';
      case AuditoriaMovimentoProtocolos.ALTERAR_STATUS:
      case AuditoriaMovimentoProtocolos.ALTERAR_STATUS_LOTE:
        return 'bg-blue-100 text-blue-800';
      case AuditoriaMovimentoProtocolos.VINCULAR_AF:
      case AuditoriaMovimentoProtocolos.VINCULAR_EMPENHO:
        return 'bg-purple-100 text-purple-800';
      case AuditoriaMovimentoProtocolos.CONSULTAR_HISTORICO:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Histórico do Protocolo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='py-4 text-center'>Carregando histórico...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Histórico do Protocolo</CardTitle>
      </CardHeader>
      <CardContent>
        {historico.length === 0 ? (
          <div className='py-4 text-center text-gray-500'>
            Nenhum histórico encontrado
          </div>
        ) : (
          <ScrollArea className='h-[400px] w-full'>
            <div className='space-y-4'>
              {historico.map((entry) => (
                <div
                  key={entry.id}
                  className='border-l-4 border-gray-200 pb-4 pl-4'
                >
                  <div className='mb-2 flex items-center justify-between'>
                    <div className='flex items-center gap-2'>
                      <Badge className={getAcaoColor(entry.acao)}>
                        {getAcaoDescription(entry.acao)}
                      </Badge>
                      <span className='text-sm text-gray-500'>
                        {format(
                          new Date(entry.dataHora),
                          "dd/MM/yyyy 'às' HH:mm",
                          { locale: ptBR }
                        )}
                      </span>
                    </div>
                    {entry.usuario && (
                      <span className='text-sm text-gray-600'>
                        {entry.usuario.nome}
                      </span>
                    )}
                  </div>

                  {entry.details && (
                    <div className='space-y-1 text-sm text-gray-700'>
                      {entry.details.statusAnterior !== undefined && (
                        <div>
                          Status alterado de {entry.details.statusAnterior} para{' '}
                          {entry.details.novoStatus}
                        </div>
                      )}
                      {entry.details.observacoes && (
                        <div>
                          <strong>Observações:</strong>{' '}
                          {entry.details.observacoes}
                        </div>
                      )}
                      {entry.details.numeroAF && (
                        <div>
                          <strong>AF vinculada:</strong>{' '}
                          {entry.details.numeroAF}/{entry.details.exercicioAF}
                        </div>
                      )}
                      {entry.details.numeroEmpenho && (
                        <div>
                          <strong>Empenho vinculado:</strong>{' '}
                          {entry.details.numeroEmpenho}/
                          {entry.details.exercicioEmpenho}
                        </div>
                      )}
                    </div>
                  )}

                  <div className='mt-1 text-xs text-gray-400'>
                    IP: {entry.ip}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
