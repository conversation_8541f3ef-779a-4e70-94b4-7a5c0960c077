-- CreateTable
CREATE TABLE "cotasReducao_audit_valores" (
    "id" SERIAL NOT NULL,
    "idCotaReducaoAudit" INTEGER NOT NULL,
    "mes" SMALLINT NOT NULL,
    "de" DECIMAL(18,2) NOT NULL,
    "para" DECIMAL(18,2) NOT NULL,

    CONSTRAINT "cotasReducao_audit_valores_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "cotasReducao_audit_valores" ADD CONSTRAINT "cotasReducao_audit_valores_idCotaReducaoAudit_fkey" FOREIGN KEY ("idCotaReducaoAudit") REFERENCES "cotasReducao_audit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
