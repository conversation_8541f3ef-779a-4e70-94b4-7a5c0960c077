'use client';

import * as React from 'react';
import { ArrowDown, Check, ChevronsUpDown } from 'lucide-react';

import { cn, codigoSecretariaMask } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { AcessoCargo } from '@/types/app';
import { listarSubdepartamentosAtivos } from '@/lib/database/gerenciamento/cargos';

export function ComboboxSelecionarSubdepartamento({
  acessosConfigurados,
  setAcessosConfigurados,
  subdepartamentos,
}: {
  acessosConfigurados: AcessoCargo[];
  setAcessosConfigurados: React.Dispatch<React.SetStateAction<AcessoCargo[]>>;
  subdepartamentos: Awaited<ReturnType<typeof listarSubdepartamentosAtivos>>;
}) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState('');

  if (!subdepartamentos.data) return <></>;

  const subdepartamentoSelecionado = subdepartamentos.data.find(
    (subdepartamento) => subdepartamento.id === Number(value)
  );
  const codigoString = `${subdepartamentoSelecionado?.departamento.secretaria.codigo.toString()}.${subdepartamentoSelecionado?.departamento.codigo.toString()}.${subdepartamentoSelecionado?.codigo.toString()}`;

  const setAcessos = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();

    if (!subdepartamentoSelecionado || !value) return;

    const acessos = acessosConfigurados.filter((m) => m.id !== Number(value));
    setAcessosConfigurados([
      {
        id: Number(value),
        nome: subdepartamentoSelecionado.nome,
        codigo: codigoString,
      },
      ...acessos,
    ]);

    setValue('');
  };

  const acessosConfiguradosIds = acessosConfigurados.map((acesso) => {
    return acesso.id;
  });

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {value && subdepartamentoSelecionado
              ? `${codigoString} - ${subdepartamentoSelecionado?.nome}`
              : 'Selecione o subdepartamento...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar subdepartamento...' />
            <CommandEmpty>Subdepartamento não encontrado.</CommandEmpty>
            <CommandList>
              {subdepartamentos.data
                .filter(
                  (subdepartamento) =>
                    !acessosConfiguradosIds.includes(subdepartamento.id)
                )
                .map((subdepartamento) => (
                  <CommandItem
                    key={subdepartamento.id}
                    value={`${subdepartamento.id}`}
                    onSelect={(currentValue) => {
                      setValue(
                        Number(currentValue) !== subdepartamento.id
                          ? ''
                          : currentValue
                      );
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        Number(value) === subdepartamento.id
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                    />
                    {codigoSecretariaMask(
                      subdepartamento.departamento.secretaria.codigo.toString()
                    )}
                    .
                    {codigoSecretariaMask(
                      subdepartamento.departamento.codigo.toString()
                    )}
                    .{codigoSecretariaMask(subdepartamento.codigo.toString())} -{' '}
                    {subdepartamento.nome}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <Button
        type='button'
        variant='secondary'
        disabled={value === ''}
        onClick={setAcessos}
      >
        <ArrowDown className='mr-2 h-4 w-4' /> Adicionar Subdepartamento
      </Button>
    </>
  );
}
