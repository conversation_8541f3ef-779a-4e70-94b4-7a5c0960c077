import { Modu<PERSON> } from '@/lib/modulos';
import { MainNavItem } from '@/types/nav';

interface MenuConfig {
  mainNav: MainNavItem[];
}

export const menuConfig: MenuConfig = {
  mainNav: [
    {
      title: '1.00 - Gerenciamento',
      items: [
        {
          title: '1.01 - Secretarias',
          href: '/gerenciamento/secretarias',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
        },
        {
          title: '1.02 - Cargos',
          href: '/gerenciamento/cargos',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_CARGOS,
        },
        {
          title: '1.03 - Usuários',
          href: '/gerenciamento/usuarios',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
        },
        {
          title: '1.04 - Econômicas',
          href: '/gerenciamento/economicas',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
        },
        {
          title: '1.05 - Funcionais',
          href: '/gerenciamento/funcionais',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
        },
        {
          title: '1.06 - Dotações',
          href: '/gerenciamento/dotacoes',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
        },
        {
          title: '1.07 - Fornecedores',
          href: '/gerenciamento/fornecedores',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
        },
        {
          title: '1.08 - Superavits',
          href: '/gerenciamento/superavits',
          items: [],
          modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
        },
        // {
        //   title: '1.09 - Usuarios Externos',
        //   href: '/gerenciamento/usuarios-externos',
        //   items: [],
        //   modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
        // },
        // {
        //   title: '1.10 - Gerenciamento de Templates',
        //   href: '/gerenciamento/templates-documentos',
        //   items: [],
        //   modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
        // }
      ],
    },
    {
      title: '2.00 - Movimento',
      items: [
        {
          title: '2.01 - Reservas',
          href: '/movimento/reservas',
          items: [],
          modulo: Modulos.MOVIMENTO_RESERVA,
        },
        {
          title: '2.02 - Empenhos',
          href: '/movimento/empenhos',
          items: [],
          modulo: Modulos.MOVIMENTO_EMPENHO,
        },
        {
          title: '2.03 - AFs',
          href: '/movimento/af',
          items: [],
          modulo: Modulos.MOVIMENTO_AFS,
        },
        {
          title: '2.04 - Liquidações',
          href: '/movimento/liquidacoes',
          items: [],
          modulo: Modulos.MOVIMENTO_LIQUIDACAO,
        },
        {
          title: '2.05 - Alteração Orçamentária',
          href: '/movimento/alteracaoOrcamentaria',
          items: [],
          modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
        },
        {
          title: '2.06 - Contratos',
          href: '/movimento/contratos',
          items: [],
          modulo: Modulos.MOVIMENTO_CONTRATO,
        },
        {
          title: '2.07 - Protocolos',
          href: '/movimento/protocolo',
          items: [],
          modulo: Modulos.MOVIMENTO_PROTOCOLO,
        },
      ],
    },
    {
      title: '3.00 - Relatórios',
      items: [
        {
          title: '3.01 - Dotações',
          href: '/relatorios/gerenciamento/dotacoes',
          items: [],
          modulo: Modulos.RELATORIOS_GERENCIAMENTO,
        },
        {
          title: '3.02 - Despesas',
          href: '/relatorios/gerenciamento/despesas',
          items: [],
          modulo: Modulos.RELATORIOS_GERENCIAMENTO,
        },
        {
          title: '3.03 - Cotas',
          href: '/relatorios/gerenciamento/cotas',
          items: [],
          modulo: Modulos.RELATORIOS_GERENCIAMENTO,
        },
        {
          title: '3.04 - AFs',
          href: '/relatorios/movimento/af/lista',
          items: [
            {
              title: 'Relatório de AF Individual',
              href: '/relatorios/movimento/af',
              items: [],
            },
            {
              title: 'Lista de AFs',
              href: '/relatorios/movimento/af/lista',
              items: [],
            },
          ],
          modulo: Modulos.RELATORIOS_MOVIMENTO,
        },
        {
          title: '3.05 - Protocolos',
          href: '/relatorios/movimento/protocolo',
          items: [],
          modulo: Modulos.RELATORIOS_MOVIMENTO,
        },
        {
          title: '3.06 - Empenhos',
          href: '/relatorios/movimento/empenhos',
          items: [],
          modulo: Modulos.RELATORIOS_MOVIMENTO,
        },
        {
          title: '3.07 - Liquidações',
          href: '/relatorios/movimento/liquidacoes',
          items: [],
          modulo: Modulos.RELATORIOS_MOVIMENTO,
        },
      ],
    },
    // {
    //   title: '4.00 - Assinatura',
    //   items: [
    //     {
    //       title: '4.01 - Assinatura de Reservas',
    //       href: '/assinatura/reservas',
    //       items: [],
    //       modulo: Modulos.ASSINATURA_RESERVA,
    //     },
    //     {
    //       title: '4.02 - Assinatura Externa',
    //       href: '/assinatura/externa',
    //       items: [],
    //       modulo: Modulos.ASSINATURA_EXTERNA,
    //     },
    //   ],
    // },
    // {
    //     title: "Tabelas Iniciais",
    //     items: [
    //         {
    //             title: "Pessoa Física",
    //             href: "/",
    //             items: [],
    //         },

    //     ],
    // },
    // {
    //     title: "Receitas",
    //     items: [
    //         {
    //             title: "Next.js",
    //             href: "/docs/installation/next",
    //             items: [],
    //         },
    //     ],
    // },
    // {
    //     title: "Movimento",
    //     items: [
    //         {
    //             title: "Reservar",
    //             href: "/",
    //             items: [],
    //         },

    //     ],
    // },
    // {
    //     title: "Relatórios",
    //     items: [
    //         {
    //             title: "Relatório de Pessoas Físicas",
    //             href: "/relatorios/pessoas-fisicas",
    //             items: [],
    //         },
    //         {
    //             title: "Relatório de Usuários",
    //             href: "/relatorios/usuarios",
    //             items: [],
    //         },
    //         {
    //             title: "Relatório de Locais",
    //             href: "/relatorios/locais",
    //             items: [],
    //         },
    //         {
    //             title: "Relatório de Pedidos de Compras",
    //             href: "/relatorios/pedido-de-compras",
    //             items: [],
    //         },
    //         {
    //             title: "Relatório de Pedidos Plurianuais",
    //             href: "/relatorios/pedidos-plurianuais",
    //             items: [],
    //         },
    //         {
    //             title: "Relatório de Empenhos",
    //             href: "/relatorios/empenhos",
    //             items: [],
    //         },
    //         {
    //             title: "Relatório de Empenhos Liquidados",
    //             href: "/relatorios/empenhos-liquidados",
    //             items: [],
    //         },
    //     ],
    // },
    // {
    //     title: "Mensagens",
    //     items: [
    //         {
    //             title: "Mensagens",
    //             href: "/",
    //             items: [],
    //         },
    //     ]
    // },
    // {
    //     title: "Segurança",
    //     items: [
    //         {
    //             title: "Segurança",
    //             href: "/",
    //             items: [],
    //         },
    //     ]
    // }
  ],
};
