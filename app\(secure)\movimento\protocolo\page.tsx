import ProtocolosDatatableWrapper from '@/components/movimento/protocolo/protocolosDatatableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';

export default function ProtocoloPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Movimento de Protocolos</PageTitle>
      </PageHeader>
      <PageContent>
        <ProtocolosDatatableWrapper />
      </PageContent>
    </PageWrapper>
  );
}
