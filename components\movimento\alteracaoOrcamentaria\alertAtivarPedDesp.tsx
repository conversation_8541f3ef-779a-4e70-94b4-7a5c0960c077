import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ativarPedDesp } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Ban } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertAtivarPedDesp({
  idPedDesp,
  descAcao,
  descAlter,
  secretaria,
  departamento,
}: {
  idPedDesp: number;
  descAcao: string;
  descAlter: string;
  secretaria: string;
  departamento: string;
}) {
  const [loading, setLoading] = useState(false);

  const ativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idPedDesp,
      };

      const res = await ativarPedDesp(data);
      if (res?.error) {
        setLoading(false);
        toast.error(res.error);
      } else {
        toast.success('Pedido Ativado.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline' className='text-red-800'>
          <Ban className='mr-2 size-4' /> Ativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Ativar Pedido?</AlertDialogTitle>
          <AlertDialogDescription>
            A alteração orçamentária de despesas{' '}
            <span className='font-bold'>
              {' '}
              {descAcao} - {descAlter} - {secretaria} - {departamento}
            </span>{' '}
            será ativado.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              ativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Ativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
