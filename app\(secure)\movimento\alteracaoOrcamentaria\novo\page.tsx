import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import FormCriarAlteracaoOrcamentaria from '@/components/movimento/alteracaoOrcamentaria/formCriarAlteracaoOrcamentaria';
import { ErrorAlert } from '@/components/error-alert';
import { obterSecretariaDepartamento } from '@/lib/database/movimento/alteracaoOrcamentaria';

export default async function CriacaoSuplementacaoDespesaPage() {
  const secretariasDepartamentosPromise = obterSecretariaDepartamento();
  const secretariasDepartamentos: any = await Promise.all([
    secretariasDepartamentosPromise,
  ]);

  if (secretariasDepartamentos.error) {
    return <ErrorAlert error={secretariasDepartamentos.error} />;
  }

  if (secretariasDepartamentos.data) {
    return <ErrorAlert error='Falha ao obter secretarias e departamentos.' />;
  }

  const { departamentos, secretarias } = secretariasDepartamentos[0].data;

  if (secretarias.error) {
    return <ErrorAlert error={secretarias.error} />;
  }

  if (departamentos.error) {
    return <ErrorAlert error={departamentos.error} />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Nova Alteração Orçamentária</PageTitle>
      </PageHeader>
      <PageContent>
        <FormCriarAlteracaoOrcamentaria
          secretarias={secretarias}
          departamentos={departamentos}
        />
      </PageContent>
    </PageWrapper>
  );
}
