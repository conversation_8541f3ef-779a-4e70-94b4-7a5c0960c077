'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { PlusCircle, Upload } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { documentoMetadataLiquidacaoSchema } from '@/lib/validation';
import {
  moneyMask,
  moneyUnmask,
  currencyOptionsNoSymbol,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import currency from 'currency.js';
import { toast } from 'sonner';
import { uploadPDF } from '@/lib/supabase/clientUtils';
import { Progress } from '@/components/ui/progress';

interface Documento {
  numeroDocumento: string;
  dataEmissao: Date;
  dataRecebimento: Date;
  dataMaterialServico?: Date;
  valorDocumento: number;
  // Optional file fields (for edit mode when files are uploaded)
  nomeArquivo?: string;
  caminhoArquivo?: string;
  tamanho?: number;
}

interface FormAdicionarDocumentoProps {
  onAdicionarDocumento: (documento: Documento) => void;
  saldoDisponivel: number;
  valorJaAdicionado: number;
  // Optional props for edit mode (when liquidação ID exists)
  idLiquidacao?: number;
  exercicio?: number;
  idDotacao?: number;
  idEmpenho?: number;
}

export function FormAdicionarDocumento({
  onAdicionarDocumento,
  saldoDisponivel,
  valorJaAdicionado,
  idLiquidacao,
  exercicio,
  idDotacao,
  idEmpenho,
}: FormAdicionarDocumentoProps) {
  const [open, setOpen] = useState(false);
  const [valorBRL, setValorBRL] = useState('0,00');

  // File upload state (only used in edit mode when idLiquidacao exists)
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Check if we're in edit mode (liquidação ID exists)
  const isEditMode = !!idLiquidacao;

  const form = useForm<z.infer<typeof documentoMetadataLiquidacaoSchema>>({
    resolver: zodResolver(documentoMetadataLiquidacaoSchema),
    defaultValues: {
      numeroDocumento: '',
      dataEmissao: new Date(),
      dataRecebimento: new Date(),
      valorDocumento: 0,
    },
  });

  const saldoRestante = saldoDisponivel - valorJaAdicionado;

  const onSubmit = async (
    values: z.infer<typeof documentoMetadataLiquidacaoSchema>
  ) => {
    if (values.valorDocumento > saldoRestante) {
      toast.error(
        `Valor do documento (${currency(values.valorDocumento, currencyOptionsNoSymbol).format()}) excede o saldo disponível (${currency(saldoRestante, currencyOptionsNoSymbol).format()})`
      );
      return;
    }

    // Get file info if uploaded (in edit mode)
    const fileInfo = (form as any).fileInfo;

    onAdicionarDocumento({
      numeroDocumento: values.numeroDocumento,
      dataEmissao: values.dataEmissao,
      dataRecebimento: values.dataRecebimento,
      dataMaterialServico: values.dataMaterialServico,
      valorDocumento: values.valorDocumento,
      // Include file info if available (edit mode)
      ...(fileInfo && {
        nomeArquivo: fileInfo.nomeArquivo,
        caminhoArquivo: fileInfo.caminhoArquivo,
        tamanho: fileInfo.tamanho,
      }),
    });

    form.reset();
    setValorBRL('0,00');
    // Clear file info
    (form as any).fileInfo = null;
    setOpen(false);
    toast.success('Documento adicionado com sucesso!');
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Only allow file upload in edit mode
    if (
      !isEditMode ||
      !idLiquidacao ||
      !exercicio ||
      !idDotacao ||
      !idEmpenho
    ) {
      toast.error(
        'Upload de arquivos disponível apenas na edição da liquidação'
      );
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('Arquivo muito grande. Tamanho máximo: 10MB');
      return;
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/jpg',
    ];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Tipo de arquivo não permitido. Use PDF, JPG ou PNG.');
      return;
    }

    try {
      setUploading(true);
      setUploadProgress(0);

      // Generate file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = file.name.split('.').pop();
      const fileName = `LIQUIDACAO_${idLiquidacao}_${timestamp}.${fileExtension}`;
      const filePath = `private/${exercicio}/dotacoes/${idDotacao}/empenhos/${idEmpenho}/liquidacoes/${fileName}`;

      // Upload to Supabase
      const uploadResult = await uploadPDF(
        file,
        'reservas',
        filePath,
        (progress) => setUploadProgress(progress)
      );

      if (uploadResult.error) {
        toast.error(uploadResult.error);
        return;
      }

      // Store file info in form for when document is submitted
      (form as any).fileInfo = {
        nomeArquivo: file.name,
        caminhoArquivo: uploadResult.data?.path || filePath,
        tamanho: file.size,
      };

      toast.success('Arquivo carregado com sucesso!');
    } catch (error) {
      toast.error(toastAlgoDeuErrado);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusCircle className='mr-2 size-4' />
          Adicionar Documento
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[600px]'>
        <DialogHeader>
          <DialogTitle>Adicionar Documento</DialogTitle>
          <DialogDescription>
            Adicione um documento comprobatório para a liquidação. Saldo
            disponível:{' '}
            {currency(saldoRestante, currencyOptionsNoSymbol).format()}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='numeroDocumento'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='w-full text-left'>
                      Número do Documento *
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder='Ex: NF-001' />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='valorDocumento'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='w-full text-left'>
                      Valor do Documento *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={valorBRL}
                        onChange={(e) => {
                          setValorBRL(moneyMask(e.target.value, 2));
                          field.onChange(
                            Number(moneyUnmask(e.target.value)) || 0
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        className='text-right'
                        placeholder='R$ 0,00'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='dataEmissao'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='w-full text-left'>
                      Data de Emissão *
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='date'
                        {...field}
                        value={
                          field.value
                            ? field.value.toISOString().split('T')[0]
                            : ''
                        }
                        onChange={(e) =>
                          field.onChange(new Date(e.target.value))
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='dataRecebimento'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='w-full text-left'>
                      Data de Recebimento *
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='date'
                        {...field}
                        value={
                          field.value
                            ? field.value.toISOString().split('T')[0]
                            : ''
                        }
                        onChange={(e) =>
                          field.onChange(new Date(e.target.value))
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='dataMaterialServico'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='w-full text-left'>
                    Data do Material/Serviço
                  </FormLabel>
                  <FormControl>
                    <Input
                      type='date'
                      {...field}
                      value={
                        field.value
                          ? field.value.toISOString().split('T')[0]
                          : ''
                      }
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? new Date(e.target.value) : undefined
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {isEditMode ? (
              <div className='space-y-2'>
                <Label>Arquivo do Documento</Label>
                <div className='flex items-center gap-2'>
                  <Input
                    type='file'
                    accept='.pdf,.jpg,.jpeg,.png'
                    onChange={handleFileUpload}
                    disabled={uploading}
                  />
                  <Upload className='text-muted-foreground size-4' />
                </div>
                {uploading && (
                  <div className='space-y-2'>
                    <Progress value={uploadProgress} className='w-full' />
                    <p className='text-muted-foreground text-sm'>
                      Enviando arquivo... {Math.round(uploadProgress)}%
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className='space-y-2'>
                <p className='text-muted-foreground text-sm'>
                  📄 Arquivos serão enviados após a criação da liquidação
                </p>
              </div>
            )}

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => setOpen(false)}
              >
                Cancelar
              </Button>
              <Button type='submit'>Adicionar</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
