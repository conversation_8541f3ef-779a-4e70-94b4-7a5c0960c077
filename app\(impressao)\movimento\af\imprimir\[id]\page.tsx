'use server';
import { z } from 'zod';
import { obterAFParaRelatorio } from '@/lib/database/relatorios/afs';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioAF } from '@/components/relatorios/relatorioAF';
import { isNumeric } from '@/lib/utils';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function ImprimirAFPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_AFS,
    permissao: Permissoes.ACESSAR,
  };

  const { id } = await params;
  if (!isNumeric(id)) return <ErrorAlert error='ID inválido.' />;

  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }

  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();

  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }

  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const af = await obterAFParaRelatorio({ id: Number(id), bearer });

  if (af.error) {
    return <ErrorAlert error={af.error} />;
  }

  if (!af.data) {
    return <ErrorAlert error='Falha ao obter AF.' />;
  }

  return (
    <>
      <RelatorioAF af={af.data} />
      <ClientCompletionTrigger />
    </>
  );
}
