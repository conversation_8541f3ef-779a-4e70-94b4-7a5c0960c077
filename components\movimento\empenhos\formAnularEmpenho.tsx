'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, XCircle, AlertTriangle } from 'lucide-react';
import { cn, currencyOptionsNoSymbol, toastAlgoDeuErrado } from '@/lib/utils';
import { anularEmpenhoSchema } from '@/lib/validation';
import { StatusEmpenho } from '@/lib/enums';
import { toast } from 'sonner';
import {
  obterEmpenho,
  cancelarEmpenho,
} from '@/lib/database/movimento/empenhos';
import currency from 'currency.js';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function FormAnularEmpenho({
  empenho,
}: {
  empenho: Awaited<ReturnType<typeof obterEmpenho>>;
}) {
  const router = useRouter();

  const dadosEmpenho = empenho.data!;

  const [loading, setLoading] = useState(false);

  const valorAnulado = currency(
    dadosEmpenho.valorAnulado || 0,
    currencyOptionsNoSymbol
  );
  const valorLiquidado = currency(
    dadosEmpenho.valorLiquidado || 0,
    currencyOptionsNoSymbol
  );
  const saldoDisponivelParaAnulacao = currency(
    dadosEmpenho.valorTotal,
    currencyOptionsNoSymbol
  )
    .subtract(valorAnulado)
    .subtract(valorLiquidado);

  const form = useForm<z.infer<typeof anularEmpenhoSchema>>({
    resolver: zodResolver(anularEmpenhoSchema),
    defaultValues: {
      id: dadosEmpenho.id,
      valorAnulacao: saldoDisponivelParaAnulacao.value,
      obs: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof anularEmpenhoSchema>) => {
    try {
      setLoading(true);
      const res = await cancelarEmpenho(values);
      if (res?.error) {
        setLoading(false);
        toast(res.error, { duration: 10000 });
      } else {
        toast.success('Empenho anulado com sucesso', {
          description: `Valor anulado: ${currency(res?.data?.valorAnulado || 0, currencyOptionsNoSymbol).format()}`,
          action: {
            label: 'Visualizar',
            onClick: () =>
              router.push('/movimento/empenhos/visualizar/' + res?.data?.id),
          },
        });
        router.push('/movimento/empenhos');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const subdepartamento = dadosEmpenho.dotacao?.subdepartamento || null;
  const departamento = subdepartamento
    ? dadosEmpenho.dotacao?.subdepartamento?.departamento || null
    : dadosEmpenho.dotacao?.departamento || null;
  const _secretaria = subdepartamento
    ? dadosEmpenho.dotacao?.subdepartamento?.departamento?.secretaria || null
    : departamento
      ? dadosEmpenho.dotacao?.departamento?.secretaria || null
      : dadosEmpenho.dotacao?.secretaria || null;

  return (
    <>
      <div className='w-full'>
        <Card className='mb-6 border-red-200 bg-red-50'>
          <CardHeader>
            <CardTitle className='flex items-center gap-2 text-red-800'>
              <AlertTriangle className='h-5 w-5' />
              Atenção: Anulação de Empenho
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-sm text-red-700'>
              <p className='mb-2'>
                Você está prestes a anular parcial ou totalmente o empenho{' '}
                <strong>{dadosEmpenho.id}</strong>.
              </p>
              <p className='mb-2'>
                <strong>Valor Total:</strong>{' '}
                {currency(
                  dadosEmpenho.valorTotal,
                  currencyOptionsNoSymbol
                ).format()}
              </p>
              <p className='mb-2'>
                <strong>Valor já Liquidado:</strong> {valorLiquidado.format()}
              </p>
              <p className='mb-2'>
                <strong>Valor já Anulado:</strong> {valorAnulado.format()}
              </p>
              <p>
                <strong>Saldo Disponível para Anulação:</strong>{' '}
                {saldoDisponivelParaAnulacao.format()}
              </p>
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex gap-2'>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Empenho Nº
                </FormLabel>
                <Input disabled={true} value={dadosEmpenho.id} />
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>Status</FormLabel>
                <Input
                  disabled={true}
                  value={StatusEmpenho[dadosEmpenho.status]}
                />
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Fornecedor
                </FormLabel>
                <Input
                  disabled={true}
                  value={dadosEmpenho.fornecedor?.nome || ''}
                />
              </FormItem>
            </div>

            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='obs'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>
                      Motivo da Anulação *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='w-full'
                        placeholder='Descreva o motivo da anulação...'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator className='my-12' />
            <span className='text-lg'>Valor a Anular</span>

            <div className='mt-8'>
              <FormField
                control={form.control}
                name='valorAnulacao'
                render={({ field }) => (
                  <FormItem className='flex max-w-[300px] flex-wrap'>
                    <FormLabel className='flex w-full text-left'>
                      Valor da Anulação *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type='number'
                        step='0.01'
                        min='0'
                        max={saldoDisponivelParaAnulacao.value}
                        className='w-full text-right text-red-600'
                        onChange={(e) => {
                          const valor = Math.min(
                            Number(e.target.value),
                            saldoDisponivelParaAnulacao.value
                          );
                          field.onChange(valor);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='mt-12 flex flex-wrap justify-between gap-6'>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>
                  Saldo Disponível
                </FormLabel>
                <FormControl>
                  <Input
                    className='text-right text-base text-green-600 disabled:opacity-100'
                    value={saldoDisponivelParaAnulacao
                      .format()
                      .replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>Valor a Anular</FormLabel>
                <FormControl>
                  <Input
                    className={cn(
                      'text-right text-base disabled:opacity-100',
                      form.watch('valorAnulacao') === 0
                        ? 'text-red-600'
                        : 'font-bold text-red-600'
                    )}
                    value={currency(
                      form.watch('valorAnulacao') || 0,
                      currencyOptionsNoSymbol
                    )
                      .format()
                      .replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
              {form.watch('valorAnulacao') > 0 && (
                <FormItem className='w-[150px]'>
                  <FormLabel className='flex text-left'>
                    Saldo Restante
                  </FormLabel>
                  <FormControl>
                    <Input
                      className='text-right text-base text-orange-600 disabled:opacity-100'
                      value={saldoDisponivelParaAnulacao
                        .subtract(form.watch('valorAnulacao') || 0)
                        .format()
                        .replace(/0$/, '')}
                      disabled={true}
                    />
                  </FormControl>
                </FormItem>
              )}
            </div>
          </form>
        </Form>
      </div>

      <div className='mt-12 flex w-full justify-between'>
        <Button
          variant={'outline'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/movimento/empenhos');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button
          variant={'destructive'}
          onClick={form.handleSubmit(onSubmit)}
          disabled={
            loading ||
            !form.watch('obs')?.trim() ||
            !form.watch('valorAnulacao') ||
            form.watch('valorAnulacao') <= 0 ||
            form.watch('valorAnulacao') > saldoDisponivelParaAnulacao.value
          }
        >
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <XCircle className='mr-2 h-4 w-4' /> Anular Empenho
            </>
          )}
        </Button>
      </div>
    </>
  );
}
