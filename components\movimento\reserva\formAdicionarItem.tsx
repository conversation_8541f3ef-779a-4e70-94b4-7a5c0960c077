'use client';

import * as React from 'react';
import { ArrowDown, Loader } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Item } from '@/types/app';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { itemFormSchema } from '@/lib/validation';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { z } from 'zod';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Unidades, UnidadesDesc } from '@/lib/enums';
import {
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  uppercaseMask,
} from '@/lib/utils';
import currency from 'currency.js';
import CadastroItensDatatable from './cadastroItensDatatable';

export function FormAdicionarItem({
  data,
  setData,
  decimais,
}: {
  data: Item[];
  setData: React.Dispatch<React.SetStateAction<Item[]>>;
  decimais: number;
}) {
  const [loading, setLoading] = React.useState(false);
  const [valorUnitarioBRL, setValorUnitarioBRL] = React.useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );

  const form = useForm<z.infer<typeof itemFormSchema>>({
    resolver: zodResolver(itemFormSchema),
    defaultValues: {
      //@ts-ignore
      quantidade: '',
      //@ts-ignore
      unidade: '',
      desc: '',
      //@ts-ignore
      valor: '',
    },
  });

  const resetForm = () => {
    form.reset();
    setValorUnitarioBRL(
      currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
    );
  };

  const onSubmit = async (values: z.infer<typeof itemFormSchema>) => {
    setLoading(true);

    setData([
      {
        quantidade: values.quantidade,
        unidade: values.unidade,
        desc: values.desc,
        valor: values.valor,
      },
      ...data,
    ]);

    resetForm();

    setLoading(false);
  };

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className='flex flex-wrap justify-between gap-4 py-4'>
            <div className='flex flex-wrap gap-4'>
              <FormField
                control={form.control}
                name='quantidade'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Quantidade
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type='number'
                        step={0.0001}
                        className='w-[120px]'
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='unidade'
                render={({ field }) => (
                  <FormItem className='flex w-[300px] flex-wrap'>
                    <FormLabel className='w-full text-left'>Unidade</FormLabel>
                    <Select
                      onValueChange={(e) => {
                        form.setValue('unidade', Number(e), {
                          shouldDirty: true,
                        });
                      }}
                      value={`${field.value}`}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Selecione a unidade' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(Unidades)
                          .filter(
                            (v) => !isNaN(Number(v[0])) && Number(v[0]) >= 0
                          )
                          .map((unidade) => (
                            <SelectItem
                              key={unidade[0]}
                              value={`${unidade[0]}`}
                            >
                              {UnidadesDesc[Number(unidade[0])]} - {unidade[1]}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
            <div className='flex flex-wrap gap-4'>
              <FormField
                control={form.control}
                name='valor'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Valor Unitário
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='0,00'
                        className='w-[150px] text-right'
                        value={valorUnitarioBRL}
                        onChange={(e) => {
                          setValorUnitarioBRL(
                            moneyMask(e.target.value, decimais)
                          );
                          form.setValue(
                            'valor',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => {
                          e.currentTarget.select();
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Valor Total
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder='0,00'
                    className='w-[150px] text-right'
                    value={currency(valorUnitarioBRL, currencyOptionsNoSymbol)
                      .multiply(form.watch('quantidade'))
                      .format()
                      .replace(/0$/, '')}
                    disabled
                  />
                </FormControl>
              </FormItem>
            </div>
            <FormField
              control={form.control}
              name='desc'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormLabel className='flex w-full text-left'>
                    Descrição
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder='Digite...'
                      minLength={2}
                      maxLength={3000}
                      onChange={(event) => {
                        const { value } = event.target;
                        form.setValue('desc', uppercaseMask(value));
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className='flex w-full'>
            <Button
              type='submit'
              disabled={loading}
              variant={'secondary'}
              className='w-full'
            >
              {loading ? (
                <>
                  <Loader className='mr-2 h-4 w-4 animate-spin' />
                  Aguarde...
                </>
              ) : (
                <>
                  <ArrowDown className='mr-2 h-4 w-4' /> Adicionar Item
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
      <CadastroItensDatatable
        data={data}
        setData={setData}
        form={form}
        setValorUnitarioBRL={setValorUnitarioBRL}
        decimais={decimais}
      />
    </>
  );
}
