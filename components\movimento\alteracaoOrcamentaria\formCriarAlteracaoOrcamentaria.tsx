'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Loader2, Save, Search } from 'lucide-react';
import {
  cn,
  // currencyOptions,
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  numeroMask,
  obterAnoAtual,
  obterMesAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import {
  alteracaoOrcamentariaSchema,
  idExercicioSchema,
} from '@/lib/validation';
import {
  listarDepartamentosAtivos,
  listarSecretariasAtivas,
  criarAlteracaoOrcamentaria,
  buscarDespesa,
  obterSecretariaDepartamento,
} from '@/lib/database/movimento/alteracaoOrcamentaria';
import currency from 'currency.js';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ComboboxSelecionaSecretaria } from './comboboxSelecionarSecretaria';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ComboboxSelecionaDepartamento } from './comboboxSelecionarDepartamento';
import { ComboboxSelecionaEconomica } from './comboboxSelecionarEconomica';
import { ComboboxSelecionaFuncional } from './comboboxSelecionarFuncional';
import { Fontes, StatusAlteracaoOrcamentaria } from '@/lib/enums';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function FormCriarAlteracaoOrcamentaria({
  secretarias,
  departamentos,
}: {
  secretarias: Awaited<ReturnType<typeof listarSecretariasAtivas>>;
  departamentos: Awaited<ReturnType<typeof listarDepartamentosAtivos>>;
}) {
  const router = useRouter();
  const [loadingAcao, setLoadingAcao] = useState(false);
  const [loadingSuplementacao, setLoadingSuplementacao] = useState(false);
  const [loadingCopia, setLoadingCopia] = useState(false);
  const [loading, setLoading] = useState(false);
  const [despesa, setDespesa] = useState<Awaited<
    ReturnType<typeof buscarDespesa>
  > | null>(null);
  const [despesaNova, setDespesaNova] = useState<Awaited<any>>(null);
  const [tpAlt, setTpAlt] = useState('anulacao');
  const [tpAcao, setTpAcao] = useState('suplementacao');

  const [economicaId, setEconomicaId] = useState<number | null>(null);
  const [funcionalId, setFuncionalId] = useState<number | null>(null);
  const [economicas, setEconomicas] = useState<any>([{}]);
  const [funcionais, setFuncionais] = useState<any>([{}]);
  const [funcHabilitado, setfuncHabilitado] = useState<boolean>(true);
  const [secretarios, setSecretarios] = useState<any>([{}]);

  const [totalBRL, setTotalBRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes1BRL, setMes1BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes2BRL, setMes2BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes3BRL, setMes3BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes4BRL, setMes4BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes5BRL, setMes5BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes6BRL, setMes6BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes7BRL, setMes7BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes8BRL, setMes8BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes9BRL, setMes9BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes10BRL, setMes10BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes11BRL, setMes11BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes12BRL, setMes12BRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );

  const {
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
    valorAtual,
  } = despesa?.data || {};

  const {
    cotaSupMes1,
    cotaSupMes2,
    cotaSupMes3,
    cotaSupMes4,
    cotaSupMes5,
    cotaSupMes6,
    cotaSupMes7,
    cotaSupMes8,
    cotaSupMes9,
    cotaSupMes10,
    cotaSupMes11,
    cotaSupMes12,
  }: any = despesaNova || {};

  const anoAtual = obterAnoAtual();
  const mesAtual = obterMesAtual();

  const form = useForm<z.infer<typeof alteracaoOrcamentariaSchema>>({
    resolver: zodResolver(alteracaoOrcamentariaSchema),
    defaultValues: {
      id: 0,
      exercicio: anoAtual,
      tipoAlteracao: 1,
      tipoAcao: 1,
      valorTotal: 0,
      valorMes1: 0,
      valorMes2: 0,
      valorMes3: 0,
      valorMes4: 0,
      valorMes5: 0,
      valorMes6: 0,
      valorMes7: 0,
      valorMes8: 0,
      valorMes9: 0,
      valorMes10: 0,
      valorMes11: 0,
      valorMes12: 0,
      obs: '',
      fonte: 0,
      codAplicacao: 0,
      idSecretaria: 0,
      idDepto: 0,
      idEconomica: null,
      idFuncional: 0,
      despesaCopia: 0,
      despesaAcao: 0,
      despesaNova: 0,
      descrDespNova: '',
      status: StatusAlteracaoOrcamentaria['Em Aberto'],
      idSecretario: null,
    },
  });

  const supMes1 = currency(cotaSupMes1 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes1'))
    .format()
    .replace(/0$/, '');
  const supMes2 = currency(cotaSupMes2 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes2'))
    .format()
    .replace(/0$/, '');
  const supMes3 = currency(cotaSupMes3 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes3'))
    .format()
    .replace(/0$/, '');
  const supMes4 = currency(cotaSupMes4 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes4'))
    .format()
    .replace(/0$/, '');
  const supMes5 = currency(cotaSupMes5 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes5'))
    .format()
    .replace(/0$/, '');
  const supMes6 = currency(cotaSupMes6 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes6'))
    .format()
    .replace(/0$/, '');
  const supMes7 = currency(cotaSupMes7 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes7'))
    .format()
    .replace(/0$/, '');
  const supMes8 = currency(cotaSupMes8 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes8'))
    .format()
    .replace(/0$/, '');
  const supMes9 = currency(cotaSupMes9 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes9'))
    .format()
    .replace(/0$/, '');
  const supMes10 = currency(cotaSupMes10 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes10'))
    .format()
    .replace(/0$/, '');
  const supMes11 = currency(cotaSupMes11 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes11'))
    .format()
    .replace(/0$/, '');
  const supMes12 = currency(cotaSupMes12 || 0, currencyOptionsNoSymbol)
    .add(form.getValues('valorMes12'))
    .format()
    .replace(/0$/, '');

  const subMes1 = currency(cotaMes1 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes1'))
    .format()
    .replace(/0$/, '');
  const subMes2 = currency(cotaMes2 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes2'))
    .format()
    .replace(/0$/, '');
  const subMes3 = currency(cotaMes3 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes3'))
    .format()
    .replace(/0$/, '');
  const subMes4 = currency(cotaMes4 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes4'))
    .format()
    .replace(/0$/, '');
  const subMes5 = currency(cotaMes5 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes5'))
    .format()
    .replace(/0$/, '');
  const subMes6 = currency(cotaMes6 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes6'))
    .format()
    .replace(/0$/, '');
  const subMes7 = currency(cotaMes7 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes7'))
    .format()
    .replace(/0$/, '');
  const subMes8 = currency(cotaMes8 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes8'))
    .format()
    .replace(/0$/, '');
  const subMes9 = currency(cotaMes9 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes9'))
    .format()
    .replace(/0$/, '');
  const subMes10 = currency(cotaMes10 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes10'))
    .format()
    .replace(/0$/, '');
  const subMes11 = currency(cotaMes11 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes11'))
    .format()
    .replace(/0$/, '');
  const subMes12 = currency(cotaMes12 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes12'))
    .format()
    .replace(/0$/, '');

  const subValorAtual = currency(valorAtual || 0, currencyOptionsNoSymbol)
    .format()
    .replace(/0$/, '');

  const total = currency(mes1BRL, currencyOptionsNoSymbol)
    .add(mes2BRL)
    .add(mes3BRL)
    .add(mes4BRL)
    .add(mes5BRL)
    .add(mes6BRL)
    .add(mes7BRL)
    .add(mes8BRL)
    .add(mes9BRL)
    .add(mes10BRL)
    .add(mes11BRL)
    .add(mes12BRL);

  const restante = currency(
    totalBRL, //dotacao.data!.valorAtual,
    currencyOptionsNoSymbol
  ).subtract(total);

  const getTpAcao = (e: string, tpAlter?: string) => {
    let tipoAcao = 0;
    let tipoAlterr = tpAlter || tpAlt;
    setTpAcao(e);

    switch (e) {
      case 'criacaoDespesas': // Nenhum avaliaar
        tipoAcao = 0;
        break;
      case 'suplementacao':
        if (tipoAlterr == 'anulacao') {
          tipoAcao = 1;
        } else {
          tipoAcao = 2;
        }
        break;
    }

    form.resetField('despesaAcao');
    form.resetField('despesaCopia');
    form.resetField('despesaNova');
    form.resetField('idEconomica');
    form.resetField('idFuncional');
    form.resetField('fonte');
    setDespesa(null);
    setDespesaNova(null);
    return tipoAcao;
  };

  const carregarDespesaNova = async () => {
    await carregarDespesa('despesaNova');
  };

  const carregarDespesaAcao = async () => {
    await carregarDespesa('despesaAcao');
  };

  const carregarDespesaCopia = async () => {
    await carregarDespesa('despesaCopia');
  };

  const carregarDespesa = async (campo: any) => {
    if (!form.getValues(campo)) {
      return;
    }
    try {
      const data: z.infer<typeof idExercicioSchema> = {
        id: form.getValues(campo) || 0, //usando id mas com numero da despesa
        exercicio: anoAtual,
      };

      if (campo == 'despesaAcao') {
        setLoadingAcao(true);
      } else if (campo == 'despesaCopia') {
        setLoadingCopia(true);
      } else if (campo == 'despesaNova') {
        setLoadingSuplementacao(true);
      }
      // obterSecretariaDepartamento
      const res: any = await buscarDespesa(data);
      if (res?.error) {
        toast.error(res.error);
        setDespesa(null);
        setDespesaNova(null);
        setLoading(false);
      } else {
        if (campo == 'despesaAcao') {
          setDespesa(res);
          // form.resetField('idEconomica');
          setSecretarios(res?.data?.secretariosAssinantes || []);
          form.setValue('fonte', res?.data?.fonte || 0);
          form.setValue('idDepto', res?.data?.departamento?.id);
          form.setValue('idSecretaria', res?.data?.departamento?.secretaria.id);
          form.setValue(
            'idSecretario',
            res?.data?.secretariosAssinantes[0].id || null
          );
        } else if (campo == 'despesaCopia') {
          setFuncionalId(res?.data?.funcional.id);
          setFuncionais({ data: [res?.data?.funcional] });
          setfuncHabilitado(false);
          setEconomicaId(res.data.economica.id);
          setEconomicas({ data: res.data.economicasItens });
          setSecretarios(res?.data?.secretariosAssinantes || []);

          form.setValue('idEconomica', res.data.economica.id);
          form.setValue('idFuncional', res.data.funcional.id);
          form.setValue('codAplicacao', res.data.codAplicacao);
          form.setValue('idDepto', res?.data?.departamento?.id);
          form.setValue('idSecretaria', res?.data?.departamento?.secretaria.id);
          form.setValue(
            'idSecretario',
            res?.data?.secretariosAssinantes[0].id || null
          );

          if (tpAlt != 'anulacao' && tpAcao == 'criacaoDespesas') {
            form.setValue('fonte', res?.data?.fonte || 0);
          }
        } else if ((campo = 'despesaNova')) {
          if (tpAlt == 'anulacao') {
            const cotasDespNova: any = {
              cotaSupMes1: res?.data?.cotaMes1,
              cotaSupMes2: res?.data?.cotaMes2,
              cotaSupMes3: res?.data?.cotaMes3,
              cotaSupMes4: res?.data?.cotaMes4,
              cotaSupMes5: res?.data?.cotaMes5,
              cotaSupMes6: res?.data?.cotaMes6,
              cotaSupMes7: res?.data?.cotaMes7,
              cotaSupMes8: res?.data?.cotaMes8,
              cotaSupMes9: res?.data?.cotaMes9,
              cotaSupMes10: res?.data?.cotaMes1,
              cotaSupMes11: res?.data?.cotaMes11,
              cotaSupMes12: res?.data?.cotaMes12,
              fonte: res?.data?.fonte,
              secretaria: res?.data?.departamento.secretaria,
              departamento: res?.data?.departamento,
              despesa: res?.data?.despesa,
            };
            setDespesaNova(cotasDespNova);
          } else {
            form.setValue('fonte', res?.data?.fonte);
            setDespesa(res);
          }
          setFuncionalId(res?.data?.funcional.id);
          setFuncionais({ data: [res?.data?.funcional] });
          setfuncHabilitado(false);
          setEconomicaId(res?.data?.economica.id);
          setEconomicas({ data: res?.data?.economicasItens });
          setSecretarios(res?.data?.secretariosAssinantes || []);
          form.setValue('idEconomica', res?.data?.economica.id);
          form.setValue('idFuncional', res?.data?.funcional.id);
          form.setValue('codAplicacao', res?.data?.codAplicacao);
          form.setValue(
            'idSecretario',
            res?.data?.secretariosAssinantes[0].id || null
          );
        }
        setLoading(false);
      }
    } catch (error: any) {
      console.log(JSON.stringify(error));
      toast.error(toastAlgoDeuErrado);
      setLoading(false);
    }

    setLoadingAcao(false);
    setLoadingCopia(false);
    setLoadingSuplementacao(false);
    setLoading(false);
  };

  useEffect(() => {
    const secretariass = async () => {
      const secretaria = await obterSecretariaDepartamento();
      if (secretaria.data && !secretaria.data?.acessoTotal) {
        form.setValue(
          'idSecretaria',
          secretaria.data?.secretarias.data[0].id || undefined
        );
      }
    };
    secretariass();
    // form.setFocus('despesaAcao');
  }, [form]);

  const onSubmit = async (
    values: z.infer<typeof alteracaoOrcamentariaSchema>
  ) => {
    try {
      setLoading(true);

      const res: any = await criarAlteracaoOrcamentaria({
        ...values,
        // idSecretaria: secretariaId,
        // idDepto: departamentoId,
        idEconomica: economicaId,
        idFuncional: funcionalId,
        fonte: Number(values.fonte),
      });
      if (res?.error) {
        toast.error(res.error);
        setLoading(false);
      } else {
        toast.success('Alteração orcamentária incluido com sucesso.');
        router.push(`/movimento/alteracaoOrcamentaria`);
        setLoading(false);
      }
    } catch (error: any) {
      toast.error(toastAlgoDeuErrado);
      setLoading(false);
    }
  };

  const onInvalid = (errors: any) => {
    console.log(errors);
    toast.error(JSON.stringify(errors));
  };

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit, onInvalid)}>
          <div className='flex w-full justify-between gap-2'>
            <FormField
              name='id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='flex text-left'>Codigo</FormLabel>
                  <FormControl>
                    <Input {...field} disabled />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              name='idSecretaria'
              render={() => (
                <FormItem>
                  <FormLabel className='flex text-left'>Secretaria</FormLabel>
                  <FormControl>
                    <ComboboxSelecionaSecretaria
                      secretarias={secretarias}
                      form={form}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              name='idDepto'
              render={() => (
                <FormItem>
                  <FormLabel className='flex text-left'>Departamento</FormLabel>
                  <FormControl>
                    <ComboboxSelecionaDepartamento
                      departamentos={departamentos}
                      form={form}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className='mt-8 flex flex-wrap justify-center gap-6'>
            {secretarios.length > 1 && (
              <FormField
                control={form.control}
                name='idSecretario'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='w-full text-left'>
                      Secretário
                    </FormLabel>
                    <Select
                      onValueChange={(e) => {
                        form.setValue('idSecretario', Number(e), {
                          shouldDirty: true,
                        });
                      }}
                      value={`${field.value}`}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Selecione o secretário que irá assinar...' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {secretarios.map((secretario: any) => (
                          <SelectItem
                            key={secretario.id}
                            value={`${secretario.id}`}
                          >
                            {secretario.nome}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            )}
          </div>
          <div className='mt-8 flex flex-wrap justify-center gap-6'>
            <RadioGroup
              defaultValue={tpAlt}
              className='grid-flow-col'
              onValueChange={(e) => {
                let tpAlter = 0;
                setTpAlt(e);
                switch (e) {
                  case 'anulacao':
                    tpAlter = 1;
                    break;
                  case 'superavit':
                    tpAlter = 2;
                    break;
                  case 'excesso':
                    tpAlter = 3;
                    break;
                }
                form.setValue('tipoAlteracao', tpAlter);

                form.setValue('tipoAcao', getTpAcao(tpAcao, e));

                form.resetField('despesaAcao');
                form.resetField('despesaCopia');
                form.resetField('despesaNova');
                form.resetField('idEconomica');
                form.resetField('idFuncional');
                form.resetField('fonte');
                setDespesa(null);
                setDespesaNova(null);
              }}
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='anulacao' />
                <Label htmlFor='r1'>Anulação</Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='superavit' />
                <Label htmlFor='r2'>Superávit</Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='excesso' />
                <Label htmlFor='r3'>Excesso de Arrecadação</Label>
              </div>
            </RadioGroup>
          </div>

          <div className='mt-8 flex flex-wrap justify-center gap-6'>
            <RadioGroup
              defaultValue={tpAcao}
              className='grid-flow-col'
              onValueChange={(e) => {
                setTpAcao(e);
                form.setValue('tipoAcao', getTpAcao(e));
              }}
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='suplementacao' />
                <Label htmlFor='r1'>Suplementação</Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='criacaoDespesas' />
                <Label htmlFor='r2'>Criação de Despesas</Label>
              </div>
            </RadioGroup>
          </div>
          <Separator className='my-12' />
          <>
            <div>
              {tpAcao == 'suplementacao' ? (
                <div className='mt-6 flex flex-wrap justify-between gap-6'>
                  {tpAlt == 'anulacao' && (
                    <div className='flex flex-wrap justify-between'>
                      <FormLabel className='flex text-left'>
                        Anular a Despesa
                      </FormLabel>
                      <div className='mt-2 flex w-full max-w-sm items-center space-x-2'>
                        <FormField
                          name='despesaAcao' //Debitar a Despesa:
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  pattern='[0-99]*'
                                  placeholder='0'
                                  className='max-w-[100px] text-right text-base'
                                  onBlur={carregarDespesaAcao}
                                  onChange={(event) => {
                                    const { value } = event.target;
                                    form.setValue(
                                      'despesaAcao',
                                      Number(numeroMask(value))
                                    );
                                    form.setValue('fonte', Number(0));
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <Button
                          aria-description='Buscar Despesa'
                          disabled={loadingAcao || !form.watch('despesaAcao')}
                          onClick={carregarDespesaAcao}
                        >
                          Buscar
                          {loadingAcao ? (
                            <Loader2 className='size-4 animate-spin' />
                          ) : (
                            <Search className='size-4' />
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                  <div className='flex flex-wrap justify-between'>
                    <FormLabel className='flex text-left'>
                      Suplementar a Despesa
                    </FormLabel>
                    <div className='mt-2 flex w-full max-w-sm items-center space-x-2'>
                      <FormField
                        name='despesaNova' //Debitar a Despesa:
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                {...field}
                                pattern='[0-99]*'
                                placeholder='0'
                                className='max-w-[100px] text-right text-base'
                                onBlur={carregarDespesaNova}
                                onChange={(event) => {
                                  const { value } = event.target;
                                  form.setValue(
                                    'despesaNova',
                                    Number(numeroMask(value))
                                  );
                                }}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <Button
                        className='mr-8'
                        aria-description='Buscar Despesa'
                        disabled={
                          loadingSuplementacao || !form.watch('despesaNova')
                        }
                        onClick={carregarDespesaNova}
                      >
                        Buscar
                        {loadingSuplementacao ? (
                          <Loader2 className='size-4 animate-spin' />
                        ) : (
                          <Search className='size-4' />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className='mt-6 flex flex-wrap justify-between gap-6'>
                  {tpAlt == 'anulacao' ? (
                    <div className='flex flex-wrap justify-between'>
                      <FormLabel className='flex text-left'>
                        Anular a Despesa
                      </FormLabel>
                      <div className='flex w-full max-w-sm items-center space-x-2'>
                        <FormField
                          name='despesaAcao' //Debitar a Despesa:
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  pattern='[0-99]*'
                                  placeholder='0'
                                  className='max-w-[100px] text-right text-base'
                                  onBlur={carregarDespesaAcao}
                                  onChange={(event) => {
                                    const { value } = event.target;
                                    form.setValue(
                                      'despesaAcao',
                                      Number(numeroMask(value))
                                    );
                                    form.setValue('fonte', Number(0));
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <Button
                          className='mr-8'
                          aria-description='Buscar Despesa'
                          disabled={loadingAcao || !form.watch('despesaAcao')}
                          onClick={carregarDespesaAcao}
                        >
                          Buscar
                          {loadingAcao ? (
                            <Loader2 className='size-4 animate-spin' />
                          ) : (
                            <Search className='size-4' />
                          )}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    ''
                  )}
                  <div className='flex flex-wrap justify-between'>
                    <FormLabel className='flex text-left'>
                      Copiar dados da Despesa
                    </FormLabel>
                    <div className='flex w-full max-w-sm items-center space-x-2'>
                      <FormField
                        name='despesaCopia'
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                {...field}
                                pattern='[0-99]*'
                                placeholder='0'
                                className='max-w-[100px] text-right text-base'
                                onBlur={carregarDespesaCopia}
                                onChange={(event) => {
                                  const { value } = event.target;
                                  form.setValue(
                                    'despesaCopia',
                                    Number(numeroMask(value))
                                  );
                                }}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <Button
                        aria-description='Buscar Despesa'
                        disabled={loadingCopia || !form.watch('despesaCopia')}
                        onClick={carregarDespesaCopia}
                      >
                        Buscar
                        {loadingCopia ? (
                          <Loader2 className='size-4 animate-spin' />
                        ) : (
                          <Search className='size-4' />
                        )}
                      </Button>
                    </div>
                  </div>

                  <FormField
                    name='idEconomica'
                    render={() => (
                      <FormItem>
                        <FormLabel className='flex text-left'>
                          Economica
                        </FormLabel>
                        <FormControl>
                          <ComboboxSelecionaEconomica
                            economicas={economicas}
                            economicaId={economicaId}
                            setEconomicaId={setEconomicaId}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    name='idFuncional'
                    render={() => (
                      <FormItem>
                        <FormLabel className='flex text-left'>
                          Funcional
                        </FormLabel>
                        <FormControl>
                          <ComboboxSelecionaFuncional
                            funcionais={funcionais}
                            funcionalId={funcionalId}
                            setFuncionalId={setFuncionalId}
                            habilitado={funcHabilitado}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    name='fonte'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='flex text-left'>Fonte</FormLabel>
                        <Select
                          onValueChange={(e) => {
                            form.setValue('fonte', Number(e), {
                              shouldDirty: true,
                            });
                          }}
                          value={`${field.value}`}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Selecione a fonte' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.entries(Fontes)
                              .filter(
                                (v) => !isNaN(Number(v[0])) && Number(v[0]) > 0
                              )
                              .map((fonte) => (
                                <SelectItem
                                  key={fonte[0]}
                                  value={`${fonte[0]}`}
                                >
                                  {fonte[0]} - {fonte[1]}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='codAplicacao'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='flex text-left'>
                          Código da Aplicação
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            pattern='[0-9]*'
                            placeholder='0'
                            className='max-w-[100px] text-right text-base'
                            onChange={(event) => {
                              const { value } = event.target;
                              form.setValue('codAplicacao', Number(value));
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              )}
              {despesa &&
                !loading &&
                tpAcao == 'suplementacao' &&
                tpAlt == 'anulacao' && (
                  <div className='mt-6 w-full'>{`Despesa: ${despesa?.data?.despesa || ''} - Secretaria: ${despesa?.data?.departamento?.secretaria?.nome || ''} - Departamento: ${despesa?.data?.departamento?.nome || ''} - Fonte: ${despesa?.data?.fonte || ''}`}</div>
                )}
              {despesaNova && !loading && tpAcao == 'suplementacao' && (
                <div className='mt-4 w-full'>{`Despesa: ${despesaNova?.despesa || ''} - Secretaria: ${despesaNova?.secretaria?.nome || ''} - Departamento: ${despesaNova?.departamento?.nome || ''} - Fonte: ${despesaNova?.fonte || ''}`}</div>
              )}
              {despesa &&
                !loading &&
                tpAcao == 'suplementacao' &&
                tpAlt != 'anulacao' && (
                  <div className='mt-4 w-full'>{`Despesa: ${despesa?.data?.despesa || ''} - Secretaria: ${despesa?.data?.departamento?.secretaria?.nome || ''} - Departamento: ${despesa?.data?.departamento?.nome || ''} - Fonte: ${despesa?.data?.fonte || ''}`}</div>
                )}
            </div>
          </>
          <Separator className='my-12' />
          <span>Informe os valores</span>
          <div className='mt-8 flex flex-wrap justify-center gap-6'>
            <FormField
              name='valorTotal'
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='flex text-left'>
                    Valor Total da Operação
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right text-base'
                      value={totalBRL}
                      onChange={(e) => {
                        setTotalBRL(moneyMask(e.target.value, 2));
                        form.setValue(
                          'valorTotal',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              name='restante'
              render={({ field }) => (
                <FormItem className='w-[150px]'>
                  <FormLabel className='flex text-left'>
                    Valor Restante
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className={cn(
                        'text-right text-base disabled:opacity-100',
                        restante.value != 0 ? 'text-red-600' : 'text-green-600'
                      )}
                      value={restante.format().replace(/0$/, '')}
                      disabled={true}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className='mt-8 flex flex-wrap justify-between gap-6'>
            <FormField
              name='valorMes1'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>
                    Janeiro
                  </FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes1') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes1
                          : subMes1
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes1
                        : subMes1
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes1BRL}
                      onChange={(e) => {
                        setMes1BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes1',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 1
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes1) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes1}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes2'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>
                    Fevereiro
                  </FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes2') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes2
                          : subMes2
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes2
                        : subMes2
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes2BRL}
                      onChange={(e) => {
                        setMes2BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes2',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 2
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes2') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes2) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes2}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes3'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>Março</FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes3') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes3
                          : subMes3
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes3
                        : subMes3
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes3BRL}
                      onChange={(e) => {
                        setMes3BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes3',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 3
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes3) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes3}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes4'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>Abril</FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes4') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes4
                          : subMes4
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes4
                        : subMes4
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes4BRL}
                      onChange={(e) => {
                        setMes4BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes4',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 4
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes4) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes4}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes5'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>Maio</FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes5') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes5
                          : subMes5
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes5
                        : subMes5
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes5BRL}
                      onChange={(e) => {
                        setMes5BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes5',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 5
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes5) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes5}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes6'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>Junho</FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes6') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes6
                          : subMes6
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes6
                        : subMes6
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes6BRL}
                      onChange={(e) => {
                        setMes6BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes6',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 6
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes6) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes6}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes7'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>Julho</FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes7') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes7
                          : subMes7
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes7
                        : subMes7
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes7BRL}
                      onChange={(e) => {
                        setMes7BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes7',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 7
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes7) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes7}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes8'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>
                    Agosto
                  </FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes8') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes8
                          : subMes8
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes8
                        : subMes8
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes8BRL}
                      onChange={(e) => {
                        setMes8BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes8',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 8
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes8) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes8}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes9'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>
                    Setembro
                  </FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes9') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes9
                          : subMes9
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes9
                        : subMes9
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes9BRL}
                      onChange={(e) => {
                        setMes9BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes9',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 9
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes9) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes9}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes10'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>
                    Outubro
                  </FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes10') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes10
                          : subMes10
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes10
                        : subMes10
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes10BRL}
                      onChange={(e) => {
                        setMes10BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes10',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 10
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes10) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes10}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes11'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>
                    Novembro
                  </FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes11') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes11
                          : subMes11
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes11
                        : subMes11
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes11BRL}
                      onChange={(e) => {
                        setMes11BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes11',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 11
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes11) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes11}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
            <FormField
              name='valorMes12'
              render={({ field }) => (
                <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                  <FormLabel className='flex w-full text-left'>
                    Dezembro
                  </FormLabel>
                  <Input
                    {...field}
                    className={cn(
                      'max-w-[150px] text-right',
                      form.getValues('valorMes12') > 0 &&
                        'text-green-600 disabled:opacity-100',
                      moneyUnmask(
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes12
                          : subMes12
                      ) < 0 && 'text-red-600 disabled:opacity-100'
                    )}
                    value={
                      tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                        ? supMes12
                        : subMes12
                    }
                    disabled={true}
                  />
                  <FormLabel className='mt-2 flex w-full text-left'>
                    Usar
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className='max-w-[150px] text-right'
                      value={mes12BRL}
                      onChange={(e) => {
                        setMes12BRL(moneyMask(e.target.value));
                        form.setValue(
                          'valorMes12',
                          Number(moneyUnmask(e.target.value)),
                          { shouldDirty: true }
                        );
                      }}
                      onFocus={(e) => e.currentTarget.select()}
                      disabled={
                        (tpAlt == 'anulacao' && !despesa) || mesAtual > 12
                      }
                    />
                  </FormControl>
                  {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                    <div>
                      <FormLabel className='flex w-full text-left'>
                        Suplementado
                      </FormLabel>
                      <Input
                        {...field}
                        className={cn(
                          'max-w-[150px] text-right',
                          form.getValues('valorMes1') > 0 &&
                            'text-green-600 disabled:opacity-100',
                          moneyUnmask(supMes12) < 0 &&
                            'text-red-600 disabled:opacity-100'
                        )}
                        value={supMes12}
                        disabled={true}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </FormItem>
              )}
            />
          </div>
          <div className='mt-12 flex flex-wrap justify-center gap-6'>
            <FormField
              name='valorAtual'
              render={() => (
                <FormItem className='w-[150px]'>
                  <FormLabel className='flex text-left'>
                    Valor Disponível na Despesa
                  </FormLabel>
                  <FormControl>
                    <Input
                      className={cn(
                        'text-right text-base disabled:opacity-100'
                        /*total.value === 0 ||
                          (despesa?.data?.valorAtual &&
                            total.value > despesa.data.valorAtual)
                          ? 'text-red-600'
                          : 'text-green-600'*/
                      )}
                      value={subValorAtual}
                      disabled={true}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <Separator className='my-12' />
          <div className='mt-8 flex flex-wrap justify-between gap-6'>
            <FormField
              control={form.control}
              name='obs'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormLabel className='flex w-full text-left'>
                    Observação
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Digite...'
                      className='resize-none'
                      {...field}
                      maxLength={255}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className='mt-12 flex w-full justify-between'>
            <Button
              variant={'destructive'}
              disabled={loading}
              onClick={(e) => {
                e.preventDefault();
                router.push('/movimento/alteracaoOrcamentaria');
              }}
            >
              <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
            </Button>
            <Button
              onClick={form.handleSubmit(onSubmit, onInvalid)}
              // type='submit'
              /*onClick={() => {
                onSubmit(form.getValues())
              }}*/
              disabled={
                loading ||
                total.value === 0 ||
                restante.value != 0 ||
                Object.keys(form.formState.dirtyFields).length === 0
              }
            >
              {loading ? (
                <>
                  <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                  Aguarde...
                </>
              ) : (
                <>
                  <Save className='mr-2 h-4 w-4' /> Salvar
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
