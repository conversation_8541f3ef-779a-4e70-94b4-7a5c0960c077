// Utility functions for enhanced decimal precision calculations
export function precisionDecimal4(valor: number): number {
  return Math.round(valor * 10000) / 10000;
}

// Function to format currency with 4 decimal precision for internal calculations
export function formatCurrency4Decimals(valor: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 4,
    maximumFractionDigits: 4,
    roundingMode: 'halfEven',
  }).format(valor);
}

// Function to format currency with 2 decimal precision for display (Brazilian standard)
export function formatCurrencyDisplay(valor: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    roundingMode: 'halfEven',
  }).format(valor);
}
