import { temPermissao } from '@/lib/database/usuarios';
import { Permisso<PERSON> } from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import { permissaoSchema } from '@/lib/validation';
import { BotaoSolicitarAssinaturaExterna } from './botaoSolicitarAssinaturaExterna';
import { StatusAssinaturasExternas } from './statusAssinaturasExternas';
import z from 'zod';

interface AssinaturaExternaWrapperProps {
  idReserva: number;
  reserva: {
    id: number;
    resumo: string;
    dotacao: {
      id: number;
      despesa: number;
    };
    exercicio: number;
  };
  statusReserva: number;
}

export async function AssinaturaExternaWrapper({
  idReserva,
  reserva,
  statusReserva,
}: AssinaturaExternaWrapperProps) {
  // Verificar permissão para criar documentos externos
  const parametrosPermissaoSolicitar: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.CRIAR,
  };

  const resultPermissaoSolicitar = await temPermissao(
    parametrosPermissaoSolicitar
  );

  // Verificar permissão para acessar documentos externos
  const parametrosPermissaoAcessar: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.ASSINATURA_EXTERNA,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissaoAcessar = await temPermissao(parametrosPermissaoAcessar);

  // Se não tem nenhuma permissão, não exibir nada
  if (
    (!resultPermissaoSolicitar?.temPermissao &&
      !resultPermissaoAcessar?.temPermissao) ||
    resultPermissaoSolicitar?.error ||
    resultPermissaoAcessar?.error
  ) {
    return null;
  }

  const podeAcessar = resultPermissaoAcessar?.temPermissao;
  const podeSolicitar = resultPermissaoSolicitar?.temPermissao;

  return (
    <div className='space-y-4'>
      {/* Botão para solicitar assinatura externa - apenas para status Reservado */}
      {podeSolicitar &&
        statusReserva === 0 && ( // StatusReserva.Reservado = 0
          <div className='flex justify-end'>
            <BotaoSolicitarAssinaturaExterna
              idReserva={idReserva}
              reserva={reserva}
            />
          </div>
        )}

      {/* Status das assinaturas externas - sempre que tiver permissão de acesso */}
      {podeAcessar && <StatusAssinaturasExternas idReserva={idReserva} />}
    </div>
  );
}
