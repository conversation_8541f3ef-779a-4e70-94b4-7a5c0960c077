import { ErrorAlert } from '@/components/error-alert';
import EmpenhosDatatable from './empenhosDataTable';
import { listarEmpenhos } from '@/lib/database/movimento/empenhos';

export default async function EmpenhosDatatableWrapper() {
  const result = await listarEmpenhos();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Empenhos não encontrados.'} />;

  return <EmpenhosDatatable data={result} />;
}
