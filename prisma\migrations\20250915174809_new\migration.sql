-- CreateTable
CREATE TABLE "empenhos" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "numero" INTEGER NOT NULL,
    "idReserva" INTEGER,
    "idDotacao" INTEGER NOT NULL,
    "idFornecedor" INTEGER,
    "resumo" TEXT,
    "obs" TEXT,
    "valorTotal" DECIMAL(18,3) NOT NULL,
    "status" SMALLINT NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "empenhos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "empenhos_audit" (
    "id" SERIAL NOT NULL,
    "idEmpenho" INTEGER NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "obs" TEXT,

    CONSTRAINT "empenhos_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "afs" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "numero" INTEGER NOT NULL,
    "idEmpenho" INTEGER NOT NULL,
    "resumo" TEXT,
    "obs" TEXT,
    "valorTotal" DECIMAL(18,3) NOT NULL,
    "status" SMALLINT NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "afs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "afs_audit" (
    "id" SERIAL NOT NULL,
    "idAF" INTEGER NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "obs" TEXT,

    CONSTRAINT "afs_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "liquidacoes" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "numero" INTEGER NOT NULL,
    "idEmpenho" INTEGER NOT NULL,
    "valorTotal" DECIMAL(18,3) NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" SMALLINT NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "liquidacoes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "liquidacoes_audit" (
    "id" SERIAL NOT NULL,
    "idLiquidacao" INTEGER NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "obs" TEXT,

    CONSTRAINT "liquidacoes_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "protocolos" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "numero" INTEGER NOT NULL,
    "status" SMALLINT NOT NULL,
    "tipo" SMALLINT NOT NULL,
    "resumo" TEXT,
    "obs" TEXT,
    "idReserva" INTEGER,
    "numeroEmpenho" INTEGER,
    "exercicioEmpenho" INTEGER,
    "numeroAF" INTEGER,
    "exercicioAF" INTEGER,
    "dataAbertura" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dataStatus" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "protocolos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "protocolos_audit" (
    "id" SERIAL NOT NULL,
    "idProtocolo" INTEGER NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "deStatus" SMALLINT NOT NULL,
    "paraStatus" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "obs" TEXT,

    CONSTRAINT "protocolos_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "empenhos_exercicio_idDotacao_idx" ON "empenhos"("exercicio", "idDotacao");

-- CreateIndex
CREATE UNIQUE INDEX "empenhos_exercicio_numero_key" ON "empenhos"("exercicio", "numero");

-- CreateIndex
CREATE UNIQUE INDEX "afs_idEmpenho_numero_key" ON "afs"("idEmpenho", "numero");

-- CreateIndex
CREATE INDEX "liquidacoes_idEmpenho_idx" ON "liquidacoes"("idEmpenho");

-- CreateIndex
CREATE UNIQUE INDEX "liquidacoes_exercicio_numero_key" ON "liquidacoes"("exercicio", "numero");

-- CreateIndex
CREATE INDEX "protocolos_idReserva_idx" ON "protocolos"("idReserva");

-- CreateIndex
CREATE UNIQUE INDEX "protocolos_exercicio_numero_key" ON "protocolos"("exercicio", "numero");

-- AddForeignKey
ALTER TABLE "empenhos" ADD CONSTRAINT "empenhos_idReserva_fkey" FOREIGN KEY ("idReserva") REFERENCES "reservas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "empenhos" ADD CONSTRAINT "empenhos_idDotacao_fkey" FOREIGN KEY ("idDotacao") REFERENCES "dotacoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "empenhos" ADD CONSTRAINT "empenhos_idFornecedor_fkey" FOREIGN KEY ("idFornecedor") REFERENCES "fornecedores"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "empenhos_audit" ADD CONSTRAINT "empenhos_audit_idEmpenho_fkey" FOREIGN KEY ("idEmpenho") REFERENCES "empenhos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "empenhos_audit" ADD CONSTRAINT "empenhos_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "afs" ADD CONSTRAINT "afs_idEmpenho_fkey" FOREIGN KEY ("idEmpenho") REFERENCES "empenhos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "afs_audit" ADD CONSTRAINT "afs_audit_idAF_fkey" FOREIGN KEY ("idAF") REFERENCES "afs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "afs_audit" ADD CONSTRAINT "afs_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "liquidacoes" ADD CONSTRAINT "liquidacoes_idEmpenho_fkey" FOREIGN KEY ("idEmpenho") REFERENCES "empenhos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "liquidacoes_audit" ADD CONSTRAINT "liquidacoes_audit_idLiquidacao_fkey" FOREIGN KEY ("idLiquidacao") REFERENCES "liquidacoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "liquidacoes_audit" ADD CONSTRAINT "liquidacoes_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "protocolos" ADD CONSTRAINT "protocolos_idReserva_fkey" FOREIGN KEY ("idReserva") REFERENCES "reservas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "protocolos_audit" ADD CONSTRAINT "protocolos_audit_idProtocolo_fkey" FOREIGN KEY ("idProtocolo") REFERENCES "protocolos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "protocolos_audit" ADD CONSTRAINT "protocolos_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
