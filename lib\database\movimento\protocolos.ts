'use server';

import { revalidatePath } from 'next/cache';
import { prisma } from '@/lib/prisma';
import { Modulos } from '@/lib/modulos';
import { <PERSON><PERSON><PERSON><PERSON>, StatusProtocolo, StatusReserva } from '@/lib/enums';

export interface ProtocoloWithRelations {
  id: number;
  exercicio: number;
  numero: number;
  status: number;
  resumo?: string | null;
  obs?: string | null;
  numeroAF?: number | null;
  exercicioAF?: number | null;
  numeroEmpenho?: number | null;
  exercicioEmpenho?: number | null;
  ativo: boolean;
  dataAbertura: Date;
  dataStatus: Date;
  secretariaId?: number | null;
  departamentoId?: number | null;
  reserva?: {
    id: number;
    resumo: string;
    obs: string;
    usarTotal: number;
    dotacao?: {
      despesa: number;
      secretaria?: {
        id: number;
        nome: string;
        codigo: number;
        ativo: boolean;
      } | null;
      departamento?: {
        id: number;
        nome: string;
        codigo: number;
        ativo: boolean;
        secretariaId: number;
      } | null;
      subdepartamento?: {
        id: number;
        nome: string;
        codigo: number;
        ativo: boolean;
        departamentoId: number;
      } | null;
    } | null;
  } | null;
  protocolos_audit?: Array<{
    id: number;
    idUsuario: number;
    ip: string;
    data: Date;
    obs?: string | null;
    idProtocolo: number;
    deStatus: number;
    paraStatus: number;
    usuario?: {
      id: number;
      user_id: string;
      nome: string;
      email: string;
      exercicio: number;
      gerente: boolean;
      ativo: boolean;
    } | null;
  }>;
}
import {
  protocoloIdSchema,
  criarProtocoloSchema,
  alterarStatusProtocoloSchema,
  alterarStatusLoteSchema,
  vincularAFSchema,
  vincularEmpenhoSchema,
  permissaoSchema,
  auditoriaErroSchema,
} from '@/lib/validation';
import {
  listarIdsDotacoesUsuarioConectadoTemAcesso,
  obterIpUsuarioConectado,
  temPermissao,
} from '../usuarios';
import { inserirErroAudit } from '../auditoria/erro';
import { Prisma } from '@prisma/client';

const MODULE = Modulos.MOVIMENTO_PROTOCOLO;
const ROUTE = '/movimento/protocolo';

// Matriz de transições de status válidas
const TRANSICOES_STATUS_VALIDAS: Record<number, number[]> = {
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS]: [
    StatusProtocolo.RECEBIMENTO_EM_COMPRAS,
    StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_COMPRAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
    StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE]: [
    StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE,
    StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE,
    StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE]: [
    StatusProtocolo.RECEBIMENTO_EM_GABINETE,
    StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_GABINETE]: [
    StatusProtocolo.AF_ENCAMINHADA,
    StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS,
  ],
  [StatusProtocolo.AF_ENCAMINHADA]: [
    StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM,
  ],
  [StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
  ],
  [StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
  ],
  [StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS]: [
    StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE,
  ],
  [StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM]: [
    StatusProtocolo.AF_ENCAMINHADA,
  ],
  // Status para departamentos específicos
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_SAUDE]: [
    StatusProtocolo.RECEBIMENTO_EM_SAUDE,
    StatusProtocolo.DEVOLUCAO_DE_SAUDE_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_SAUDE]: [
    StatusProtocolo.DEVOLUCAO_DE_SAUDE_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_ESPORTES]: [
    StatusProtocolo.RECEBIMENTO_EM_ESPORTES,
    StatusProtocolo.DEVOLUCAO_DE_ESPORTES_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_ESPORTES]: [
    StatusProtocolo.DEVOLUCAO_DE_ESPORTES_PARA_FINANCAS,
  ],
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_EDUCACAO]: [
    StatusProtocolo.RECEBIMENTO_EM_EDUCACAO,
    StatusProtocolo.DEVOLUCAO_DE_EDUCACAO_PARA_FINANCAS,
  ],
  [StatusProtocolo.RECEBIMENTO_EM_EDUCACAO]: [
    StatusProtocolo.DEVOLUCAO_DE_EDUCACAO_PARA_FINANCAS,
  ],
};

export const listarReservasParaProtocolo = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    let acessos;
    if (!resultPermissao.gerente) {
      acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

      if (acessos.error) {
        return {
          error: acessos.error,
        };
      }

      if (!acessos.data) {
        return {
          error: 'Não foi possível obter acessos do usuário.',
        };
      }
    }

    let where: Prisma.reservasWhereInput | null;

    if (resultPermissao.gerente || acessos?.data?.acessoTotal) {
      where = {
        exercicio: resultPermissao.exercicio,
        status: StatusReserva.Reservado,
      };
    } else {
      where = {
        exercicio: resultPermissao.exercicio,
        status: StatusReserva.Reservado,
        idDotacao: { in: acessos?.data?.dotacoes },
      };
    }

    const reservas = await prisma.reservas.findMany({
      select: {
        id: true,
        resumo: true,
        obs: true,
        usarTotal: true,
        status: true,
        dotacao: {
          select: {
            despesa: true,
            secretariaId: true,
            departamentoId: true,
            departamento: { select: { secretariaId: true } },
          },
        },
      },
      where,
      orderBy: { id: 'desc' },
    });

    return {
      data: {
        reservas: reservas.map((reserva) => {
          return {
            ...reserva,
            usarTotal: reserva.usarTotal.toNumber(),
            secretariaId:
              reserva.dotacao.secretariaId ||
              reserva.dotacao.departamento?.secretariaId ||
              null,
            departamentoId: reserva.dotacao.departamentoId || null,
          };
        }),
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter reservas.`,
    };
  }
};

// Funções de validação
function validarTransicaoStatus(
  statusAtual: number,
  novoStatus: number
): boolean {
  const transicoesPermitidas = TRANSICOES_STATUS_VALIDAS[statusAtual];
  if (!transicoesPermitidas) return false;
  return transicoesPermitidas.includes(novoStatus);
}

async function gerarNumeroProtocolo(exercicio: number): Promise<number> {
  const ultimoProtocolo = await prisma.protocolos.findFirst({
    where: { exercicio },
    orderBy: { numero: 'desc' },
  });
  return ultimoProtocolo ? ultimoProtocolo.numero + 1 : 1;
}

export const listarProtocolos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    let acessos;
    if (!resultPermissao.gerente) {
      acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

      if (acessos.error) {
        return {
          error: acessos.error,
        };
      }

      if (!acessos.data) {
        return {
          error: 'Não foi possível obter acessos do usuário.',
        };
      }
    }

    let where: Prisma.protocolosWhereInput | null;

    if (resultPermissao.gerente || acessos?.data?.acessoTotal) {
      where = {
        ativo: true,
        exercicio: resultPermissao.exercicio,
      };
    } else {
      where = {
        ativo: true,
        exercicio: resultPermissao.exercicio,
        reserva: {
          idDotacao: { in: acessos?.data?.dotacoes },
        },
      };
    }

    const protocolos = await prisma.protocolos.findMany({
      select: {
        id: true,
        exercicio: true,
        numero: true,
        status: true,
        resumo: true,
        obs: true,
        numeroAF: true,
        exercicioAF: true,
        numeroEmpenho: true,
        exercicioEmpenho: true,
        ativo: true,
        dataAbertura: true,
        dataStatus: true,
        reserva: {
          select: {
            id: true,
            resumo: true,
            obs: true,
            usarTotal: true,
            dotacao: {
              select: {
                despesa: true,
                secretariaId: true,
                departamentoId: true,
                secretaria: { select: { nome: true } },
                departamento: { select: { nome: true, secretariaId: true } },
              },
            },
          },
        },
      },
      where,
      orderBy: [{ dataStatus: 'desc' }, { numero: 'desc' }],
    });

    // Extract unique secretarias and departamentos from the protocolos data
    const secretariasIds = protocolos
      .map((protocolo) => protocolo.reserva?.dotacao?.secretariaId || 0)
      .filter((id, index, array) => id !== 0 && array.indexOf(id) === index);

    const departamentosIds = protocolos
      .map((protocolo) => protocolo.reserva?.dotacao?.departamentoId || 0)
      .filter((id, index, array) => id !== 0 && array.indexOf(id) === index);

    const secretariasPromise = prisma.secretarias.findMany({
      where: {
        id: {
          in: secretariasIds,
        },
        ativo: true,
      },
    });

    const departamentosPromise = prisma.departamentos.findMany({
      where: {
        id: {
          in: departamentosIds,
        },
        ativo: true,
      },
      include: { secretaria: true },
    });

    let [secretarias, departamentos] = await Promise.all([
      secretariasPromise,
      departamentosPromise,
    ]);

    // If no secretarias found, use secretarias from departamentos
    if (!secretarias || !secretarias.length) {
      secretarias = departamentos.reduce(
        (acc, departamento) => {
          if (
            acc.find(
              (secretaria) => secretaria.id === departamento.secretaria.id
            )
          ) {
            return acc;
          }
          return [...acc, departamento.secretaria];
        },
        [] as typeof secretarias
      );
    }

    return {
      data: {
        protocolos: protocolos.map((protocolo) => {
          const reserva = protocolo.reserva;

          const returnObj = {
            ...protocolo,
            secretariaId:
              reserva?.dotacao?.secretariaId ||
              reserva?.dotacao?.departamento?.secretariaId ||
              null,
            departamentoId: reserva?.dotacao?.departamentoId || null,
            reserva: reserva
              ? {
                  ...reserva,
                  usarTotal: reserva.usarTotal.toNumber(),
                }
              : null,
          };

          return returnObj;
        }),
        secretarias,
        departamentos,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao listar protocolos.' };
  }
};

export const obterProtocolo = async (params: unknown) => {
  const parsed = protocoloIdSchema.safeParse(params);
  if (!parsed.success) return { error: 'Parâmetros inválidos.' };

  const parametrosPermissao = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    bearer: parsed.data.bearer,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const { id } = parsed.data;

    const protocolo = await prisma.protocolos.findUnique({
      where: { id },
      include: {
        reserva: {
          select: {
            id: true,
            resumo: true,
            obs: true,
            status: true,
            usarTotal: true,
            dotacao: {
              select: {
                id: true,
                despesa: true,
                secretariaId: true,
                departamentoId: true,
                secretaria: {
                  select: {
                    id: true,
                    nome: true,
                    codigo: true,
                    ativo: true,
                  },
                },
                departamento: {
                  select: {
                    id: true,
                    nome: true,
                    codigo: true,
                    ativo: true,
                    secretariaId: true,
                  },
                },
              },
            },
          },
        },
        protocolos_audit: {
          select: {
            id: true,
            idUsuario: true,
            ip: true,
            data: true,
            obs: true,
            idProtocolo: true,
            deStatus: true,
            paraStatus: true,
            usuario: {
              select: {
                id: true,
                user_id: true,
                nome: true,
                email: true,
                exercicio: true,
                gerente: true,
                ativo: true,
              },
            },
          },
          orderBy: { data: 'desc' },
        },
      },
    });

    if (!protocolo) return { error: 'Protocolo não encontrado.' };

    const reserva = protocolo.reserva;

    const returnObj = {
      ...protocolo,
      secretariaId:
        reserva?.dotacao?.secretariaId ||
        reserva?.dotacao?.departamento?.secretariaId ||
        null,
      departamentoId: reserva?.dotacao?.departamentoId || null,
      reserva: reserva
        ? {
            ...reserva,
            usarTotal: reserva.usarTotal.toNumber(),
          }
        : null,
    };

    return { data: returnObj };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao obter protocolo.' };
  }
};

export const listarStatusDisponiveis = async () => {
  const parametrosPermissao = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    // Retorna todos os status disponíveis com base no sistema legado
    const statusDisponiveis = Object.values(StatusProtocolo).filter(
      (value) => typeof value === 'number'
    );
    return { data: statusDisponiveis };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao listar status disponíveis.' };
  }
};

export const criarProtocolo = async (params: unknown) => {
  const parametrosPermissao = {
    modulo: MODULE,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsed = criarProtocoloSchema.safeParse(params);
  if (!parsed.success) return { error: 'Parâmetros inválidos.' };

  const ip = (await obterIpUsuarioConectado()).data;
  try {
    const { idReserva, resumo, obs } = parsed.data;
    const { idUsuario, exercicio: exercicioUsuario } = resultPermissao;

    // Verificar se a reserva existe e está em status "Reservado"
    const reserva = await prisma.reservas.findUnique({
      where: { id: idReserva },
      include: {
        dotacao: true,
      },
    });

    if (!reserva) return { error: 'Reserva não encontrada.' };
    if (reserva.status !== StatusReserva.Reservado)
      return {
        error:
          'Reserva precisa estar em status "Reservado" para criar protocolo.',
      };
    if (reserva.exercicio !== exercicioUsuario)
      return { error: 'Reserva não pertence ao exercício atual.' };

    // Verificar se já existe protocolo para esta reserva
    const protocoloExistente = await prisma.protocolos.findFirst({
      where: {
        idReserva,
        ativo: true,
      },
    });

    if (protocoloExistente)
      return { error: 'Já existe um protocolo ativo para esta reserva.' };

    const numero = await gerarNumeroProtocolo(exercicioUsuario);

    const protocolo = await prisma.$transaction(async (tx) => {
      const novoProtocolo = await tx.protocolos.create({
        data: {
          exercicio: exercicioUsuario,
          numero,
          status: StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
          tipo: 1, // Default tipo for reserva-based protocols
          resumo,
          obs,
          idReserva,
          dataAbertura: new Date(),
          dataStatus: new Date(),
        },
      });

      // Criar registro de auditoria
      await tx.protocolos_audit.create({
        data: {
          idProtocolo: novoProtocolo.id,
          idUsuario: idUsuario!,
          deStatus: StatusProtocolo.Nenhum,
          paraStatus: StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
          ip,
          data: new Date(),
          obs: 'Protocolo criado a partir da reserva',
        },
      });

      return novoProtocolo;
    });

    revalidatePath(ROUTE);
    return { data: protocolo };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao criar protocolo.' };
  }
};

export const atualizarStatusProtocolo = async (params: unknown) => {
  const parametrosPermissao = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsed = alterarStatusProtocoloSchema.safeParse(params);
  if (!parsed.success) return { error: 'Parâmetros inválidos.' };

  const ip = (await obterIpUsuarioConectado()).data;

  try {
    const { id, novoStatus, obs } = parsed.data;
    const { idUsuario } = resultPermissao;

    const protocolo = await prisma.protocolos.findUnique({
      where: { id },
      include: { reserva: true },
    });

    if (!protocolo) return { error: 'Protocolo não encontrado.' };
    if (!protocolo.ativo) return { error: 'Protocolo não está ativo.' };

    // Validar transição de status
    if (!validarTransicaoStatus(protocolo.status, novoStatus)) {
      return { error: 'Transição de status inválida.' };
    }

    const atualizado = await prisma.$transaction(async (tx) => {
      const protocoloAtualizado = await tx.protocolos.update({
        where: { id },
        data: {
          status: novoStatus,
          dataStatus: new Date(),
        },
      });

      // Criar registro de auditoria (apenas se tiver usuário)
      if (idUsuario) {
        await tx.protocolos_audit.create({
          data: {
            idProtocolo: id,
            idUsuario,
            deStatus: protocolo.status,
            paraStatus: novoStatus,
            ip,
            data: new Date(),
            obs,
          },
        });
      }

      return protocoloAtualizado;
    });

    revalidatePath(ROUTE);
    return { data: atualizado };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao atualizar status do protocolo.' };
  }
};

export const atualizarStatusProtocolosLote = async (params: unknown) => {
  const parametrosPermissao = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsed = alterarStatusLoteSchema.safeParse(params);
  if (!parsed.success) return { error: 'Parâmetros inválidos.' };

  const ip = (await obterIpUsuarioConectado()).data;

  try {
    const { ids, novoStatus, obs } = parsed.data;
    const { idUsuario } = resultPermissao;

    const resultados = {
      alterados: [] as number[],
      rejeitados: [] as { id: number; motivo: string }[],
    };

    for (const id of ids) {
      try {
        const protocolo = await prisma.protocolos.findUnique({
          where: { id },
        });

        if (!protocolo || !protocolo.ativo) {
          resultados.rejeitados.push({
            id,
            motivo: 'Protocolo não encontrado ou inativo',
          });
          continue;
        }

        if (!validarTransicaoStatus(protocolo.status, novoStatus)) {
          resultados.rejeitados.push({
            id,
            motivo: 'Transição de status inválida',
          });
          continue;
        }

        await prisma.$transaction(async (tx) => {
          await tx.protocolos.update({
            where: { id },
            data: {
              status: novoStatus,
              dataStatus: new Date(),
            },
          });

          if (idUsuario) {
            await tx.protocolos_audit.create({
              data: {
                idProtocolo: id,
                idUsuario,
                deStatus: protocolo.status,
                paraStatus: novoStatus,
                ip,
                data: new Date(),
                obs: obs,
              },
            });
          }
        });

        resultados.alterados.push(id);
      } catch (e) {
        console.error(`Erro ao processar protocolo ${id}:`, e);
        resultados.rejeitados.push({ id, motivo: 'Erro interno' });
      }
    }

    revalidatePath(ROUTE);
    return { data: resultados };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao atualizar status em lote.' };
  }
};

export const vincularAF = async (params: unknown) => {
  const parametrosPermissao = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsed = vincularAFSchema.safeParse(params);
  if (!parsed.success) return { error: 'Parâmetros inválidos.' };

  const ip = (await obterIpUsuarioConectado()).data;

  try {
    const { id, numeroAF, exercicioAF } = parsed.data;
    const { idUsuario } = resultPermissao;

    const protocolo = await prisma.protocolos.findUnique({
      where: { id },
    });

    if (!protocolo) return { error: 'Protocolo não encontrado.' };
    if (!protocolo.ativo) return { error: 'Protocolo não está ativo.' };
    if (protocolo.numeroAF)
      return { error: 'Protocolo já possui AF vinculada.' };

    const atualizado = await prisma.$transaction(async (tx) => {
      const protocoloAtualizado = await tx.protocolos.update({
        where: { id },
        data: {
          numeroAF,
          exercicioAF,
        },
      });

      // Criar registro de auditoria (apenas se tiver usuário)
      if (idUsuario) {
        await tx.protocolos_audit.create({
          data: {
            idProtocolo: id,
            idUsuario,
            deStatus: StatusProtocolo.Nenhum, // Indica que não é uma mudança de status, mas uma ação de vinculação
            paraStatus: protocolo.status,
            ip,
            data: new Date(),
            obs: `Vinculação da AF ${numeroAF}/${exercicioAF}`,
          },
        });
      }

      return protocoloAtualizado;
    });

    revalidatePath(ROUTE);
    return { data: atualizado };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao vincular AF.' };
  }
};

export const vincularEmpenho = async (params: unknown) => {
  const parametrosPermissao = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsed = vincularEmpenhoSchema.safeParse(params);
  if (!parsed.success) return { error: 'Parâmetros inválidos.' };

  const ip = (await obterIpUsuarioConectado()).data;

  try {
    const { id, numeroEmpenho, exercicioEmpenho } = parsed.data;
    const { idUsuario } = resultPermissao;

    const protocolo = await prisma.protocolos.findUnique({
      where: { id },
    });

    if (!protocolo) return { error: 'Protocolo não encontrado.' };
    if (!protocolo.ativo) return { error: 'Protocolo não está ativo.' };
    if (protocolo.numeroEmpenho)
      return { error: 'Protocolo já possui empenho vinculado.' };

    const atualizado = await prisma.$transaction(async (tx) => {
      const protocoloAtualizado = await tx.protocolos.update({
        where: { id },
        data: {
          numeroEmpenho,
          exercicioEmpenho,
        },
      });

      // Criar registro de auditoria (apenas se tiver usuário)
      if (idUsuario) {
        await tx.protocolos_audit.create({
          data: {
            idProtocolo: id,
            idUsuario,
            deStatus: StatusProtocolo.Nenhum, // Indica que não é uma mudança de status, mas uma ação de vinculação
            paraStatus: protocolo.status,
            ip,
            data: new Date(),
            obs: `Vinculação do Empenho ${numeroEmpenho}/${exercicioEmpenho}`,
          },
        });
      }

      return protocoloAtualizado;
    });

    revalidatePath(ROUTE);
    return { data: atualizado };
  } catch (e) {
    console.log(e);
    const auditData = { erro: JSON.stringify(e), modulo: MODULE };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao vincular empenho.' };
  }
};
