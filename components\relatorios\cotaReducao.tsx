import { siteConfig } from '@/config/site';
import { toCurrency } from '@/lib/serverUtils';
import { CotaReducaoReport } from '@/lib/database/relatorios/dotacoes';

interface RelatorioCotaReducaoProps {
  cotas: CotaReducaoReport[];
  titulo?: string;
  filtros?: {
    exercicio?: number;
    secretaria?: string;
    departamento?: string;
    subdepartamento?: string;
    tipo?: 'cota' | 'reducao';
  };
}

export const RelatorioCotaReducao = ({
  cotas,
  titulo = 'Relatório de Cotas e Reduções',
  filtros,
}: RelatorioCotaReducaoProps) => {
  const valorInicialTotal = cotas.reduce(
    (sum, cota) => sum + cota.valorInicial,
    0
  );
  const valorUtilizadoTotal = cotas.reduce(
    (sum, cota) => sum + (cota.valorInicial - cota.valorAtual),
    0
  );
  const valorAtualTotal = cotas.reduce((sum, cota) => sum + cota.valorAtual, 0);

  return (
    <div className='mx-auto max-w-[900px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
            {filtros.secretaria && <p>Secretaria: {filtros.secretaria}</p>}
            {filtros.departamento && (
              <p>Departamento: {filtros.departamento}</p>
            )}
            {filtros.subdepartamento && (
              <p>Subdepartamento: {filtros.subdepartamento}</p>
            )}
            {filtros.tipo && (
              <p>Tipo: {filtros.tipo === 'cota' ? 'Cotas' : 'Reduções'}</p>
            )}
          </div>
        )}
      </div>

      {/* Tabela */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Despesa</th>
              <th className='p-1 text-right font-bold'>Valor Inicial</th>
              <th className='p-1 text-right font-bold'>Valor Utilizado</th>
              <th className='p-1 text-right font-bold'>Saldo Atual</th>
              <th className='p-1 text-left font-bold'>Último Motivo</th>
            </tr>
          </thead>
          <tbody>
            {cotas.map((cota, index) => (
              <tr
                key={cota.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {cota.despesa}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(cota.valorInicial).format().replace(/0$/, '')}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(cota.valorInicial - cota.valorAtual)
                    .format()
                    .replace(/0$/, '')}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(cota.valorAtual).format().replace(/0$/, '')}
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div className='truncate' title={cota.ultimoMotivo}>
                    {cota.ultimoMotivo || '-'}
                  </div>
                </td>
              </tr>
            ))}
            {/* Linha do Total */}
            <tr className='border-t-2 border-gray-800 bg-gray-100 font-bold'>
              <td className='p-1 text-right'>TOTAIS:</td>
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorInicialTotal).format().replace(/0$/, '')}
              </td>
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorUtilizadoTotal).format().replace(/0$/, '')}
              </td>
              <td className='p-1 text-right font-mono'>
                {toCurrency(valorAtualTotal).format().replace(/0$/, '')}
              </td>
              <td className='p-1'></td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Resumo */}
      <div className='mt-6 grid grid-cols-3 gap-4 text-sm'>
        <div className='rounded bg-gray-100 p-2 text-center'>
          <p className='font-semibold'>Valor Inicial</p>
          <p className='font-mono'>
            {toCurrency(valorInicialTotal).format().replace(/0$/, '')}
          </p>
        </div>
        <div className='rounded bg-gray-100 p-2 text-center'>
          <p className='font-semibold'>Valor Utilizado</p>
          <p className='font-mono'>
            {toCurrency(valorUtilizadoTotal).format().replace(/0$/, '')}
          </p>
        </div>
        <div className='rounded bg-gray-100 p-2 text-center'>
          <p className='font-semibold'>Saldo Atual</p>
          <p className='font-mono'>
            {toCurrency(valorAtualTotal).format().replace(/0$/, '')}
          </p>
        </div>
      </div>

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de registros: {cotas.length}</p>
      </div>
    </div>
  );
};
