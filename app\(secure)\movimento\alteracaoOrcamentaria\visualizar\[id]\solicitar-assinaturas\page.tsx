'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { usuarioEhGerente } from '@/lib/database/usuarios';
import { IframePDF } from '@/components/movimento/alteracaoOrcamentaria/solicitar-assinaturas/iframePDF';

export default async function VisualizarAlteracaoOrcamentariaPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const gerente = await usuarioEhGerente();

  if (gerente.error) {
    return <ErrorAlert error={gerente.error} />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Solicitar Assinaturas</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='mx-6 flex w-full flex-wrap justify-between gap-4'>
          <Link href={`/movimento/alteracaoOrcamentaria/visualizar/${id}`}>
            <Button className='mb-4' variant='secondary'>
              <ArrowLeft className='mr-2 size-4' /> Voltar
            </Button>
          </Link>
        </div>
        {gerente.data && (
          <div className='flex items-center space-x-2'>
            <Checkbox id='incluirGestor' defaultChecked />
            <label
              htmlFor='incluirGestor'
              className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
            >
              Incluir assinatura gestor(a)
            </label>
          </div>
        )}
        <div className='flex w-full flex-wrap justify-center'>
          <IframePDF url={`/movimento/alteracaoOrcamentaria/imprimir/${id}`} />
        </div>
      </PageContent>
    </PageWrapper>
  );
}
