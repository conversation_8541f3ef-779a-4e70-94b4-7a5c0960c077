'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Departamento } from '@/types/app';

export function ComboboxSelecionaDepartamento({
  departamentos,
  depSelecionado,
  setDepSelecionado,
}: {
  departamentos: Departamento[];
  depSelecionado: Departamento | null;
  setDepSelecionado: React.Dispatch<React.SetStateAction<Departamento | null>>;
}) {
  const [open, setOpen] = React.useState(false);

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {depSelecionado
              ? depSelecionado.codigo + ' - ' + depSelecionado.nome
              : 'Selecione o departamento...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar departamento...' />
            <CommandEmpty>Departamento não encontrado.</CommandEmpty>
            <CommandList>
              {departamentos
                .filter((v) => !isNaN(Number(v.codigo)) && Number(v.codigo) > 0)
                .map((dep) => (
                  <CommandItem
                    key={dep.codigo}
                    value={dep.codigo.toString()}
                    onSelect={(currentValue) => {
                      setDepSelecionado(
                        currentValue !== dep.codigo.toString()
                          ? null
                          : {
                              ...dep,
                            }
                      );
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        depSelecionado?.codigo === dep.codigo
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                    />
                    {dep.codigo} - {dep.nome}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
