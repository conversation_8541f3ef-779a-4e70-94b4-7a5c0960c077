'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ChevronsUpDown, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { listarTemplatesDocumentos } from '@/lib/database/gerenciamento/templatesDocumentos';

interface ComboboxSelecionarTemplateProps {
  value?: number;
  onChange: (value: number) => void;
  onTemplateSelect?: (template: any) => void;
}

export function ComboboxSelecionarTemplate({
  value,
  onChange,
  onTemplateSelect,
}: ComboboxSelecionarTemplateProps) {
  const [open, setOpen] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const carregarTemplates = async () => {
      try {
        const resultado = await listarTemplatesDocumentos();
        if (resultado.data) {
          setTemplates(resultado.data.templates);
        }
      } catch (error) {
        console.error('Erro ao carregar templates:', error);
      } finally {
        setLoading(false);
      }
    };

    carregarTemplates();
  }, []);

  const templateSelecionado = templates.find(
    (template) => template.id === value
  );

  const handleSelect = (template: any) => {
    onChange(template.id);
    setOpen(false);
    if (onTemplateSelect) {
      onTemplateSelect(template);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
          disabled={loading}
        >
          {loading
            ? 'Carregando...'
            : templateSelecionado
              ? templateSelecionado.nome
              : 'Selecione um template...'}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-full p-0'>
        <Command>
          <CommandInput placeholder='Buscar template...' />
          <CommandEmpty>Template não encontrado.</CommandEmpty>
          <CommandList>
            {templates.map((template) => (
              <CommandItem
                key={template.id}
                value={template.nome}
                onSelect={() => handleSelect(template)}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    value === template.id ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className='flex flex-col'>
                  <span className='font-medium'>{template.nome}</span>
                  {template.descricao && (
                    <span className='text-muted-foreground text-sm'>
                      {template.descricao}
                    </span>
                  )}
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
