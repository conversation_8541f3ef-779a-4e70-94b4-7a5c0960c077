'use client';
import { useState } from 'react';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Printer, Download, Calendar } from 'lucide-react';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';

export default function RelatorioEmpenhosPage() {
  const [periodoForm, setPeriodoForm] = useState({
    periodoInicio: '',
    periodoFim: '',
    agruparPor: 'mes',
    exercicio: new Date().getFullYear().toString(),
    secretaria: '',
    departamento: '',
    subdepartamento: '',
    status: '',
    fornecedor: '',
  });

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const agrupamentoOptions = [
    { value: 'mes', label: 'Mês' },
    { value: 'secretaria', label: 'Secretaria' },
    { value: 'departamento', label: 'Departamento' },
    { value: 'fornecedor', label: 'Fornecedor' },
  ];

  const handleGerarRelatorioPeriodo = () => {
    if (
      !periodoForm.periodoInicio ||
      !periodoForm.periodoFim ||
      !periodoForm.agruparPor
    ) {
      return null;
    }

    const params: Record<string, string> = {
      periodoInicio: periodoForm.periodoInicio,
      periodoFim: periodoForm.periodoFim,
      agruparPor: periodoForm.agruparPor,
    };

    if (periodoForm.exercicio) params.exercicio = periodoForm.exercicio;
    if (periodoForm.secretaria) params.secretaria = periodoForm.secretaria;
    if (periodoForm.departamento)
      params.departamento = periodoForm.departamento;
    if (periodoForm.subdepartamento)
      params.subdepartamento = periodoForm.subdepartamento;
    if (periodoForm.status) params.status = periodoForm.status;
    if (periodoForm.fornecedor) params.fornecedor = periodoForm.fornecedor;

    return params;
  };

  const reportOptions: Array<{
    id: string;
    title: string;
    description: string;
    url: string;
    icon: React.ComponentType<{ className?: string }>;
    isPeriod?: boolean;
  }> = [
    {
      id: 'geral',
      title: 'Relatório Geral de Empenhos',
      description: 'Relatório completo de todos os empenhos',
      url: '/movimento/empenhos/imprimir',
      icon: Printer,
    },
    {
      id: 'liquidados',
      title: 'Relatório de Empenhos Liquidados',
      description: 'Relatório de empenhos com status de liquidados',
      url: '/movimento/empenhos/imprimir/liquidados',
      icon: Download,
    },
    {
      id: 'periodo',
      title: 'Relatório de Empenhos por Período',
      description: 'Relatório de empenhos filtrado por período específico',
      url: '/movimento/empenhos/imprimir/periodo',
      icon: Calendar,
      isPeriod: true,
    },
    {
      id: 'saldo-nao-processado',
      title: 'Relatório de Saldo Não Processado',
      description: 'Relatório de empenhos com saldo não processado',
      url: '/movimento/empenhos/imprimir/saldo-nao-processado',
      icon: Printer,
    },
  ];

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de Empenhos</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='space-y-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold tracking-tight'>
                Relatórios de Empenhos
              </h1>
              <p className='text-muted-foreground'>
                Visualize e exporte relatórios de empenhos do sistema
              </p>
            </div>
          </div>

          <div className='grid gap-6 md:grid-cols-2'>
            {reportOptions.map((report) => {
              const Icon = report.icon;

              if (report.isPeriod) {
                return (
                  <Dialog
                    open={isDialogOpen}
                    onOpenChange={setIsDialogOpen}
                    key={report.id}
                  >
                    <Card
                      className='cursor-pointer transition-shadow hover:shadow-lg'
                      onClick={() => setIsDialogOpen(true)}
                    >
                      <CardHeader>
                        <CardTitle className='flex items-center gap-2'>
                          <Icon className='h-5 w-5' />
                          {report.title}
                        </CardTitle>
                        <CardDescription>{report.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Button variant='outline' className='w-full'>
                          Configurar Relatório
                        </Button>
                      </CardContent>
                    </Card>

                    <DialogContent className='max-w-2xl'>
                      <DialogHeader>
                        <DialogTitle>
                          Configurar Relatório por Período
                        </DialogTitle>
                        <DialogDescription>
                          Preencha os parâmetros obrigatórios para gerar o
                          relatório de empenhos por período.
                        </DialogDescription>
                      </DialogHeader>

                      <div className='grid gap-4 py-4'>
                        <div className='grid grid-cols-2 gap-4'>
                          <div>
                            <Label htmlFor='periodoInicio'>
                              Período Início *
                            </Label>
                            <Input
                              id='periodoInicio'
                              type='date'
                              value={periodoForm.periodoInicio}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  periodoInicio: e.target.value,
                                }))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor='periodoFim'>Período Fim *</Label>
                            <Input
                              id='periodoFim'
                              type='date'
                              value={periodoForm.periodoFim}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  periodoFim: e.target.value,
                                }))
                              }
                            />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor='agruparPor'>Agrupar por *</Label>
                          <Select
                            value={periodoForm.agruparPor}
                            onValueChange={(value) =>
                              setPeriodoForm((prev) => ({
                                ...prev,
                                agruparPor: value,
                              }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {agrupamentoOptions.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className='grid grid-cols-2 gap-4'>
                          <div>
                            <Label htmlFor='exercicio'>Exercício</Label>
                            <Input
                              id='exercicio'
                              type='number'
                              value={periodoForm.exercicio}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  exercicio: e.target.value,
                                }))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor='fornecedor'>Fornecedor</Label>
                            <Input
                              id='fornecedor'
                              type='number'
                              value={periodoForm.fornecedor}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  fornecedor: e.target.value,
                                }))
                              }
                              placeholder='ID do fornecedor'
                            />
                          </div>
                        </div>

                        <div className='grid grid-cols-3 gap-4'>
                          <div>
                            <Label htmlFor='secretaria'>Secretaria</Label>
                            <Input
                              id='secretaria'
                              type='number'
                              value={periodoForm.secretaria}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  secretaria: e.target.value,
                                }))
                              }
                              placeholder='ID da secretaria'
                            />
                          </div>
                          <div>
                            <Label htmlFor='departamento'>Departamento</Label>
                            <Input
                              id='departamento'
                              type='number'
                              value={periodoForm.departamento}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  departamento: e.target.value,
                                }))
                              }
                              placeholder='ID do departamento'
                            />
                          </div>
                          <div>
                            <Label htmlFor='subdepartamento'>
                              Subdepartamento
                            </Label>
                            <Input
                              id='subdepartamento'
                              type='number'
                              value={periodoForm.subdepartamento}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  subdepartamento: e.target.value,
                                }))
                              }
                              placeholder='ID do subdepartamento'
                            />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor='status'>Status</Label>
                          <Input
                            id='status'
                            type='number'
                            value={periodoForm.status}
                            onChange={(e) =>
                              setPeriodoForm((prev) => ({
                                ...prev,
                                status: e.target.value,
                              }))
                            }
                            placeholder='ID do status (opcional)'
                          />
                        </div>
                      </div>

                      <DialogFooter>
                        <Button
                          variant='outline'
                          onClick={() => setIsDialogOpen(false)}
                        >
                          Cancelar
                        </Button>
                        {handleGerarRelatorioPeriodo() ? (
                          <BotaoDownloadPDF
                            url={report.url}
                            title='Gerar Relatório'
                            params={handleGerarRelatorioPeriodo()!}
                          />
                        ) : (
                          <Button disabled className='w-full'>
                            Preencha os campos obrigatórios
                          </Button>
                        )}
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                );
              }

              return (
                <Card
                  key={report.id}
                  className='transition-shadow hover:shadow-lg'
                >
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <Icon className='h-5 w-5' />
                      {report.title}
                    </CardTitle>
                    <CardDescription>{report.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <BotaoDownloadPDF
                      url={report.url}
                      title='Gerar Relatório'
                    />
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
