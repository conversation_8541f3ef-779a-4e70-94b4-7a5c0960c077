import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { obterLiquidacao } from '@/lib/database/movimento/liquidacoes';
import { VisualizarLiquidacao } from '@/components/movimento/liquidacoes/visualizarLiquidacao';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface VisualizarLiquidacaoPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({
  params,
}: VisualizarLiquidacaoPageProps): Promise<Metadata> {
  const { id: idParam } = await params;
  const id = parseInt(idParam);

  if (isNaN(id)) {
    return {
      title: 'Liquidação não encontrada',
    };
  }

  const result = await obterLiquidacao({ id });

  if (result?.error || !result?.data) {
    return {
      title: 'Liquidação não encontrada',
    };
  }

  return {
    title: `Liquidação ${result.data.numero}/${result.data.exercicio}`,
    description: `Visualizar liquidação ${result.data.numero}/${result.data.exercicio}`,
  };
}

export default async function VisualizarLiquidacaoPage({
  params,
}: VisualizarLiquidacaoPageProps) {
  const { id: idParam } = await params;
  const id = parseInt(idParam);

  if (isNaN(id)) {
    notFound();
  }

  const result = await obterLiquidacao({ id });

  if (result?.error || !result?.data) {
    notFound();
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>
          Liquidação {result.data.numero}/{result.data.exercicio}
        </PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <VisualizarLiquidacao liquidacao={result.data} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
