'use server';

import {
  AuditoriaMovimentoEmpenhos,
  ErrosEmpenho,
  Permissoes,
  StatusEmpenho,
  StatusReserva,
  StatusLiquidacao,
} from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  alterarEmpenhoSchema,
  cancelarEmpenhoSchema,
  criarEmpenhoSchema,
  empenhoIdSchema,
  anularEmpenhoSchema,
  estornarAnulacaoSchema,
  permissaoSchema,
} from '@/lib/validation';
import {
  listarIdsDotacoesUsuarioConectadoTemAcesso,
  obterIpUsuarioConectado,
  temPermissao,
} from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { revalidatePath } from 'next/cache';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { toCurrency } from '@/lib/serverUtils';
import { Prisma } from '@prisma/client';

const MODULE = Modulos.MOVIMENTO_EMPENHO;
const ROUTE = '/movimento/empenhos';

export const listarEmpenhos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    let acessos;
    if (!resultPermissao.gerente) {
      acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

      if (acessos.error) {
        return {
          error: acessos.error,
        };
      }

      if (!acessos.data) {
        return {
          error: 'Não foi possível obter acessos do usuário.',
        };
      }
    }

    let where: Prisma.empenhosWhereInput = {
      ativo: true,
      exercicio: resultPermissao.exercicio,
    };

    if (!resultPermissao.gerente && acessos?.data?.dotacoes) {
      where.idDotacao = { in: acessos.data.dotacoes };
    }

    const empenhos = await prisma.empenhos.findMany({
      include: {
        reserva: {
          include: {
            protocolos: {
              where: { ativo: true },
              take: 1,
            },
          },
        },
        fornecedor: {
          select: {
            id: true,
            nome: true,
            cnpjCpf: true,
          },
        },
        dotacao: {
          select: {
            id: true,
            despesa: true,
            desc: true,
            secretariaId: true,
            departamentoId: true,
            departamento: { select: { secretariaId: true } },
            valorInicial: true,
            valorAtual: true,
            cotaMes1: true,
            cotaMes2: true,
            cotaMes3: true,
            cotaMes4: true,
            cotaMes5: true,
            cotaMes6: true,
            cotaMes7: true,
            cotaMes8: true,
            cotaMes9: true,
            cotaMes10: true,
            cotaMes11: true,
            cotaMes12: true,
          },
        },
        liquidacoes: {
          select: { valorTotal: true },
          where: {
            ativo: true,
            status: { not: StatusLiquidacao.ESTORNADA },
          },
        },
        empenhos_anulacoes: {
          select: { valorAnulado: true },
          where: { ativo: true },
        },
      },
      where,
      orderBy: { id: 'desc' },
    });

    const secretariasIds = empenhos
      .map(
        (empenho) =>
          empenho.dotacao.secretariaId ||
          empenho.dotacao.departamento?.secretariaId ||
          0
      )
      .filter(Boolean);
    const departamentosIds = empenhos
      .map((empenho) => empenho.dotacao.departamentoId || 0)
      .filter(Boolean);

    const [secretarias, departamentos, fornecedores] = await Promise.all([
      prisma.secretarias.findMany({
        where: {
          id: { in: secretariasIds },
          ativo: true,
        },
      }),
      prisma.departamentos.findMany({
        where: {
          id: { in: departamentosIds },
          ativo: true,
        },
        include: { secretaria: true },
      }),
      prisma.fornecedores.findMany({
        where: {
          ativo: true,
          id: {
            in: empenhos
              .map((e) => e.fornecedor?.id)
              .filter((id): id is number => id !== undefined),
          },
        },
      }),
    ]);

    return {
      data: {
        empenhos: empenhos.map((empenho) => {
          // Calculate values for each empenho
          const valorLiquidado = empenho.liquidacoes.reduce(
            (sum, liquidacao) => sum.add(liquidacao.valorTotal.toNumber()),
            currency(0, currencyOptionsNoSymbol)
          );

          const valorAnulado = empenho.empenhos_anulacoes.reduce(
            (sum, anulacao) => sum.add(anulacao.valorAnulado.toNumber()),
            currency(0, currencyOptionsNoSymbol)
          );

          // FIXED: Use sum of monthly quotas instead of valorTotal (matching legacy logic)
          const valorOriginalEmpenho = currency(0, currencyOptionsNoSymbol)
            .add(empenho.usarMes1.toNumber())
            .add(empenho.usarMes2.toNumber())
            .add(empenho.usarMes3.toNumber())
            .add(empenho.usarMes4.toNumber())
            .add(empenho.usarMes5.toNumber())
            .add(empenho.usarMes6.toNumber())
            .add(empenho.usarMes7.toNumber())
            .add(empenho.usarMes8.toNumber())
            .add(empenho.usarMes9.toNumber())
            .add(empenho.usarMes10.toNumber())
            .add(empenho.usarMes11.toNumber())
            .add(empenho.usarMes12.toNumber());

          const saldo = valorOriginalEmpenho
            .subtract(valorLiquidado)
            .subtract(valorAnulado);

          // Convert dotacao Decimal fields to numbers
          const dotacaoConvertido = empenho.dotacao
            ? {
                ...empenho.dotacao,
                valorInicial: empenho.dotacao.valorInicial.toNumber(),
                valorAtual: empenho.dotacao.valorAtual?.toNumber(),
                cotaMes1: empenho.dotacao.cotaMes1?.toNumber(),
                cotaMes2: empenho.dotacao.cotaMes2?.toNumber(),
                cotaMes3: empenho.dotacao.cotaMes3?.toNumber(),
                cotaMes4: empenho.dotacao.cotaMes4?.toNumber(),
                cotaMes5: empenho.dotacao.cotaMes5?.toNumber(),
                cotaMes6: empenho.dotacao.cotaMes6?.toNumber(),
                cotaMes7: empenho.dotacao.cotaMes7?.toNumber(),
                cotaMes8: empenho.dotacao.cotaMes8?.toNumber(),
                cotaMes9: empenho.dotacao.cotaMes9?.toNumber(),
                cotaMes10: empenho.dotacao.cotaMes10?.toNumber(),
                cotaMes11: empenho.dotacao.cotaMes11?.toNumber(),
                cotaMes12: empenho.dotacao.cotaMes12?.toNumber(),
              }
            : null;

          // Convert reserva Decimal fields to numbers
          const reservaConvertida = empenho.reserva
            ? {
                ...empenho.reserva,
                usarTotal: empenho.reserva.usarTotal?.toNumber(),
                usarMes1: empenho.reserva.usarMes1?.toNumber(),
                usarMes2: empenho.reserva.usarMes2?.toNumber(),
                usarMes3: empenho.reserva.usarMes3?.toNumber(),
                usarMes4: empenho.reserva.usarMes4?.toNumber(),
                usarMes5: empenho.reserva.usarMes5?.toNumber(),
                usarMes6: empenho.reserva.usarMes6?.toNumber(),
                usarMes7: empenho.reserva.usarMes7?.toNumber(),
                usarMes8: empenho.reserva.usarMes8?.toNumber(),
                usarMes9: empenho.reserva.usarMes9?.toNumber(),
                usarMes10: empenho.reserva.usarMes10?.toNumber(),
                usarMes11: empenho.reserva.usarMes11?.toNumber(),
                usarMes12: empenho.reserva.usarMes12?.toNumber(),
                pluUsarTotal: empenho.reserva.pluUsarTotal?.toNumber(),
                pluUsarMes1: empenho.reserva.pluUsarMes1?.toNumber(),
                pluUsarMes2: empenho.reserva.pluUsarMes2?.toNumber(),
                pluUsarMes3: empenho.reserva.pluUsarMes3?.toNumber(),
                pluUsarMes4: empenho.reserva.pluUsarMes4?.toNumber(),
                pluUsarMes5: empenho.reserva.pluUsarMes5?.toNumber(),
                pluUsarMes6: empenho.reserva.pluUsarMes6?.toNumber(),
                pluUsarMes7: empenho.reserva.pluUsarMes7?.toNumber(),
                pluUsarMes8: empenho.reserva.pluUsarMes8?.toNumber(),
                pluUsarMes9: empenho.reserva.pluUsarMes9?.toNumber(),
                pluUsarMes10: empenho.reserva.pluUsarMes10?.toNumber(),
                pluUsarMes11: empenho.reserva.pluUsarMes11?.toNumber(),
                pluUsarMes12: empenho.reserva.pluUsarMes12?.toNumber(),
              }
            : null;

          return {
            id: empenho.id,
            exercicio: empenho.exercicio,
            numero: empenho.numero,
            resumo: empenho.resumo,
            obs: empenho.obs,
            valorTotal: empenho.valorTotal.toNumber(),
            status: empenho.status,
            data: empenho.data,
            ativo: empenho.ativo,
            valorLiquidado: valorLiquidado.value,
            valorAnulado: valorAnulado.value,
            saldo: saldo.value,
            protocolo: empenho.reserva?.protocolos[0] || null,
            secretariaId:
              empenho.dotacao.secretariaId ||
              empenho.dotacao.departamento?.secretariaId ||
              null,
            departamentoId: empenho.dotacao.departamentoId || null,
            fornecedor: empenho.fornecedor,
            dotacao: dotacaoConvertido,
            reserva: reservaConvertida,
          };
        }),
        secretarias,
        departamentos,
        fornecedores,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter empenhos.`,
    };
  }
};

export const obterEmpenho = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = empenhoIdSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { id } = parsedParams.data;

  try {
    const acessosPromise = listarIdsDotacoesUsuarioConectadoTemAcesso();
    const empenhoPromise = prisma.empenhos.findUnique({
      where: { id },
      include: {
        reserva: true,
        fornecedor: true,
        dotacao: {
          include: {
            secretaria: true,
            departamento: { include: { secretaria: true } },
            subdepartamento: {
              include: { departamento: { include: { secretaria: true } } },
            },
            economica: true,
            funcional: true,
          },
        },
        afs: {
          where: { ativo: true },
          orderBy: { numero: 'asc' },
        },
      },
    });

    const [acessos, empenho, liquidacoes, anulacoes] = await Promise.all([
      acessosPromise,
      empenhoPromise,
      prisma.liquidacoes.findMany({
        where: {
          idEmpenho: parsedParams.data.id,
          ativo: true,
          status: { not: StatusLiquidacao.ESTORNADA },
        },
        select: { valorTotal: true },
      }),
      prisma.empenhos_anulacoes.findMany({
        where: { idEmpenho: parsedParams.data.id, ativo: true },
        select: { valorAnulado: true },
      }),
    ]);

    if (!empenho) {
      return {
        error: 'Empenho nao encontrado.',
      };
    }

    const protocolo = await prisma.protocolos.findFirst({
      where: {
        numeroEmpenho: empenho.numero,
        exercicioEmpenho: empenho.exercicio,
        ativo: true,
      },
    });

    if (acessos.error) {
      return {
        error: acessos.error,
      };
    }

    if (!acessos.data) {
      return {
        error: 'Não foi possível obter acessos do usuário.',
      };
    }

    if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
      const temAcesso = acessos.data.dotacoes?.includes(empenho.idDotacao);
      if (!temAcesso) {
        return {
          error: 'Usuário não pode acessar esse empenho.',
        };
      }
    }

    // FIXED: Calculate balance using legacy logic - sum of monthly quotas instead of valorTotal
    // This matches the legacy getSaldo() method exactly
    const valorOriginal = currency(0, currencyOptionsNoSymbol)
      .add(empenho.usarMes1.toNumber())
      .add(empenho.usarMes2.toNumber())
      .add(empenho.usarMes3.toNumber())
      .add(empenho.usarMes4.toNumber())
      .add(empenho.usarMes5.toNumber())
      .add(empenho.usarMes6.toNumber())
      .add(empenho.usarMes7.toNumber())
      .add(empenho.usarMes8.toNumber())
      .add(empenho.usarMes9.toNumber())
      .add(empenho.usarMes10.toNumber())
      .add(empenho.usarMes11.toNumber())
      .add(empenho.usarMes12.toNumber());

    const valorLiquidado = liquidacoes.reduce(
      (acc, liq) => acc.add(liq.valorTotal.toNumber()),
      currency(0, currencyOptionsNoSymbol)
    );
    const valorAnulado = anulacoes.reduce(
      (acc, anul) => acc.add(anul.valorAnulado.toNumber()),
      currency(0, currencyOptionsNoSymbol)
    );

    const saldoDisponivel = valorOriginal
      .subtract(valorLiquidado)
      .subtract(valorAnulado);

    return {
      data: {
        id: empenho.id,
        exercicio: empenho.exercicio,
        numero: empenho.numero,
        resumo: empenho.resumo,
        obs: empenho.obs,
        data: empenho.data,
        status: empenho.status,
        ativo: empenho.ativo,
        idReserva: empenho.idReserva,
        idDotacao: empenho.idDotacao,
        idFornecedor: empenho.idFornecedor,
        fornecedor: empenho.fornecedor,
        empenhos_anulacoes: anulacoes.map((anul) =>
          anul.valorAnulado.toNumber()
        ),
        protocolo: protocolo,
        valorTotal: empenho.valorTotal.toNumber(),
        usarMes1: empenho.usarMes1.toNumber(),
        usarMes2: empenho.usarMes2.toNumber(),
        usarMes3: empenho.usarMes3.toNumber(),
        usarMes4: empenho.usarMes4.toNumber(),
        usarMes5: empenho.usarMes5.toNumber(),
        usarMes6: empenho.usarMes6.toNumber(),
        usarMes7: empenho.usarMes7.toNumber(),
        usarMes8: empenho.usarMes8.toNumber(),
        usarMes9: empenho.usarMes9.toNumber(),
        usarMes10: empenho.usarMes10.toNumber(),
        usarMes11: empenho.usarMes11.toNumber(),
        usarMes12: empenho.usarMes12.toNumber(),
        valorLiquidado: valorLiquidado.value,
        valorAnulado: valorAnulado.value,
        saldoDisponivel: saldoDisponivel.value,
        // Convert reserva Decimal fields if reserva exists
        reserva: empenho.reserva
          ? {
              ...empenho.reserva,
              usarMes1: empenho.reserva.usarMes1.toNumber(),
              usarMes2: empenho.reserva.usarMes2.toNumber(),
              usarMes3: empenho.reserva.usarMes3.toNumber(),
              usarMes4: empenho.reserva.usarMes4.toNumber(),
              usarMes5: empenho.reserva.usarMes5.toNumber(),
              usarMes6: empenho.reserva.usarMes6.toNumber(),
              usarMes7: empenho.reserva.usarMes7.toNumber(),
              usarMes8: empenho.reserva.usarMes8.toNumber(),
              usarMes9: empenho.reserva.usarMes9.toNumber(),
              usarMes10: empenho.reserva.usarMes10.toNumber(),
              usarMes11: empenho.reserva.usarMes11.toNumber(),
              usarMes12: empenho.reserva.usarMes12.toNumber(),
              usarTotal: empenho.reserva.usarTotal.toNumber(),
              pluUsarMes1: empenho.reserva.pluUsarMes1.toNumber(),
              pluUsarMes2: empenho.reserva.pluUsarMes2.toNumber(),
              pluUsarMes3: empenho.reserva.pluUsarMes3.toNumber(),
              pluUsarMes4: empenho.reserva.pluUsarMes4.toNumber(),
              pluUsarMes5: empenho.reserva.pluUsarMes5.toNumber(),
              pluUsarMes6: empenho.reserva.pluUsarMes6.toNumber(),
              pluUsarMes7: empenho.reserva.pluUsarMes7.toNumber(),
              pluUsarMes8: empenho.reserva.pluUsarMes8.toNumber(),
              pluUsarMes9: empenho.reserva.pluUsarMes9.toNumber(),
              pluUsarMes10: empenho.reserva.pluUsarMes10.toNumber(),
              pluUsarMes11: empenho.reserva.pluUsarMes11.toNumber(),
              pluUsarMes12: empenho.reserva.pluUsarMes12.toNumber(),
              pluUsarTotal: empenho.reserva.pluUsarTotal.toNumber(),
            }
          : null,
        // Convert dotacao Decimal fields
        dotacao: empenho.dotacao
          ? {
              ...empenho.dotacao,
              valorInicial: empenho.dotacao.valorInicial.toNumber(),
              cotaReducaoInicial: empenho.dotacao.cotaReducaoInicial.toNumber(),
              valorLiberado: empenho.dotacao.valorLiberado.toNumber(),
              cotaReducao: empenho.dotacao.cotaReducao.toNumber(),
              suplementacao: empenho.dotacao.suplementacao.toNumber(),
              anulacao: empenho.dotacao.anulacao.toNumber(),
              valorAtual: empenho.dotacao.valorAtual.toNumber(),
              cotaMes1: empenho.dotacao.cotaMes1.toNumber(),
              cotaMes2: empenho.dotacao.cotaMes2.toNumber(),
              cotaMes3: empenho.dotacao.cotaMes3.toNumber(),
              cotaMes4: empenho.dotacao.cotaMes4.toNumber(),
              cotaMes5: empenho.dotacao.cotaMes5.toNumber(),
              cotaMes6: empenho.dotacao.cotaMes6.toNumber(),
              cotaMes7: empenho.dotacao.cotaMes7.toNumber(),
              cotaMes8: empenho.dotacao.cotaMes8.toNumber(),
              cotaMes9: empenho.dotacao.cotaMes9.toNumber(),
              cotaMes10: empenho.dotacao.cotaMes10.toNumber(),
              cotaMes11: empenho.dotacao.cotaMes11.toNumber(),
              cotaMes12: empenho.dotacao.cotaMes12.toNumber(),
            }
          : null,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter empenho.`,
    };
  }
};

export const criarEmpenho = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = criarEmpenhoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    idReserva,
    idFornecedor,
    resumo,
    obs,
    valorTotal,
    usarMes1,
    usarMes2,
    usarMes3,
    usarMes4,
    usarMes5,
    usarMes6,
    usarMes7,
    usarMes8,
    usarMes9,
    usarMes10,
    usarMes11,
    usarMes12,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Validar soma das cotas mensais usando currency.js
    const somaCotas = currency(0, currencyOptionsNoSymbol)
      .add(usarMes1)
      .add(usarMes2)
      .add(usarMes3)
      .add(usarMes4)
      .add(usarMes5)
      .add(usarMes6)
      .add(usarMes7)
      .add(usarMes8)
      .add(usarMes9)
      .add(usarMes10)
      .add(usarMes11)
      .add(usarMes12);

    if (Math.abs(somaCotas.value - valorTotal) > 0.01) {
      return {
        error:
          'Soma das cotas mensais deve ser igual ao valor total do empenho.',
      };
    }

    let idEmpenho = 0;

    await prisma.$transaction(async (tx) => {
      // Validar reserva
      const reserva = await tx.reservas.findUnique({
        where: { id: idReserva },
        include: { dotacao: true },
      });

      if (!reserva) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.RESERVA_NAO_ENCONTRADA]);
      }

      if (reserva.status !== StatusReserva.Reservado) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.RESERVA_NAO_RESERVADA]);
      }

      // Validar fornecedor
      const fornecedor = await tx.fornecedores.findUnique({
        where: { id: idFornecedor, ativo: true },
      });

      if (!fornecedor) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.FORNECEDOR_NAO_ENCONTRADO]);
      }

      // CRITICAL FIX: Return reserva value to dotação and validate empenho against updated balance
      const valorReserva = toCurrency(reserva.usarTotal);
      const dotacaoComReservaDevolvida = toCurrency(
        reserva.dotacao.valorAtual
      ).add(valorReserva);

      // Validate new empenho value against the now-updated balance
      const valorEmpenho = currency(valorTotal, currencyOptionsNoSymbol);
      if (valorEmpenho.value > dotacaoComReservaDevolvida.value) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.SALDO_INSUFICIENTE]);
      }

      // Calculate final dotação balance after creating empenho
      const valorAtualFinalDotacao =
        dotacaoComReservaDevolvida.subtract(valorEmpenho);

      // Update dotação with correct balance and monthly quotas
      await tx.dotacoes.update({
        where: { id: reserva.idDotacao },
        data: {
          valorAtual: valorAtualFinalDotacao.value,
          // Return reserva quotas and subtract empenho quotas
          cotaMes1: toCurrency(reserva.dotacao.cotaMes1)
            .add(reserva.usarMes1.toNumber())
            .subtract(usarMes1).value,
          cotaMes2: toCurrency(reserva.dotacao.cotaMes2)
            .add(reserva.usarMes2.toNumber())
            .subtract(usarMes2).value,
          cotaMes3: toCurrency(reserva.dotacao.cotaMes3)
            .add(reserva.usarMes3.toNumber())
            .subtract(usarMes3).value,
          cotaMes4: toCurrency(reserva.dotacao.cotaMes4)
            .add(reserva.usarMes4.toNumber())
            .subtract(usarMes4).value,
          cotaMes5: toCurrency(reserva.dotacao.cotaMes5)
            .add(reserva.usarMes5.toNumber())
            .subtract(usarMes5).value,
          cotaMes6: toCurrency(reserva.dotacao.cotaMes6)
            .add(reserva.usarMes6.toNumber())
            .subtract(usarMes6).value,
          cotaMes7: toCurrency(reserva.dotacao.cotaMes7)
            .add(reserva.usarMes7.toNumber())
            .subtract(usarMes7).value,
          cotaMes8: toCurrency(reserva.dotacao.cotaMes8)
            .add(reserva.usarMes8.toNumber())
            .subtract(usarMes8).value,
          cotaMes9: toCurrency(reserva.dotacao.cotaMes9)
            .add(reserva.usarMes9.toNumber())
            .subtract(usarMes9).value,
          cotaMes10: toCurrency(reserva.dotacao.cotaMes10)
            .add(reserva.usarMes10.toNumber())
            .subtract(usarMes10).value,
          cotaMes11: toCurrency(reserva.dotacao.cotaMes11)
            .add(reserva.usarMes11.toNumber())
            .subtract(usarMes11).value,
          cotaMes12: toCurrency(reserva.dotacao.cotaMes12)
            .add(reserva.usarMes12.toNumber())
            .subtract(usarMes12).value,
        },
      });

      // Gerar número sequencial
      const ultimoEmpenho = await tx.empenhos.findFirst({
        where: { exercicio: resultPermissao.exercicio! },
        orderBy: { numero: 'desc' },
        select: { numero: true },
      });

      const numeroEmpenho = (ultimoEmpenho?.numero || 0) + 1;

      // Criar empenho
      const empenho = await tx.empenhos.create({
        data: {
          exercicio: resultPermissao.exercicio!,
          numero: numeroEmpenho,
          idReserva,
          idDotacao: reserva.idDotacao,
          idFornecedor,
          resumo,
          obs,
          valorTotal,
          status: StatusEmpenho.EMPENHADO,
          usarMes1,
          usarMes2,
          usarMes3,
          usarMes4,
          usarMes5,
          usarMes6,
          usarMes7,
          usarMes8,
          usarMes9,
          usarMes10,
          usarMes11,
          usarMes12,
        },
      });

      // Update reserva status to EMPENHO
      await tx.reservas.update({
        where: { id: idReserva },
        data: { status: StatusReserva.Empenho },
      });

      // Registrar auditoria
      await tx.empenhos_audit.create({
        data: {
          idEmpenho: empenho.id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoEmpenhos.CRIAR_EMPENHO,
          ip,
          obs: `Empenho criado a partir da reserva ${idReserva}`,
        },
      });

      idEmpenho = empenho.id;
    });

    revalidatePath(ROUTE);
    return {
      data: {
        idEmpenho,
      },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (
        Object.values(ErrosEmpenho).includes(
          e.message as unknown as ErrosEmpenho
        )
      ) {
        return {
          error: e.message,
        };
      }
    }
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: 'Empenho já existe.',
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao criar empenho.',
    };
  }
};

export const editarEmpenho = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = alterarEmpenhoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    id,
    resumo,
    obs,
    valorTotal,
    usarMes1,
    usarMes2,
    usarMes3,
    usarMes4,
    usarMes5,
    usarMes6,
    usarMes7,
    usarMes8,
    usarMes9,
    usarMes10,
    usarMes11,
    usarMes12,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Validar soma das cotas mensais usando currency.js
    const somaCotas = currency(0, currencyOptionsNoSymbol)
      .add(usarMes1)
      .add(usarMes2)
      .add(usarMes3)
      .add(usarMes4)
      .add(usarMes5)
      .add(usarMes6)
      .add(usarMes7)
      .add(usarMes8)
      .add(usarMes9)
      .add(usarMes10)
      .add(usarMes11)
      .add(usarMes12);

    if (Math.abs(somaCotas.value - valorTotal) > 0.01) {
      return {
        error:
          'Soma das cotas mensais deve ser igual ao valor total do empenho.',
      };
    }

    await prisma.$transaction(async (tx) => {
      const empenhoAntigo = await tx.empenhos.findUnique({
        where: { id },
        include: {
          dotacao: true,
        },
      });

      if (!empenhoAntigo) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ENCONTRADO]);
      }

      if (empenhoAntigo.status !== StatusEmpenho.EMPENHADO) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ATIVO]);
      }

      // Verificar se há liquidações que impedem a alteração
      const liquidacoes = await tx.liquidacoes.findMany({
        where: {
          idEmpenho: id,
          ativo: true,
          status: { not: StatusLiquidacao.ESTORNADA },
        },
        select: { valorTotal: true },
      });
      const valorLiquidado = liquidacoes.reduce(
        (acc, liq) => acc.add(liq.valorTotal.toNumber()),
        currency(0, currencyOptionsNoSymbol)
      );

      if (valorLiquidado.value > 0) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_COM_LIQUIDACOES]);
      }

      // FIXED: Validate availability using corrected logic and update dotação
      const dotacao = (empenhoAntigo as any).dotacao;
      const valorAntigoEmpenho = toCurrency(empenhoAntigo.valorTotal);
      const valorNovoEmpenho = currency(valorTotal, currencyOptionsNoSymbol);
      const diferencaValor = valorNovoEmpenho.subtract(valorAntigoEmpenho);

      // Check if dotação has enough balance for the difference
      const saldoAtualDotacao = toCurrency(dotacao.valorAtual);
      if (
        diferencaValor.value > 0 &&
        saldoAtualDotacao.value < diferencaValor.value
      ) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.SALDO_INSUFICIENTE]);
      }

      // Update dotação balance with the difference
      const novoValorAtualDotacao = saldoAtualDotacao.subtract(diferencaValor);

      // Calculate monthly quota differences
      const diferencaCotas = {
        cotaMes1: currency(usarMes1, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes1.toNumber()
        ).value,
        cotaMes2: currency(usarMes2, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes2.toNumber()
        ).value,
        cotaMes3: currency(usarMes3, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes3.toNumber()
        ).value,
        cotaMes4: currency(usarMes4, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes4.toNumber()
        ).value,
        cotaMes5: currency(usarMes5, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes5.toNumber()
        ).value,
        cotaMes6: currency(usarMes6, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes6.toNumber()
        ).value,
        cotaMes7: currency(usarMes7, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes7.toNumber()
        ).value,
        cotaMes8: currency(usarMes8, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes8.toNumber()
        ).value,
        cotaMes9: currency(usarMes9, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes9.toNumber()
        ).value,
        cotaMes10: currency(usarMes10, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes10.toNumber()
        ).value,
        cotaMes11: currency(usarMes11, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes11.toNumber()
        ).value,
        cotaMes12: currency(usarMes12, currencyOptionsNoSymbol).subtract(
          empenhoAntigo.usarMes12.toNumber()
        ).value,
      };

      // Update dotação
      await tx.dotacoes.update({
        where: { id: empenhoAntigo.idDotacao },
        data: {
          valorAtual: novoValorAtualDotacao.value,
          cotaMes1: toCurrency(dotacao.cotaMes1).subtract(
            diferencaCotas.cotaMes1
          ).value,
          cotaMes2: toCurrency(dotacao.cotaMes2).subtract(
            diferencaCotas.cotaMes2
          ).value,
          cotaMes3: toCurrency(dotacao.cotaMes3).subtract(
            diferencaCotas.cotaMes3
          ).value,
          cotaMes4: toCurrency(dotacao.cotaMes4).subtract(
            diferencaCotas.cotaMes4
          ).value,
          cotaMes5: toCurrency(dotacao.cotaMes5).subtract(
            diferencaCotas.cotaMes5
          ).value,
          cotaMes6: toCurrency(dotacao.cotaMes6).subtract(
            diferencaCotas.cotaMes6
          ).value,
          cotaMes7: toCurrency(dotacao.cotaMes7).subtract(
            diferencaCotas.cotaMes7
          ).value,
          cotaMes8: toCurrency(dotacao.cotaMes8).subtract(
            diferencaCotas.cotaMes8
          ).value,
          cotaMes9: toCurrency(dotacao.cotaMes9).subtract(
            diferencaCotas.cotaMes9
          ).value,
          cotaMes10: toCurrency(dotacao.cotaMes10).subtract(
            diferencaCotas.cotaMes10
          ).value,
          cotaMes11: toCurrency(dotacao.cotaMes11).subtract(
            diferencaCotas.cotaMes11
          ).value,
          cotaMes12: toCurrency(dotacao.cotaMes12).subtract(
            diferencaCotas.cotaMes12
          ).value,
        },
      });

      // Atualizar empenho
      await tx.empenhos.update({
        where: { id },
        data: {
          resumo,
          obs,
          valorTotal,
          usarMes1,
          usarMes2,
          usarMes3,
          usarMes4,
          usarMes5,
          usarMes6,
          usarMes7,
          usarMes8,
          usarMes9,
          usarMes10,
          usarMes11,
          usarMes12,
        },
      });

      // Registrar auditoria
      await tx.empenhos_audit.create({
        data: {
          idEmpenho: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoEmpenhos.ALTERAR_EMPENHO,
          ip,
          obs: `Empenho alterado. Valor antigo: R$ ${empenhoAntigo.valorTotal}, Novo valor: R$ ${valorTotal}`,
        },
      });
    });

    revalidatePath(ROUTE);
    return {
      data: { success: true },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (
        Object.values(ErrosEmpenho).includes(
          e.message as unknown as ErrosEmpenho
        )
      ) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao editar empenho.',
    };
  }
};

export const cancelarEmpenho = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = cancelarEmpenhoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, obs } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    const result = await prisma.$transaction(async (tx) => {
      const empenho = await tx.empenhos.findUnique({
        where: { id },
        include: { dotacao: true },
      });

      if (!empenho) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ENCONTRADO]);
      }

      if (empenho.status !== StatusEmpenho.EMPENHADO) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ATIVO]);
      }

      // Verificar se há liquidações que impedem o cancelamento
      const liquidacoes = await tx.liquidacoes.findMany({
        where: {
          idEmpenho: id,
          ativo: true,
          status: { not: StatusLiquidacao.ESTORNADA },
        },
        select: { valorTotal: true },
      });
      const valorLiquidado = liquidacoes.reduce(
        (acc, liq) => acc.add(liq.valorTotal.toNumber()),
        currency(0, currencyOptionsNoSymbol)
      );

      if (valorLiquidado.value > 0) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_COM_LIQUIDACOES]);
      }

      // Return empenho value back to dotação
      const valorEmpenho = toCurrency(empenho.valorTotal);
      const novoValorAtualDotacao = toCurrency(empenho.dotacao.valorAtual).add(
        valorEmpenho
      );

      // Update dotação with restored values
      await tx.dotacoes.update({
        where: { id: empenho.idDotacao },
        data: {
          valorAtual: novoValorAtualDotacao.value,
          // Return the monthly quota values as well
          cotaMes1: toCurrency(empenho.dotacao.cotaMes1).add(
            empenho.usarMes1.toNumber()
          ).value,
          cotaMes2: toCurrency(empenho.dotacao.cotaMes2).add(
            empenho.usarMes2.toNumber()
          ).value,
          cotaMes3: toCurrency(empenho.dotacao.cotaMes3).add(
            empenho.usarMes3.toNumber()
          ).value,
          cotaMes4: toCurrency(empenho.dotacao.cotaMes4).add(
            empenho.usarMes4.toNumber()
          ).value,
          cotaMes5: toCurrency(empenho.dotacao.cotaMes5).add(
            empenho.usarMes5.toNumber()
          ).value,
          cotaMes6: toCurrency(empenho.dotacao.cotaMes6).add(
            empenho.usarMes6.toNumber()
          ).value,
          cotaMes7: toCurrency(empenho.dotacao.cotaMes7).add(
            empenho.usarMes7.toNumber()
          ).value,
          cotaMes8: toCurrency(empenho.dotacao.cotaMes8).add(
            empenho.usarMes8.toNumber()
          ).value,
          cotaMes9: toCurrency(empenho.dotacao.cotaMes9).add(
            empenho.usarMes9.toNumber()
          ).value,
          cotaMes10: toCurrency(empenho.dotacao.cotaMes10).add(
            empenho.usarMes10.toNumber()
          ).value,
          cotaMes11: toCurrency(empenho.dotacao.cotaMes11).add(
            empenho.usarMes11.toNumber()
          ).value,
          cotaMes12: toCurrency(empenho.dotacao.cotaMes12).add(
            empenho.usarMes12.toNumber()
          ).value,
        },
      });

      // Cancelar empenho
      await tx.empenhos.update({
        where: { id },
        data: {
          status: StatusEmpenho.CANCELADO,
          obs: empenho.obs
            ? `${empenho.obs}\n\nMOTIVO CANCELAMENTO: ${obs}`
            : `MOTIVO CANCELAMENTO: ${obs}`,
        },
      });

      // Revert the associated Reserva back to RESERVADO status
      if (empenho.idReserva) {
        await tx.reservas.update({
          where: { id: empenho.idReserva },
          data: { status: StatusReserva.Reservado },
        });
      }

      // Registrar auditoria
      await tx.empenhos_audit.create({
        data: {
          idEmpenho: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoEmpenhos.CANCELAR_EMPENHO,
          ip,
          obs: `Empenho cancelado. Motivo: ${obs}`,
        },
      });

      return {
        valorEmpenho: valorEmpenho.value,
        id: empenho.id,
      };
    });

    revalidatePath(ROUTE);
    return {
      data: { success: true, valorAnulado: result.valorEmpenho, id: result.id },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (
        Object.values(ErrosEmpenho).includes(
          e.message as unknown as ErrosEmpenho
        )
      ) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao cancelar empenho.',
    };
  }
};

export const anularEmpenhoParcial = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = anularEmpenhoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, valorAnulacao, obs } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const empenho = await tx.empenhos.findUnique({
        where: { id },
        include: { dotacao: true },
      });

      if (!empenho) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ENCONTRADO]);
      }

      if (empenho.status !== StatusEmpenho.EMPENHADO) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ATIVO]);
      }

      // FIXED: Calculate balance using sum of monthly quotas (matching legacy logic)
      const valorOriginal = currency(0, currencyOptionsNoSymbol)
        .add(empenho.usarMes1.toNumber())
        .add(empenho.usarMes2.toNumber())
        .add(empenho.usarMes3.toNumber())
        .add(empenho.usarMes4.toNumber())
        .add(empenho.usarMes5.toNumber())
        .add(empenho.usarMes6.toNumber())
        .add(empenho.usarMes7.toNumber())
        .add(empenho.usarMes8.toNumber())
        .add(empenho.usarMes9.toNumber())
        .add(empenho.usarMes10.toNumber())
        .add(empenho.usarMes11.toNumber())
        .add(empenho.usarMes12.toNumber());
      const [liquidacoes, anulacoes] = await Promise.all([
        tx.liquidacoes.findMany({
          where: {
            idEmpenho: id,
            ativo: true,
            status: { not: StatusLiquidacao.ESTORNADA },
          },
          select: { valorTotal: true },
        }),
        tx.empenhos_anulacoes.findMany({
          where: { idEmpenho: id, ativo: true },
          select: { valorAnulado: true },
        }),
      ]);
      const valorLiquidado = liquidacoes.reduce(
        (acc, liq) => acc.add(liq.valorTotal.toNumber()),
        currency(0, currencyOptionsNoSymbol)
      );
      const valorAnuladoExistente = anulacoes.reduce(
        (acc, anul) => acc.add(anul.valorAnulado.toNumber()),
        currency(0, currencyOptionsNoSymbol)
      );

      const saldoAtual = valorOriginal
        .subtract(valorLiquidado)
        .subtract(valorAnuladoExistente);

      // Validar valor de anulação
      const valorAnular = currency(valorAnulacao, currencyOptionsNoSymbol);
      if (valorAnular.value > saldoAtual.value) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.VALOR_ANULACAO_MAIOR_SALDO]);
      }

      // Calcular novo valor total
      const novoValorTotal = valorOriginal.subtract(valorAnular);

      // Redistribuir cotas mensais proporcionalmente
      const fatorReducao = novoValorTotal.divide(valorOriginal);

      const novasCotasMensais = {
        usarMes1: toCurrency(empenho.usarMes1).multiply(fatorReducao).value,
        usarMes2: toCurrency(empenho.usarMes2).multiply(fatorReducao).value,
        usarMes3: toCurrency(empenho.usarMes3).multiply(fatorReducao).value,
        usarMes4: toCurrency(empenho.usarMes4).multiply(fatorReducao).value,
        usarMes5: toCurrency(empenho.usarMes5).multiply(fatorReducao).value,
        usarMes6: toCurrency(empenho.usarMes6).multiply(fatorReducao).value,
        usarMes7: toCurrency(empenho.usarMes7).multiply(fatorReducao).value,
        usarMes8: toCurrency(empenho.usarMes8).multiply(fatorReducao).value,
        usarMes9: toCurrency(empenho.usarMes9).multiply(fatorReducao).value,
        usarMes10: toCurrency(empenho.usarMes10).multiply(fatorReducao).value,
        usarMes11: toCurrency(empenho.usarMes11).multiply(fatorReducao).value,
        usarMes12: toCurrency(empenho.usarMes12).multiply(fatorReducao).value,
      };

      // Validar soma das novas cotas
      const somaNovasCotas = Object.values(novasCotasMensais).reduce(
        (acc, val) => acc.add(val),
        currency(0, currencyOptionsNoSymbol)
      );

      if (Math.abs(somaNovasCotas.value - novoValorTotal.value) > 0.01) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.ERRO_REDISTRIBUICAO_COTAS]);
      }

      // CRITICAL FIX: Return annulled amount back to dotação
      const novoValorAtualDotacao = toCurrency(empenho.dotacao.valorAtual).add(
        valorAnular
      );

      // Calculate the difference for each monthly quota to return
      const fatorAnulacao = valorAnular.divide(valorOriginal);
      const cotasAnuladas = {
        usarMes1: toCurrency(empenho.usarMes1).multiply(fatorAnulacao).value,
        usarMes2: toCurrency(empenho.usarMes2).multiply(fatorAnulacao).value,
        usarMes3: toCurrency(empenho.usarMes3).multiply(fatorAnulacao).value,
        usarMes4: toCurrency(empenho.usarMes4).multiply(fatorAnulacao).value,
        usarMes5: toCurrency(empenho.usarMes5).multiply(fatorAnulacao).value,
        usarMes6: toCurrency(empenho.usarMes6).multiply(fatorAnulacao).value,
        usarMes7: toCurrency(empenho.usarMes7).multiply(fatorAnulacao).value,
        usarMes8: toCurrency(empenho.usarMes8).multiply(fatorAnulacao).value,
        usarMes9: toCurrency(empenho.usarMes9).multiply(fatorAnulacao).value,
        usarMes10: toCurrency(empenho.usarMes10).multiply(fatorAnulacao).value,
        usarMes11: toCurrency(empenho.usarMes11).multiply(fatorAnulacao).value,
        usarMes12: toCurrency(empenho.usarMes12).multiply(fatorAnulacao).value,
      };

      // Update dotação and its quotas
      await tx.dotacoes.update({
        where: { id: empenho.idDotacao },
        data: {
          valorAtual: novoValorAtualDotacao.value,
          cotaMes1: toCurrency(empenho.dotacao.cotaMes1).add(
            cotasAnuladas.usarMes1
          ).value,
          cotaMes2: toCurrency(empenho.dotacao.cotaMes2).add(
            cotasAnuladas.usarMes2
          ).value,
          cotaMes3: toCurrency(empenho.dotacao.cotaMes3).add(
            cotasAnuladas.usarMes3
          ).value,
          cotaMes4: toCurrency(empenho.dotacao.cotaMes4).add(
            cotasAnuladas.usarMes4
          ).value,
          cotaMes5: toCurrency(empenho.dotacao.cotaMes5).add(
            cotasAnuladas.usarMes5
          ).value,
          cotaMes6: toCurrency(empenho.dotacao.cotaMes6).add(
            cotasAnuladas.usarMes6
          ).value,
          cotaMes7: toCurrency(empenho.dotacao.cotaMes7).add(
            cotasAnuladas.usarMes7
          ).value,
          cotaMes8: toCurrency(empenho.dotacao.cotaMes8).add(
            cotasAnuladas.usarMes8
          ).value,
          cotaMes9: toCurrency(empenho.dotacao.cotaMes9).add(
            cotasAnuladas.usarMes9
          ).value,
          cotaMes10: toCurrency(empenho.dotacao.cotaMes10).add(
            cotasAnuladas.usarMes10
          ).value,
          cotaMes11: toCurrency(empenho.dotacao.cotaMes11).add(
            cotasAnuladas.usarMes11
          ).value,
          cotaMes12: toCurrency(empenho.dotacao.cotaMes12).add(
            cotasAnuladas.usarMes12
          ).value,
        },
      });

      // Atualizar empenho
      await tx.empenhos.update({
        where: { id },
        data: {
          valorTotal: novoValorTotal.value,
          ...novasCotasMensais,
          status:
            novoValorTotal.value === 0
              ? StatusEmpenho.ANULADO
              : StatusEmpenho.EMPENHADO,
        },
      });

      // Registrar anulação
      await tx.empenhos_anulacoes.create({
        data: {
          idEmpenho: id,
          valorAnulado: valorAnular.value,
          motivo: obs,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      // Registrar auditoria
      await tx.empenhos_audit.create({
        data: {
          idEmpenho: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoEmpenhos.ANULAR_EMPENHO_PARCIAL,
          ip,
          obs: `Anulação parcial: R$ ${valorAnular.format()} - ${obs}`,
        },
      });
    });

    revalidatePath(ROUTE);
    return {
      data: { success: true },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (
        Object.values(ErrosEmpenho).includes(
          e.message as unknown as ErrosEmpenho
        )
      ) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao anular empenho.',
    };
  }
};

export const estornarAnulacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = estornarAnulacaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, motivo } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    const result = await prisma.$transaction(async (tx) => {
      // Get existing empenho with related data
      const empenho = await tx.empenhos.findUnique({
        where: { id },
        include: {
          dotacao: true,
          empenhos_anulacoes: {
            where: { ativo: true },
            orderBy: { data: 'desc' },
          },
        },
      });

      if (!empenho) {
        throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ENCONTRADO]);
      }

      // Validate empenho status - only can undo annulment if it's ANULADO or ANULADO_PARCIAL
      if (
        empenho.status !== StatusEmpenho.ANULADO &&
        empenho.status !== StatusEmpenho.ANULADO_PARCIAL
      ) {
        throw new Error(
          'Apenas empenhos anulados podem ter suas anulações estornadas.'
        );
      }

      // Check if there are any active liquidacoes that would conflict with undoing annulment
      const liquidacoes = await tx.liquidacoes.findMany({
        where: {
          idEmpenho: id,
          ativo: true,
          status: { not: StatusLiquidacao.ESTORNADA },
        },
        select: { valorTotal: true },
      });

      if (liquidacoes.length > 0) {
        throw new Error(
          'Não é possível estornar anulação pois existem liquidações ativas para este empenho.'
        );
      }

      // Get total annulled amount
      const valorTotalAnulado = empenho.empenhos_anulacoes.reduce(
        (acc, anul) => acc.add(anul.valorAnulado.toNumber()),
        currency(0, currencyOptionsNoSymbol)
      );

      if (valorTotalAnulado.value === 0) {
        throw new Error('Não há anulações para estornar neste empenho.');
      }

      // Calculate the original empenho value before annulment
      const valorOriginal = currency(0, currencyOptionsNoSymbol)
        .add(empenho.usarMes1.toNumber())
        .add(empenho.usarMes2.toNumber())
        .add(empenho.usarMes3.toNumber())
        .add(empenho.usarMes4.toNumber())
        .add(empenho.usarMes5.toNumber())
        .add(empenho.usarMes6.toNumber())
        .add(empenho.usarMes7.toNumber())
        .add(empenho.usarMes8.toNumber())
        .add(empenho.usarMes9.toNumber())
        .add(empenho.usarMes10.toNumber())
        .add(empenho.usarMes11.toNumber())
        .add(empenho.usarMes12.toNumber());

      // Calculate new values after undoing annulment
      const novoValorTotal = valorOriginal;

      // Check if dotação has enough balance for the restoration
      const saldoAtualDotacao = toCurrency(empenho.dotacao.valorAtual);
      if (saldoAtualDotacao.value < valorTotalAnulado.value) {
        throw new Error(
          'Saldo insuficiente na dotação para estornar a anulação.'
        );
      }

      // Update dotação - subtract the annulled amount from available balance
      const novoValorAtualDotacao =
        saldoAtualDotacao.subtract(valorTotalAnulado);

      await tx.dotacoes.update({
        where: { id: empenho.idDotacao },
        data: {
          valorAtual: novoValorAtualDotacao.value,
          // Return the monthly quota values that were restored during annulment
          cotaMes1: toCurrency(empenho.dotacao.cotaMes1).subtract(
            empenho.usarMes1.toNumber()
          ).value,
          cotaMes2: toCurrency(empenho.dotacao.cotaMes2).subtract(
            empenho.usarMes2.toNumber()
          ).value,
          cotaMes3: toCurrency(empenho.dotacao.cotaMes3).subtract(
            empenho.usarMes3.toNumber()
          ).value,
          cotaMes4: toCurrency(empenho.dotacao.cotaMes4).subtract(
            empenho.usarMes4.toNumber()
          ).value,
          cotaMes5: toCurrency(empenho.dotacao.cotaMes5).subtract(
            empenho.usarMes5.toNumber()
          ).value,
          cotaMes6: toCurrency(empenho.dotacao.cotaMes6).subtract(
            empenho.usarMes6.toNumber()
          ).value,
          cotaMes7: toCurrency(empenho.dotacao.cotaMes7).subtract(
            empenho.usarMes7.toNumber()
          ).value,
          cotaMes8: toCurrency(empenho.dotacao.cotaMes8).subtract(
            empenho.usarMes8.toNumber()
          ).value,
          cotaMes9: toCurrency(empenho.dotacao.cotaMes9).subtract(
            empenho.usarMes9.toNumber()
          ).value,
          cotaMes10: toCurrency(empenho.dotacao.cotaMes10).subtract(
            empenho.usarMes10.toNumber()
          ).value,
          cotaMes11: toCurrency(empenho.dotacao.cotaMes11).subtract(
            empenho.usarMes11.toNumber()
          ).value,
          cotaMes12: toCurrency(empenho.dotacao.cotaMes12).subtract(
            empenho.usarMes12.toNumber()
          ).value,
        },
      });

      // Update empenho status back to EMPENHADO and restore original values
      await tx.empenhos.update({
        where: { id },
        data: {
          status: StatusEmpenho.EMPENHADO,
          valorTotal: novoValorTotal.value,
          // Restore original monthly quotas
          usarMes1: empenho.usarMes1.toNumber(),
          usarMes2: empenho.usarMes2.toNumber(),
          usarMes3: empenho.usarMes3.toNumber(),
          usarMes4: empenho.usarMes4.toNumber(),
          usarMes5: empenho.usarMes5.toNumber(),
          usarMes6: empenho.usarMes6.toNumber(),
          usarMes7: empenho.usarMes7.toNumber(),
          usarMes8: empenho.usarMes8.toNumber(),
          usarMes9: empenho.usarMes9.toNumber(),
          usarMes10: empenho.usarMes10.toNumber(),
          usarMes11: empenho.usarMes11.toNumber(),
          usarMes12: empenho.usarMes12.toNumber(),
        },
      });

      // Deactivate all annulment records (mark them as estornadas)
      await tx.empenhos_anulacoes.updateMany({
        where: { idEmpenho: id, ativo: true },
        data: { ativo: false },
      });

      // Registrar auditoria
      await tx.empenhos_audit.create({
        data: {
          idEmpenho: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoEmpenhos.ESTORNAR_ANULACAO,
          ip,
          obs: `Anulação estornada. Valor restaurado: R$ ${valorTotalAnulado.format()}. Motivo: ${motivo}`,
        },
      });

      return {
        idEmpenho: id,
        valorRestaurado: valorTotalAnulado.value,
      };
    });

    revalidatePath(ROUTE);
    return {
      data: {
        idEmpenho: result.idEmpenho,
        valorRestaurado: result.valorRestaurado,
        success: true,
      },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (
        Object.values(ErrosEmpenho).includes(
          e.message as unknown as ErrosEmpenho
        )
      ) {
        return {
          error: e.message,
        };
      }
      // Handle custom validation errors
      if (
        e.message.includes('apenas empenhos anulados') ||
        e.message.includes('liquidações ativas') ||
        e.message.includes('Não há anulações') ||
        e.message.includes('Saldo insuficiente')
      ) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao estornar anulação.',
    };
  }
};

export const calcularSaldoEmpenho = async (idEmpenho: number) => {
  try {
    const empenho = await prisma.empenhos.findUnique({
      where: { id: idEmpenho },
      include: {},
    });

    if (!empenho) {
      throw new Error('Empenho não encontrado');
    }

    // Valor original (soma das cotas mensais)
    const valorOriginal = currency(0, currencyOptionsNoSymbol)
      .add(empenho.usarMes1.toNumber())
      .add(empenho.usarMes2.toNumber())
      .add(empenho.usarMes3.toNumber())
      .add(empenho.usarMes4.toNumber())
      .add(empenho.usarMes5.toNumber())
      .add(empenho.usarMes6.toNumber())
      .add(empenho.usarMes7.toNumber())
      .add(empenho.usarMes8.toNumber())
      .add(empenho.usarMes9.toNumber())
      .add(empenho.usarMes10.toNumber())
      .add(empenho.usarMes11.toNumber())
      .add(empenho.usarMes12.toNumber());

    // Buscar liquidacoes e anulacoes
    const [liquidacoes, anulacoes] = await Promise.all([
      prisma.liquidacoes.findMany({
        where: {
          idEmpenho: idEmpenho,
          ativo: true,
          status: { not: StatusLiquidacao.ESTORNADA },
        },
        select: { valorTotal: true },
      }),
      prisma.empenhos_anulacoes.findMany({
        where: { idEmpenho: idEmpenho, ativo: true },
        select: { valorAnulado: true },
      }),
    ]);

    // Valor liquidado
    const valorLiquidado = liquidacoes.reduce(
      (acc, liq) => acc.add(liq.valorTotal.toNumber()),
      currency(0, currencyOptionsNoSymbol)
    );

    // Valor anulado
    const valorAnulado = anulacoes.reduce(
      (acc, anul) => acc.add(anul.valorAnulado.toNumber()),
      currency(0, currencyOptionsNoSymbol)
    );

    // Saldo disponível
    const saldoDisponivel = valorOriginal
      .subtract(valorLiquidado)
      .subtract(valorAnulado);

    return {
      valorOriginal: valorOriginal.value,
      valorLiquidado: valorLiquidado.value,
      valorAnulado: valorAnulado.value,
      saldoDisponivel: saldoDisponivel.value,
    };
  } catch (e) {
    console.log(e);
    throw new Error('Erro ao calcular saldo do empenho');
  }
};

export const listarReservasParaEmpenho = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    const reservas = await prisma.reservas.findMany({
      where: {
        exercicio: resultPermissao.exercicio,
        status: StatusReserva.Reservado,
      },
      select: {
        id: true,
        resumo: true,
        usarTotal: true,
        dotacao: {
          select: {
            despesa: true,
            desc: true,
          },
        },
      },
      orderBy: { id: 'desc' },
    });

    return {
      data: {
        reservas: reservas.map((reserva) => ({
          ...reserva,
          usarTotal: reserva.usarTotal.toNumber(),
        })),
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao obter reservas disponíveis.',
    };
  }
};

export const validarDisponibilidadeOrcamentaria = async (
  idDotacao: number,
  valorSolicitado: number,
  idEmpenhoExclusao?: number
) => {
  try {
    const dotacao = await prisma.dotacoes.findUnique({
      where: { id: idDotacao },
    });

    if (!dotacao) {
      throw new Error('Dotação não encontrada');
    }

    // FIXED: After our corrections, dotacao.valorAtual already reflects the correct available balance
    // No need to subtract empenhos again as they are now properly debited from dotacao when created
    let saldoDisponivel = toCurrency(dotacao.valorAtual);

    // If we're editing an existing empenho, add its current value back to available balance
    if (idEmpenhoExclusao) {
      const empenhoExistente = await prisma.empenhos.findUnique({
        where: { id: idEmpenhoExclusao },
        select: { valorTotal: true },
      });

      if (empenhoExistente) {
        saldoDisponivel = saldoDisponivel.add(
          empenhoExistente.valorTotal.toNumber()
        );
      }
    }

    const valorSolicitadoCurrency = currency(
      valorSolicitado,
      currencyOptionsNoSymbol
    );

    return {
      disponivel: saldoDisponivel.value >= valorSolicitadoCurrency.value,
      saldoDisponivel: saldoDisponivel.value,
      valorSolicitado: valorSolicitadoCurrency.value,
    };
  } catch (e) {
    console.log(e);
    throw new Error('Erro ao validar disponibilidade orçamentária');
  }
};
