import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { Suspense } from 'react';
import DialogImportarEmpenhos from '@/components/movimento/empenhos/DialogImportarEmpenhos';
import EmpenhosDatatableWrapper from '@/components/movimento/empenhos/empenhosDatatableWrapper';

export default function EmpenhosPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Movimento de Empenhos</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='flex w-full justify-end gap-2 pr-6'>
            <Link href='/movimento/empenhos/novo'>
              <Button>
                Novo Empenho <PlusCircle className='ml-1 h-4 w-4' />
              </Button>
            </Link>
          </div>
          <EmpenhosDatatableWrapper />
        </Suspense>

        <DialogImportarEmpenhos />
      </PageContent>
    </PageWrapper>
  );
}
