'use server';

import { revalidatePath } from 'next/cache';
import { prisma } from '../../prisma';
import {
  alteracaoOrcamentariaSchema,
  aprovarAlteracaoOrcamentariaSchema,
  auditoriaErroSchema,
  idExercicioSchema,
  idSchema,
  imprimirAlteracaoSchema,
  insereNovaDespesaSchema,
  permissaoSchema,
} from '../../validation';
import { Modulos } from '@/lib/modulos';
import {
  Permissoes,
  tiposAlteracao,
  tiposAcao,
  StatusAlteracaoOrcamentaria,
  AuditoriaAlteracaoOrcamentaria,
  StatusSuperavitDetalhes,
  TiposAssinatura,
  TiposAcesso,
} from '@/lib/enums';
import {
  obterIpUsuarioConectado,
  temPermissao,
  listarIdsDotacoesUsuarioConectadoTemAcesso,
  listarAcessosOrgaosUsuarioConectado,
} from '../usuarios';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { inserirErroAudit } from '../auditoria/erro';
import currency from 'currency.js';
import {
  currencyOptionsNoSymbol,
  formatDataHora,
  obterDataAtual,
} from '@/lib/utils';
import { Prisma } from '@prisma/client';
import { redistribuirCotas } from '../gerenciamento/dotacoes';
import { toCurrency } from '@/lib/serverUtils';

export const listarAlteracoesOrcamentarias = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  let acessos;
  if (!resultPermissao.gerente) {
    acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

    if (acessos.error) {
      return {
        error: acessos.error,
      };
    }

    if (!acessos.data) {
      return {
        error: 'Não foi possível obter acessos do usuário.',
      };
    }
  }

  let where: Prisma.alteracaoOrcamentariaWhereInput | null;

  if (resultPermissao.gerente || acessos?.data?.acessoTotal) {
    where = {
      exercicio: resultPermissao.exercicio || undefined,
    };
  } else {
    where = {
      exercicio: resultPermissao.exercicio || undefined,
      OR: [
        { despesaAcao: { in: acessos?.data?.dotacoes } },
        { despesaCopia: { in: acessos?.data?.dotacoes } },
        { despesaNova: { in: acessos?.data?.dotacoes } },
      ],
    };
  }

  try {
    const pedDesp = await prisma.alteracaoOrcamentaria.findMany({
      include: {
        secretaria: {
          select: {
            nome: true,
          },
        },
        departamento: {
          select: {
            nome: true,
          },
        },
        usuario: {
          select: {
            nome: true,
          },
        },
      },
      where,
      orderBy: { id: 'desc' },
    });

    return {
      data: pedDesp.map((row) => {
        return {
          ...row,
          secretaria: row.secretaria?.nome,
          departamento: row.departamento?.nome,
          usuario: row.usuario?.nome,
          dataAbertura: formatDataHora(row.dataAbertura),
          tipoAlteracao: tiposAlteracao[row.tipoAlteracao],
          tipoAcao: tiposAcao[row.tipoAcao],
          status: StatusAlteracaoOrcamentaria[row.status],
          valorTotal: row.valorTotal.toNumber(),
          valorMes1: row.valorMes1.toNumber(),
          valorMes2: row.valorMes2.toNumber(),
          valorMes3: row.valorMes3.toNumber(),
          valorMes4: row.valorMes4.toNumber(),
          valorMes5: row.valorMes5.toNumber(),
          valorMes6: row.valorMes6.toNumber(),
          valorMes7: row.valorMes7.toNumber(),
          valorMes8: row.valorMes8.toNumber(),
          valorMes9: row.valorMes9.toNumber(),
          valorMes10: row.valorMes10.toNumber(),
          valorMes11: row.valorMes11.toNumber(),
          valorMes12: row.valorMes12.toNumber(),
        };
      }),
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao obter Alterações Orçamentárias.`,
    };
  }
};

export const criarAlteracaoOrcamentaria = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = alteracaoOrcamentariaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

  if (acessos.error) {
    return {
      error: acessos.error,
    };
  }

  if (!acessos.data) {
    return {
      error: 'Não foi possível obter acessos do usuário.',
    };
  }

  const {
    exercicio,
    tipoAlteracao,
    valorTotal,
    valorMes1,
    valorMes2,
    valorMes3,
    valorMes4,
    valorMes5,
    valorMes6,
    valorMes7,
    valorMes8,
    valorMes9,
    valorMes10,
    valorMes11,
    valorMes12,
    obs,
    fonte,
    codAplicacao,
    idSecretaria,
    idDepto,
    idEconomica,
    idFuncional,
    despesaCopia,
    despesaAcao,
    despesaNova,
    descrDespNova,
    tipoAcao,
    idSecretario,
  } = parsedParams.data;

  let descDespNova = descrDespNova;

  let despesas = [];
  if (despesaAcao) {
    despesas.push(despesaAcao);
  }
  if (despesaCopia) {
    despesas.push(despesaCopia);
  }
  if (despesaNova) {
    despesas.push(despesaNova);
  }

  let updatedotacoesPromise: any = null;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const dotacoes = await tx.dotacoes.findMany({
        where: {
          exercicio,
          despesa: {
            in: despesas,
          },
        },
      });

      if (!dotacoes) {
        return { error: 'Não foi possivel encontrar as despesas' };
      }

      if (tipoAlteracao == tiposAlteracao['Anulação']) {
        const dotacaoAcao = dotacoes.filter(
          (dotacao: any) => dotacao.despesa == despesaAcao
        )[0];
        if (!dotacaoAcao) {
          throw new Error('Dotação não encontrada');
        }
        console.log('1');
        const validacao = await vldDados(
          parsedParams.data,
          dotacaoAcao,
          null,
          true,
          acessos
        );
        if (!validacao.status) {
          throw new Error(validacao.error);
        }

        if (
          tipoAcao == tiposAcao['Suplementação'] ||
          tipoAcao == tiposAcao['Debito']
        ) {
          const dotacaoDespNova = dotacoes.filter(
            (dotacao: any) => dotacao.despesa == despesaNova
          )[0];
          if (!dotacaoDespNova) {
            throw new Error('Dotação não encontrada');
          }

          const validacao = await vldDados(
            parsedParams.data,
            dotacaoDespNova,
            null,
            false,
            acessos
          );
          if (!validacao.status) {
            throw new Error(validacao.error);
          }
        } else {
          if (!despesaCopia) {
            throw new Error('Despesa Copia não preenchida');
          }
          console.log('2');
          const dotacaoCopia = dotacoes.filter(
            (dotacao: any) => dotacao.despesa == despesaCopia
          )[0];
          if (!dotacaoCopia) {
            throw new Error('Dotação não encontrada');
          }
          console.log('3');
          if (Number(dotacaoCopia.valorAtual) < 0.01) {
            throw new Error(
              'Valor da dotação (despesa) cópia com valor menor que R$ 0.01 centavo'
            );
          }

          const vldValores = await vldDados(
            parsedParams.data,
            dotacaoCopia,
            null,
            false,
            acessos
          );
          if (!vldValores.status) {
            throw new Error(vldValores.error);
          }
          descDespNova = dotacaoCopia.desc;
        }

        // Reserva valores da dotação
        updatedotacoesPromise = tx.dotacoes.update({
          where: {
            id: dotacaoAcao?.id,
            ativo: true,
          },
          data: {
            anulacao: toCurrency(dotacaoAcao.anulacao).add(valorTotal).value,
            valorAtual: toCurrency(dotacaoAcao.valorAtual).subtract(valorTotal)
              .value,
            cotaMes1: toCurrency(dotacaoAcao.cotaMes1).subtract(valorMes1)
              .value,
            cotaMes2: toCurrency(dotacaoAcao.cotaMes2).subtract(valorMes2)
              .value,
            cotaMes3: toCurrency(dotacaoAcao.cotaMes3).subtract(valorMes3)
              .value,
            cotaMes4: toCurrency(dotacaoAcao.cotaMes4).subtract(valorMes4)
              .value,
            cotaMes5: toCurrency(dotacaoAcao.cotaMes5).subtract(valorMes5)
              .value,
            cotaMes6: toCurrency(dotacaoAcao.cotaMes6).subtract(valorMes6)
              .value,
            cotaMes7: toCurrency(dotacaoAcao.cotaMes7).subtract(valorMes7)
              .value,
            cotaMes8: toCurrency(dotacaoAcao.cotaMes8).subtract(valorMes8)
              .value,
            cotaMes9: toCurrency(dotacaoAcao.cotaMes9).subtract(valorMes9)
              .value,
            cotaMes10: toCurrency(dotacaoAcao.cotaMes10).subtract(valorMes10)
              .value,
            cotaMes11: toCurrency(dotacaoAcao.cotaMes11).subtract(valorMes11)
              .value,
            cotaMes12: toCurrency(dotacaoAcao.cotaMes12).subtract(valorMes12)
              .value,
          },
        });
      } else if (tipoAcao == tiposAcao['Suplementação']) {
        const vldValores = await vldDados(
          parsedParams.data,
          null,
          null,
          true,
          acessos
        );
        if (!vldValores.status) {
          throw new Error(vldValores.error);
        }
      } else if (tipoAcao == tiposAcao['Criação de Despesa']) {
        if (!despesaCopia) {
          throw new Error('Despesa Copia não preenchida');
        }

        const dotacao = dotacoes.filter(
          (dotacao: any) => dotacao.despesa == despesaCopia
        )[0];
        if (!dotacao) {
          throw new Error('Dotação não encontrada');
        }

        if (Number(dotacao.valorAtual) < 0.01) {
          throw new Error(
            'Valor da dotação (despesa) cópia com valor menor que R$ 0.01 centavo'
          );
        }

        const vldValores = await vldDados(
          parsedParams.data,
          dotacao,
          null,
          true,
          acessos
        );
        if (!vldValores.status) {
          throw new Error(vldValores.error);
        }

        descDespNova = dotacao.desc;
      }

      const alteracaoOrcamentariaPromise = tx.alteracaoOrcamentaria.create({
        data: {
          exercicio,
          tipoAlteracao,
          tipoAcao,
          status: 0,
          valorTotal: valorTotal,
          valorMes1: valorMes1,
          valorMes2: valorMes2,
          valorMes3: valorMes3,
          valorMes4: valorMes4,
          valorMes5: valorMes5,
          valorMes6: valorMes6,
          valorMes7: valorMes7,
          valorMes8: valorMes8,
          valorMes9: valorMes9,
          valorMes10: valorMes10,
          valorMes11: valorMes11,
          valorMes12: valorMes12,
          dataAbertura: obterDataAtual(),
          dataStatus: obterDataAtual(),
          fonte: fonte || 0,
          codAplicacao: codAplicacao || 0,
          idSecretaria,
          idDepto: idDepto,
          idEconomica: idEconomica,
          idFuncional: idFuncional,
          despesaCopia: despesaCopia || 0,
          despesaAcao: despesaAcao || 0,
          obs: obs!,
          idUsuario: resultPermissao.idUsuario!,
          despesaNova: despesaNova || 0,
          descrDespNova: descDespNova || '',
          ativo: true,
          dataDecreto: null,
          tipoDecreto: 0,
          numDecreto: 0,
          idSecretario,
        },
      });

      let alteracaoOrcamentaria,
        updatedotacoes = null;

      if (updatedotacoesPromise) {
        [updatedotacoes, alteracaoOrcamentaria] = await Promise.all([
          updatedotacoesPromise,
          alteracaoOrcamentariaPromise,
        ]);
        if (!updatedotacoes) {
          throw new Error('Erro ao reservar valores na dotação de anulação');
        }
      } else {
        [alteracaoOrcamentaria] = await Promise.all([
          alteracaoOrcamentariaPromise,
        ]);
      }

      if (!alteracaoOrcamentaria) {
        throw new Error('Erro ao incluir alteração orçamentária');
      }

      const altOrcAudit = await tx.controleAlteracaoOrcamentaria_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idAlterOrca: alteracaoOrcamentaria.id,
          acao: AuditoriaAlteracaoOrcamentaria.CRIAR_ALTERACAO_ORCAMENTARIA,
          ip,
        },
      });

      if (!altOrcAudit) {
        throw new Error('Erro criar log alteração orçamentária');
      }

      console.log(
        tipoAlteracao +
          ' =! ' +
          tiposAlteracao['Anulação'] +
          ' - ' +
          (tipoAcao +
            ' == ' +
            tiposAcao['Suplementação'] +
            ' - ' +
            tipoAcao +
            ' == ' +
            tiposAcao['Criação de Despesa'])
      );
      if (
        tipoAlteracao != tiposAlteracao['Anulação'] &&
        (tipoAcao == tiposAcao['Suplementação'] ||
          tipoAcao == tiposAcao['Criação de Despesa'])
      ) {
        // (tx: any, alteracaoOrcamentariaData: any, idUsuario: number, tela: string, acao: number, ip: any)
        const paramsData = {
          ...parsedParams.data,
          idAltOrcament: alteracaoOrcamentaria.id,
        };

        const tela = 'Alteração Orçamentária';
        const processaSuperavit = await processaPedidoSuperavit(
          tx,
          paramsData,
          resultPermissao.idUsuario || 0,
          tela,
          AuditoriaAlteracaoOrcamentaria.CRIAR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!processaSuperavit.status) {
          throw new Error(processaSuperavit.error);
        }
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
  } catch (e: any) {
    console.log(e.message);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Código da Alteração Orçamentária já existe.`,
        };
      }
    }
    return {
      error: e.message || `Erro ao criar Alteração Orçamentária.`,
    };
  }
};

export const editarAlteracaoOrcamentaria = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const acesso = await listarAcessosOrgaosUsuarioConectado();

  const parsedParams = alteracaoOrcamentariaSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const {
    id,
    tipoAlteracao,
    tipoAcao,
    exercicio,
    valorTotal,
    valorMes1,
    valorMes2,
    valorMes3,
    valorMes4,
    valorMes5,
    valorMes6,
    valorMes7,
    valorMes8,
    valorMes9,
    valorMes10,
    valorMes11,
    valorMes12,
    obs,
    despesaAcao,
    despesaCopia,
    despesaNova,
    descrDespNova,
    idSecretario,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    const alterOrcAntigo = await prisma.alteracaoOrcamentaria.findUnique({
      where: {
        id: id,
      },
      select: {
        valorTotal: true,
        valorMes1: true,
        valorMes2: true,
        valorMes3: true,
        valorMes4: true,
        valorMes5: true,
        valorMes6: true,
        valorMes7: true,
        valorMes8: true,
        valorMes9: true,
        valorMes10: true,
        valorMes11: true,
        valorMes12: true,
        obs: true,
        ativo: true,
        status: true,
      },
    });

    if (!alterOrcAntigo?.ativo) {
      return { error: 'Alteração Orçamentária Inativa' };
    }

    if (
      !(
        acesso.data?.acessoTotal &&
        (Number(alterOrcAntigo.status) ==
          StatusAlteracaoOrcamentaria['Em Andamento'] ||
          Number(alterOrcAntigo.status) ==
            StatusAlteracaoOrcamentaria['Em Aberto'])
      ) ||
      (!acesso.data?.acessoTotal &&
        Number(alterOrcAntigo.status) !=
          StatusAlteracaoOrcamentaria['Em Aberto'])
    ) {
      return {
        error: 'Status da alteração orçamentária diferente de Em Aberto',
      };
    }

    let descDespNova = descrDespNova;

    let despesas = [];
    if (despesaAcao) {
      despesas.push(despesaAcao);
    }
    if (despesaCopia) {
      despesas.push(despesaCopia);
    }
    if (despesaNova) {
      despesas.push(despesaNova);
    }

    let updatedotacoesPromise: Prisma.Prisma__dotacoesClient<any> | null = null;

    await prisma.$transaction(async (tx) => {
      const dotacoes = await tx.dotacoes.findMany({
        where: {
          exercicio,
          despesa: {
            in: despesas,
          },
        },
      });

      if (!dotacoes) {
        throw new Error('Dotação não encontrada');
      }

      if (tipoAlteracao == tiposAlteracao['Anulação']) {
        const dotacaoAcao = dotacoes.filter(
          (dotacao: any) => dotacao.despesa == despesaAcao
        )[0];
        if (!dotacaoAcao) {
          throw new Error('Dotação não encontrada');
        }

        const validacao = await vldDados(
          parsedParams.data,
          dotacaoAcao,
          alterOrcAntigo,
          true,
          acesso
        );
        if (!validacao.status) {
          throw new Error(validacao.error);
        }

        if (
          tipoAcao == tiposAcao['Suplementação'] ||
          tipoAcao == tiposAcao['Debito']
        ) {
          const dotacaoDespNova = dotacoes.filter(
            (dotacao: any) => dotacao.despesa == despesaNova
          )[0];
          if (!dotacaoDespNova) {
            throw new Error('Dotação não encontrada');
          }

          const validacao = await vldDados(
            parsedParams.data,
            dotacaoDespNova,
            null,
            false,
            acesso
          );
          if (!validacao.status) {
            throw new Error(validacao.error);
          }
        } else {
          if (!despesaCopia) {
            throw new Error('Despesa Copia não preenchida');
          }

          const dotacaoCopia = dotacoes.filter(
            (dotacao: any) => dotacao.despesa == despesaCopia
          )[0];
          if (!dotacaoCopia) {
            throw new Error('Dotação não encontrada');
          }

          if (Number(dotacaoCopia.valorAtual) < 0.01) {
            throw new Error(
              'Valor da dotação (despesa) cópia com valor menor que R$ 0.01 centavo'
            );
          }

          const vldValores = await vldDados(
            parsedParams.data,
            dotacaoCopia,
            null,
            false,
            acesso
          );
          if (!vldValores.status) {
            throw new Error(vldValores.error);
          }
          descDespNova = dotacaoCopia.desc;
        }

        updatedotacoesPromise = tx.dotacoes.update({
          where: {
            id: dotacaoAcao?.id,
            ativo: true,
          },
          data: {
            anulacao: toCurrency(dotacaoAcao.anulacao)
              .subtract(Number(alterOrcAntigo.valorTotal))
              .add(valorTotal).value, // ver se é agora ou na aprovação
            valorAtual: toCurrency(dotacaoAcao.valorAtual)
              .add(Number(alterOrcAntigo.valorTotal))
              .subtract(valorTotal).value,
            cotaMes1: toCurrency(dotacaoAcao.cotaMes1)
              .add(Number(alterOrcAntigo.valorMes1))
              .subtract(valorMes1).value,
            cotaMes2: toCurrency(dotacaoAcao.cotaMes2)
              .add(Number(alterOrcAntigo.valorMes2))
              .subtract(valorMes2).value,
            cotaMes3: toCurrency(dotacaoAcao.cotaMes3)
              .add(Number(alterOrcAntigo.valorMes3))
              .subtract(valorMes3).value,
            cotaMes4: toCurrency(dotacaoAcao.cotaMes4)
              .add(Number(alterOrcAntigo.valorMes4))
              .subtract(valorMes4).value,
            cotaMes5: toCurrency(dotacaoAcao.cotaMes5)
              .add(Number(alterOrcAntigo.valorMes5))
              .subtract(valorMes5).value,
            cotaMes6: toCurrency(dotacaoAcao.cotaMes6)
              .add(Number(alterOrcAntigo.valorMes6))
              .subtract(valorMes6).value,
            cotaMes7: toCurrency(dotacaoAcao.cotaMes7)
              .add(Number(alterOrcAntigo.valorMes7))
              .subtract(valorMes7).value,
            cotaMes8: toCurrency(dotacaoAcao.cotaMes8)
              .add(Number(alterOrcAntigo.valorMes8))
              .subtract(valorMes8).value,
            cotaMes9: toCurrency(dotacaoAcao.cotaMes9)
              .add(Number(alterOrcAntigo.valorMes9))
              .subtract(valorMes9).value,
            cotaMes10: toCurrency(dotacaoAcao.cotaMes10)
              .add(Number(alterOrcAntigo.valorMes10))
              .subtract(valorMes10).value,
            cotaMes11: toCurrency(dotacaoAcao.cotaMes11)
              .add(Number(alterOrcAntigo.valorMes11))
              .subtract(valorMes11).value,
            cotaMes12: toCurrency(dotacaoAcao.cotaMes12)
              .add(Number(alterOrcAntigo.valorMes12))
              .subtract(valorMes12).value,
          },
        });
      } else if (tipoAcao == tiposAcao['Suplementação']) {
        const vldValores = await vldDados(
          parsedParams.data,
          null,
          null,
          true,
          acesso
        );
        if (!vldValores.status) {
          throw new Error(vldValores.error);
        }
      } else if (tipoAcao == tiposAcao['Criação de Despesa']) {
        // Criação de despesa para excesso e superavit
        if (!despesaCopia) {
          throw new Error('Despesa Copia não preenchida');
        }

        const dotacao = dotacoes.filter(
          (dotacao: any) => dotacao.despesa == despesaCopia
        )[0];
        if (!dotacao) {
          throw new Error('Dotação (despesa cópia) não encontrada');
        }

        if (Number(dotacao.valorAtual) < 0.01) {
          throw new Error(
            'Valor da dotação (despesa) cópia com valor menor que R$ 0.01 centavo'
          );
        }

        const validacao = await vldDados(
          parsedParams.data,
          dotacao,
          alterOrcAntigo,
          true,
          acesso
        );
        if (!validacao.status) {
          throw new Error(validacao.error);
        }

        descDespNova = dotacao.desc;
      }

      // atualiza e insere log
      const alteracaoOrcamentariaUpdPromise = tx.alteracaoOrcamentaria.update({
        where: {
          id: id,
          ativo: true,
        },
        data: {
          valorTotal,
          valorMes1,
          valorMes2,
          valorMes3,
          valorMes4,
          valorMes5,
          valorMes6,
          valorMes7,
          valorMes8,
          valorMes9,
          valorMes10,
          valorMes11,
          valorMes12,
          obs,
          dataStatus: obterDataAtual(),
          status: StatusAlteracaoOrcamentaria['Em Aberto'],
          descrDespNova: descDespNova,
          idSecretario,
        },
      });

      let alteracaoOrcamentariaData,
        updatedotacoes = null;

      if (updatedotacoesPromise) {
        [updatedotacoes, alteracaoOrcamentariaData] = await Promise.all([
          updatedotacoesPromise,
          alteracaoOrcamentariaUpdPromise,
        ]);
        if (!updatedotacoes) {
          throw new Error('Erro ao reservar valores na dotação de anulação');
        }
      } else {
        [alteracaoOrcamentariaData] = await Promise.all([
          alteracaoOrcamentariaUpdPromise,
        ]);
      }

      if (!alteracaoOrcamentariaData) {
        throw new Error('Erro ao incluir alteração orçamentária');
      }

      const dataAudit = await criarAuditAlterOrcamentaria(
        alterOrcAntigo,
        parsedParams,
        resultPermissao.idUsuario!,
        id,
        ip,
        AuditoriaAlteracaoOrcamentaria.ALTERAR_ALTERACAO_ORCAMENTARIA
      );

      const auditAlteracaoOrcamentariaPromise =
        tx.controleAlteracaoOrcamentaria_audit.createMany({
          data: dataAudit,
        });

      const [alteracaoOrcamentaria, auditAlteracaoOrcamentaria] =
        await Promise.all([
          alteracaoOrcamentariaUpdPromise,
          auditAlteracaoOrcamentariaPromise,
        ]);

      if (!alteracaoOrcamentaria) {
        throw new Error('Alteração orçamentária não foi Alterada');
      }

      if (!auditAlteracaoOrcamentaria) {
        throw new Error('Auditoria da alteração orçamentária não foi criada');
      }

      if (
        tipoAlteracao != tiposAlteracao['Anulação'] &&
        (tipoAcao == tiposAcao['Suplementação'] ||
          tipoAcao == tiposAcao['Criação de Despesa'])
      ) {
        const paramsData = {
          ...parsedParams.data,
          idAltOrcament: alteracaoOrcamentaria.id,
        };

        const tela = 'Alteração Orçamentária Editar';

        const processaSuperavit = await processaEditPedidoSuperavit(
          tx,
          paramsData,
          resultPermissao.idUsuario || 0,
          tela,
          AuditoriaAlteracaoOrcamentaria.ALTERAR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!processaSuperavit.status) {
          throw new Error(processaSuperavit.error);
        }
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao Editar Pedido.`,
    };
  }
};

const vldDados = async (
  dados: any,
  dotacao?: any,
  dadosAntigos?: any,
  vldValores?: boolean,
  acessos?: any
) => {
  const {
    tipoAlteracao,
    tipoAcao,
    valorTotal,
    valorMes1,
    valorMes2,
    valorMes3,
    valorMes4,
    valorMes5,
    valorMes6,
    valorMes7,
    valorMes8,
    valorMes9,
    despesaAcao,
    valorMes10,
    valorMes11,
    valorMes12,
    fonte,
    idSecretaria,
    despesaCopia,
    despesaNova,
  } = dados;
  const somaMeses = currency(valorMes1, currencyOptionsNoSymbol)
    .add(valorMes2)
    .add(valorMes3)
    .add(valorMes4)
    .add(valorMes5)
    .add(valorMes6)
    .add(valorMes7)
    .add(valorMes8)
    .add(valorMes9)
    .add(valorMes10)
    .add(valorMes11)
    .add(valorMes12);

  /*const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

  if (acessos.error) {
    return {
      error: acessos.error,
    };
  }

  if (!acessos.data) {
    return {
      error: 'Não foi possível obter acessos do usuário.',
    };
  }*/

  if (valorTotal != somaMeses) {
    return {
      status: false,
      error:
        'Valor total diferente das somas dos meses. ' +
        'Valor Total: ' +
        valorTotal +
        ' somaMeses: ' +
        somaMeses,
    };
  }

  if (tipoAcao == tiposAcao['Criação de Despesa']) {
    if (!dotacao) {
      return { status: false, error: 'Dotação não encontrada' };
    }

    if (!despesaCopia) {
      return { status: false, error: 'Despesa Cópia não preenchida' };
    }
  }

  if (tipoAcao == tiposAcao['Suplementação'] && !acessos.data.acessoTotal) {
    if (dotacao && dotacao.idSecretaria == idSecretaria) {
      return {
        status: false,
        error: 'Secretária da dotação diferente da despesa',
      };
    }
  }

  if (tipoAlteracao == tiposAlteracao['Superavit']) {
    if (fonte.toString().charAt(0) != '9') {
      return {
        status: false,
        error: 'Fonte da despesa deve iniciar com 9 (Exercicio anterior)',
      };
    }
  }

  if (tipoAlteracao == tiposAlteracao['Anulação']) {
    /* if (despesaAcao == despesaCopia) {
      return {
        status: false,
        error: 'Despesa de anulação deve ser diferente da despesa cópia',
      };
    } */

    if (despesaAcao == despesaNova) {
      return {
        status: false,
        error:
          'Despesa de anulação deve ser diferente da despesa suplementação',
      };
    }

    if (!dotacao) {
      return { status: false, error: 'Dotação não encontrada' };
    }

    if (dotacao.secretariaId != idSecretaria && !acessos.data.acessoTotal) {
      return {
        status: false,
        error: 'Secretaria diferente da dotação a ser anulada',
      };
    }

    console.log('aqui3: ' + dotacao.fonte + '!=' + fonte);
    if (dotacao.fonte != fonte) {
      return { status: false, error: 'Fonte da despesa de anulação diferente' };
    }

    if (vldValores || true) {
      let valAtual = toCurrency(dotacao.valorAtual);
      let cotaMes1 = toCurrency(dotacao.cotaMes1);
      let cotaMes2 = toCurrency(dotacao.cotaMes2);
      let cotaMes3 = toCurrency(dotacao.cotaMes3);
      let cotaMes4 = toCurrency(dotacao.cotaMes4);
      let cotaMes5 = toCurrency(dotacao.cotaMes5);
      let cotaMes6 = toCurrency(dotacao.cotaMes6);
      let cotaMes7 = toCurrency(dotacao.cotaMes7);
      let cotaMes8 = toCurrency(dotacao.cotaMes8);
      let cotaMes9 = toCurrency(dotacao.cotaMes9);
      let cotaMes10 = toCurrency(dotacao.cotaMes10);
      let cotaMes11 = toCurrency(dotacao.cotaMes11);
      let cotaMes12 = toCurrency(dotacao.cotaMes12);

      if (dadosAntigos) {
        valAtual = valAtual.add(Number(dadosAntigos.valorTotal));
        cotaMes1 = cotaMes1.add(Number(dadosAntigos.valorMes1));
        cotaMes2 = cotaMes2.add(Number(dadosAntigos.valorMes2));
        cotaMes3 = cotaMes3.add(Number(dadosAntigos.valorMes3));
        cotaMes4 = cotaMes4.add(Number(dadosAntigos.valorMes4));
        cotaMes5 = cotaMes5.add(Number(dadosAntigos.valorMes5));
        cotaMes6 = cotaMes6.add(Number(dadosAntigos.valorMes6));
        cotaMes7 = cotaMes7.add(Number(dadosAntigos.valorMes7));
        cotaMes8 = cotaMes8.add(Number(dadosAntigos.valorMes8));
        cotaMes9 = cotaMes9.add(Number(dadosAntigos.valorMes9));
        cotaMes10 = cotaMes10.add(Number(dadosAntigos.valorMes10));
        cotaMes11 = cotaMes11.add(Number(dadosAntigos.valorMes11));
        cotaMes12 = cotaMes12.add(Number(dadosAntigos.valorMes12));
      }

      if (!dotacao.ativo) {
        return { status: false, error: 'Dotação Inativa' };
      } else if (valorTotal > valAtual.value) {
        return {
          status: false,
          error: 'Valor Total está maior que o Saldo da Dotação',
        };
      } else if (valorMes1 > cotaMes1.value) {
        return {
          status: false,
          error: 'Valor de Janeiro está maior que o Saldo Atual',
        };
      } else if (valorMes2 > cotaMes2.value) {
        return {
          status: false,
          error: 'Valor de Fevereiro está maior que o Saldo Atual',
        };
      } else if (valorMes3 > cotaMes3.value) {
        return {
          status: false,
          error: 'Valor de Março está maior que o Saldo Atual',
        };
      } else if (valorMes4 > cotaMes4.value) {
        return {
          status: false,
          error: 'Valor de Abril está maior que o Saldo Atual',
        };
      } else if (valorMes5 > cotaMes5.value) {
        return {
          status: false,
          error: 'Valor de Maio está maior que o Saldo Atual',
        };
      } else if (valorMes6 > cotaMes6.value) {
        return {
          status: false,
          error: 'Valor de Junho está maior que o Saldo Atual',
        };
      } else if (valorMes7 > cotaMes7.value) {
        return {
          status: false,
          error: 'Valor de Julho está maior que o Saldo Atual',
        };
      } else if (valorMes8 > cotaMes8.value) {
        return {
          status: false,
          error: 'Valor de Agosto está maior que o Saldo Atual',
        };
      } else if (valorMes9 > cotaMes9.value) {
        return {
          status: false,
          error: 'Valor de Setembro está maior que o Saldo Atual',
        };
      } else if (valorMes10 > cotaMes10.value) {
        return {
          status: false,
          error: 'Valor de Outubro está maior que o Saldo Atual',
        };
      } else if (valorMes11 > cotaMes11.value) {
        return {
          status: false,
          error: 'Valor de Novembro está maior que o Saldo Atual',
        };
      } else if (valorMes12 > cotaMes12.value) {
        return {
          status: false,
          error: 'Valor de Dezembro está maior que o Saldo Atual',
        };
      }
      return { status: true };
    }
  }
  return { status: true };
};

export const deletarAlteracaoOrcamentaria = async (id: number) => {
  try {
    const altOrcamentaria = await prisma.alteracaoOrcamentaria.delete({
      where: {
        id,
        ativo: true,
      },
    });
    revalidatePath('/movimento/alteracaoOrcamentaria');
    return {
      data: altOrcamentaria,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: `Erro ao deletar Alteração Orçamentária.`,
    };
  }
};

const criarAuditAlterOrcamentaria = (
  obj: any,
  params: any,
  idUsuario: number,
  idAlterOrca: number,
  ip: string,
  acao: number
) => {
  const data = params.data;
  let arr: any[] = [];

  Object.keys(obj).map((key) => {
    Object.keys(data).map((key1) => {
      if (key == key1) {
        if (obj[key] != data[key1]) {
          let objAux = {
            idUsuario: idUsuario,
            idAlterOrca: idAlterOrca,
            acao,
            de: obj[key].toString(),
            para: data[key1].toString(),
            ip,
          };
          arr.push(objAux);
        }
      }
    });
  });

  return arr;
};

export const obterAlteracaoPorId = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const alteracaoOrcamentaria = await prisma.alteracaoOrcamentaria.findUnique(
      {
        where: {
          id: id,
        },
        include: {
          secretario: true,
        },
      }
    );

    return {
      data: {
        ...alteracaoOrcamentaria,
        valorTotal: Number(alteracaoOrcamentaria?.valorTotal),
        valorMes1: Number(alteracaoOrcamentaria?.valorMes1),
        valorMes2: Number(alteracaoOrcamentaria?.valorMes2),
        valorMes3: Number(alteracaoOrcamentaria?.valorMes3),
        valorMes4: Number(alteracaoOrcamentaria?.valorMes4),
        valorMes5: Number(alteracaoOrcamentaria?.valorMes5),
        valorMes6: Number(alteracaoOrcamentaria?.valorMes6),
        valorMes7: Number(alteracaoOrcamentaria?.valorMes7),
        valorMes8: Number(alteracaoOrcamentaria?.valorMes8),
        valorMes9: Number(alteracaoOrcamentaria?.valorMes9),
        valorMes10: Number(alteracaoOrcamentaria?.valorMes10),
        valorMes11: Number(alteracaoOrcamentaria?.valorMes11),
        valorMes12: Number(alteracaoOrcamentaria?.valorMes12),
      },
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || 'Erro ao obter Pedido.',
    };
  }
};

export const ativarPedDesp = async (params: unknown) => {
  // TODO audit para dotacao
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  const alteracaoOrcamentariaData =
    await prisma.alteracaoOrcamentaria.findUnique({
      where: {
        id,
      },
    });

  if (!alteracaoOrcamentariaData) {
    return { error: 'Alteração orçamentária não encontrada' };
  }

  if (
    alteracaoOrcamentariaData.status != StatusAlteracaoOrcamentaria['Em Aberto']
  ) {
    return { error: 'Alteração orçamentária não está com status "Em Aberto" ' };
  }

  const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

  if (acessos.error) {
    return {
      error: acessos.error,
    };
  }

  if (!acessos.data) {
    return {
      error: 'Não foi possível obter acessos do usuário.',
    };
  }

  const { exercicio, tipoAlteracao, tipoAcao, despesaAcao, despesaCopia } =
    alteracaoOrcamentariaData;

  const valorTotal = Number(alteracaoOrcamentariaData?.valorTotal || 0);
  const valorMes1 = Number(alteracaoOrcamentariaData?.valorMes1 || 0);
  const valorMes2 = Number(alteracaoOrcamentariaData?.valorMes2 || 0);
  const valorMes3 = Number(alteracaoOrcamentariaData?.valorMes3 || 0);
  const valorMes4 = Number(alteracaoOrcamentariaData?.valorMes4 || 0);
  const valorMes5 = Number(alteracaoOrcamentariaData?.valorMes5 || 0);
  const valorMes6 = Number(alteracaoOrcamentariaData?.valorMes6 || 0);
  const valorMes7 = Number(alteracaoOrcamentariaData?.valorMes7 || 0);
  const valorMes8 = Number(alteracaoOrcamentariaData?.valorMes8 || 0);
  const valorMes9 = Number(alteracaoOrcamentariaData?.valorMes9 || 0);
  const valorMes10 = Number(alteracaoOrcamentariaData?.valorMes10 || 0);
  const valorMes11 = Number(alteracaoOrcamentariaData?.valorMes11 || 0);
  const valorMes12 = Number(alteracaoOrcamentariaData?.valorMes12 || 0);

  const dados = {
    ...alteracaoOrcamentariaData,
    valorTotal,
    valorMes1,
    valorMes2,
    valorMes3,
    valorMes4,
    valorMes5,
    valorMes6,
    valorMes7,
    valorMes8,
    valorMes9,
    valorMes10,
    valorMes11,
    valorMes12,
  };

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      if (tipoAlteracao == tiposAlteracao['Anulação']) {
        const dotacao = await getDotacao(
          tx,
          alteracaoOrcamentariaData?.exercicio || 0,
          despesaAcao!
        );
        if (!dotacao) {
          throw new Error('Dotação a ser anulada não encontrada');
        }

        const validacao = await vldDados(dados, dotacao, null, true, acessos);
        if (!validacao.status) {
          throw new Error(validacao.error);
        }

        const dotacaoUpd = await tx.dotacoes.update({
          where: {
            id: dotacao?.id,
            ativo: true,
          },
          data: {
            anulacao: toCurrency(dotacao.anulacao).add(valorTotal).value,
            valorAtual: toCurrency(dotacao.valorAtual).subtract(valorTotal)
              .value,
            cotaMes1: toCurrency(dotacao.cotaMes1).subtract(valorMes1).value,
            cotaMes2: toCurrency(dotacao.cotaMes2).subtract(valorMes2).value,
            cotaMes3: toCurrency(dotacao.cotaMes3).subtract(valorMes3).value,
            cotaMes4: toCurrency(dotacao.cotaMes4).subtract(valorMes4).value,
            cotaMes5: toCurrency(dotacao.cotaMes5).subtract(valorMes5).value,
            cotaMes6: toCurrency(dotacao.cotaMes6).subtract(valorMes6).value,
            cotaMes7: toCurrency(dotacao.cotaMes7).subtract(valorMes7).value,
            cotaMes8: toCurrency(dotacao.cotaMes8).subtract(valorMes8).value,
            cotaMes9: toCurrency(dotacao.cotaMes9).subtract(valorMes9).value,
            cotaMes10: toCurrency(dotacao.cotaMes10).subtract(valorMes10).value,
            cotaMes11: toCurrency(dotacao.cotaMes11).subtract(valorMes11).value,
            cotaMes12: toCurrency(dotacao.cotaMes12).subtract(valorMes12).value,
          },
        });

        if (!dotacaoUpd) {
          throw new Error('Erro ao reservar valores na dotação de anulação');
        }
      } else if (tipoAcao == tiposAcao['Suplementação']) {
        // implementar fonte Superavit começa com 9
        const vldValores = await vldDados(dados, null, null, true, acessos);
        if (!vldValores.status) {
          throw new Error(vldValores.error);
        }
        const processaSuperavit = await processaReabrirPedidoSuperavit(
          tx,
          alteracaoOrcamentariaData,
          resultPermissao.idUsuario || 0,
          AuditoriaAlteracaoOrcamentaria.CRIAR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!processaSuperavit.status) {
          throw new Error(processaSuperavit.error);
        }
      } else if (tipoAcao == tiposAcao['Criação de Despesa']) {
        if (!despesaCopia) {
          throw new Error('Despesa Copia não preenchida');
        }

        const dotacao = await getDotacao(tx, exercicio, despesaCopia);
        if (!dotacao) {
          throw new Error('Dotação não encontrada');
        }

        const vldValores = await vldDados(dados, dotacao, null, true, acessos);
        if (!vldValores.status) {
          throw new Error(vldValores.error);
        }

        const processaSuperavit = await processaReabrirPedidoSuperavit(
          tx,
          alteracaoOrcamentariaData,
          resultPermissao.idUsuario || 0,
          AuditoriaAlteracaoOrcamentaria.CRIAR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!processaSuperavit.status) {
          throw new Error(processaSuperavit.error);
        }
      }

      const resultPromise = tx.alteracaoOrcamentaria.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.controleAlteracaoOrcamentaria_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idAlterOrca: id,
          acao: AuditoriaAlteracaoOrcamentaria.ATIVAR_ALTERACAO_ORCAMENTARIA,
          ip,
        },
      });

      const [altOrc, altOrcAudit] = await Promise.all([
        resultPromise,
        auditPromise,
      ]);

      if (!altOrc) {
        throw new Error('Erro atualizar alteração orçamentária');
      }

      if (!altOrcAudit) {
        throw new Error('Erro criar log alteração orçamentária');
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
    return {
      data: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao ativar Alteração Orçamentária.`,
    };
  }
};

export const desativarPedDesp = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  const alteracaoOrcamentariaData =
    await prisma.alteracaoOrcamentaria.findUnique({
      where: {
        id,
      },
    });

  if (!alteracaoOrcamentariaData) {
    return { error: 'Alteração orçamentária não encontrada' };
  }

  if (
    alteracaoOrcamentariaData.status != StatusAlteracaoOrcamentaria['Em Aberto']
  ) {
    return { error: 'Alteração orçamentária não está com status "Em Aberto" ' };
  }

  const { tipoAlteracao, tipoAcao, despesaAcao } = alteracaoOrcamentariaData;

  const valorTotal = Number(alteracaoOrcamentariaData?.valorTotal) || 0;
  const valorMes1 = Number(alteracaoOrcamentariaData?.valorMes1) || 0;
  const valorMes2 = Number(alteracaoOrcamentariaData?.valorMes2) || 0;
  const valorMes3 = Number(alteracaoOrcamentariaData?.valorMes3) || 0;
  const valorMes4 = Number(alteracaoOrcamentariaData?.valorMes4) || 0;
  const valorMes5 = Number(alteracaoOrcamentariaData?.valorMes5) || 0;
  const valorMes6 = Number(alteracaoOrcamentariaData?.valorMes6) || 0;
  const valorMes7 = Number(alteracaoOrcamentariaData?.valorMes7) || 0;
  const valorMes8 = Number(alteracaoOrcamentariaData?.valorMes8) || 0;
  const valorMes9 = Number(alteracaoOrcamentariaData?.valorMes9) || 0;
  const valorMes10 = Number(alteracaoOrcamentariaData?.valorMes10) || 0;
  const valorMes11 = Number(alteracaoOrcamentariaData?.valorMes11) || 0;
  const valorMes12 = Number(alteracaoOrcamentariaData?.valorMes12) || 0;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      if (tipoAlteracao == tiposAlteracao['Anulação']) {
        const dotacao = await getDotacao(
          tx,
          alteracaoOrcamentariaData?.exercicio || 0,
          despesaAcao!
        );
        if (!dotacao) {
          throw new Error('Dotação de anulação a ser estornada não encontrada');
        }

        const dotacaoUpd = await tx.dotacoes.update({
          where: {
            id: dotacao?.id,
            ativo: true,
          },
          data: {
            anulacao: toCurrency(dotacao.anulacao).subtract(valorTotal).value,
            valorAtual: toCurrency(dotacao.valorAtual).add(valorTotal).value,
            cotaMes1: toCurrency(dotacao.cotaMes1).add(valorMes1).value,
            cotaMes2: toCurrency(dotacao.cotaMes2).add(valorMes2).value,
            cotaMes3: toCurrency(dotacao.cotaMes3).add(valorMes3).value,
            cotaMes4: toCurrency(dotacao.cotaMes4).add(valorMes4).value,
            cotaMes5: toCurrency(dotacao.cotaMes5).add(valorMes5).value,
            cotaMes6: toCurrency(dotacao.cotaMes6).add(valorMes6).value,
            cotaMes7: toCurrency(dotacao.cotaMes7).add(valorMes7).value,
            cotaMes8: toCurrency(dotacao.cotaMes8).add(valorMes8).value,
            cotaMes9: toCurrency(dotacao.cotaMes9).add(valorMes9).value,
            cotaMes10: toCurrency(dotacao.cotaMes10).add(valorMes10).value,
            cotaMes11: toCurrency(dotacao.cotaMes11).add(valorMes11).value,
            cotaMes12: toCurrency(dotacao.cotaMes12).add(valorMes12).value,
          },
        });

        if (!dotacaoUpd) {
          throw new Error('Erro ao reservar valores na dotação de anulação');
        }
      } else if (
        tipoAcao == tiposAcao['Suplementação'] ||
        tipoAcao == tiposAcao['Criação de Despesa']
      ) {
        const processaSuperavit = await processaReprovacaoPedidoSuperavit(
          tx,
          alteracaoOrcamentariaData,
          resultPermissao.idUsuario || 0,
          AuditoriaAlteracaoOrcamentaria.DESATIVAR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!processaSuperavit.status) {
          throw new Error(processaSuperavit.error);
        }
      }

      const resultPromise = tx.alteracaoOrcamentaria.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });

      const auditPromise = tx.controleAlteracaoOrcamentaria_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idAlterOrca: id,
          acao: AuditoriaAlteracaoOrcamentaria.ATIVAR_ALTERACAO_ORCAMENTARIA,
          ip,
        },
      });

      const [altOrc, altOrcAudit] = await Promise.all([
        resultPromise,
        auditPromise,
      ]);

      if (!altOrc) {
        throw new Error('Erro atualizar alteração orçamentária');
      }

      if (!altOrcAudit) {
        throw new Error('Erro criar log alteração orçamentária');
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
    return {
      status: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao desativar Alteração Orçamentária.`,
    };
  }
};

export const listarSecretariasAtivas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const secretarias = await prisma.secretarias.findMany({
      where: { ativo: true },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: secretarias,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao obter secretarias.`,
    };
  }
};

export const listarDepartamentosAtivos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const departamentos = await prisma.departamentos.findMany({
      orderBy: [{ secretaria: { codigo: 'asc' } }, { codigo: 'asc' }],
      where: { ativo: true },
      include: {
        secretaria: {
          select: {
            codigo: true,
          },
        },
      },
    });

    return {
      data: departamentos,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao obter departamentos.`,
    };
  }
};

export const listarEconomicasAtivas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const economicas = await prisma.economicas.findMany({
      where: { ativo: true },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: economicas,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao obter economicas.`,
    };
  }
};

export const listarFuncionaisAtivas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const funcionais = await prisma.funcionais.findMany({
      where: { ativo: true },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: funcionais,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao obter funcionais.`,
    };
  }
};

export const aprovarAltOrc = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = aprovarAlteracaoOrcamentariaSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

  if (acessos.error) {
    return {
      error: acessos.error,
    };
  }

  if (!acessos.data) {
    return {
      error: 'Não foi possível obter acessos do usuário.',
    };
  }

  if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
    return {
      error: 'Usuário sem permissão para essa operação.',
    };
  }

  const { id, dataDecreto, numDecreto, tipoDecreto } = parsedParams.data;

  const alteracaoOrcamentariaData =
    await prisma.alteracaoOrcamentaria.findUnique({
      where: {
        id,
      },
    });

  if (!alteracaoOrcamentariaData) {
    return { error: 'Alteração Orçamentária não encontrada para aprovação' };
  }

  const {
    exercicio,
    tipoAlteracao,
    tipoAcao,
    // despesaAcao,
    despesaNova,
    descrDespNova,
    ativo,
    despesaCopia,
    fonte,
    idFuncional,
    idEconomica,
    idSecretaria,
    idDepto,
    codAplicacao,
    status,
  } = alteracaoOrcamentariaData;

  if (!ativo && status != StatusAlteracaoOrcamentaria['Em Andamento']) {
    return {
      error:
        'Alteração Orçamentária tem que estar ativa e com status "Em Andamento" ',
    };
  }

  if (!despesaNova) {
    return {
      error: 'Nova despesa ou despesa suplementada precisa estar preenchida',
    };
  }

  const idAltOrc = alteracaoOrcamentariaData?.id;
  const valorTotal = Number(alteracaoOrcamentariaData.valorTotal);
  const valorMes1 = Number(alteracaoOrcamentariaData.valorMes1);
  const valorMes2 = Number(alteracaoOrcamentariaData.valorMes2);
  const valorMes3 = Number(alteracaoOrcamentariaData.valorMes3);
  const valorMes4 = Number(alteracaoOrcamentariaData.valorMes4);
  const valorMes5 = Number(alteracaoOrcamentariaData.valorMes5);
  const valorMes6 = Number(alteracaoOrcamentariaData.valorMes6);
  const valorMes7 = Number(alteracaoOrcamentariaData.valorMes7);
  const valorMes8 = Number(alteracaoOrcamentariaData.valorMes8);
  const valorMes9 = Number(alteracaoOrcamentariaData.valorMes9);
  const valorMes10 = Number(alteracaoOrcamentariaData.valorMes10);
  const valorMes11 = Number(alteracaoOrcamentariaData.valorMes11);
  const valorMes12 = Number(alteracaoOrcamentariaData.valorMes12);

  try {
    // Processar a reserva
    // Transferir o saldo para a despesa
    // Criar dotação se necessário e gerar saldo
    // Mudar o status para aprovado e validar status
    // preencher os campos de anulação, suplementacao
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      if (
        /*tipoAlteracao == tiposAlteracao["Anulação"] &&*/ tipoAcao ==
          tiposAcao['Suplementação'] ||
        tipoAcao == tiposAcao['Debito']
      ) {
        // Obtém a dotação que foi anulado o valor
        const dotacao = await getDotacao(tx, exercicio, despesaNova);
        if (!dotacao) {
          throw new Error('Dotação a ser suplementada não encontrada');
        }

        // Atualiza dotação de destino da suplementação
        const dotacaoUpd = await tx.dotacoes.update({
          where: {
            id: dotacao?.id,
            ativo: true,
          },
          data: {
            suplementacao: toCurrency(dotacao.suplementacao).add(valorTotal)
              .value, // ver se é agora ou na aprovação
            valorAtual: toCurrency(dotacao.valorAtual).add(valorTotal).value, // não atualizou
            valorLiberado: toCurrency(dotacao.valorLiberado).add(valorTotal)
              .value, // analisar se preenche
            cotaMes1: toCurrency(dotacao.cotaMes1).add(valorMes1).value,
            cotaMes2: toCurrency(dotacao.cotaMes2).add(valorMes2).value,
            cotaMes3: toCurrency(dotacao.cotaMes3).add(valorMes3).value,
            cotaMes4: toCurrency(dotacao.cotaMes4).add(valorMes4).value,
            cotaMes5: toCurrency(dotacao.cotaMes5).add(valorMes5).value,
            cotaMes6: toCurrency(dotacao.cotaMes6).add(valorMes6).value,
            cotaMes7: toCurrency(dotacao.cotaMes7).add(valorMes7).value,
            cotaMes8: toCurrency(dotacao.cotaMes8).add(valorMes8).value,
            cotaMes9: toCurrency(dotacao.cotaMes9).add(valorMes9).value,
            cotaMes10: toCurrency(dotacao.cotaMes10).add(valorMes10).value,
            cotaMes11: toCurrency(dotacao.cotaMes11).add(valorMes11).value,
            cotaMes12: toCurrency(dotacao.cotaMes12).add(valorMes12).value,
          },
        });

        if (!dotacaoUpd) {
          throw new Error('Erro ao atualizar dotação a ser suplementada');
        }
      }

      if (
        tipoAlteracao != tiposAlteracao['Anulação'] &&
        (tipoAcao == tiposAcao['Suplementação'] ||
          tipoAcao == tiposAcao['Criação de Despesa'])
      ) {
        const aprovaSuperavit: any = await processaAprovacaoPedidoSuperavit(
          tx,
          alteracaoOrcamentariaData,
          resultPermissao.idUsuario!,
          AuditoriaAlteracaoOrcamentaria.APROVAR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!aprovaSuperavit.status) {
          throw new Error(aprovaSuperavit.error);
        }
      }

      if (tipoAcao == tiposAcao['Criação de Despesa']) {
        // Se for criação de despesas, necessário criar a dotação
        const dotacao = await getDotacao(tx, exercicio, despesaNova);
        if (dotacao) {
          throw new Error('Nova despesa já existe');
        }

        const dotacaoCopia: any = await getDotacao(tx, exercicio, despesaCopia);
        if (!dotacaoCopia) {
          throw new Error(
            'Não foi encontrado a dotação (despesa) para a despesa cópia'
          );
        }

        const resultDotacao = await tx.dotacoes.create({
          data: {
            exercicio,
            despesa: despesaNova,
            desc: descrDespNova,
            fonte,
            codAplicacao: codAplicacao,
            funcionalId: idFuncional!,
            economicaId: idEconomica!,
            secretariaId: idSecretaria,
            departamentoId: idDepto,
            subdepartamentoId: dotacaoCopia.subdepartamentoId || undefined,
            valorInicial: 0,
            cotaReducaoInicial: 0,
            suplementacao: valorTotal,
            valorLiberado: valorTotal,
            anulacao: 0,
            cotaReducao: 0,
            valorAtual: valorTotal,
            cotaMes1: valorMes1,
            cotaMes2: valorMes2,
            cotaMes3: valorMes3,
            cotaMes4: valorMes4,
            cotaMes5: valorMes5,
            cotaMes6: valorMes6,
            cotaMes7: valorMes7,
            cotaMes8: valorMes8,
            cotaMes9: valorMes9,
            cotaMes10: valorMes10,
            cotaMes11: valorMes11,
            cotaMes12: valorMes12,
          },
        });

        if (!resultDotacao) {
          throw new Error('Erro ao criar a nova despesa');
        }

        const dotacaoAudit = await tx.gerenciamentoDotacoes_audit.create({
          data: {
            idDotacao: resultDotacao.id,
            acao: AuditoriaAlteracaoOrcamentaria.APROVAR_ALTERACAO_ORCAMENTARIA,
            idUsuario: resultPermissao.idUsuario!,
            ip,
          },
        });

        if (!dotacaoAudit) {
          throw new Error('Erro ao audit da nova despesa');
        }
      }

      // Atualizar alteração orcamentaria
      const resultPromise = tx.alteracaoOrcamentaria.update({
        where: {
          id: id,
        },
        data: {
          dataDecreto,
          numDecreto,
          tipoDecreto,
          status: StatusAlteracaoOrcamentaria['Aprovado'],
          dataStatus: obterDataAtual(),
        },
      });

      const auditPromise = tx.controleAlteracaoOrcamentaria_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idAlterOrca: idAltOrc,
          acao: AuditoriaAlteracaoOrcamentaria.APROVAR_ALTERACAO_ORCAMENTARIA,
          ip,
        },
      });

      const [alteracaoOrcamentariaUpd, auditAlteracaoOrcamentariaUpd] =
        await Promise.all([resultPromise, auditPromise]);
      if (!alteracaoOrcamentariaUpd) {
        throw new Error('Erro ao aprovar a alteração orcamentária');
      }

      if (!auditAlteracaoOrcamentariaUpd) {
        throw new Error(
          'Erro ao inserir auditoria da aprovação da alteração orçamentária'
        );
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
    return {
      status: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao aprovar Alteração Orçamentária.`,
    };
  }
};

export const ReprovarAltOrc = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

  if (acessos.error) {
    return {
      error: acessos.error,
    };
  }

  if (!acessos.data) {
    return {
      error: 'Não foi possível obter acessos do usuário.',
    };
  }

  if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
    return {
      error: 'Usuário sem permissão para essa operação.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  const alteracaoOrcamentariaData =
    await prisma.alteracaoOrcamentaria.findUnique({
      where: {
        id,
      },
    });

  if (!alteracaoOrcamentariaData) {
    return { error: 'Alteração Orçamentária não encontrada para reprovação' };
  }

  if (
    alteracaoOrcamentariaData.status !=
    StatusAlteracaoOrcamentaria['Em Andamento']
  ) {
    return { error: 'Status deve estar "Em Andamento"' };
  }

  const {
    exercicio,
    tipoAlteracao,
    valorTotal,
    valorMes1,
    valorMes2,
    valorMes3,
    valorMes4,
    valorMes5,
    valorMes6,
    valorMes7,
    valorMes8,
    valorMes9,
    valorMes10,
    valorMes11,
    valorMes12,
    status,
    ativo,
    despesaAcao,
    despesaNova,
    tipoAcao,
  } = alteracaoOrcamentariaData;

  if (!ativo && status != StatusAlteracaoOrcamentaria['Em Andamento']) {
    return {
      error:
        'Alteração Orçamentária tem que estar ativa e com status "Em Andamento" ',
    };
  }

  if (!despesaNova) {
    return {
      error: 'Nova despesa ou despesa suplementada precisa estar preenchida',
    };
  }
  // Mudar status aprovação orçamentaria
  // atualizar dotação
  // atualizar superavit e detalhes de superavit

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      if (tipoAlteracao == tiposAlteracao['Anulação']) {
        const dotacao = await getDotacao(tx, exercicio, despesaAcao!);
        if (!dotacao) {
          throw new Error('Dotação anulada não encontrada');
        }

        // estorna valores da dotação
        const updatedotacoes = await tx.dotacoes.update({
          where: {
            id: dotacao?.id,
            ativo: true,
          },
          data: {
            anulacao: toCurrency(dotacao.anulacao).subtract(Number(valorTotal))
              .value,
            valorAtual: toCurrency(dotacao.valorAtual).add(Number(valorTotal))
              .value,
            cotaMes1: toCurrency(dotacao.cotaMes1).add(Number(valorMes1)).value,
            cotaMes2: toCurrency(dotacao.cotaMes2).add(Number(valorMes2)).value,
            cotaMes3: toCurrency(dotacao.cotaMes3).add(Number(valorMes3)).value,
            cotaMes4: toCurrency(dotacao.cotaMes4).add(Number(valorMes4)).value,
            cotaMes5: toCurrency(dotacao.cotaMes5).add(Number(valorMes5)).value,
            cotaMes6: toCurrency(dotacao.cotaMes6).add(Number(valorMes6)).value,
            cotaMes7: toCurrency(dotacao.cotaMes7).add(Number(valorMes7)).value,
            cotaMes8: toCurrency(dotacao.cotaMes8).add(Number(valorMes8)).value,
            cotaMes9: toCurrency(dotacao.cotaMes9).add(Number(valorMes9)).value,
            cotaMes10: toCurrency(dotacao.cotaMes10).add(Number(valorMes10))
              .value,
            cotaMes11: toCurrency(dotacao.cotaMes11).add(Number(valorMes11))
              .value,
            cotaMes12: toCurrency(dotacao.cotaMes12).add(Number(valorMes12))
              .value,
          },
        });

        if (!updatedotacoes) {
          throw new Error('Erro ao estornar valores na dotação de anulação');
        }
      } else if (
        tipoAlteracao != tiposAlteracao['Anulação'] &&
        (tipoAcao == tiposAcao['Suplementação'] ||
          tipoAcao == tiposAcao['Criação de Despesa'])
      ) {
        // (tx: any, alteracaoOrcamentariaData: any, idUsuario: number, tela: string, acao: number, ip: any)
        const processaSuperavit = await processaReprovacaoPedidoSuperavit(
          tx,
          alteracaoOrcamentariaData,
          resultPermissao.idUsuario || 0,
          AuditoriaAlteracaoOrcamentaria.REPROVAR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!processaSuperavit.status) {
          throw new Error(processaSuperavit.error);
        }

        const altOrcAudit = await tx.controleAlteracaoOrcamentaria_audit.create(
          {
            data: {
              idUsuario: resultPermissao.idUsuario!,
              idAlterOrca: alteracaoOrcamentariaData.id,
              acao: AuditoriaAlteracaoOrcamentaria.REPROVAR_ALTERACAO_ORCAMENTARIA,
              ip,
            },
          }
        );

        if (!altOrcAudit) {
          throw new Error('Erro criar log alteração orçamentária');
        }
      }

      const updAlteracaoOrcamentaria = await tx.alteracaoOrcamentaria.update({
        data: {
          status: StatusAlteracaoOrcamentaria['Reprovado'],
          dataStatus: obterDataAtual(),
        },
        where: {
          id,
          ativo: true,
        },
      });

      if (!updAlteracaoOrcamentaria) {
        throw new Error(
          'Erro criar log de reprovação de alteração orçamentária'
        );
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao reprovar Alteração Orçamentária.`,
    };
  }
};

export const andamentoAltOrc = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

  if (acessos.error) {
    return {
      error: acessos.error,
    };
  }

  if (!acessos.data) {
    return {
      error: 'Não foi possível obter acessos do usuário.',
    };
  }

  if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
    return {
      error: 'Usuário sem permissão para essa operação.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  const alteracaoOrcamentariaData =
    await prisma.alteracaoOrcamentaria.findUnique({
      where: {
        id,
        ativo: true,
      },
    });

  if (!alteracaoOrcamentariaData) {
    return { error: 'Alteração orçamentária ativa não encontrada' };
  }

  if (
    alteracaoOrcamentariaData.status != StatusAlteracaoOrcamentaria['Em Aberto']
  ) {
    return { error: 'Status tem que estar "Em aberto"' };
  }

  try {
    await prisma.$transaction(async (tx) => {
      const alteracaoOrcamentariaData = await tx.alteracaoOrcamentaria.update({
        data: {
          status: StatusAlteracaoOrcamentaria['Em Andamento'],
        },
        where: {
          id,
          ativo: true,
        },
      });

      if (!alteracaoOrcamentariaData) {
        throw new Error('Alteração orçamentária ativa não atualizada');
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
  } catch (e: any) {
    console.log(e.message);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Código da Alteração Orçamentária já existe.`,
        };
      }
    }
    return {
      error:
        e.message ||
        `Não foi possível atualizar o status da alteração orçamentária`,
    };
  }
};

export const ReabrirAltOrc = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  const alteracaoOrcamentariaData =
    await prisma.alteracaoOrcamentaria.findUnique({
      where: {
        id,
      },
    });

  if (!alteracaoOrcamentariaData) {
    return { error: 'Alteração Orçamentária não encontrada para reprovação' };
  }

  if (
    !alteracaoOrcamentariaData.ativo &&
    Number(alteracaoOrcamentariaData.status) ==
      StatusAlteracaoOrcamentaria['Reprovado']
  ) {
    return {
      error:
        'Alteração Orçamentária precisa estar ativa e com status "Reprovado" ',
    };
  }

  const { exercicio, tipoAlteracao, despesaCopia, despesaAcao, tipoAcao } =
    alteracaoOrcamentariaData;

  const valorTotal = Number(alteracaoOrcamentariaData.valorTotal);
  const valorMes1 = Number(alteracaoOrcamentariaData.valorMes1);
  const valorMes2 = Number(alteracaoOrcamentariaData.valorMes2);
  const valorMes3 = Number(alteracaoOrcamentariaData.valorMes3);
  const valorMes4 = Number(alteracaoOrcamentariaData.valorMes4);
  const valorMes5 = Number(alteracaoOrcamentariaData.valorMes5);
  const valorMes6 = Number(alteracaoOrcamentariaData.valorMes6);
  const valorMes7 = Number(alteracaoOrcamentariaData.valorMes7);
  const valorMes8 = Number(alteracaoOrcamentariaData.valorMes8);
  const valorMes9 = Number(alteracaoOrcamentariaData.valorMes9);
  const valorMes10 = Number(alteracaoOrcamentariaData.valorMes10);
  const valorMes11 = Number(alteracaoOrcamentariaData.valorMes11);
  const valorMes12 = Number(alteracaoOrcamentariaData.valorMes12);

  const data = {
    ...alteracaoOrcamentariaData,
    valorTotal,
    valorMes1,
    valorMes2,
    valorMes3,
    valorMes4,
    valorMes5,
    valorMes6,
    valorMes7,
    valorMes8,
    valorMes9,
    valorMes10,
    valorMes11,
    valorMes12,
  };

  const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

  if (acessos.error) {
    return {
      error: acessos.error,
    };
  }

  if (!acessos.data) {
    return {
      error: 'Não foi possível obter acessos do usuário.',
    };
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      if (tipoAlteracao == tiposAlteracao['Anulação']) {
        const dotacao = await getDotacao(tx, exercicio, despesaAcao!);
        if (!dotacao) {
          throw new Error('Dotação a ser anulada não encontrada');
        }

        const validacao = await vldDados(data, dotacao, null, true, acessos);
        if (!validacao.status) {
          throw new Error(validacao.error);
        }

        // Reserva valores da dotação
        const updatedotacoes = await tx.dotacoes.update({
          where: {
            id: dotacao?.id,
            ativo: true,
          },
          data: {
            anulacao: toCurrency(dotacao.anulacao).add(valorTotal).value,
            valorAtual: toCurrency(dotacao.valorAtual).subtract(valorTotal)
              .value,
            cotaMes1: toCurrency(dotacao.cotaMes1).subtract(valorMes1).value,
            cotaMes2: toCurrency(dotacao.cotaMes2).subtract(valorMes2).value,
            cotaMes3: toCurrency(dotacao.cotaMes3).subtract(valorMes3).value,
            cotaMes4: toCurrency(dotacao.cotaMes4).subtract(valorMes4).value,
            cotaMes5: toCurrency(dotacao.cotaMes5).subtract(valorMes5).value,
            cotaMes6: toCurrency(dotacao.cotaMes6).subtract(valorMes6).value,
            cotaMes7: toCurrency(dotacao.cotaMes7).subtract(valorMes7).value,
            cotaMes8: toCurrency(dotacao.cotaMes8).subtract(valorMes8).value,
            cotaMes9: toCurrency(dotacao.cotaMes9).subtract(valorMes9).value,
            cotaMes10: toCurrency(dotacao.cotaMes10).subtract(valorMes10).value,
            cotaMes11: toCurrency(dotacao.cotaMes11).subtract(valorMes11).value,
            cotaMes12: toCurrency(dotacao.cotaMes12).subtract(valorMes12).value,
          },
        });

        if (!updatedotacoes) {
          throw new Error('Erro ao reservar valores na dotação de anulação');
        }
      } else if (tipoAcao == tiposAcao['Suplementação']) {
        // implementar fonte Superavit começa com 9
        const vldValores = await vldDados(data, null, null, true, acessos);
        if (!vldValores.status) {
          throw new Error(vldValores.error);
        }
      }

      if (tipoAcao == tiposAcao['Criação de Despesa']) {
        if (!despesaCopia) {
          throw new Error('Despesa Copia não preenchida');
        }

        const dotacao = await getDotacao(tx, exercicio, despesaCopia);
        if (!dotacao) {
          throw new Error('Dotação não encontrada');
        }

        const vldValores = await vldDados(data, dotacao, null, true, acessos);
        if (!vldValores.status) {
          throw new Error(vldValores.error);
        }
      }

      if (
        tipoAlteracao != tiposAlteracao['Anulação'] &&
        (tipoAcao == tiposAcao['Suplementação'] ||
          tipoAcao == tiposAcao['Criação de Despesa'])
      ) {
        // (tx: any, alteracaoOrcamentariaData: any, idUsuario: number, tela: string, acao: number, ip: any)

        const processaSuperavit = await processaReabrirPedidoSuperavit(
          tx,
          alteracaoOrcamentariaData,
          resultPermissao.idUsuario || 0,
          AuditoriaAlteracaoOrcamentaria.REABRIR_ALTERACAO_ORCAMENTARIA,
          ip
        );
        if (!processaSuperavit.status) {
          throw new Error(processaSuperavit.error);
        }
      }

      const alteracaoOrcamentariaUpd = await tx.alteracaoOrcamentaria.update({
        where: {
          id: id,
        },
        data: {
          status: StatusAlteracaoOrcamentaria['Em Aberto'],
          dataStatus: obterDataAtual(),
        },
      });

      const altOrcAudit = await tx.controleAlteracaoOrcamentaria_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idAlterOrca: alteracaoOrcamentariaUpd.id,
          acao: AuditoriaAlteracaoOrcamentaria.CRIAR_ALTERACAO_ORCAMENTARIA,
          ip,
        },
      });

      if (!altOrcAudit) {
        throw new Error('Erro criar log alteração orçamentária');
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
  } catch (e: any) {
    console.log(e.message);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Código da Alteração Orçamentária já existe.`,
        };
      }
    }
    return {
      error: e.message || `Erro ao criar Alteração Orçamentária.`,
    };
  }
};

const processaPedidoSuperavit = async (
  tx: any,
  alteracaoOrcamentariaData: any,
  idUsuario: number,
  tela: string,
  acao: number,
  ip: any
) => {
  try {
    if (
      alteracaoOrcamentariaData.tipoAlteracao == tiposAlteracao['Superavit'] ||
      alteracaoOrcamentariaData.tipoAlteracao ==
        tiposAlteracao['Excesso de Arrecadação']
    ) {
      // && (alteracaoOrcamentariaData.tipoAcao == tiposAcao['Criação de Despesa']|| alteracaoOrcamentariaData.tipoAcao == tiposAcao['Suplementação'])) {

      const buscaSuperavitExcesso = await tx.controleSuperavits.findFirst({
        where: {
          exercicio: alteracaoOrcamentariaData.exercicio,
          fonte: alteracaoOrcamentariaData.fonte,
          codAplicacao: alteracaoOrcamentariaData.codAplicacao,
          ativo: true,
        },
      });

      if (!buscaSuperavitExcesso) {
        return {
          status: false,
          error:
            'Não foi encontrado superavit ou excesso de arrecadação cadastrado',
        };
      }

      if (
        toCurrency(buscaSuperavitExcesso.valorReceita).subtract(
          Number(alteracaoOrcamentariaData.valorTotal)
        ).value < 0
      ) {
        return {
          status: false,
          error:
            'Valor da alteração orçamentária maior que o valor do Superavit',
        };
      }

      const superavitPromise = tx.controleSuperavits.update({
        where: {
          id: buscaSuperavitExcesso?.id,
          ativo: true,
        },
        data: {
          valorReceita: toCurrency(
            buscaSuperavitExcesso.valorReceita || 0
          ).subtract(Number(alteracaoOrcamentariaData.valorTotal)).value,
        },
      });

      const superavitDetalhesPromise = tx.controleSuperavitsDetalhes.create({
        data: {
          exercicio: alteracaoOrcamentariaData.exercicio,
          despesa: alteracaoOrcamentariaData.despesaNova || 0,
          fonte: alteracaoOrcamentariaData.fonte,
          codAplicacao: alteracaoOrcamentariaData.codAplicacao,
          valor: alteracaoOrcamentariaData.valorTotal,
          valorReservado: alteracaoOrcamentariaData.valorTotal,
          valorSuplementado: 0,
          tela,
          idAltOrcament: alteracaoOrcamentariaData.idAltOrcament,
          idSuperavit: buscaSuperavitExcesso.id,
          tipoAcao: alteracaoOrcamentariaData.tipoAcao,
          ativo: true,
          status: StatusSuperavitDetalhes['Reservado'],
          data: obterDataAtual(),
        },
      });

      const [superavit, superavitDetalhes] = await Promise.all([
        superavitPromise,
        superavitDetalhesPromise,
      ]);

      if (!superavit) {
        return { status: false, error: 'Erro ao debitar o valor do superavit' };
      }

      if (!superavitDetalhes) {
        return {
          status: false,
          error: 'Erro ao incluir o registro de detalhamento do superavit',
        };
      }

      const auditSuperavitPromise = tx.controleSuperavits_audit.create({
        data: {
          idUsuario: idUsuario,
          idSuperavit: superavit?.id || 0,
          acao,
          ip,
        },
      });

      const auditSuperavitDetalhesPromise =
        tx.controleSuperavitsDetalhes_audit.create({
          data: {
            idUsuario,
            idSuperDeta: superavitDetalhes.id,
            acao,
            ip,
          },
        });

      const [auditSuperavit, auditDetalhesSuperavit] = await Promise.all([
        auditSuperavitPromise,
        auditSuperavitDetalhesPromise,
      ]);
      if (!auditSuperavit) {
        return {
          status: false,
          error: 'Erro ao incluir auditoria do superavit',
        };
      }

      if (!auditDetalhesSuperavit) {
        return {
          status: false,
          error: 'Erro ao incluir auditoria do detalhamento do superavit',
        };
      }
    }

    return {
      status: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      status: false,
      error: e.message || `Erro ao Processar Superavit.`,
    };
  }
};

const processaEditPedidoSuperavit = async (
  tx: any,
  alteracaoOrcamentariaData: any,
  idUsuario: number,
  tela: string,
  acao: number,
  ip: any
) => {
  try {
    if (
      alteracaoOrcamentariaData.tipoAlteracao == tiposAlteracao['Superavit'] ||
      alteracaoOrcamentariaData.tipoAlteracao ==
        tiposAlteracao['Excesso de Arrecadação']
    ) {
      const superavitDetalhesData =
        await tx.controleSuperavitsDetalhes.findFirst({
          where: {
            idAltOrcament: alteracaoOrcamentariaData.id,
            ativo: true,
          },
        });

      if (!superavitDetalhesData) {
        throw new Error(
          'Não foi localizado o registro do detalhamento de superavit'
        );
      }

      const buscaSuperavitExcesso = await tx.controleSuperavits.findUnique({
        where: {
          id: superavitDetalhesData.idSuperavit,
          ativo: true,
        },
      });

      if (!buscaSuperavitExcesso) {
        throw new Error(
          'Não foi encontrado superavit ou excesso de arrecadação cadastrado'
        );
      }

      if (
        toCurrency(buscaSuperavitExcesso.valorReceita)
          .add(Number(superavitDetalhesData.valor))
          .subtract(Number(alteracaoOrcamentariaData.valorTotal)).value < 0
      ) {
        return {
          status: false,
          error:
            'Valor da alteração orçamentária maior que o valor do Superavit',
        };
      }

      const superavitPromise = tx.controleSuperavits.update({
        where: {
          id: buscaSuperavitExcesso.id,
          ativo: true,
        },
        data: {
          valorReceita: toCurrency(buscaSuperavitExcesso.valorReceita)
            .add(Number(superavitDetalhesData.valor))
            .subtract(Number(alteracaoOrcamentariaData.valorTotal)).value,
        },
      });

      const superavitDetalhesPromise = tx.controleSuperavitsDetalhes.update({
        where: {
          id: superavitDetalhesData.id,
          ativo: true,
        },
        data: {
          valor: Number(alteracaoOrcamentariaData.valorTotal),
          valorReservado: Number(alteracaoOrcamentariaData.valorTotal),
          valorSuplementado: 0,
          tela,
          status: StatusSuperavitDetalhes['Reservado'],
          ativo: true,
        },
      });

      const [superavit, superavitDetalhes] = await Promise.all([
        superavitPromise,
        superavitDetalhesPromise,
      ]);

      if (!superavit) {
        return {
          status: false,
          error: 'Erro ao atualizar o valor do superavit',
        };
      }

      if (!superavitDetalhes) {
        return {
          status: false,
          error: 'Erro ao atualizar o registro de detalhamento do superavit',
        };
      }

      const auditSuperavitPromise = tx.controleSuperavits_audit.create({
        data: {
          idUsuario: idUsuario,
          idSuperavit: superavit?.id || 0,
          acao,
          de: buscaSuperavitExcesso.valorReceita.toString(),
          para: superavit.valorReceita.toString(),
          ip,
        },
      });

      const auditSuperavitDetalhesPromise =
        tx.controleSuperavitsDetalhes_audit.create({
          data: {
            idUsuario,
            idSuperDeta: superavitDetalhes.id,
            acao,
            de: superavitDetalhesData.valorReservado.toString(),
            para: superavitDetalhes.valorReservado.toString(),
            ip,
          },
        });

      const [auditSuperavit, auditDetalhesSuperavit] = await Promise.all([
        auditSuperavitPromise,
        auditSuperavitDetalhesPromise,
      ]);

      if (!auditSuperavit) {
        return {
          status: false,
          error: 'Erro ao incluir auditoria do superavit',
        };
      }

      if (!auditDetalhesSuperavit) {
        return {
          status: false,
          error: 'Erro ao incluir auditoria do detalhamento do superavit',
        };
      }
    }

    return {
      status: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      status: false,
      error: e.message || `Erro ao Atualizar o registro de Superavit.`,
    };
  }
};

const processaAprovacaoPedidoSuperavit = async (
  tx: any,
  alteracaoOrcamentariaData: any,
  idUsuario: number,
  acao: number,
  ip: any
) => {
  try {
    if (
      alteracaoOrcamentariaData.tipoAlteracao == tiposAlteracao['Superavit'] ||
      alteracaoOrcamentariaData.tipoAlteracao ==
        tiposAlteracao['Excesso de Arrecadação']
    ) {
      const superavitDetalhesData =
        await tx.controleSuperavitsDetalhes.findFirst({
          where: {
            idAltOrcament: alteracaoOrcamentariaData.id,
            ativo: true,
          },
        });

      if (!superavitDetalhesData) {
        throw new Error(
          'Não foi localizado o registro do detalhamento de superavit'
        );
      }

      const buscaSuperavitExcesso = await tx.controleSuperavits.findUnique({
        where: {
          id: superavitDetalhesData.idSuperavit,
          ativo: true,
        },
      });

      if (!buscaSuperavitExcesso) {
        throw new Error(
          'Não foi encontrado superavit ou excesso de arrecadação cadastrado'
        );
      }

      const superavitDetalhes = await tx.controleSuperavitsDetalhes.update({
        where: {
          id: superavitDetalhesData.id,
          ativo: true,
        },
        data: {
          valor: Number(alteracaoOrcamentariaData.valorTotal),
          valorReservado: toCurrency(
            superavitDetalhesData.valorReservado
          ).subtract(Number(alteracaoOrcamentariaData.valorTotal)).value,
          valorSuplementado: Number(alteracaoOrcamentariaData.valorTotal),
          status: StatusSuperavitDetalhes['Aprovado'],
          ativo: true,
          despesa: alteracaoOrcamentariaData.despesaNova,
        },
      });

      if (!superavitDetalhes) {
        return {
          status: false,
          error: 'Erro ao atualizar o registro de detalhamento do superavit',
        };
      }

      const auditSuperavitDetalhes =
        await tx.controleSuperavitsDetalhes_audit.create({
          data: {
            idUsuario,
            idSuperDeta: superavitDetalhes.id,
            acao,
            de: superavitDetalhesData.valorReservado.toString(),
            para: superavitDetalhes.valorReservado.toString(),
            ip,
          },
        });

      if (!auditSuperavitDetalhes) {
        return {
          status: false,
          error: 'Erro ao incluir auditoria do detalhamento do superavit',
        };
      }
    }

    return {
      status: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      status: false,
      error: e.message || `Erro ao Processar Aprovaçao do Superavit.`,
    };
  }
};

const processaReprovacaoPedidoSuperavit = async (
  tx: any,
  alteracaoOrcamentariaData: any,
  idUsuario: number,
  acao: number,
  ip: any
) => {
  try {
    if (
      alteracaoOrcamentariaData.tipoAlteracao == tiposAlteracao['Superavit'] ||
      alteracaoOrcamentariaData.tipoAlteracao ==
        tiposAlteracao['Excesso de Arrecadação']
    ) {
      const superavitDetalhesData =
        await tx.controleSuperavitsDetalhes.findFirst({
          where: {
            exercicio: alteracaoOrcamentariaData.exercicio,
            idAltOrcament: alteracaoOrcamentariaData.id,
            ativo: true,
          },
        });

      if (!superavitDetalhesData) {
        throw new Error(
          'Não foi localizado o registro do detalhamento de superavit'
        );
      }

      const buscaSuperavitExcesso = await tx.controleSuperavits.findUnique({
        where: {
          id: superavitDetalhesData.idSuperavit,
          ativo: true,
        },
      });

      if (!buscaSuperavitExcesso) {
        throw new Error(
          'Não foi encontrado superavit ou excesso de arrecadação cadastrado'
        );
      }

      const superavitPromise = tx.controleSuperavits.update({
        where: {
          id: buscaSuperavitExcesso.id,
          ativo: true,
        },
        data: {
          valorReceita: toCurrency(buscaSuperavitExcesso.valorReceita).add(
            Number(alteracaoOrcamentariaData.valorTotal)
          ).value,
        },
      });

      const superavitDetalhesPromise = tx.controleSuperavitsDetalhes.update({
        where: {
          id: superavitDetalhesData.id,
          ativo: true,
        },
        data: {
          valor: 0,
          valorReservado: 0,
          valorSuplementado: 0,
          status: StatusSuperavitDetalhes['Reprovado'],
          ativo: true,
        },
      });

      const [superavit, superavitDetalhes] = await Promise.all([
        superavitPromise,
        superavitDetalhesPromise,
      ]);

      if (!superavit) {
        return {
          status: false,
          error: 'Erro ao estornar o valor do superavit',
        };
      }

      if (!superavitDetalhes) {
        return {
          status: false,
          error: 'Erro ao reprovar o registro de detalhamento do superavit',
        };
      }

      const auditSuperavitPromise = tx.controleSuperavits_audit.create({
        data: {
          idUsuario: idUsuario,
          idSuperavit: superavit?.id || 0,
          acao,
          de: buscaSuperavitExcesso.valorReceita.toString(),
          para: superavit.valorReceita.toString(),
          ip,
        },
      });

      const auditSuperavitDetalhesPromise =
        await tx.controleSuperavitsDetalhes_audit.create({
          data: {
            idUsuario,
            idSuperDeta: superavitDetalhes.id,
            acao,
            de: superavitDetalhesData.valorReservado.toString(),
            para: superavitDetalhes.valorReservado.toString(),
            ip,
          },
        });

      const [auditSuperavit, auditDetalhesSuperavit] = await Promise.all([
        auditSuperavitPromise,
        auditSuperavitDetalhesPromise,
      ]);

      if (!auditSuperavit) {
        return {
          status: false,
          error: 'Erro ao incluir auditoria de reprovacao do superavit',
        };
      }

      if (!auditDetalhesSuperavit) {
        return {
          status: false,
          error:
            'Erro ao incluir auditoria de reprovação do detalhamento do superavit',
        };
      }
    }

    return {
      status: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      status: false,
      error: e.message || `Erro ao Processar Aprovaçao do Superavit.`,
    };
  }
};

const processaReabrirPedidoSuperavit = async (
  tx: any,
  alteracaoOrcamentariaData: any,
  idUsuario: number,
  acao: number,
  ip: any
) => {
  try {
    if (
      alteracaoOrcamentariaData.tipoAlteracao == tiposAlteracao['Superavit'] ||
      alteracaoOrcamentariaData.tipoAlteracao ==
        tiposAlteracao['Excesso de Arrecadação']
    ) {
      const superavitDetalhesData =
        await tx.controleSuperavitsDetalhes.findFirst({
          where: {
            idAltOrcament: alteracaoOrcamentariaData.id,
            ativo: true,
          },
        });

      if (!superavitDetalhesData) {
        throw new Error(
          'Não foi localizado o registro do detalhamento de superavit'
        );
      }

      const buscaSuperavitExcesso = await tx.controleSuperavits.findUnique({
        where: {
          id: superavitDetalhesData.idSuperavit,
          ativo: true,
        },
      });

      if (!buscaSuperavitExcesso) {
        throw new Error(
          'Não foi encontrado superavit ou excesso de arrecadação cadastrado'
        );
      }

      if (
        toCurrency(buscaSuperavitExcesso?.valorReceita || 0).subtract(
          Number(alteracaoOrcamentariaData.valorTotal)
        ).value < 0
      ) {
        throw new Error(
          'Valor da alteração orçamentária maior que o valor do Superavit'
        );
      }

      const superavitPromise = tx.controleSuperavits.update({
        where: {
          id: buscaSuperavitExcesso?.id,
          ativo: true,
        },
        data: {
          valorReceita: toCurrency(
            buscaSuperavitExcesso?.valorReceita || 0
          ).subtract(Number(alteracaoOrcamentariaData.valorTotal)).value,
        },
      });

      const superavitDetalhesPromise = tx.controleSuperavitsDetalhes.update({
        where: {
          id: superavitDetalhesData.id,
          ativo: true,
        },
        data: {
          valor: Number(alteracaoOrcamentariaData.valorTotal),
          valorReservado: Number(alteracaoOrcamentariaData.valorTotal),
          valorSuplementado: 0,
          status: StatusSuperavitDetalhes['Reservado'],
          ativo: true,
        },
      });

      const [superavit, superavitDetalhes] = await Promise.all([
        superavitPromise,
        superavitDetalhesPromise,
      ]);

      if (!superavit) {
        return {
          status: false,
          error: 'Erro ao estornar o valor do superavit',
        };
      }

      if (!superavitDetalhes) {
        return {
          status: false,
          error: 'Erro ao reprovar o registro de detalhamento do superavit',
        };
      }

      const auditSuperavitPromise = tx.controleSuperavits_audit.create({
        data: {
          idUsuario: idUsuario,
          idSuperavit: superavit?.id || 0,
          acao,
          de: buscaSuperavitExcesso.valorReceita.toString(),
          para: superavit.valorReceita.toString(),
          ip,
        },
      });

      const auditSuperavitDetalhesPromise =
        await tx.controleSuperavitsDetalhes_audit.create({
          data: {
            idUsuario,
            idSuperDeta: superavitDetalhes.id,
            acao,
            de: superavitDetalhesData.valorReservado.toString(),
            para: superavitDetalhes.valorReservado.toString(),
            ip,
          },
        });

      const [auditSuperavit, auditDetalhesSuperavit] = await Promise.all([
        auditSuperavitPromise,
        auditSuperavitDetalhesPromise,
      ]);

      if (!auditSuperavit) {
        return {
          status: false,
          error: 'Erro ao incluir auditoria de reprovacao do superavit',
        };
      }

      if (!auditDetalhesSuperavit) {
        return {
          status: false,
          error:
            'Erro ao incluir auditoria de reprovação do detalhamento do superavit',
        };
      }
    }

    return {
      status: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      status: false,
      error: e.message || `Erro ao Processar Aprovaçao do Superavit.`,
    };
  }
};

export const getDotacao = async (
  tx: any,
  exercicio: number,
  despesa: number
) => {
  return await tx.dotacoes.findFirst({
    include: {
      departamento: true,
    },
    where: {
      exercicio,
      despesa,
      ativo: true,
    },
  });
};

export const buscarDespesa = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = idExercicioSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Despesa inválida.',
    };
  }

  const { id: despesa, exercicio } = parsedParams.data;

  try {
    const acessosPromise = listarIdsDotacoesUsuarioConectadoTemAcesso();
    const dotacaoPromise = prisma.dotacoes.findFirst({
      select: {
        id: true,
        exercicio: true,
        despesa: true,
        desc: true,
        fonte: true,
        codAplicacao: true,
        economica: true,
        funcional: true,
        secretaria: true,
        departamento: {
          include: {
            secretaria: true,
          },
        },
        subdepartamento: {
          include: {
            departamento: {
              include: {
                secretaria: true,
              },
            },
          },
        },
        ativo: true,
      },
      where: {
        exercicio, // resultPermissao.exercicio,
        despesa: despesa,
      },
    });

    const [acessos, dotacao] = await Promise.all([
      acessosPromise,
      dotacaoPromise,
    ]);

    if (acessos.error) {
      return {
        error: acessos.error,
      };
    }

    if (!acessos.data) {
      return {
        error: 'Não foi possível obter acessos do usuário.',
      };
    }

    if (!dotacao) {
      return {
        error: 'Despesa não encontrada.',
      };
    }

    if (!dotacao.ativo) {
      return {
        error: 'Despesa desativada.',
      };
    }

    const subdepartamento = dotacao?.subdepartamento || null;
    const departamento = subdepartamento
      ? dotacao?.subdepartamento?.departamento || null
      : dotacao?.departamento || null;
    const secretaria = subdepartamento
      ? dotacao?.subdepartamento?.departamento?.secretaria || null
      : departamento
        ? dotacao?.departamento?.secretaria || null
        : dotacao?.secretaria || null;

    if (!secretaria) {
      return {
        error: 'Falha ao determinar secretaria da despesa.',
      };
    }

    if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
      if (
        !acessos.data.dotacoes ||
        !acessos.data.dotacoes.includes(dotacao.id)
      ) {
        return {
          error: 'Usuário sem permissão para essa despesa.',
        };
      }
    }

    await redistribuirCotas({
      dotacaoId: dotacao.id,
      exercicio: dotacao.exercicio,
    });

    const cotasPromise = prisma.dotacoes.findFirst({
      select: {
        cotaMes1: true,
        cotaMes2: true,
        cotaMes3: true,
        cotaMes4: true,
        cotaMes5: true,
        cotaMes6: true,
        cotaMes7: true,
        cotaMes8: true,
        cotaMes9: true,
        cotaMes10: true,
        cotaMes11: true,
        cotaMes12: true,
        valorAtual: true,
      },
      where: {
        id: dotacao.id,
      },
    });
    // categoria, grupo, modalidade, elemento, subelemento
    const economicasItensPromise = prisma.economicas.findMany({
      where: {
        ativo: true,
        categoria: dotacao.economica.categoria,
        grupo: dotacao.economica.grupo,
        modalidade: dotacao.economica.modalidade,
        // elemento: dotacao.economica.elemento,
        subelemento: dotacao.economica.subelemento,
      },
      orderBy: { subelemento: 'asc' },
    });

    const cargosSecretariosAssinantesPromise = prisma.cargos.findMany({
      where: {
        tipoAssinatura: TiposAssinatura.SECRETARIO,
        OR: [
          {
            AND: [
              {
                cargos_acessos: {
                  every: {
                    secretariaId: secretaria.id,
                  },
                },
              },
              {
                tipoAcesso: TiposAcesso.SECRETARIAS,
              },
            ],
          },
          {
            tipoAcesso: TiposAcesso.TODAS_SECRETARIAS,
          },
        ],
      },
      select: {
        user_profiles_cargos: {
          select: {
            usuario: {
              select: {
                id: true,
                nome: true,
              },
            },
          },
        },
      },
    });

    const [cotas, economicasItens, cargosSecretariosAssinantes] =
      await Promise.all([
        cotasPromise,
        economicasItensPromise,
        cargosSecretariosAssinantesPromise,
      ]);

    if (!cotas) {
      return {
        error: 'Falha ao buscar cotas, tente novamente.',
      };
    }

    if (!economicasItens) {
      return {
        error: 'Nenhuma econômica encontrada.',
      };
    }

    if (!cargosSecretariosAssinantes) {
      return {
        error: 'Nenhum secretário configurado para assinar.',
      };
    }

    const secretariosAssinantes = cargosSecretariosAssinantes
      .map((cargo) => cargo.user_profiles_cargos.map((cargo) => cargo.usuario))
      .flat();

    return {
      data: {
        ...dotacao,
        cotaMes1: cotas.cotaMes1.toNumber(),
        cotaMes2: cotas.cotaMes2.toNumber(),
        cotaMes3: cotas.cotaMes3.toNumber(),
        cotaMes4: cotas.cotaMes4.toNumber(),
        cotaMes5: cotas.cotaMes5.toNumber(),
        cotaMes6: cotas.cotaMes6.toNumber(),
        cotaMes7: cotas.cotaMes7.toNumber(),
        cotaMes8: cotas.cotaMes8.toNumber(),
        cotaMes9: cotas.cotaMes9.toNumber(),
        cotaMes10: cotas.cotaMes10.toNumber(),
        cotaMes11: cotas.cotaMes11.toNumber(),
        cotaMes12: cotas.cotaMes12.toNumber(),
        valorAtual: cotas.valorAtual.toNumber(),
        economicasItens,
        secretariosAssinantes,
      },
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao buscar despesa.`,
    };
  }
};

export const obterSecretariaDepartamento = async () => {
  const acessos: any = await listarAcessosOrgaosUsuarioConectado();
  let acessosSecretarias = null;
  let acessosDepartamentos = null;

  try {
    if (acessos && acessos.data && acessos.data.acessoTotal) {
      acessosSecretarias = await listarSecretariasAtivas();
      acessosDepartamentos = await listarDepartamentosAtivos();
    } else if (acessos && acessos.data && !acessos.data.acessoTotal) {
      acessosSecretarias = acessos.data.acessosSecretarias.data;
      acessosDepartamentos = acessos.data.acessosDepartamentos.data;
    } else {
      return { error: acessos.error };
    }

    return {
      data: {
        secretarias: acessosSecretarias,
        departamentos: acessosDepartamentos,
        acessoTotal: acessos.data.acessoTotal,
      },
    };
  } catch (error) {
    return {
      error: `Erro ao buscar secretarias e departamentos.`,
    };
  }
};

export const insereNovaDespesa = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = insereNovaDespesaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id, despesaNova, descrDespNova } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const updAlteracaoOrcamentaria = await tx.alteracaoOrcamentaria.update({
        data: {
          despesaNova,
          descrDespNova,
          status: StatusAlteracaoOrcamentaria['Em Andamento'],
          dataStatus: obterDataAtual(),
        },
        where: {
          id,
          ativo: true,
        },
      });

      if (!updAlteracaoOrcamentaria) {
        throw new Error(
          'Não foi possível atualizar a despesa na Alteração Orçamentária'
        );
      }

      const dotacaoPromise = await tx.dotacoes.findFirst({
        where: {
          exercicio: updAlteracaoOrcamentaria.exercicio,
          despesa: updAlteracaoOrcamentaria.despesaNova,
          ativo: true,
        },
      });

      const auditAlteracaoOrcamentariaPromise =
        tx.controleAlteracaoOrcamentaria_audit.create({
          data: {
            idUsuario: resultPermissao.idUsuario!,
            idAlterOrca: id,
            acao: AuditoriaAlteracaoOrcamentaria.ALTERAR_ALTERACAO_ORCAMENTARIA,
            ip,
            de: '',
            para: despesaNova.toString(),
          },
        });

      const [dotacao, alteracaoOrcamentaria] = await Promise.all([
        dotacaoPromise,
        auditAlteracaoOrcamentariaPromise,
      ]);

      if (dotacao) {
        throw new Error('Dotação já existe');
      }

      if (!alteracaoOrcamentaria) {
        throw new Error(
          'Não foi possível incluir a auditoria da alteração orçamentária'
        );
      }
    });

    revalidatePath('/movimento/alteracaoOrcamentaria');
  } catch (e: any) {
    console.log(e.message);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Código despesa (Dotação) já existe.`,
        };
      }
    }
    return {
      error: e.message || `Erro ao inserir Nova Despesa (Dotação)`,
    };
  }
};

// Modelo a ser seguido
export const obterAlteracaoParaRelatorio = async (params: unknown) => {
  const parsedParams = imprimirAlteracaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { id, bearer, incluirGestor } = parsedParams.data;
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
    bearer,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Exercício não encontrado.',
    };
  }

  try {
    const alteracaoOrcamentariaPromise =
      prisma.alteracaoOrcamentaria.findUnique({
        where: { id },
        include: {
          secretaria: true,
          departamento: true,
          economica: true,
          funcional: true,
          secretario: true,
        },
      });
    const configsExercicioPromise = prisma.configuracoesExercicio.findUnique({
      where: {
        exercicio: resultPermissao.exercicio!,
      },
    });

    const [alteracaoOrcamentaria, configsExercicio] = await Promise.all([
      alteracaoOrcamentariaPromise,
      configsExercicioPromise,
    ]);

    if (!alteracaoOrcamentaria) {
      return {
        error: 'Alteraçao Orçamentária não encontrada.',
      };
    }

    if (!configsExercicio) {
      return {
        error: 'Configurações do exercício não encontradas.',
      };
    }

    let despesaAcao: any = null;
    if (alteracaoOrcamentaria.despesaAcao) {
      despesaAcao = await prisma.dotacoes.findFirst({
        include: {
          secretaria: true,
          departamento: {
            include: {
              secretaria: true,
            },
          },
          economica: true,
          funcional: true,
          subdepartamento: {
            include: {
              departamento: {
                include: {
                  secretaria: true,
                },
              },
            },
          },
        },
        where: {
          exercicio: alteracaoOrcamentaria.exercicio,
          despesa: alteracaoOrcamentaria.despesaAcao,
        },
      });
    }

    let despesaCopia: any = null;
    if (alteracaoOrcamentaria.despesaCopia) {
      despesaCopia = await prisma.dotacoes.findFirst({
        include: {
          secretaria: true,
          departamento: {
            include: {
              secretaria: true,
            },
          },
          economica: true,
          funcional: true,
          subdepartamento: {
            include: {
              departamento: {
                include: {
                  secretaria: true,
                },
              },
            },
          },
        },
        where: {
          exercicio: alteracaoOrcamentaria.exercicio,
          despesa: alteracaoOrcamentaria.despesaCopia,
        },
      });
    }

    let despesaNova: any = null;
    if (alteracaoOrcamentaria.despesaNova) {
      despesaNova = await prisma.dotacoes.findFirst({
        include: {
          secretaria: true,
          departamento: {
            include: {
              secretaria: true,
            },
          },
          economica: true,
          funcional: true,
          subdepartamento: {
            include: {
              departamento: {
                include: {
                  secretaria: true,
                },
              },
            },
          },
        },
        where: {
          exercicio: alteracaoOrcamentaria.exercicio,
          despesa: alteracaoOrcamentaria.despesaNova,
        },
      });
    }

    let gestor: any = null;
    if (alteracaoOrcamentaria.idUsuario) {
      gestor = await prisma.user_profiles.findFirst({
        where: {
          id: alteracaoOrcamentaria.idUsuario,
        },
      });
    }

    return {
      data: {
        ...alteracaoOrcamentaria,
        valorMes1: alteracaoOrcamentaria.valorMes1.toNumber(),
        valorMes2: alteracaoOrcamentaria.valorMes2.toNumber(),
        valorMes3: alteracaoOrcamentaria.valorMes3.toNumber(),
        valorMes4: alteracaoOrcamentaria.valorMes4.toNumber(),
        valorMes5: alteracaoOrcamentaria.valorMes5.toNumber(),
        valorMes6: alteracaoOrcamentaria.valorMes6.toNumber(),
        valorMes7: alteracaoOrcamentaria.valorMes7.toNumber(),
        valorMes8: alteracaoOrcamentaria.valorMes8.toNumber(),
        valorMes9: alteracaoOrcamentaria.valorMes9.toNumber(),
        valorMes10: alteracaoOrcamentaria.valorMes10.toNumber(),
        valorMes11: alteracaoOrcamentaria.valorMes11.toNumber(),
        valorMes12: alteracaoOrcamentaria.valorMes12.toNumber(),
        configsExercicio,
        incluirGestor,
        gerente: resultPermissao.gerente,
        dotacaoAcao: despesaAcao,
        dotacaoCopia: despesaCopia,
        dotacaoNova: despesaNova,
        gestor,
      },
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao obter alteração orçamentária.`,
    };
  }
};

// Verificar se vou utilizar quando for assinar
export const getSecretariosAssinantes = async (secretaria: any) => {
  return await prisma.cargos.findMany({
    where: {
      tipoAssinatura: TiposAssinatura.SECRETARIO,
      OR: [
        {
          AND: [
            {
              cargos_acessos: {
                every: {
                  secretariaId: secretaria.id,
                },
              },
            },
            {
              tipoAcesso: TiposAcesso.SECRETARIAS,
            },
          ],
        },
        {
          tipoAcesso: TiposAcesso.TODAS_SECRETARIAS,
        },
      ],
    },
    select: {
      user_profiles_cargos: {
        select: {
          usuario: {
            select: {
              id: true,
              nome: true,
            },
          },
        },
      },
    },
  });
};
