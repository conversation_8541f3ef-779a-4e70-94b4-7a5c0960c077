'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import { superavitSchema } from '@/lib/validation';
import {
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  obterAnoAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import {
  criarSuperavit,
  listarSuperavits,
} from '@/lib/database/gerenciamento/superavit';
import currency from 'currency.js';
import { Fontes } from '@/lib/enums';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function CriarSuperavitForm({}: {
  fornecedores: Awaited<ReturnType<typeof listarSuperavits>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);

  const anoAtual = obterAnoAtual();

  const [valReceita, setValReceita] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );

  const form = useForm<z.infer<typeof superavitSchema>>({
    resolver: zodResolver(superavitSchema),
    defaultValues: {
      exercicio: anoAtual,
      fonte: 0,
      codAplicacao: 0,
      valorReceita: 0,
      dotInicial: 0,
      suplementado: 0,
      valReserva: 0,
      saldoAtual: 0,
    },
  });

  const onSubmit = async (values: z.infer<typeof superavitSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof superavitSchema> = {
        exercicio: values.exercicio,
        fonte: values.fonte,
        codAplicacao: values.codAplicacao,
        valorReceita: values.valorReceita,
        // idDotacao: 0 // values.idDotacao
      };

      const res = await criarSuperavit(data);

      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Superavit adicionado.');
        router.push('/gerenciamento/superavits');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='grid gap-8 py-4'>
          <FormField
            control={form.control}
            name='exercicio'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Exercicio
                </FormLabel>
                <FormControl>
                  <Input {...field} maxLength={4} minLength={1} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='fonte'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Fonte</FormLabel>
                <Select onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Selecione a fonte' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(Fontes)
                      .filter((v) => !isNaN(Number(v[0])) && Number(v[0]) > 0)
                      .map((fonte) => (
                        <SelectItem key={fonte[0]} value={`${fonte[0]}`}>
                          {fonte[0]} - {fonte[1]}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='codAplicacao'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Codigo Aplicação
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    maxLength={7} // verificar minimo e maximo
                    minLength={7}
                    onChange={(e) => {
                      form.setValue('codAplicacao', Number(e.target.value), {
                        shouldDirty: true,
                      });
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='valorReceita'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Receita</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={valReceita}
                    maxLength={20}
                    minLength={1}
                    onChange={(e) => {
                      setValReceita(moneyMask(e.target.value));
                      form.setValue(
                        'valorReceita',
                        Number(moneyUnmask(e.target.value)),
                        { shouldDirty: true }
                      );

                      /*const valIni = dotacao(form.getValues('exercicio'), form.getValues('fonte'), form.getValues('codAplicacao'))
                      form.setValue(
                        'dotInicial',
                        Number(valIni),
                        { shouldDirty: true }
                      );*/
                    }}
                    onFocus={(e) => e.currentTarget.select()}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          {/*<FormField
            control={form.control}
            name='dotInicial'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Dotação Inicial</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={valDotIni}
                    readOnly
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='suplementado'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Suplementado</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={valSuplem}
                    readOnly
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='valReserva'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Valor Reservado</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={valReserva}
                    readOnly
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='saldoAtual'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Saldo Atual</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    value={valSaldoAtu}
                    readOnly
                  />
                </FormControl>
              </FormItem>
            )}
          />*/}
        </div>
        <div className='mt-12 flex justify-between'>
          <Button
            variant={'destructive'}
            disabled={loading}
            onClick={(e) => {
              e.preventDefault();
              router.push('/gerenciamento/superavits');
            }}
          >
            <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
          </Button>
          <Button
            type='submit'
            disabled={loading} // || FornecedoresConfigurados.length === 0}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' /> Adicionar Superavit
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
