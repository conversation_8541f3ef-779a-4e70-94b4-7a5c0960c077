// Utility functions for monetary calculations
export function precisionDecimal(valor: number, decimais: number = 4): number {
  return Math.round(valor * Math.pow(10, decimais)) / Math.pow(10, decimais);
}

export function formatarValorMonetario(valor: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(valor);
}
