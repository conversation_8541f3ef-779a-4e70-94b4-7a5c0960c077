'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { cancelarReservaSchema } from '@/lib/validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { cancelarReserva } from '@/lib/database/movimento/reservas';
import { DialogDescription } from '@radix-ui/react-dialog';
import { useRouter } from 'next/navigation';

export function DialogCancelarReserva({ idReserva }: { idReserva: number }) {
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof cancelarReservaSchema>>({
    resolver: zodResolver(cancelarReservaSchema),
    defaultValues: {
      id: idReserva,
      motivo: '',
    },
  });
  const router = useRouter();

  const onSubmit = async (values: z.infer<typeof cancelarReservaSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof cancelarReservaSchema> = {
        id: idReserva,
        motivo: values.motivo,
      };
      const res = await cancelarReserva(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Reserva cancelada com sucesso.');
        router.push('/movimento/reservas');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button type='button' variant='outline' className='text-red-800'>
          <X className='size-4' /> Cancelar
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Cancelar Reserva</DialogTitle>
          <DialogDescription>
            Cancelar e devolver valores reservados?
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex flex-wrap gap-4 py-4'>
              <FormField
                control={form.control}
                name='motivo'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Motivo
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={255}
                        minLength={2}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogFooter>
          <Button
            type='submit'
            variant='destructive'
            disabled={loading || !form.formState.isValid}
            onClick={form.handleSubmit(onSubmit)}
          >
            {loading ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <X className='h-4 w-4' /> Cancelar Reserva
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
