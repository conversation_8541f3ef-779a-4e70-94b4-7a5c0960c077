'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { usuarioEhGerente } from '@/lib/database/usuarios';
import { ComponentesPDF } from '@/components/movimento/reserva/solicitar-assinaturas/componentesPDF';
import { obterReservaParaRelatorio } from '@/lib/database/movimento/reservas';
import { StatusReserva } from '@/lib/enums';

export default async function VisualizarReservaPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const gerentePromise = usuarioEhGerente();
  const reservaPromise = obterReservaParaRelatorio({
    id: Number(id),
  });

  const [gerente, reserva] = await Promise.all([
    gerentePromise,
    reservaPromise,
  ]);

  if (gerente.error) {
    return <ErrorAlert error={gerente.error} />;
  }

  if (reserva.error) {
    return <ErrorAlert error={reserva.error} />;
  }
  if (!reserva.data) {
    return <ErrorAlert error='Reserva não encontrada' />;
  }
  if (reserva.data.status !== StatusReserva.Reservado) {
    return <ErrorAlert error='Status Inválido' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Solicitar Assinaturas</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='mx-6 flex w-full flex-wrap justify-between gap-4'>
          <Link href={`/movimento/reservas/visualizar/${id}`}>
            <Button className='mb-4' variant='secondary'>
              <ArrowLeft className='mr-2 size-4' /> Voltar
            </Button>
          </Link>
        </div>
        {gerente.data && (
          <div className='flex items-center space-x-2'>
            <Checkbox id='incluirGestor' defaultChecked />
            <label
              htmlFor='incluirGestor'
              className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
            >
              Incluir assinatura gestor(a)
            </label>
          </div>
        )}
        <div className='mt-2 w-full text-center'>
          Por favor valide o documento:
        </div>
        <div className='flex w-full flex-wrap justify-center'>
          <ComponentesPDF
            url={`/movimento/reservas/imprimir/${id}`}
            reserva={reserva.data}
          />
        </div>
      </PageContent>
    </PageWrapper>
  );
}
