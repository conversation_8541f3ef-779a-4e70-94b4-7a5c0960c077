'use client';
import { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

export default function CadastroPage() {
  const [isMounted, setIsMounted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className='text-red-500'>
              Link Expirado
            </AlertDialogTitle>
          </AlertDialogHeader>
          O Link acessado expirou ou já foi utilizado. Se necessário, tente
          realizar o processo novamente.
          <AlertDialogFooter>
            <Button
              disabled={isLoading}
              onClick={() => {
                setIsLoading(true);
                router.replace(`/login`);
              }}
            >
              {isLoading ? 'Carregando...' : 'Voltar'}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
