import { ErrorAlert } from '@/components/error-alert';
import SecretariasDatatable from './secretariasDataTable';
import { listarSecretarias } from '@/lib/database/gerenciamento/secretarias';

export default async function SecretariasDatatableWrapper() {
  const result = await listarSecretarias();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Secretarias não encontradas.'} />;

  return <SecretariasDatatable data={result} />;
}
