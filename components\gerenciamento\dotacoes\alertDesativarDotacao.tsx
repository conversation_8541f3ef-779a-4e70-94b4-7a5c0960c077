'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { desativarDotacao } from '@/lib/database/gerenciamento/dotacoes';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Ban } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertDesativarDotacao({
  idDotacao,
  despesa,
  descDotacao,
}: {
  idDotacao: number;
  despesa: number;
  descDotacao: string;
}) {
  const [loading, setLoading] = useState(false);

  const desativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idDotacao,
      };

      const res = await desativarDotacao(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Dotação desativada.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline' className='text-red-800'>
          <Ban className='mr-2 size-4' /> Desativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Desativar Dotação?</AlertDialogTitle>
          <AlertDialogDescription>
            A dotação{' '}
            <span className='font-bold'>
              {despesa} - {descDotacao}
            </span>{' '}
            será desativada.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              desativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Desativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
