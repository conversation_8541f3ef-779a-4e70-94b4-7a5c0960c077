'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface ComboboxSelecionarFornecedorProps {
  fornecedorId: number | null;
  setFornecedorId: (id: number) => void;
  fornecedores: any[];
  disabled?: boolean;
}

export function ComboboxSelecionarFornecedor({
  fornecedorId,
  setFornecedorId,
  fornecedores,
  disabled = false,
}: ComboboxSelecionarFornecedorProps) {
  const [open, setOpen] = React.useState(false);

  const fornecedorSelecionado = fornecedores.find(
    (fornecedor) => fornecedor.id === fornecedorId
  );

  const handleSelect = (fornecedor: any) => {
    setFornecedorId(fornecedor.id);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
          disabled={disabled || fornecedores.length === 0}
        >
          {fornecedores.length === 0
            ? 'Nenhum fornecedor disponível'
            : fornecedorSelecionado
              ? `${fornecedorSelecionado.codigo || fornecedorSelecionado.id} - ${fornecedorSelecionado.nome}`
              : 'Selecione o fornecedor...'}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[500px] p-0'>
        <Command>
          <CommandInput placeholder='Buscar fornecedor por código ou nome...' />
          <CommandList>
            <CommandEmpty>Nenhum fornecedor encontrado.</CommandEmpty>
            {fornecedores.map((fornecedor) => (
              <CommandItem
                key={fornecedor.id}
                value={`${fornecedor.codigo || fornecedor.id} ${fornecedor.nome} ${fornecedor.cpfCnpj || ''}`}
                onSelect={() => handleSelect(fornecedor)}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    fornecedor.id === fornecedorId ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className='flex w-full flex-col'>
                  <span className='font-medium'>
                    {fornecedor.codigo || fornecedor.id} - {fornecedor.nome}
                  </span>
                  {fornecedor.cpfCnpj && (
                    <span className='text-muted-foreground text-sm'>
                      {fornecedor.cpfCnpj}
                    </span>
                  )}
                  {fornecedor.email && (
                    <span className='text-muted-foreground text-xs'>
                      {fornecedor.email}
                    </span>
                  )}
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
