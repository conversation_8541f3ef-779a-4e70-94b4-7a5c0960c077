'use client';

import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';
import { criarProtocolo } from '@/lib/database/movimento/protocolos';
import { toast } from 'sonner';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface BotaoCriarProtocoloGenericoProps {
  reservaId?: number;
  empenhoId?: number;
  resumo: string;
}

export function BotaoCriarProtocoloGenerico({
  reservaId,
  empenhoId,
  resumo,
}: BotaoCriarProtocoloGenericoProps) {
  const [isCreating, setIsCreating] = useState(false);
  const router = useRouter();

  const handleCriarProtocolo = async () => {
    setIsCreating(true);
    try {
      const result = await criarProtocolo({
        idReserva: reservaId,
        idEmpenho: empenhoId,
        resumo: resumo,
        obs: empenhoId
          ? 'Protocolo criado a partir do empenho'
          : 'Protocolo criado a partir da reserva',
      });

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Protocolo criado com sucesso!');
        router.refresh(); // Refresh the page to show the new protocol
      }
    } catch (error) {
      toast.error('Erro ao criar protocolo');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Button type='button' onClick={handleCriarProtocolo} disabled={isCreating}>
      <FileText className='mr-1 size-4' />
      {isCreating ? 'Criando...' : 'Criar Protocolo'}
    </Button>
  );
}
