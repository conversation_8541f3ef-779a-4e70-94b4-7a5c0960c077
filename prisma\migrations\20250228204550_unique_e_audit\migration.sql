/*
  Warnings:

  - A unique constraint covering the columns `[exercicio,fonte,codAplicacao]` on the table `controleSuperavitsDetalhes` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `tipoAcao` to the `controleSuperavitsDetalhes` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "controleSuperavitsDetalhes" ADD COLUMN     "tipoAcao" SMALLINT NOT NULL;

-- CreateTable
CREATE TABLE "controleSuperavitsDetalhes_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idSuperDeta" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "controleSuperavitsDetalhes_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "controleSuperavitsDetalhes_exercicio_fonte_codAplicacao_key" ON "controleSuperavitsDetalhes"("exercicio", "fonte", "codAplicacao");

-- AddForeignKey
ALTER TABLE "controleSuperavitsDetalhes_audit" ADD CONSTRAINT "controleSuperavitsDetalhes_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "controleSuperavitsDetalhes_audit" ADD CONSTRAINT "controleSuperavitsDetalhes_audit_idSuperDeta_fkey" FOREIGN KEY ("idSuperDeta") REFERENCES "controleSuperavitsDetalhes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
