'use client';
import { ColumnDef } from '@tanstack/react-table';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { codigoSecretariaMask } from '@/lib/utils';
import { listarSubdepartamentos } from '@/lib/database/gerenciamento/subdepartamentos';
import { obterSecretaria } from '@/lib/database/gerenciamento/secretarias';
import { obterDepartamento } from '@/lib/database/gerenciamento/departamentos';
import { DialogRenomearSubdepartamento } from './dialogRenomearSubdepartamento';
import { AlertDesativarSubdepartamento } from './alertDesativarSubdepartamento';
import { AlertAtivarSubdepartamento } from './alertAtivarSubdepartamento';

export default function SubdepartamentosDatatable({
  data,
  secretaria,
  departamento,
}: {
  data: Awaited<ReturnType<typeof listarSubdepartamentos>>;
  secretaria: Awaited<ReturnType<typeof obterSecretaria>>;
  departamento: Awaited<ReturnType<typeof obterDepartamento>>;
}) {
  if (!data.data || !secretaria.data || !departamento.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Código',
      filterFn: 'includesString',
      cell: ({ row }) =>
        `${codigoSecretariaMask(secretaria.data.codigo.toString())}.${codigoSecretariaMask(departamento.data.codigo.toString())}.${codigoSecretariaMask(row.getValue('codigo'))}`,
    },
    {
      accessorKey: 'nome',
      header: 'Subdepartamento',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <DialogRenomearSubdepartamento
            idSecretaria={secretaria.data.id}
            idSubdepartamento={row.original.id}
            nomeSubdepartamento={row.original.nome}
            codigoSubdepartamento={row.original.codigo}
            codigoSecretaria={secretaria.data.codigo}
            idDepartamento={departamento.data.id}
            codigoDepartamento={departamento.data.codigo}
          />
          {row.original.ativo ? (
            <AlertDesativarSubdepartamento
              idSecretaria={secretaria.data.id}
              idSubdepartamento={row.original.id}
              nomeSubdepartamento={row.original.nome}
              codigoSubdepartamento={row.original.codigo}
              codigoSecretaria={secretaria.data.codigo}
              idDepartamento={departamento.data.id}
              codigoDepartamento={departamento.data.codigo}
            />
          ) : (
            <AlertAtivarSubdepartamento
              idSecretaria={secretaria.data.id}
              idSubdepartamento={row.original.id}
              nomeSubdepartamento={row.original.nome}
              codigoSubdepartamento={row.original.codigo}
              codigoSecretaria={secretaria.data.codigo}
              idDepartamento={departamento.data.id}
              codigoDepartamento={departamento.data.codigo}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <>
      <div>
        <span className='mr-2 font-bold'>Departamento:</span>{' '}
        {`${codigoSecretariaMask(secretaria.data.codigo.toString())}.${departamento.data.codigo.toString()}.00`}{' '}
        - {departamento.data.nome}
      </div>
      <div className='container mx-auto'>
        <ReusableDatatable columns={columns} data={data.data} />
      </div>
    </>
  );
}
