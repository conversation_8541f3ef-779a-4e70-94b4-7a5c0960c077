/*
  Warnings:

  - Added the required column `idDiretor` to the `reservas` table without a default value. This is not possible if the table is not empty.
  - Added the required column `idPrefeito` to the `reservas` table without a default value. This is not possible if the table is not empty.
  - Added the required column `idDiretor` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.
  - Added the required column `idPrefeito` to the `reservas_audit` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "reservas" ADD COLUMN     "idDiretor" INTEGER NOT NULL,
ADD COLUMN     "idPrefeito" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "reservas_audit" ADD COLUMN     "idDiretor" INTEGER NOT NULL,
ADD COLUMN     "idPrefeito" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "reservas" ADD CONSTRAINT "reservas_idDiretor_fkey" FOREIGN KEY ("idDiretor") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas" ADD CONSTRAINT "reservas_idPrefeito_fkey" FOREIGN KEY ("idPrefeito") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas_audit" ADD CONSTRAINT "reservas_audit_idDiretor_fkey" FOREIGN KEY ("idDiretor") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas_audit" ADD CONSTRAINT "reservas_audit_idPrefeito_fkey" FOREIGN KEY ("idPrefeito") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
