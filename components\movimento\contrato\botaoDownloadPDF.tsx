'use client';

import { useState } from 'react';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { toast } from 'sonner';
import { createClient } from '@/lib/supabase/client';
import { Loader2, Printer } from 'lucide-react';
import { siteConfig } from '@/config/site';
import { Button } from '@/components/ui/button';
import { CHAVE_API, URL_SERVER_IMPRESSAO } from '@/lib/consts';

export const BotaoDownloadPDFContrato = ({ url }: { url: string }) => {
  const [isLoading, setIsLoading] = useState(false);
  const onClick = async () => {
    if (isLoading) return null;
    setIsLoading(true);
    function popUpWasBlocked(popUp: Window | null) {
      return !popUp || popUp.closed || typeof popUp.closed === 'undefined';
    }
    const w = window.open('/carregando', '_blank');
    try {
      const supabase = createClient();
      const session = await supabase.auth.getSession();
      if (!session) {
        setIsLoading(false);
        return toast.error(toastAlgoDeuErrado);
      }

      if (!w || popUpWasBlocked(w)) {
        setIsLoading(false);
        return toast.error(
          'Janela popup bloqueada. Verifique as configurações do seu navegador.'
        );
      }

      const incluirGestorCheckbox = document.getElementById(
        'incluirGestor'
      ) as HTMLInputElement;
      const incluirGestor = incluirGestorCheckbox
        ? incluirGestorCheckbox.getAttribute('data-state') !== 'unchecked'
        : true;

      const pdf = await fetch(URL_SERVER_IMPRESSAO, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.data.session?.access_token}`,
          url: siteConfig.url + url + `?incluirGestor=${incluirGestor}`,
          'Content-Type': 'application/json',
          chave: CHAVE_API,
        },
        body: JSON.stringify({ landscape: true }),
      });
      if (!pdf.ok) {
        setIsLoading(false);
        w.close();
        return toast.error(toastAlgoDeuErrado);
      }
      const fileURL = URL.createObjectURL(await pdf.blob());
      w.location.href = fileURL;
    } catch (e) {
      setIsLoading(false);
      if (w) {
        w.close();
      }
      toast.error(toastAlgoDeuErrado);
      const env = process.env.NODE_ENV;
      if (env == 'development') {
        console.log(e);
      }
    }
    setIsLoading(false);
  };

  return (
    <Button onClick={onClick} disabled={isLoading}>
      Imprimir
      {isLoading ? (
        <Loader2 className='h-4 w-4 animate-spin' />
      ) : (
        <Printer className='h-4 w-4' />
      )}
    </Button>
  );
};
