'use server';

import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { obterFornecedor } from '@/lib/database/gerenciamento/fornecedores';
import EditarFornecedorForm from '@/components/gerenciamento/Fornecedor/editarFornecedorForm';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export default async function EditarFornecedorPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const fornecedor = await obterFornecedor({
    id: Number(id),
  });

  if (fornecedor.error) {
    return <ErrorAlert error={fornecedor.error} />;
  }
  if (!fornecedor.data) {
    return <ErrorAlert error='Falha ao obter Fornecedor.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Alterar Fornecedor</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <EditarFornecedorForm fornecedor={fornecedor} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
