/*
  Warnings:

  - You are about to drop the column `acaoId` on the `funcionais` table. All the data in the column will be lost.
  - You are about to drop the column `funcaoId` on the `funcionais` table. All the data in the column will be lost.
  - You are about to drop the column `programaId` on the `funcionais` table. All the data in the column will be lost.
  - You are about to drop the column `subfuncaoId` on the `funcionais` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[codigoFuncao,codigoSubfuncao,codigoPrograma,codigoAcao]` on the table `funcionais` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `codigo` to the `funcionais` table without a default value. This is not possible if the table is not empty.
  - Added the required column `codigoAcao` to the `funcionais` table without a default value. This is not possible if the table is not empty.
  - Added the required column `codigoFuncao` to the `funcionais` table without a default value. This is not possible if the table is not empty.
  - Added the required column `codigoPrograma` to the `funcionais` table without a default value. This is not possible if the table is not empty.
  - Added the required column `codigoSubfuncao` to the `funcionais` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "funcionais" DROP CONSTRAINT "funcionais_acaoId_fkey";

-- DropForeignKey
ALTER TABLE "funcionais" DROP CONSTRAINT "funcionais_funcaoId_fkey";

-- DropForeignKey
ALTER TABLE "funcionais" DROP CONSTRAINT "funcionais_programaId_fkey";

-- DropForeignKey
ALTER TABLE "funcionais" DROP CONSTRAINT "funcionais_subfuncaoId_fkey";

-- DropIndex
DROP INDEX "funcionais_funcaoId_subfuncaoId_programaId_acaoId_key";

-- AlterTable
ALTER TABLE "funcionais" DROP COLUMN "acaoId",
DROP COLUMN "funcaoId",
DROP COLUMN "programaId",
DROP COLUMN "subfuncaoId",
ADD COLUMN     "codigo" VARCHAR(16) NOT NULL,
ADD COLUMN     "codigoAcao" INTEGER NOT NULL,
ADD COLUMN     "codigoFuncao" INTEGER NOT NULL,
ADD COLUMN     "codigoPrograma" INTEGER NOT NULL,
ADD COLUMN     "codigoSubfuncao" INTEGER NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "funcionais_codigoFuncao_codigoSubfuncao_codigoPrograma_codi_key" ON "funcionais"("codigoFuncao", "codigoSubfuncao", "codigoPrograma", "codigoAcao");

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_codigoFuncao_fkey" FOREIGN KEY ("codigoFuncao") REFERENCES "funcoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_codigoSubfuncao_fkey" FOREIGN KEY ("codigoSubfuncao") REFERENCES "subfuncoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_codigoPrograma_fkey" FOREIGN KEY ("codigoPrograma") REFERENCES "programas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_codigoAcao_fkey" FOREIGN KEY ("codigoAcao") REFERENCES "acoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
