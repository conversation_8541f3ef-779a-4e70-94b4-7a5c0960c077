'use client';
import { ColumnDef } from '@tanstack/react-table';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dispatch } from 'react';
import { Departamento } from '@/types/app';
import { codigoSecretariaMask } from '@/lib/utils';

export default function CadastroDepsDatatable({
  data,
  setData,
  codigoSecretaria,
}: {
  data: Departamento[];
  setData: Dispatch<React.SetStateAction<Departamento[]>>;
  codigoSecretaria: number | null;
}) {
  if (!data) return null;
  const columns: ColumnDef<(typeof data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Código',
      filterFn: 'includesString',
      cell: ({ row }) =>
        `${codigoSecretariaMask(codigoSecretaria?.toString() || '0')}.${codigoSecretariaMask(row.getValue('codigo'))}.00`,
    },
    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <Button
          type='button'
          variant='ghost'
          aria-description='Remover Departamento'
          onClick={() => {
            setData(
              data.filter((mod) => mod.codigo !== row.getValue('codigo'))
            );
          }}
        >
          <Trash className='size-4' />
        </Button>
      ),
    },
  ];
  return (
    <div className='overflow-x-scroll'>
      <ReusableDatatable columns={columns} data={data} />
    </div>
  );
}
