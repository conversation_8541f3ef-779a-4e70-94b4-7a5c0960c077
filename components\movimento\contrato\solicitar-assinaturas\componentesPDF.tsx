'use client';

import { Button } from '@/components/ui/button';
import { siteConfig } from '@/config/site';
import {
  CHAVE_API,
  URL_SERVER_ASSINATURA,
  URL_SERVER_IMPRESSAO,
} from '@/lib/consts';
import { obterReservaParaRelatorio } from '@/lib/database/movimento/reservas';
import { createClient } from '@/lib/supabase/client';
import { uploadPDFLegacy } from '@/lib/supabase/clientUtils';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { Signature } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export const ComponentesPDF = ({
  url,
  reserva,
}: {
  url: string;
  reserva: Awaited<ReturnType<typeof obterReservaParaRelatorio>>['data'];
}) => {
  const [loading, setLoading] = useState(true);
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [incluirGestorCheckbox, setIncluirGestorCheckbox] =
    useState<HTMLInputElement | null>(null);

  const incluirGestor = incluirGestorCheckbox
    ? incluirGestorCheckbox.getAttribute('data-state') !== 'unchecked'
    : true;

  useEffect(() => {
    setIncluirGestorCheckbox(
      document.getElementById('incluirGestor') as HTMLInputElement
    );
  }, []);

  useEffect(() => {
    const carregarPDF = async () => {
      try {
        const supabase = createClient();
        const session = await supabase.auth.getSession();
        const pdf = await fetch(URL_SERVER_IMPRESSAO, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${session.data.session?.access_token}`,
            url: siteConfig.url + url + `?incluirGestor=${incluirGestor}`,
            'Content-Type': 'application/json',
            chave: CHAVE_API,
          },
          body: JSON.stringify({ assinaturaDigital: true, landscape: true }),
        });
        if (!pdf.ok) {
          return toast.error(
            'Falha ao carregar o documento, por favor recarregue a página.'
          );
        }
        const pdfBlob = await pdf.blob();
        setPdfBlob(pdfBlob);

        const fileURL = URL.createObjectURL(pdfBlob);
        const iframe = document.getElementById(
          'documento'
        ) as HTMLIFrameElement;
        iframe.src = fileURL;
        setLoading(false);
      } catch (error) {
        toast.error(toastAlgoDeuErrado);
      }
    };
    carregarPDF();
  }, [url, incluirGestor, incluirGestorCheckbox]);

  const solicitarAssinaturas = async () => {
    try {
      if (!pdfBlob) {
        return toast.error('Documento não encontrado.');
      }
      setLoading(true);

      //Acesso controlado pelo ID das dotações - deve ser a primeira pasta dentro de private
      const idDotacao = reserva?.dotacao.id;
      const exercicio = reserva?.exercicio;
      const nomeDocumento = `Pedido de Compra - Reserva ${reserva?.id}.pdf`;

      const pdfFile = new File([pdfBlob], nomeDocumento, {
        type: 'application/pdf',
      });
      await uploadPDFLegacy(
        'reservas',
        `private/${idDotacao}/${exercicio}/${nomeDocumento}`,
        pdfFile
      );
      setLoading(false);
    } catch (error) {
      toast.error(toastAlgoDeuErrado);
      console.log(error);
      setLoading(false);
    }
  };

  const testeAssinatura = async () => {
    try {
      setLoading(true);
      const supabase = createClient();
      const session = await supabase.auth.getSession();
      const idDotacao = reserva?.dotacao.id;
      const exercicio = reserva?.exercicio;
      const nomeDocumento = `Pedido de Compra - Reserva ${reserva?.id}.pdf`;
      const pdfPath = `private/${exercicio}/dotacoes/${idDotacao}/reservas/${nomeDocumento}`;
      const senha = '1234';
      const bucket = 'reservas';
      const assinatura = await fetch(URL_SERVER_ASSINATURA, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.data.session?.access_token}`,
          'Content-Type': 'application/json',
          chave: CHAVE_API,
        },
        body: JSON.stringify({ pdfPath, senha, bucket, top: 15.2, left: 3.3 }),
      });
      if (!assinatura.ok) {
        return toast.error('Falha ao assinar o documento.');
      }
    } catch (error) {
      toast.error(toastAlgoDeuErrado);
      console.log(error);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <iframe id='documento' src={`/carregando`} className='h-[800px] w-full' />
      <Button
        className='mt-4'
        variant='secondary'
        disabled={loading}
        onClick={solicitarAssinaturas}
      >
        <Signature className='size-4' />
        Solicitar Assinaturas
      </Button>
      <Button
        className='mt-4'
        variant='secondary'
        disabled={loading}
        onClick={testeAssinatura}
      >
        <Signature className='size-4' />
        Teste Assinatura
      </Button>
    </>
  );
};
