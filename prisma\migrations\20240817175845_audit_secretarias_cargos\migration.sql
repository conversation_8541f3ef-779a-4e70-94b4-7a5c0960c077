-- CreateTable
CREATE TABLE "gerenciamentoSecretarias_audit" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "secretariaId" INTEGER,
    "departamentoId" INTEGER,
    "subdepartamentoId" INTEGER,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gerenciamentoSecretarias_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gerenciamentoCargos_audit" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "cargos_acessoId" INTEGER,
    "cargos_permissoesId" INTEGER,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gerenciamentoCargos_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "gerenciamentoSecretarias_audit" ADD CONSTRAINT "gerenciamentoSecretarias_audit_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoSecretarias_audit" ADD CONSTRAINT "gerenciamentoSecretarias_audit_secretariaId_fkey" FOREIGN KEY ("secretariaId") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoSecretarias_audit" ADD CONSTRAINT "gerenciamentoSecretarias_audit_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "departamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoSecretarias_audit" ADD CONSTRAINT "gerenciamentoSecretarias_audit_subdepartamentoId_fkey" FOREIGN KEY ("subdepartamentoId") REFERENCES "subdepartamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoCargos_audit" ADD CONSTRAINT "gerenciamentoCargos_audit_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoCargos_audit" ADD CONSTRAINT "gerenciamentoCargos_audit_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoCargos_audit" ADD CONSTRAINT "gerenciamentoCargos_audit_cargos_acessoId_fkey" FOREIGN KEY ("cargos_acessoId") REFERENCES "cargos_acessos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoCargos_audit" ADD CONSTRAINT "gerenciamentoCargos_audit_cargos_permissoesId_fkey" FOREIGN KEY ("cargos_permissoesId") REFERENCES "cargos_permissoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
