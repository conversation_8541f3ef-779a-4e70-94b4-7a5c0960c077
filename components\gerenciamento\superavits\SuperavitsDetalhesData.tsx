import { ErrorAlert } from '@/components/error-alert';
import { listarSuperavitsDetalhes } from '@/lib/database/gerenciamento/superavit';
import SuperavitsDetalhesDatatable from './SuperavitsDetalhesDataTable';

export default async function SuperavitsDetalhesData({
  idSuperavit,
}: {
  idSuperavit: number;
}) {
  const result = await listarSuperavitsDetalhes(idSuperavit);

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Detalhes dos Superavits não encontrados.'} />;

  return <SuperavitsDetalhesDatatable data={result} />;
}
