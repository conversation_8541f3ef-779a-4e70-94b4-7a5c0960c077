'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { HistoricoProtocolo } from '@/components/movimento/protocolo/historicoProtocolo';
import { Search, Filter, RefreshCw, FileText } from 'lucide-react';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';

export default function HistoricoProtocolosPage() {
  const [protocoloId, setProtocoloId] = useState<string>('');
  const [selectedProtocolo, setSelectedProtocolo] = useState<number | null>(
    null
  );
  const [filtroUsuario, setFiltroUsuario] = useState<string>('');
  const [filtroAcao, setFiltroAcao] = useState<string>('');
  const [filtroDataInicio, setFiltroDataInicio] = useState<string>('');
  const [filtroDataFim, setFiltroDataFim] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const acoesDisponiveis = [
    { value: 'CRIACAO', label: 'Criação' },
    { value: 'ATUALIZACAO', label: 'Atualização' },
    { value: 'ALTERACAO_STATUS', label: 'Alteração de Status' },
    { value: 'VINCULACAO_AF', label: 'Vinculação de AF' },
    { value: 'VINCULACAO_EMPENHO', label: 'Vinculação de Empenho' },
    { value: 'DEVOLUCAO', label: 'Devolução' },
    { value: 'EXCLUSAO', label: 'Exclusão' },
  ];

  const handleBuscarProtocolo = async () => {
    if (!protocoloId) return;

    setIsLoading(true);
    try {
      // Simulate API call - replace with actual implementation
      const id = parseInt(protocoloId);
      if (!isNaN(id)) {
        setSelectedProtocolo(id);
      }
    } catch (error) {
      console.error('Erro ao buscar protocolo:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLimparFiltros = () => {
    setProtocoloId('');
    setSelectedProtocolo(null);
    setFiltroUsuario('');
    setFiltroAcao('');
    setFiltroDataInicio('');
    setFiltroDataFim('');
  };

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Histórico de Protocolos</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='space-y-6'>
          {/* Search and Filter Section */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Search className='h-5 w-5' />
                Buscar Protocolo
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='flex items-end gap-4'>
                <div className='flex-1'>
                  <label className='mb-2 block text-sm font-medium'>
                    Número do Protocolo
                  </label>
                  <Input
                    placeholder='Digite o número do protocolo...'
                    value={protocoloId}
                    onChange={(e) => setProtocoloId(e.target.value)}
                    onKeyPress={(e) =>
                      e.key === 'Enter' && handleBuscarProtocolo()
                    }
                  />
                </div>
                <Button
                  onClick={handleBuscarProtocolo}
                  disabled={isLoading || !protocoloId}
                >
                  <Search className='mr-2 h-4 w-4' />
                  {isLoading ? 'Buscando...' : 'Buscar'}
                </Button>
                <Button variant='outline' onClick={handleLimparFiltros}>
                  <RefreshCw className='mr-2 h-4 w-4' />
                  Limpar
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Results Section */}
          {selectedProtocolo && (
            <div className='grid grid-cols-1 gap-6 lg:grid-cols-4'>
              {/* Protocolo Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <FileText className='h-5 w-5' />
                    Protocolo #{selectedProtocolo}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <div>
                      <div className='text-sm text-gray-500'>Status</div>
                      <Badge variant='secondary'>Em Andamento</Badge>
                    </div>
                    <div>
                      <div className='text-sm text-gray-500'>
                        Data de Abertura
                      </div>
                      <div className='text-sm'>15/09/2024</div>
                    </div>
                    <div>
                      <div className='text-sm text-gray-500'>
                        Última Atualização
                      </div>
                      <div className='text-sm'>20/09/2024</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Filters Card */}
              <Card className='lg:col-span-3'>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Filter className='h-5 w-5' />
                    Filtros do Histórico
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-1 gap-4 md:grid-cols-4'>
                    <div>
                      <label className='mb-2 block text-sm font-medium'>
                        Usuário
                      </label>
                      <Input
                        placeholder='Filtrar por usuário...'
                        value={filtroUsuario}
                        onChange={(e) => setFiltroUsuario(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className='mb-2 block text-sm font-medium'>
                        Ação
                      </label>
                      <Select value={filtroAcao} onValueChange={setFiltroAcao}>
                        <SelectTrigger>
                          <SelectValue placeholder='Todas as ações' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value=''>Todas as ações</SelectItem>
                          {acoesDisponiveis.map((acao) => (
                            <SelectItem key={acao.value} value={acao.value}>
                              {acao.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className='mb-2 block text-sm font-medium'>
                        Data Início
                      </label>
                      <Input
                        type='date'
                        value={filtroDataInicio}
                        onChange={(e) => setFiltroDataInicio(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className='mb-2 block text-sm font-medium'>
                        Data Fim
                      </label>
                      <Input
                        type='date'
                        value={filtroDataFim}
                        onChange={(e) => setFiltroDataFim(e.target.value)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Historico Display */}
          {selectedProtocolo ? (
            <HistoricoProtocolo protocoloId={selectedProtocolo} />
          ) : (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <FileText className='mb-4 h-12 w-12 text-gray-400' />
                <h3 className='mb-2 text-lg font-medium text-gray-900'>
                  Nenhum protocolo selecionado
                </h3>
                <p className='max-w-md text-center text-gray-500'>
                  Digite o número de um protocolo acima para visualizar seu
                  histórico completo de movimentações.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </PageContent>
    </PageWrapper>
  );
}
