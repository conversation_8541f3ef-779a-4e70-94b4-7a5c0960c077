'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Download, BarChart3, PieChart } from 'lucide-react';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { StatusProtocolo } from '@/lib/enums';

export default function RelatorioProtocolosPage() {
  const [relatorioConfig, setRelatorioConfig] = useState({
    tipoRelatorio: 'resumo',
    formato: 'pdf',
    dataInicio: '',
    dataFim: '',
    status: [] as string[],
    secretarias: [] as string[],
    incluirHistorico: false,
    incluirDetalhes: true,
    agruparPor: 'nenhum',
  });

  const [isGenerating, setIsGenerating] = useState(false);

  const tiposRelatorio = [
    {
      value: 'resumo',
      label: 'Resumo Geral',
      description: 'Visão geral dos protocolos no período',
    },
    {
      value: 'detalhado',
      label: 'Relatório Detalhado',
      description: 'Informações completas de cada protocolo',
    },
    {
      value: 'estatistico',
      label: 'Análise Estatística',
      description: 'Métricas e gráficos de desempenho',
    },
    {
      value: 'consolidado',
      label: 'Consolidado por Status',
      description: 'Agrupamento por status de protocolos',
    },
  ];

  const statusOptions = Object.entries(StatusProtocolo)
    .filter(([key]) => isNaN(Number(key)))
    .map(([key, value]) => ({
      value: value.toString(),
      label: key.replace(/_/g, ' '),
    }));

  const agrupamentoOptions = [
    { value: 'nenhum', label: 'Sem Agrupamento' },
    { value: 'status', label: 'Agrupar por Status' },
    { value: 'secretaria', label: 'Agrupar por Secretaria' },
    { value: 'mes', label: 'Agrupar por Mês' },
    { value: 'usuario', label: 'Agrupar por Usuário' },
  ];

  const handleGerarRelatorio = async () => {
    setIsGenerating(true);
    try {
      // Simulate report generation - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Create and download report
      const blob = new Blob(['Relatório de Protocolos'], {
        type: 'application/pdf',
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `relatorio-protocolos-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleStatusChange = (statusValue: string, checked: boolean) => {
    setRelatorioConfig((prev) => ({
      ...prev,
      status: checked
        ? [...prev.status, statusValue]
        : prev.status.filter((s) => s !== statusValue),
    }));
  };

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatórios de Protocolos</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
          {/* Configuration Panel */}
          <Card className='lg:col-span-2'>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <BarChart3 className='h-5 w-5' />
                Configuração do Relatório
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Report Type */}
              <div className='space-y-3'>
                <Label className='text-sm font-medium'>Tipo de Relatório</Label>
                <RadioGroup
                  value={relatorioConfig.tipoRelatorio}
                  onValueChange={(value) =>
                    setRelatorioConfig((prev) => ({
                      ...prev,
                      tipoRelatorio: value,
                    }))
                  }
                  className='grid grid-cols-1 gap-3 md:grid-cols-2'
                >
                  {tiposRelatorio.map((tipo) => (
                    <div
                      key={tipo.value}
                      className='flex items-center space-x-2 rounded-lg border p-3'
                    >
                      <RadioGroupItem value={tipo.value} id={tipo.value} />
                      <div className='flex-1'>
                        <Label
                          htmlFor={tipo.value}
                          className='cursor-pointer text-sm font-medium'
                        >
                          {tipo.label}
                        </Label>
                        <p className='mt-1 text-xs text-gray-500'>
                          {tipo.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {/* Date Range */}
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div>
                  <Label className='text-sm font-medium'>Data Início</Label>
                  <Input
                    type='date'
                    value={relatorioConfig.dataInicio}
                    onChange={(e) =>
                      setRelatorioConfig((prev) => ({
                        ...prev,
                        dataInicio: e.target.value,
                      }))
                    }
                  />
                </div>
                <div>
                  <Label className='text-sm font-medium'>Data Fim</Label>
                  <Input
                    type='date'
                    value={relatorioConfig.dataFim}
                    onChange={(e) =>
                      setRelatorioConfig((prev) => ({
                        ...prev,
                        dataFim: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className='space-y-3'>
                <Label className='text-sm font-medium'>
                  Filtrar por Status
                </Label>
                <div className='grid max-h-40 grid-cols-2 gap-2 overflow-y-auto rounded border p-2 md:grid-cols-3'>
                  {statusOptions.map((status) => (
                    <div
                      key={status.value}
                      className='flex items-center space-x-2'
                    >
                      <Checkbox
                        id={`status-${status.value}`}
                        checked={relatorioConfig.status.includes(status.value)}
                        onCheckedChange={(checked) =>
                          handleStatusChange(status.value, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`status-${status.value}`}
                        className='cursor-pointer text-xs'
                      >
                        {status.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Grouping */}
              <div>
                <Label className='text-sm font-medium'>
                  Agrupar Resultados por
                </Label>
                <Select
                  value={relatorioConfig.agruparPor}
                  onValueChange={(value) =>
                    setRelatorioConfig((prev) => ({
                      ...prev,
                      agruparPor: value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {agrupamentoOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Additional Options */}
              <div className='space-y-3'>
                <Label className='text-sm font-medium'>Opções Adicionais</Label>
                <div className='space-y-2'>
                  <div className='flex items-center space-x-2'>
                    <Checkbox
                      id='historico'
                      checked={relatorioConfig.incluirHistorico}
                      onCheckedChange={(checked) =>
                        setRelatorioConfig((prev) => ({
                          ...prev,
                          incluirHistorico: checked as boolean,
                        }))
                      }
                    />
                    <Label htmlFor='historico' className='text-sm'>
                      Incluir histórico de alterações
                    </Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Checkbox
                      id='detalhes'
                      checked={relatorioConfig.incluirDetalhes}
                      onCheckedChange={(checked) =>
                        setRelatorioConfig((prev) => ({
                          ...prev,
                          incluirDetalhes: checked as boolean,
                        }))
                      }
                    />
                    <Label htmlFor='detalhes' className='text-sm'>
                      Incluir detalhes completos
                    </Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sidebar */}
          <div className='space-y-6'>
            {/* Format Selection */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Download className='h-5 w-5' />
                  Formato de Saída
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup
                  value={relatorioConfig.formato}
                  onValueChange={(value) =>
                    setRelatorioConfig((prev) => ({ ...prev, formato: value }))
                  }
                  className='space-y-2'
                >
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='pdf' id='pdf' />
                    <Label htmlFor='pdf' className='text-sm'>
                      PDF
                    </Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='excel' id='excel' />
                    <Label htmlFor='excel' className='text-sm'>
                      Excel
                    </Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='csv' id='csv' />
                    <Label htmlFor='csv' className='text-sm'>
                      CSV
                    </Label>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>

            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <PieChart className='h-5 w-5' />
                  Resumo
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-3'>
                <div className='space-y-2 text-sm'>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Tipo:</span>
                    <span>
                      {
                        tiposRelatorio.find(
                          (t) => t.value === relatorioConfig.tipoRelatorio
                        )?.label
                      }
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Formato:</span>
                    <span className='uppercase'>{relatorioConfig.formato}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Status selecionados:</span>
                    <Badge variant='secondary'>
                      {relatorioConfig.status.length}
                    </Badge>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Agrupamento:</span>
                    <span>
                      {
                        agrupamentoOptions.find(
                          (a) => a.value === relatorioConfig.agruparPor
                        )?.label
                      }
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Generate Button */}
            <Button
              onClick={handleGerarRelatorio}
              disabled={
                isGenerating ||
                !relatorioConfig.dataInicio ||
                !relatorioConfig.dataFim
              }
              className='w-full'
              size='lg'
            >
              <Download className='mr-2 h-4 w-4' />
              {isGenerating ? 'Gerando...' : 'Gerar Relatório'}
            </Button>
          </div>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
