'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import {
  moneyMask,
  moneyUnmask,
  numeroMask,
  toastAlgoDeuErrado,
  uppercaseMask,
} from '@/lib/utils';
import { criarDotacaoSchema } from '@/lib/validation';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  listarDepartamentosAtivos,
  listarSecretariasAtivas,
  listarSubdepartamentosAtivos,
} from '@/lib/database/gerenciamento/cargos';
import { Label } from '@/components/ui/label';
import { ComboboxSelecionaSecretaria } from './comboboxSelecionarSecretaria';
import { ComboboxSelecionaDepartamento } from './comboboxSelecionarDepartamento';
import { ComboboxSelecionaSubdepartamento } from './comboboxSelecionarSubdepartamento';
import {
  criarDotacao,
  listarEconomicasDotacao,
  listarFuncionaisAtivas,
} from '@/lib/database/gerenciamento/dotacoes';
import { ComboboxSelecionaEconomica } from './comboboxSelecionarEconomica';
import { ComboboxSelecionaFuncional } from './comboboxSelecionarFuncional';
import { Fontes } from '@/lib/enums';
export default function FormCriarDotacao({
  secretarias,
  departamentos,
  subdepartamentos,
  economicas,
  funcionais,
}: {
  secretarias: Awaited<ReturnType<typeof listarSecretariasAtivas>>;
  departamentos: Awaited<ReturnType<typeof listarDepartamentosAtivos>>;
  subdepartamentos: Awaited<ReturnType<typeof listarSubdepartamentosAtivos>>;
  economicas: Awaited<ReturnType<typeof listarEconomicasDotacao>>;
  funcionais: Awaited<ReturnType<typeof listarFuncionaisAtivas>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [tipoOrgao, setTipoOrgao] = useState('');

  const [secretariaId, setSecretariaId] = useState<number | null>(null);
  const [departamentoId, setDepartamentoId] = useState<number | null>(null);
  const [subdepartamentoId, setSubdepartamentoId] = useState<number | null>(
    null
  );

  const [valorInicialBRL, setValorInicialBRL] = useState('');
  const [cotaReducaoBRL, setCotaReducaoBRL] = useState('');
  const [anulacaoBRL, setAnulacaoBRL] = useState('');
  const [suplementacaoBRL, setSuplementacaoBRL] = useState('');

  useEffect(() => {
    setSecretariaId(null);
    setDepartamentoId(null);
    setSubdepartamentoId(null);
  }, [tipoOrgao, setSecretariaId, setDepartamentoId, setSubdepartamentoId]);

  const form = useForm<z.infer<typeof criarDotacaoSchema>>({
    resolver: zodResolver(criarDotacaoSchema),
    defaultValues: {
      //@ts-ignore
      codigo: '',
      nome: '',
      valorInicial: 0,
      cotaReducaoInicial: 0,
      suplementacao: 0,
      anulacao: 0,
    },
  });

  const onSubmit = async (values: z.infer<typeof criarDotacaoSchema>) => {
    if (!secretariaId && !departamentoId && !subdepartamentoId) {
      toast.warning('Selecione um orgão.');
      return;
    }

    if (!values.economicaId) {
      toast.warning('Selecione uma econômica.');
      return;
    }

    if (!values.funcionalId) {
      toast.warning('Selecione uma funcional.');
      return;
    }

    try {
      setLoading(true);
      const res = await criarDotacao({
        ...values,
        secretariaId: secretariaId || undefined,
        departamentoId: departamentoId || undefined,
        subdepartamentoId: subdepartamentoId || undefined,
      });
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Dotação criada.');
        router.push('/gerenciamento/dotacoes');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex gap-4'>
              <FormField
                control={form.control}
                name='despesa'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Despesa
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        onChange={(e) =>
                          form.setValue(
                            'despesa',
                            Number(numeroMask(e.target.value))
                          )
                        }
                        className='w-[72px]'
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='desc'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Descrição
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={150}
                        minLength={2}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('desc', uppercaseMask(value));
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-8 flex flex-wrap gap-2'>
              <Label className='w-full text-left'>Órgão</Label>
              <Select onValueChange={(e) => setTipoOrgao(e)}>
                <SelectTrigger>
                  <SelectValue placeholder='Selecione o tipo' />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Tipo</SelectLabel>
                    <SelectItem value='secretaria'>Secretaria</SelectItem>
                    <SelectItem value='departamento'>Departamento</SelectItem>
                    <SelectItem value='subdepartamento'>
                      Subdepartamento
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>

              {tipoOrgao === 'secretaria' && (
                <ComboboxSelecionaSecretaria
                  secretarias={secretarias}
                  secretariaId={secretariaId}
                  setSecretariaId={setSecretariaId}
                />
              )}

              {tipoOrgao === 'departamento' && (
                <ComboboxSelecionaDepartamento
                  departamentos={departamentos}
                  departamentoId={departamentoId}
                  setDepartamentoId={setDepartamentoId}
                />
              )}

              {tipoOrgao === 'subdepartamento' && (
                <ComboboxSelecionaSubdepartamento
                  subdepartamentos={subdepartamentos}
                  subdepartamentoId={subdepartamentoId}
                  setSubdepartamentoId={setSubdepartamentoId}
                />
              )}
            </div>

            <div className='mt-8 flex flex-wrap gap-2'>
              <Label className='w-full text-left'>Econômica</Label>
              <ComboboxSelecionaEconomica economicas={economicas} form={form} />
            </div>

            <div className='mt-8 flex flex-wrap gap-2'>
              <Label className='w-full text-left'>Funcional</Label>
              <ComboboxSelecionaFuncional funcionais={funcionais} form={form} />
            </div>

            <div className='mt-8 flex gap-2'>
              <FormField
                control={form.control}
                name='fonte'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='w-full text-left'>Fonte</FormLabel>
                    <Select onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Selecione a fonte' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(Fontes)
                          .filter(
                            (v) => !isNaN(Number(v[0])) && Number(v[0]) > 0
                          )
                          .map((fonte) => (
                            <SelectItem key={fonte[0]} value={`${fonte[0]}`}>
                              {fonte[0]} - {fonte[1]}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-8 flex gap-4'>
              <FormField
                control={form.control}
                name='codAplicacao'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Código de Aplicação
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='w-[5rem]'
                        minLength={7}
                        maxLength={7}
                        onChange={(e) =>
                          form.setValue(
                            'codAplicacao',
                            Number(numeroMask(e.target.value))
                          )
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-12 text-xl font-semibold'>Valores</div>
            <div className='mt-8 flex justify-between gap-6'>
              <FormField
                name='valorInicialBRL'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Valor Inicial
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='text-right text-base'
                        value={valorInicialBRL}
                        onChange={(e) => {
                          setValorInicialBRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorInicial',
                            Number(moneyUnmask(e.target.value))
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='cotaReducaoBRL'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Cóta de Redução Inicial
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='text-right text-base'
                        value={cotaReducaoBRL}
                        onChange={(e) => {
                          setCotaReducaoBRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaReducaoInicial',
                            Number(moneyUnmask(e.target.value))
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='suplementacaoBRL'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Suplementação
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='text-right text-base'
                        value={suplementacaoBRL}
                        onChange={(e) => {
                          setSuplementacaoBRL(moneyMask(e.target.value));
                          form.setValue(
                            'suplementacao',
                            Number(moneyUnmask(e.target.value))
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='anulacaoBRL'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Anulação</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='text-right text-base'
                        value={anulacaoBRL}
                        onChange={(e) => {
                          setAnulacaoBRL(moneyMask(e.target.value));
                          form.setValue(
                            'anulacao',
                            Number(moneyUnmask(e.target.value))
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <div className='mt-12 flex w-full justify-between'>
        <Button
          variant={'destructive'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/gerenciamento/dotacoes');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button onClick={form.handleSubmit(onSubmit)} disabled={loading}>
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <PlusCircle className='mr-2 h-4 w-4' /> Criar Dotação
            </>
          )}
        </Button>
      </div>
    </>
  );
}
