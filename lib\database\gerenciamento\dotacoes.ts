'use server';

import {
  AuditoriaCotasReducao,
  AuditoriaGerenciamentoDotacoes,
  ErrosDotacoes,
  Permissoes,
} from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  alterarCotasFormSchema,
  auditoriaErroSchema,
  cotaReducaoSchema,
  criarDotacaoSchema,
  idSchema,
  nomeEIdSchema,
  permissaoSchema,
  redistribuirCotasSchema,
} from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { revalidatePath } from 'next/cache';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import currency from 'currency.js';
import {
  currencyOptionsNoSymbol,
  obterAnoAtual,
  obterMesAtual,
} from '@/lib/utils';
import { toCurrency } from '@/lib/serverUtils';

export const listarDotacoes = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    const result = await prisma.dotacoes.findMany({
      select: {
        id: true,
        desc: true,
        despesa: true,
        valorAtual: true,
        ativo: true,
        economica: {
          select: {
            codigo: true,
          },
        },
      },
      where: { exercicio: resultPermissao.exercicio },
      orderBy: { despesa: 'asc' },
    });
    return {
      data: result.map((dotacao) => {
        return {
          ...dotacao,
          valorAtual: dotacao.valorAtual.toNumber(),
        };
      }),
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter dotações.`,
    };
  }
};

export const obterCotasPorId = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    await redistribuirCotas({
      dotacaoId: id,
      exercicio: resultPermissao.exercicio,
    });
    const result = await prisma.dotacoes.findUniqueOrThrow({
      where: { id },
    });
    return {
      data: {
        ...result,
        valorInicial: result.valorInicial.toNumber(),
        cotaReducaoInicial: result.cotaReducaoInicial.toNumber(),
        valorLiberado: result.valorLiberado.toNumber(),
        cotaReducao: result.cotaReducao.toNumber(),
        suplementacao: result.suplementacao.toNumber(),
        anulacao: result.anulacao.toNumber(),
        valorAtual: result.valorAtual.toNumber(),
        cotaMes1: result.cotaMes1.toNumber(),
        cotaMes2: result.cotaMes2.toNumber(),
        cotaMes3: result.cotaMes3.toNumber(),
        cotaMes4: result.cotaMes4.toNumber(),
        cotaMes5: result.cotaMes5.toNumber(),
        cotaMes6: result.cotaMes6.toNumber(),
        cotaMes7: result.cotaMes7.toNumber(),
        cotaMes8: result.cotaMes8.toNumber(),
        cotaMes9: result.cotaMes9.toNumber(),
        cotaMes10: result.cotaMes10.toNumber(),
        cotaMes11: result.cotaMes11.toNumber(),
        cotaMes12: result.cotaMes12.toNumber(),
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter cotas.`,
    };
  }
};

export const desativarDotacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.dotacoes.update({
        where: {
          id,
        },
        data: {
          ativo: false,
        },
      });

      const auditPromise = tx.gerenciamentoDotacoes_audit.create({
        data: {
          idDotacao: id,
          acao: AuditoriaGerenciamentoDotacoes.DESATIVAR_DOTACAO,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/dotacoes');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar dotação.`,
    };
  }
};

export const ativarDotacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.dotacoes.update({
        where: {
          id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.gerenciamentoDotacoes_audit.create({
        data: {
          idDotacao: id,
          acao: AuditoriaGerenciamentoDotacoes.ATIVAR_DOTACAO,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });
    revalidatePath('/gerenciamento/dotacoes');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar dotação.`,
    };
  }
};

export const alterarDescricaoDotacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = nomeEIdSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, nome } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const descActiga = await tx.dotacoes.findUnique({
        where: {
          id,
        },
        select: {
          desc: true,
        },
      });
      const resultPromise = tx.dotacoes.update({
        where: {
          id,
        },
        data: {
          desc: nome,
        },
      });
      const auditPromise = tx.gerenciamentoDotacoes_audit.create({
        data: {
          idDotacao: id,
          acao: AuditoriaGerenciamentoDotacoes.ALTERAR_DESCRICAO_DOTACAO,
          idUsuario: resultPermissao.idUsuario!,
          de: descActiga?.desc,
          para: nome,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });
    revalidatePath('/gerenciamento/dotacoes');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao alterar descrição da dotação.`,
    };
  }
};

export const criarDotacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = criarDotacaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    despesa,
    desc,
    fonte,
    codAplicacao,
    funcionalId,
    economicaId,
    secretariaId,
    departamentoId,
    subdepartamentoId,
    valorInicial,
    cotaReducaoInicial,
    suplementacao,
    anulacao,
  } = parsedParams.data;

  const valorLiberado = currency(valorInicial, currencyOptionsNoSymbol)
    .subtract(cotaReducaoInicial)
    .add(suplementacao)
    .subtract(anulacao);
  const cotas = valorLiberado.distribute(12);

  //Passar valores dos meses anteriores
  const anoAtual = obterAnoAtual();
  const mesAtual = resultPermissao.exercicio! < anoAtual ? 12 : obterMesAtual();
  if (mesAtual > 1) {
    for (let i = 0; i < mesAtual - 1; i++) {
      cotas[mesAtual - 1] = cotas[mesAtual - 1].add(cotas[i]);
      cotas[i] = currency(0, currencyOptionsNoSymbol);
    }
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.dotacoes.create({
        data: {
          exercicio: resultPermissao.exercicio!,
          despesa,
          desc,
          fonte,
          codAplicacao,
          funcionalId,
          economicaId,
          secretariaId,
          departamentoId,
          subdepartamentoId,
          valorInicial,
          cotaReducaoInicial,
          suplementacao,
          anulacao,
          cotaReducao: cotaReducaoInicial,
          valorLiberado: valorLiberado.value,
          valorAtual: valorLiberado.value,
          cotaMes1: cotas[0].value,
          cotaMes2: cotas[1].value,
          cotaMes3: cotas[2].value,
          cotaMes4: cotas[3].value,
          cotaMes5: cotas[4].value,
          cotaMes6: cotas[5].value,
          cotaMes7: cotas[6].value,
          cotaMes8: cotas[7].value,
          cotaMes9: cotas[8].value,
          cotaMes10: cotas[9].value,
          cotaMes11: cotas[10].value,
          cotaMes12: cotas[11].value,
        },
      });
      await tx.gerenciamentoDotacoes_audit.create({
        data: {
          idDotacao: result.id,
          acao: AuditoriaGerenciamentoDotacoes.CRIAR_DOTACAO,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });
    });
    revalidatePath('/gerenciamento/dotacoes');
  } catch (e) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Dotação já existe.`,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar dotação.`,
    };
  }
};

export const redistribuirCotas = async (params: unknown) => {
  const parsedParams = redistribuirCotasSchema.safeParse(params);

  if (!parsedParams.success) {
    return true;
  }

  const { dotacaoId, exercicio, despesa } = parsedParams.data;

  if (!dotacaoId && !despesa) {
    return true;
  }

  try {
    const anoAtual = obterAnoAtual();
    const mesAtual = exercicio < anoAtual ? 12 : obterMesAtual();

    if (mesAtual == 1) {
      return true;
    }

    await prisma.$transaction(async (tx) => {
      let result;
      if (dotacaoId) {
        result = await tx.dotacoes.findUniqueOrThrow({
          where: {
            id: dotacaoId,
          },
          select: {
            valorAtual: true,
            cotaMes1: true,
            cotaMes2: true,
            cotaMes3: true,
            cotaMes4: true,
            cotaMes5: true,
            cotaMes6: true,
            cotaMes7: true,
            cotaMes8: true,
            cotaMes9: true,
            cotaMes10: true,
            cotaMes11: true,
            cotaMes12: true,
          },
        });
      } else {
        if (despesa) {
          result = await tx.dotacoes.findUniqueOrThrow({
            where: {
              exercicio_despesa: {
                exercicio,
                despesa,
              },
            },
            select: {
              valorAtual: true,
              cotaMes1: true,
              cotaMes2: true,
              cotaMes3: true,
              cotaMes4: true,
              cotaMes5: true,
              cotaMes6: true,
              cotaMes7: true,
              cotaMes8: true,
              cotaMes9: true,
              cotaMes10: true,
              cotaMes11: true,
              cotaMes12: true,
            },
          });
        }
      }

      if (!result) {
        return true;
      }

      //Determinar se redistribuição é necessária
      let cancelar = true;
      for (let i = 1; i < mesAtual; i++) {
        if (result[`cotaMes${i}` as keyof typeof result].toNumber() > 0) {
          cancelar = false;
          break;
        }
      }
      if (cancelar) {
        return true;
      }

      //Colocar cotas em uma array e redistribuir
      const cotas = toCurrency(0).distribute(12);
      for (let i = 0; i < 12; i++) {
        if (i < mesAtual - 1) {
          // Passar valor se for mês anterior
          cotas[mesAtual - 1] = cotas[mesAtual - 1].add(
            toCurrency(result[`cotaMes${i + 1}` as keyof typeof result])
          );
        } else {
          cotas[i] = cotas[i].add(
            toCurrency(result[`cotaMes${i + 1}` as keyof typeof result])
          );
        }
      }

      //Testar
      const valorAtual = toCurrency(result.valorAtual);
      const valorCotas = cotas.reduce(
        (acc, curr) => acc.add(curr),
        currency(0, currencyOptionsNoSymbol)
      );
      if (valorCotas.value !== valorAtual.value) {
        throw new Error('Cotas não conferem');
      }

      await tx.dotacoes.update({
        where: {
          id: dotacaoId,
          exercicio: exercicio,
        },
        data: {
          cotaMes1: cotas[0].value,
          cotaMes2: cotas[1].value,
          cotaMes3: cotas[2].value,
          cotaMes4: cotas[3].value,
          cotaMes5: cotas[4].value,
          cotaMes6: cotas[5].value,
          cotaMes7: cotas[6].value,
          cotaMes8: cotas[7].value,
          cotaMes9: cotas[8].value,
          cotaMes10: cotas[9].value,
          cotaMes11: cotas[10].value,
          cotaMes12: cotas[11].value,
        },
      });
    });
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return false;
  }
};

export const alterarCotas = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = alterarCotasFormSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    id,
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const dotacao = await tx.dotacoes.findUniqueOrThrow({
        where: {
          id,
        },
        select: {
          valorAtual: true,
        },
      });

      const total = toCurrency(cotaMes1)
        .add(cotaMes2)
        .add(cotaMes3)
        .add(cotaMes4)
        .add(cotaMes5)
        .add(cotaMes6)
        .add(cotaMes7)
        .add(cotaMes8)
        .add(cotaMes9)
        .add(cotaMes10)
        .add(cotaMes11)
        .add(cotaMes12);

      if (total.value !== dotacao.valorAtual.toNumber()) {
        throw new Error('Soma das cotas não confere com o valor atual.');
      }

      const result = await tx.dotacoes.update({
        where: {
          id,
          exercicio: resultPermissao.exercicio!,
        },
        data: {
          cotaMes1,
          cotaMes2,
          cotaMes3,
          cotaMes4,
          cotaMes5,
          cotaMes6,
          cotaMes7,
          cotaMes8,
          cotaMes9,
          cotaMes10,
          cotaMes11,
          cotaMes12,
        },
      });
      await tx.gerenciamentoDotacoes_audit.create({
        data: {
          idDotacao: result.id,
          acao: AuditoriaGerenciamentoDotacoes.ALTERAR_COTAS,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });
    });

    revalidarCaminhosCotas(id);
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao alterar cotas dotação.`,
    };
  }
};

export const listarDepartamentosAtivos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const departamentos = await prisma.departamentos.findMany({
      orderBy: [{ secretaria: { codigo: 'asc' } }, { codigo: 'asc' }],
      where: { ativo: true },
      include: {
        secretaria: {
          select: {
            codigo: true,
          },
        },
      },
    });
    return {
      data: departamentos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter departamentos.`,
    };
  }
};

export const listarSecretariasAtivas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const secretarias = await prisma.secretarias.findMany({
      where: { ativo: true },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: secretarias,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter secretarias.`,
    };
  }
};

export const listarSubdepartamentosAtivos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const subdepartamentos = await prisma.subdepartamentos.findMany({
      where: { ativo: true },
      orderBy: [
        { departamento: { secretaria: { codigo: 'asc' } } },
        { departamento: { codigo: 'asc' } },
        { codigo: 'asc' },
      ],
      include: {
        departamento: {
          select: {
            codigo: true,
            secretaria: {
              select: {
                codigo: true,
              },
            },
          },
        },
      },
    });
    return {
      data: subdepartamentos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter subdepartamentos.`,
    };
  }
};

export const listarEconomicasAtivas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const economicas = await prisma.economicas.findMany({
      where: { ativo: true },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: economicas,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter economicas.`,
    };
  }
};

export const listarEconomicasDotacao = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const economicas = await prisma.economicas.findMany({
      where: { ativo: true, subelemento: 0 },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: economicas,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter economicas.`,
    };
  }
};

export const listarFuncionaisAtivas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const funcionais = await prisma.funcionais.findMany({
      where: { ativo: true },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: funcionais,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter funcionais.`,
    };
  }
};

export const reducaoParaDotacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = cotaReducaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    id,
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
    motivo,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const dotacao = await tx.dotacoes.findUniqueOrThrow({
        where: {
          id,
          exercicio: resultPermissao.exercicio!,
        },
        select: {
          valorAtual: true,
          cotaReducao: true,
          cotaMes1: true,
          cotaMes2: true,
          cotaMes3: true,
          cotaMes4: true,
          cotaMes5: true,
          cotaMes6: true,
          cotaMes7: true,
          cotaMes8: true,
          cotaMes9: true,
          cotaMes10: true,
          cotaMes11: true,
          cotaMes12: true,
        },
      });

      //Garantir integridade
      const aumentoTotal = toCurrency(cotaMes1)
        .add(cotaMes2)
        .add(cotaMes3)
        .add(cotaMes4)
        .add(cotaMes5)
        .add(cotaMes6)
        .add(cotaMes7)
        .add(cotaMes8)
        .add(cotaMes9)
        .add(cotaMes10)
        .add(cotaMes11)
        .add(cotaMes12);

      //Garantir que há dinheiro na cota de redução
      const cotaReducaoRestante = currency(
        dotacao.cotaReducao.toNumber(),
        currencyOptionsNoSymbol
      ).subtract(aumentoTotal);

      if (cotaReducaoRestante.value < 0) {
        throw new Error(ErrosDotacoes.REDUCAO_RESTANTE_MENOR_ZERO);
      }

      //Determinar o novo valor das cotas
      const cotasAumentadas = currency(0, currencyOptionsNoSymbol).distribute(
        12
      );

      for (let i = 1; i <= 12; i++) {
        cotasAumentadas[i - 1] = cotasAumentadas[i - 1]
          .add(dotacao[`cotaMes${i}` as keyof typeof dotacao].toNumber())
          .add(
            parsedParams.data[`cotaMes${i}` as keyof typeof parsedParams.data]
          );
      }

      //Validar que o novo valor das cotas ainda seja o mesmo
      const novoValorAtual = cotasAumentadas.reduce(
        (acc, curr) => acc.add(curr),
        currency(0, currencyOptionsNoSymbol)
      );

      if (
        currency(dotacao.valorAtual.toNumber(), currencyOptionsNoSymbol).add(
          aumentoTotal
        ).value !== novoValorAtual.value
      ) {
        throw new Error(ErrosDotacoes.FALHA_VALIDACAO);
      }

      const atualizarDotacao = await tx.dotacoes.update({
        where: {
          id,
          exercicio: resultPermissao.exercicio!,
        },
        data: {
          cotaMes1: cotasAumentadas[0].value,
          cotaMes2: cotasAumentadas[1].value,
          cotaMes3: cotasAumentadas[2].value,
          cotaMes4: cotasAumentadas[3].value,
          cotaMes5: cotasAumentadas[4].value,
          cotaMes6: cotasAumentadas[5].value,
          cotaMes7: cotasAumentadas[6].value,
          cotaMes8: cotasAumentadas[7].value,
          cotaMes9: cotasAumentadas[8].value,
          cotaMes10: cotasAumentadas[9].value,
          cotaMes11: cotasAumentadas[10].value,
          cotaMes12: cotasAumentadas[11].value,
          valorAtual: novoValorAtual.value,
          cotaReducao: cotaReducaoRestante.value,
        },
      });

      const dotacaoAuditPromise = tx.gerenciamentoDotacoes_audit.create({
        data: {
          idDotacao: atualizarDotacao.id,
          acao: AuditoriaGerenciamentoDotacoes.COTA_RED_PARA_DOTACAO,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      const criarCotaReducao = await tx.cotasReducao.create({
        data: {
          idDotacao: atualizarDotacao.id,
          motivo: motivo,
          reducao: false,
          valorMes1: cotaMes1,
          valorMes2: cotaMes2,
          valorMes3: cotaMes3,
          valorMes4: cotaMes4,
          valorMes5: cotaMes5,
          valorMes6: cotaMes6,
          valorMes7: cotaMes7,
          valorMes8: cotaMes8,
          valorMes9: cotaMes9,
          valorMes10: cotaMes10,
          valorMes11: cotaMes11,
          valorMes12: cotaMes12,
          valorTotal: aumentoTotal.value,
        },
      });

      const cotaReducaoAudit = await tx.cotasReducao_audit.create({
        data: {
          idCotaReducao: criarCotaReducao.id,
          acao: AuditoriaCotasReducao.COTA_RED_PARA_DOTACAO,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      const cotaReducaoAuditValoresPromises = cotasAumentadas.map(
        async (cotaRestante, index) => {
          if (
            dotacao[
              `cotaMes${index + 1}` as keyof typeof dotacao
            ].toNumber() === cotaRestante.value
          ) {
            return null;
          }
          return tx.cotasReducao_audit_valores.create({
            data: {
              idCotaReducaoAudit: cotaReducaoAudit.id,
              mes: index + 1,
              de: dotacao[
                `cotaMes${index + 1}` as keyof typeof dotacao
              ].toNumber(),
              para: cotaRestante.value,
            },
          });
        }
      );

      await Promise.all([
        ...cotaReducaoAuditValoresPromises,
        dotacaoAuditPromise,
      ]);
    });

    revalidarCaminhosCotas(id);
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosDotacoes).includes(e.message as ErrosDotacoes)) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao enviar cota redução para dotação.`,
    };
  }
};

export const dotacaoParaReducao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = cotaReducaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    id,
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
    motivo,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const dotacao = await tx.dotacoes.findUniqueOrThrow({
        where: {
          id,
          exercicio: resultPermissao.exercicio!,
        },
        select: {
          valorAtual: true,
          cotaReducao: true,
          cotaMes1: true,
          cotaMes2: true,
          cotaMes3: true,
          cotaMes4: true,
          cotaMes5: true,
          cotaMes6: true,
          cotaMes7: true,
          cotaMes8: true,
          cotaMes9: true,
          cotaMes10: true,
          cotaMes11: true,
          cotaMes12: true,
        },
      });

      //Garantir integridade
      const reducaoTotal = toCurrency(cotaMes1)
        .add(cotaMes2)
        .add(cotaMes3)
        .add(cotaMes4)
        .add(cotaMes5)
        .add(cotaMes6)
        .add(cotaMes7)
        .add(cotaMes8)
        .add(cotaMes9)
        .add(cotaMes10)
        .add(cotaMes11)
        .add(cotaMes12);

      if (reducaoTotal.value > dotacao.valorAtual.toNumber()) {
        throw new Error(ErrosDotacoes.REDUCAO_MAIOR_DOTACAO);
      }

      //Garantir que ainda há dinheiro na dotação
      const valorAtualRestante = currency(
        dotacao.valorAtual.toNumber(),
        currencyOptionsNoSymbol
      ).subtract(reducaoTotal);

      if (valorAtualRestante.value < 0) {
        throw new Error(ErrosDotacoes.REDUCAO_MAIOR_DOTACAO);
      }

      //Determinar o novo valor das cotas
      const cotasRestantes = currency(0, currencyOptionsNoSymbol).distribute(
        12
      );

      for (let i = 1; i <= 12; i++) {
        cotasRestantes[i - 1] = cotasRestantes[i - 1]
          .add(dotacao[`cotaMes${i}` as keyof typeof dotacao].toNumber())
          .subtract(
            parsedParams.data[`cotaMes${i}` as keyof typeof parsedParams.data]
          );
        if (cotasRestantes[i - 1].value < 0) {
          throw new Error(ErrosDotacoes.COTA_MENOR_ZERO);
        }
      }

      //Validar que o novo valor das cotas ainda seja o mesmo
      const novoValorAtual = cotasRestantes.reduce(
        (acc, curr) => acc.add(curr),
        currency(0, currencyOptionsNoSymbol)
      );

      if (
        currency(
          dotacao.valorAtual.toNumber(),
          currencyOptionsNoSymbol
        ).subtract(reducaoTotal).value !== novoValorAtual.value
      ) {
        throw new Error(ErrosDotacoes.FALHA_VALIDACAO);
      }

      const novoTotalCotaReducao = currency(
        dotacao.cotaReducao.toNumber(),
        currencyOptionsNoSymbol
      ).add(reducaoTotal);

      const atualizarDotacao = await tx.dotacoes.update({
        where: {
          id,
          exercicio: resultPermissao.exercicio!,
        },
        data: {
          cotaMes1: cotasRestantes[0].value,
          cotaMes2: cotasRestantes[1].value,
          cotaMes3: cotasRestantes[2].value,
          cotaMes4: cotasRestantes[3].value,
          cotaMes5: cotasRestantes[4].value,
          cotaMes6: cotasRestantes[5].value,
          cotaMes7: cotasRestantes[6].value,
          cotaMes8: cotasRestantes[7].value,
          cotaMes9: cotasRestantes[8].value,
          cotaMes10: cotasRestantes[9].value,
          cotaMes11: cotasRestantes[10].value,
          cotaMes12: cotasRestantes[11].value,
          valorAtual: novoValorAtual.value,
          cotaReducao: novoTotalCotaReducao.value,
        },
      });

      const dotacaoAuditPromise = tx.gerenciamentoDotacoes_audit.create({
        data: {
          idDotacao: atualizarDotacao.id,
          acao: AuditoriaGerenciamentoDotacoes.DOTACAO_PARA_COTA_RED,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      const criarCotaReducao = await tx.cotasReducao.create({
        data: {
          idDotacao: atualizarDotacao.id,
          motivo: motivo,
          reducao: true,
          valorMes1: cotaMes1,
          valorMes2: cotaMes2,
          valorMes3: cotaMes3,
          valorMes4: cotaMes4,
          valorMes5: cotaMes5,
          valorMes6: cotaMes6,
          valorMes7: cotaMes7,
          valorMes8: cotaMes8,
          valorMes9: cotaMes9,
          valorMes10: cotaMes10,
          valorMes11: cotaMes11,
          valorMes12: cotaMes12,
          valorTotal: reducaoTotal.value,
        },
      });

      const cotaReducaoAudit = await tx.cotasReducao_audit.create({
        data: {
          idCotaReducao: criarCotaReducao.id,
          acao: AuditoriaCotasReducao.COTA_RED_PARA_DOTACAO,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      const cotaReducaoAuditValoresPromises = cotasRestantes.map(
        async (cotaRestante, index) => {
          if (
            dotacao[
              `cotaMes${index + 1}` as keyof typeof dotacao
            ].toNumber() === cotaRestante.value
          ) {
            return null;
          }
          return tx.cotasReducao_audit_valores.create({
            data: {
              idCotaReducaoAudit: cotaReducaoAudit.id,
              mes: index + 1,
              de: dotacao[
                `cotaMes${index + 1}` as keyof typeof dotacao
              ].toNumber(),
              para: cotaRestante.value,
            },
          });
        }
      );

      await Promise.all([
        ...cotaReducaoAuditValoresPromises,
        dotacaoAuditPromise,
      ]);
    });

    revalidarCaminhosCotas(id);
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosDotacoes).includes(e.message as ErrosDotacoes)) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao enviar cota redução para dotação.`,
    };
  }
};

export const obterCotasReducao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const result = await prisma.cotasReducao.findMany({
      where: { idDotacao: id },
      select: {
        id: true,
        valorTotal: true,
        reducao: true,
        data: true,
        motivo: true,
      },
      orderBy: { id: 'desc' },
    });
    return {
      data: result.map((row) => {
        return {
          ...row,
          valorTotal: row.valorTotal.toNumber(),
        };
      }),
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter cotas de redução.`,
    };
  }
};

const revalidarCaminhosCotas = (idDotacao: number) => {
  revalidatePath(`/gerenciamento/dotacoes`);
  revalidatePath(`/gerenciamento/dotacoes/${idDotacao}/cotas`);
  revalidatePath(`/gerenciamento/dotacoes/${idDotacao}/cotas-de-reducao`);
  revalidatePath(
    `/gerenciamento/dotacoes/${idDotacao}/cotas-de-reducao/dotacao-para-reducao`
  );
  revalidatePath(
    `/gerenciamento/dotacoes/${idDotacao}/cotas-de-reducao/reducao-para-dotacao`
  );
  //TODO: revalidar outros caminhos que utilizam a mesma dotação
};
