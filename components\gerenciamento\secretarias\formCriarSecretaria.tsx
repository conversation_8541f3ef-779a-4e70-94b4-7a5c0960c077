'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { secretariaFormSchema, secretariaSchema } from '@/lib/validation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import { uppercaseMask, toastAlgoDeuErrado } from '@/lib/utils';
import CadastroDepsDatatable from '@/components/gerenciamento/secretarias/cadastroDepsDatatable';
import { Departamento, Subdepartamento } from '@/types/app';
import { FormAdicionarDep } from '@/components/gerenciamento/secretarias/formAdicionarDep';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { FormAdicionarSubDep } from './formAdicionarSubDep';
import { ComboboxSelecionaDepartamento } from './comboboxSelecionarDep';
import CadastroSubDepsDatatable from './cadastroSubDepsDatatable';
import { criarSecretaria } from '@/lib/database/gerenciamento/secretarias';
export default function FormCriarSecretaria() {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [depsConfigurados, setDepsConfigurados] = useState<Departamento[]>([]);
  const [ativarSubDeps, setAtivarSubDeps] = useState(false);
  const [codigoSecretaria, setCodigoSecretaria] = useState<number | null>(null);

  const [depSelecionado, setDepSelecionado] = useState<Departamento | null>(
    null
  );

  const form = useForm<z.infer<typeof secretariaFormSchema>>({
    resolver: zodResolver(secretariaFormSchema),
    defaultValues: {
      //@ts-ignore
      codigo: '',
      nome: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof secretariaFormSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof secretariaSchema> = {
        codigo: Number(values.codigo),
        nome: values.nome,
        departamentos: depsConfigurados,
      };
      const res = await criarSecretaria(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Secretaria criada.');
        router.push('/gerenciamento/secretarias');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  useEffect(() => {
    if (depsConfigurados.length === 0) {
      setAtivarSubDeps(false);
    }
  }, [depsConfigurados]);

  const adicionarSubDep = (codigoDep: number, subDep: Subdepartamento) => {
    const newDeps = [...depsConfigurados];
    const index = newDeps.findIndex((dep) => dep.codigo === codigoDep);
    if (index !== -1) {
      const newSubDeps = newDeps[index].subdepartamentos.filter(
        (dep) => dep.codigo !== subDep.codigo
      );
      newDeps[index].subdepartamentos = [subDep, ...newSubDeps];
    }
    setDepsConfigurados(newDeps);
  };

  const removerSubDep = (codigoDep: number, codigoSubDep: number) => {
    const newDeps = [...depsConfigurados];
    const index = newDeps.findIndex((dep) => dep.codigo === codigoDep);
    if (index !== -1) {
      const newSubDeps = newDeps[index].subdepartamentos.filter(
        (dep) => dep.codigo !== codigoSubDep
      );
      newDeps[index].subdepartamentos = newSubDeps;
    }
    setDepsConfigurados(newDeps);
  };

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex gap-4'>
              <FormField
                control={form.control}
                name='codigo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Código
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        pattern='[0-99]*'
                        placeholder='00'
                        maxLength={2}
                        className='w-[44px]'
                        onChange={(event) => {
                          const { value } = event.target;
                          setCodigoSecretaria(Number(value));
                          form.setValue('codigo', Number(value));
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='nome'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Nome da Secretaria
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={150}
                        minLength={2}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('nome', uppercaseMask(value));
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>
      <div className='grid w-full gap-4 py-8'>
        <span className='mt-4 text-lg'>Departamentos</span>
        <FormAdicionarDep
          data={depsConfigurados}
          setData={setDepsConfigurados}
        />
        <CadastroDepsDatatable
          data={depsConfigurados}
          setData={setDepsConfigurados}
          codigoSecretaria={codigoSecretaria}
        />
      </div>
      <div className='grid w-full gap-4 py-8'>
        <div className='flex items-center space-x-2'>
          <Switch
            id='ativar-subdeps'
            checked={ativarSubDeps}
            onCheckedChange={setAtivarSubDeps}
            disabled={depsConfigurados.length === 0}
          />
          <Label htmlFor="'ativar-subdeps" className='text-lg'>
            Criar Subdepartamentos
          </Label>
        </div>
        {ativarSubDeps && (
          <>
            <span className='mt-4 text-lg'>Subdepartamentos</span>

            <ComboboxSelecionaDepartamento
              departamentos={depsConfigurados}
              depSelecionado={depSelecionado}
              setDepSelecionado={setDepSelecionado}
            />
            <FormAdicionarSubDep
              adicionarSubDep={adicionarSubDep}
              depSelecionado={depSelecionado}
            />
            <CadastroSubDepsDatatable
              data={depsConfigurados}
              removerSubDep={removerSubDep}
              codSecretaria={codigoSecretaria || 0}
            />
          </>
        )}
      </div>

      <div className='flex w-full justify-between'>
        <Button
          variant={'destructive'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/gerenciamento/secretarias');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={loading || depsConfigurados.length === 0}
        >
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <PlusCircle className='mr-2 h-4 w-4' /> Criar Secretaria
            </>
          )}
        </Button>
      </div>
    </>
  );
}
