'use server';

import { prisma } from '@/lib/prisma';
import { <PERSON><PERSON><PERSON><PERSON>, StatusEmpenho, StatusLiquidacao } from '@/lib/enums';
import {
  permissaoSchema,
  relatorioEmpenhosSchema,
  relatorioEmpenhosPorPeriodoSchema,
} from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { Prisma } from '@prisma/client';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import z from 'zod';

export interface EmpenhoReportData {
  id: number;
  numero: number;
  exercicio: number;
  resumo: string | null;
  obs: string | null;
  fornecedor: FornecedorData | null;
  dotacao: DotacaoData;
  valorTotal: number;
  valorLiquidado: number;
  valorProcessado: number;
  valorNaoProcessado: number;
  saldoDisponivel: number;
  status: number;
  data: Date;
  dataLiquidacao: Date | null;
  liquidacoes: LiquidacaoReportData[];
  valoresMensais: MonthlyBreakdown[];
}

export interface FornecedorData {
  id: number;
  nome: string;
  tipo: 'FISICA' | 'JURIDICA';
  documento: string;
}

export interface DotacaoData {
  id: number;
  despesa: number;
  desc: string;
  valorAtual: number;
  secretaria: {
    id: number;
    nome: string;
  } | null;
  departamento: {
    id: number;
    nome: string;
  } | null;
  subdepartamento: {
    id: number;
    nome: string;
  } | null;
  economica: {
    id: number;
    codigo: string;
    desc: string;
  } | null;
  funcional: {
    id: number;
    codigo: string;
    desc: string;
  } | null;
}

export interface LiquidacaoReportData {
  id: number;
  numero: number;
  exercicio: number;
  valorTotal: number;
  dataLiquidacao: Date;
  mesReferencia: string | null;
  resumo: string | null;
  obs: string | null;
  empenhoOrigem: string;
  status: number;
}

export interface MonthlyBreakdown {
  mes: string;
  valorLiquidado: number;
  valorProcessado: number;
  valorNaoProcessado: number;
}

export interface EmpenhoReportSummary {
  totalEmpenhos: number;
  valorTotal: number;
  valorTotalLiquidado: number;
  valorTotalProcessado: number;
  valorTotalNaoProcessado: number;
  saldoTotalDisponivel: number;
}

export interface RelatorioEmpenhosParams {
  exercicio?: number;
  idSecretaria?: number;
  idDepartamento?: number;
  idSubdepartamento?: number;
  status?: number[];
  idFornecedor?: number;
  dataInicio?: Date;
  dataFim?: Date;
  incluirLiquidados?: boolean;
  incluirNaoLiquidados?: boolean;
  bearer?: string;
}

export const gerarRelatorioEmpenhos = async (
  params: RelatorioEmpenhosParams
) => {
  const validatedParams = relatorioEmpenhosSchema.safeParse(params);
  if (!validatedParams.success) {
    return { error: 'Parâmetros inválidos para o relatório de empenhos.' };
  }

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_EMPENHO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao({
    ...parametrosPermissao,
    bearer: validatedParams.data.bearer || '',
  });
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const where: Prisma.empenhosWhereInput = { ativo: true };

    if (validatedParams.data.exercicio)
      where.exercicio = validatedParams.data.exercicio;
    else if (resultPermissao.exercicio)
      where.exercicio = resultPermissao.exercicio;

    if (validatedParams.data.status && validatedParams.data.status.length > 0) {
      where.status = { in: validatedParams.data.status };
    }

    if (validatedParams.data.idFornecedor)
      where.idFornecedor = validatedParams.data.idFornecedor;

    if (validatedParams.data.dataInicio || validatedParams.data.dataFim) {
      where.data = {};
      if (validatedParams.data.dataInicio)
        where.data.gte = validatedParams.data.dataInicio;
      if (validatedParams.data.dataFim)
        where.data.lte = validatedParams.data.dataFim;
    }

    const empenhos = await prisma.empenhos.findMany({
      where,
      include: {
        fornecedor: {
          select: {
            id: true,
            nome: true,
            cnpjCpf: true,
          },
        },
        dotacao: {
          select: {
            id: true,
            despesa: true,
            desc: true,
            valorAtual: true,
            secretariaId: true,
            departamentoId: true,
            subdepartamentoId: true,
            economicaId: true,
            funcionalId: true,
            secretaria: {
              select: {
                id: true,
                nome: true,
              },
            },
            departamento: {
              select: {
                id: true,
                nome: true,
              },
            },
            subdepartamento: {
              select: {
                id: true,
                nome: true,
              },
            },
            economica: {
              select: {
                id: true,
                codigo: true,
                desc: true,
              },
            },
            funcional: {
              select: {
                id: true,
                codigo: true,
                desc: true,
              },
            },
          },
        },
        liquidacoes: {
          where: { ativo: true },
          orderBy: { data: 'asc' },
          select: {
            id: true,
            numero: true,
            exercicio: true,
            valorTotal: true,
            data: true,
            mesReferencia: true,
            resumo: true,
            obs: true,
            status: true,
          },
        },
        reserva: {
          select: {
            id: true,
            exercicio: true,
          },
        },
      },
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
    });

    // Filter by department hierarchy if specified
    let filteredEmpenhos = empenhos;
    if (
      validatedParams.data.idSecretaria ||
      validatedParams.data.idDepartamento ||
      validatedParams.data.idSubdepartamento
    ) {
      filteredEmpenhos = empenhos.filter((emp) => {
        if (!emp.dotacao) return false;

        if (
          validatedParams.data.idSecretaria &&
          emp.dotacao.secretariaId !== validatedParams.data.idSecretaria
        ) {
          return false;
        }

        if (
          validatedParams.data.idDepartamento &&
          emp.dotacao.departamentoId !== validatedParams.data.idDepartamento
        ) {
          return false;
        }

        if (
          validatedParams.data.idSubdepartamento &&
          emp.dotacao.subdepartamentoId !==
            validatedParams.data.idSubdepartamento
        ) {
          return false;
        }

        return true;
      });
    }

    // Filter by liquidation status if specified
    if (
      validatedParams.data.incluirLiquidados !== undefined ||
      validatedParams.data.incluirNaoLiquidados !== undefined
    ) {
      filteredEmpenhos = filteredEmpenhos.filter((emp) => {
        const temLiquidacoes = emp.liquidacoes.length > 0;
        const valorLiquidado = emp.liquidacoes.reduce(
          (sum, liq) => sum + liq.valorTotal.toNumber(),
          0
        );
        const totalmenteLiquidado = valorLiquidado >= emp.valorTotal.toNumber();

        if (
          validatedParams.data.incluirLiquidados &&
          validatedParams.data.incluirNaoLiquidados
        ) {
          return true; // Include both
        } else if (validatedParams.data.incluirLiquidados) {
          return temLiquidacoes || totalmenteLiquidado;
        } else if (validatedParams.data.incluirNaoLiquidados) {
          return !totalmenteLiquidado;
        }
        return true;
      });
    }

    // Check user access permissions
    if (!resultPermissao.gerente) {
      const acessos = await prisma.user_profiles_dotacoes.findMany({
        where: {
          uuidUsuario: await (async () => {
            const supabase = await createClientWithBearer(
              validatedParams.data.bearer || ''
            );
            const { data } = await supabase.auth.getUser();
            return data?.user?.id || undefined;
          })(),
        },
        select: {
          dotacoesIds: true,
          acessoTotal: true,
        },
      });

      // Check if user has total access
      const temAcessoTotal = acessos.some((a) => a.acessoTotal);

      // Get all accessible dotacoes IDs
      const dotacoesAcessiveis = temAcessoTotal
        ? [] // Empty array means all dotacoes are accessible when acessoTotal is true
        : acessos.flatMap((a) => a.dotacoesIds);
      filteredEmpenhos = filteredEmpenhos.filter(
        (emp) =>
          temAcessoTotal ||
          (emp.idDotacao && dotacoesAcessiveis.includes(emp.idDotacao))
      );
    }

    // Process data for report
    const reportData: EmpenhoReportData[] = filteredEmpenhos.map((emp) => {
      const valorTotal = emp.valorTotal.toNumber();
      const valorLiquidado = emp.liquidacoes.reduce(
        (sum, liq) =>
          currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal.toNumber())
            .value,
        0
      );

      // Calculate processed vs unprocessed based on liquidation status
      const valorProcessado = emp.liquidacoes
        .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
        .reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(
              liq.valorTotal.toNumber()
            ).value,
          0
        );

      const valorNaoProcessado = emp.liquidacoes
        .filter((liq) => liq.status === StatusLiquidacao.NAO_PROCESSADO)
        .reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(
              liq.valorTotal.toNumber()
            ).value,
          0
        );

      const saldoDisponivel = currency(
        valorTotal,
        currencyOptionsNoSymbol
      ).subtract(valorLiquidado).value;

      // Group liquidations by month
      const valoresMensais: MonthlyBreakdown[] = [];
      const meses = [
        'Jan',
        'Fev',
        'Mar',
        'Abr',
        'Mai',
        'Jun',
        'Jul',
        'Ago',
        'Set',
        'Out',
        'Nov',
        'Dez',
      ];

      meses.forEach((mes, index) => {
        const liquidacoesMes = emp.liquidacoes.filter((liq) => {
          const mesLiquidacao =
            liq.mesReferencia || new Date(liq.data).getMonth() + 1;
          return mesLiquidacao === index + 1;
        });

        const valorLiquidadoMes = liquidacoesMes
          .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
          .reduce(
            (sum, liq) =>
              currency(sum, currencyOptionsNoSymbol).add(
                liq.valorTotal.toNumber()
              ).value,
            0
          );

        const valorProcessadoMes = liquidacoesMes
          .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
          .reduce(
            (sum, liq) =>
              currency(sum, currencyOptionsNoSymbol).add(
                liq.valorTotal.toNumber()
              ).value,
            0
          );

        const valorNaoProcessadoMes = liquidacoesMes
          .filter((liq) => liq.status === StatusLiquidacao.NAO_PROCESSADO)
          .reduce(
            (sum, liq) =>
              currency(sum, currencyOptionsNoSymbol).add(
                liq.valorTotal.toNumber()
              ).value,
            0
          );

        if (
          valorLiquidadoMes > 0 ||
          valorProcessadoMes > 0 ||
          valorNaoProcessadoMes > 0
        ) {
          valoresMensais.push({
            mes,
            valorLiquidado: valorLiquidadoMes,
            valorProcessado: valorProcessadoMes,
            valorNaoProcessado: valorNaoProcessadoMes,
          });
        }
      });

      return {
        id: emp.id,
        numero: emp.numero,
        exercicio: emp.exercicio,
        resumo: emp.resumo,
        obs: emp.obs,
        fornecedor: emp.fornecedor
          ? {
              id: emp.fornecedor.id,
              nome: emp.fornecedor.nome,
              tipo:
                emp.fornecedor.cnpjCpf.length === 11 ? 'FISICA' : 'JURIDICA',
              documento: emp.fornecedor.cnpjCpf,
            }
          : null,
        dotacao: {
          id: emp.dotacao.id,
          despesa: emp.dotacao.despesa,
          desc: emp.dotacao.desc,
          valorAtual: emp.dotacao.valorAtual.toNumber(),
          secretaria: emp.dotacao.secretaria,
          departamento: emp.dotacao.departamento,
          subdepartamento: emp.dotacao.subdepartamento,
          economica: emp.dotacao.economica,
          funcional: emp.dotacao.funcional,
        },
        valorTotal,
        valorLiquidado,
        valorProcessado,
        valorNaoProcessado,
        saldoDisponivel,
        status: emp.status,
        data: emp.data,
        dataLiquidacao:
          emp.liquidacoes.length > 0
            ? emp.liquidacoes[emp.liquidacoes.length - 1].data
            : null,
        liquidacoes: emp.liquidacoes.map((liq) => ({
          id: liq.id,
          numero: liq.numero,
          exercicio: liq.exercicio,
          valorTotal: liq.valorTotal.toNumber(),
          dataLiquidacao: liq.data,
          mesReferencia: liq.mesReferencia,
          resumo: liq.resumo,
          obs: liq.obs,
          tipo: 'DIRETA' as const,
          empenhoOrigem: `${emp.numero}/${emp.exercicio}`,
          status: liq.status,
        })),
        valoresMensais,
      };
    });

    // Calculate summary statistics
    const summary: EmpenhoReportSummary = {
      totalEmpenhos: reportData.length,
      valorTotal: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.valorTotal).value,
        0
      ),
      valorTotalLiquidado: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.valorLiquidado).value,
        0
      ),
      valorTotalProcessado: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.valorProcessado).value,
        0
      ),
      valorTotalNaoProcessado: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.valorNaoProcessado)
            .value,
        0
      ),
      saldoTotalDisponivel: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.saldoDisponivel).value,
        0
      ),
    };

    return {
      data: {
        empenhos: reportData,
        resumo: summary,
      },
    };
  } catch (e) {
    console.error('Erro ao gerar relatório de empenhos:', e);
    return { error: 'Erro ao gerar relatório de empenhos.' };
  }
};

export const gerarRelatorioEmpenhosLiquidados = async (
  params: RelatorioEmpenhosParams
) => {
  const validatedParams = relatorioEmpenhosSchema.safeParse(params);
  if (!validatedParams.success) {
    return {
      error: 'Parâmetros inválidos para o relatório de empenhos liquidados.',
    };
  }

  return await gerarRelatorioEmpenhos({
    ...validatedParams.data,
    incluirLiquidados: true,
    incluirNaoLiquidados: false,
  });
};

export const gerarRelatorioSaldoEmpenhosNaoProcessados = async (
  params: RelatorioEmpenhosParams
) => {
  const validatedParams = relatorioEmpenhosSchema.safeParse(params);
  if (!validatedParams.success) {
    return {
      error:
        'Parâmetros inválidos para o relatório de saldo de empenhos não processados.',
    };
  }

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_EMPENHO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao({
    ...parametrosPermissao,
    bearer: validatedParams.data.bearer || '',
  });
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const where: Prisma.empenhosWhereInput = {
      ativo: true,
      status: { not: StatusEmpenho.CANCELADO },
    };

    if (validatedParams.data.exercicio)
      where.exercicio = validatedParams.data.exercicio;
    else if (resultPermissao.exercicio)
      where.exercicio = resultPermissao.exercicio;

    if (validatedParams.data.idFornecedor)
      where.idFornecedor = validatedParams.data.idFornecedor;

    if (validatedParams.data.dataInicio || validatedParams.data.dataFim) {
      where.data = {};
      if (validatedParams.data.dataInicio)
        where.data.gte = validatedParams.data.dataInicio;
      if (validatedParams.data.dataFim)
        where.data.lte = validatedParams.data.dataFim;
    }

    const empenhos = await prisma.empenhos.findMany({
      where,
      include: {
        fornecedor: {
          select: {
            id: true,
            nome: true,
            cnpjCpf: true,
          },
        },
        dotacao: {
          select: {
            id: true,
            despesa: true,
            desc: true,
            valorAtual: true,
            secretariaId: true,
            departamentoId: true,
            subdepartamentoId: true,
            economicaId: true,
            funcionalId: true,
            secretaria: {
              select: {
                id: true,
                nome: true,
              },
            },
            departamento: {
              select: {
                id: true,
                nome: true,
              },
            },
            subdepartamento: {
              select: {
                id: true,
                nome: true,
              },
            },
            economica: {
              select: {
                id: true,
                codigo: true,
                desc: true,
              },
            },
            funcional: {
              select: {
                id: true,
                codigo: true,
                desc: true,
              },
            },
          },
        },
        liquidacoes: {
          where: {
            ativo: true,
            status: StatusLiquidacao.NAO_PROCESSADO,
          },
          select: {
            id: true,
            numero: true,
            exercicio: true,
            valorTotal: true,
            data: true,
            mesReferencia: true,
            resumo: true,
            obs: true,
          },
        },
      },
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
    });

    // Filter by department hierarchy if specified
    let filteredEmpenhos = empenhos;
    if (
      validatedParams.data.idSecretaria ||
      validatedParams.data.idDepartamento ||
      validatedParams.data.idSubdepartamento
    ) {
      filteredEmpenhos = empenhos.filter((emp) => {
        if (!emp.dotacao) return false;

        if (
          validatedParams.data.idSecretaria &&
          emp.dotacao.secretariaId !== validatedParams.data.idSecretaria
        ) {
          return false;
        }

        if (
          validatedParams.data.idDepartamento &&
          emp.dotacao.departamentoId !== validatedParams.data.idDepartamento
        ) {
          return false;
        }

        if (
          validatedParams.data.idSubdepartamento &&
          emp.dotacao.subdepartamentoId !==
            validatedParams.data.idSubdepartamento
        ) {
          return false;
        }

        return true;
      });
    }

    // Check user access permissions
    if (!resultPermissao.gerente) {
      const acessos = await prisma.user_profiles_dotacoes.findMany({
        where: {
          uuidUsuario: await (async () => {
            const supabase = await createClientWithBearer(
              validatedParams.data.bearer || ''
            );
            const { data } = await supabase.auth.getUser();
            return data?.user?.id || undefined;
          })(),
        },
        select: {
          dotacoesIds: true,
          acessoTotal: true,
        },
      });

      // Check if user has total access
      const temAcessoTotal = acessos.some((a) => a.acessoTotal);

      // Get all accessible dotacoes IDs
      const dotacoesAcessiveis = temAcessoTotal
        ? [] // Empty array means all dotacoes are accessible when acessoTotal is true
        : acessos.flatMap((a) => a.dotacoesIds);
      filteredEmpenhos = filteredEmpenhos.filter(
        (emp) =>
          temAcessoTotal ||
          (emp.idDotacao && dotacoesAcessiveis.includes(emp.idDotacao))
      );
    }

    // Only include empenhos with unprocessed liquidations
    const empenhosComSaldoNaoProcessado = filteredEmpenhos.filter(
      (emp) => emp.liquidacoes.length > 0
    );

    const reportData: EmpenhoReportData[] = empenhosComSaldoNaoProcessado.map(
      (emp) => {
        const valorTotal = emp.valorTotal.toNumber();
        const valorNaoProcessado = emp.liquidacoes.reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(
              liq.valorTotal.toNumber()
            ).value,
          0
        );

        return {
          id: emp.id,
          numero: emp.numero,
          exercicio: emp.exercicio,
          resumo: emp.resumo,
          obs: emp.obs,
          fornecedor: emp.fornecedor
            ? {
                id: emp.fornecedor.id,
                nome: emp.fornecedor.nome,
                tipo:
                  emp.fornecedor.cnpjCpf.length === 11 ? 'FISICA' : 'JURIDICA',
                documento: emp.fornecedor.cnpjCpf,
              }
            : null,
          dotacao: {
            id: emp.dotacao.id,
            despesa: emp.dotacao.despesa,
            desc: emp.dotacao.desc,
            valorAtual: emp.dotacao.valorAtual.toNumber(),
            secretaria: emp.dotacao.secretaria,
            departamento: emp.dotacao.departamento,
            subdepartamento: emp.dotacao.subdepartamento,
            economica: emp.dotacao.economica,
            funcional: emp.dotacao.funcional,
          },
          valorTotal,
          valorLiquidado: 0,
          valorProcessado: 0,
          valorNaoProcessado,
          saldoDisponivel: valorTotal,
          status: emp.status,
          data: emp.data,
          dataLiquidacao: null,
          liquidacoes: emp.liquidacoes.map((liq) => ({
            id: liq.id,
            numero: liq.numero,
            exercicio: liq.exercicio,
            valorTotal: liq.valorTotal.toNumber(),
            dataLiquidacao: liq.data,
            mesReferencia: liq.mesReferencia,
            resumo: liq.resumo,
            obs: liq.obs,
            tipo: 'DIRETA' as const,
            empenhoOrigem: `${emp.numero}/${emp.exercicio}`,
            status: StatusLiquidacao.NAO_PROCESSADO,
          })),
          valoresMensais: [],
        };
      }
    );

    const summary: EmpenhoReportSummary = {
      totalEmpenhos: reportData.length,
      valorTotal: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.valorTotal).value,
        0
      ),
      valorTotalLiquidado: 0,
      valorTotalProcessado: 0,
      valorTotalNaoProcessado: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.valorNaoProcessado)
            .value,
        0
      ),
      saldoTotalDisponivel: reportData.reduce(
        (sum, emp) =>
          currency(sum, currencyOptionsNoSymbol).add(emp.saldoDisponivel).value,
        0
      ),
    };

    return {
      data: {
        empenhos: reportData,
        resumo: summary,
      },
    };
  } catch (e) {
    console.error(
      'Erro ao gerar relatório de saldo de empenhos não processados:',
      e
    );
    return {
      error: 'Erro ao gerar relatório de saldo de empenhos não processados.',
    };
  }
};

export const obterEmpenhoParaRelatorio = async (params: {
  id: number;
  bearer: string;
}) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_EMPENHO,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao({
    ...parametrosPermissao,
    bearer: params.bearer,
  });
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const empenho = await prisma.empenhos.findFirst({
      where: {
        id: params.id,
        ativo: true,
      },
      include: {
        fornecedor: {
          select: {
            id: true,
            nome: true,
            cnpjCpf: true,
          },
        },
        dotacao: {
          select: {
            id: true,
            despesa: true,
            desc: true,
            valorAtual: true,
            secretariaId: true,
            departamentoId: true,
            subdepartamentoId: true,
            economicaId: true,
            funcionalId: true,
            secretaria: {
              select: {
                id: true,
                nome: true,
              },
            },
            departamento: {
              select: {
                id: true,
                nome: true,
              },
            },
            subdepartamento: {
              select: {
                id: true,
                nome: true,
              },
            },
            economica: {
              select: {
                id: true,
                codigo: true,
                desc: true,
              },
            },
            funcional: {
              select: {
                id: true,
                codigo: true,
                desc: true,
              },
            },
          },
        },
        liquidacoes: {
          where: { ativo: true },
          orderBy: { data: 'asc' },
          select: {
            id: true,
            numero: true,
            exercicio: true,
            valorTotal: true,
            data: true,
            mesReferencia: true,
            resumo: true,
            obs: true,
            status: true,
          },
        },
        reserva: {
          select: {
            id: true,
            exercicio: true,
          },
        },
        afs: {
          where: { ativo: true },
          select: {
            id: true,
            numero: true,
            exercicio: true,
            valorTotal: true,
            data: true,
            status: true,
          },
        },
      },
    });

    if (!empenho) {
      return { error: 'Empenho não encontrado.' };
    }

    // Check user access permissions
    if (!resultPermissao.gerente && empenho.idDotacao) {
      const acessos = await prisma.user_profiles_dotacoes.findMany({
        where: {
          uuidUsuario: await (async () => {
            const supabase = await createClientWithBearer(params.bearer);
            const { data } = await supabase.auth.getUser();
            return data?.user?.id || undefined;
          })(),
        },
        select: {
          dotacoesIds: true,
          acessoTotal: true,
        },
      });

      const temAcessoTotal = acessos.some((a) => a.acessoTotal);
      const dotacoesAcessiveis = acessos.flatMap((a) => a.dotacoesIds);

      if (!temAcessoTotal && !dotacoesAcessiveis.includes(empenho.idDotacao)) {
        return { error: 'Usuário não tem acesso a esta dotação.' };
      }
    }

    const valorTotal = empenho.valorTotal.toNumber();
    const valorLiquidado = empenho.liquidacoes.reduce(
      (sum, liq) =>
        currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal.toNumber())
          .value,
      0
    );

    const valorProcessado = empenho.liquidacoes
      .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
      .reduce(
        (sum, liq) =>
          currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal.toNumber())
            .value,
        0
      );

    const valorNaoProcessado = empenho.liquidacoes
      .filter((liq) => liq.status === StatusLiquidacao.NAO_PROCESSADO)
      .reduce(
        (sum, liq) =>
          currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal.toNumber())
            .value,
        0
      );

    const saldoDisponivel = currency(
      valorTotal,
      currencyOptionsNoSymbol
    ).subtract(valorLiquidado).value;

    // Group liquidations by month
    const valoresMensais: MonthlyBreakdown[] = [];
    const meses = [
      'Jan',
      'Fev',
      'Mar',
      'Abr',
      'Mai',
      'Jun',
      'Jul',
      'Ago',
      'Set',
      'Out',
      'Nov',
      'Dez',
    ];

    meses.forEach((mes, index) => {
      const liquidacoesMes = empenho.liquidacoes.filter((liq) => {
        const mesLiquidacao =
          liq.mesReferencia || new Date(liq.data).getMonth() + 1;
        return mesLiquidacao === index + 1;
      });

      const valorLiquidadoMes = liquidacoesMes
        .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
        .reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(
              liq.valorTotal.toNumber()
            ).value,
          0
        );

      const valorProcessadoMes = liquidacoesMes
        .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
        .reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(
              liq.valorTotal.toNumber()
            ).value,
          0
        );

      const valorNaoProcessadoMes = liquidacoesMes
        .filter((liq) => liq.status === StatusLiquidacao.NAO_PROCESSADO)
        .reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(
              liq.valorTotal.toNumber()
            ).value,
          0
        );

      if (
        valorLiquidadoMes > 0 ||
        valorProcessadoMes > 0 ||
        valorNaoProcessadoMes > 0
      ) {
        valoresMensais.push({
          mes,
          valorLiquidado: valorLiquidadoMes,
          valorProcessado: valorProcessadoMes,
          valorNaoProcessado: valorNaoProcessadoMes,
        });
      }
    });

    const empenhoData: EmpenhoReportData = {
      id: empenho.id,
      numero: empenho.numero,
      exercicio: empenho.exercicio,
      resumo: empenho.resumo,
      obs: empenho.obs,
      fornecedor: empenho.fornecedor
        ? {
            id: empenho.fornecedor.id,
            nome: empenho.fornecedor.nome,
            tipo:
              empenho.fornecedor.cnpjCpf.length === 11 ? 'FISICA' : 'JURIDICA',
            documento: empenho.fornecedor.cnpjCpf,
          }
        : null,
      dotacao: {
        id: empenho.dotacao.id,
        despesa: empenho.dotacao.despesa,
        desc: empenho.dotacao.desc,
        valorAtual: empenho.dotacao.valorAtual.toNumber(),
        secretaria: empenho.dotacao.secretaria,
        departamento: empenho.dotacao.departamento,
        subdepartamento: empenho.dotacao.subdepartamento,
        economica: empenho.dotacao.economica,
        funcional: empenho.dotacao.funcional,
      },
      valorTotal,
      valorLiquidado,
      valorProcessado,
      valorNaoProcessado,
      saldoDisponivel,
      status: empenho.status,
      data: empenho.data,
      dataLiquidacao:
        empenho.liquidacoes.length > 0
          ? empenho.liquidacoes[empenho.liquidacoes.length - 1].data
          : null,
      liquidacoes: empenho.liquidacoes.map((liq) => ({
        id: liq.id,
        numero: liq.numero,
        exercicio: liq.exercicio,
        valorTotal: liq.valorTotal.toNumber(),
        dataLiquidacao: liq.data,
        mesReferencia: liq.mesReferencia,
        resumo: liq.resumo,
        obs: liq.obs,
        empenhoOrigem: `${empenho.numero}/${empenho.exercicio}`,
        status: liq.status,
      })),
      valoresMensais,
    };

    return {
      data: empenhoData,
    };
  } catch (e) {
    console.error('Erro ao obter empenho para relatório:', e);
    return { error: 'Erro ao obter empenho para relatório.' };
  }
};

export const gerarRelatorioEmpenhosPorPeriodo = async (
  params: RelatorioEmpenhosParams & {
    periodoInicio: Date;
    periodoFim: Date;
    agruparPor: 'mes' | 'secretaria' | 'departamento' | 'fornecedor';
  }
) => {
  const validatedParams = relatorioEmpenhosPorPeriodoSchema.safeParse(params);
  if (!validatedParams.success) {
    return {
      error: 'Parâmetros inválidos para o relatório de empenhos por período.',
    };
  }

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_EMPENHO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao({
    ...parametrosPermissao,
    bearer: validatedParams.data.bearer || '',
  });
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const where: Prisma.empenhosWhereInput = {
      ativo: true,
      data: {
        gte: validatedParams.data.periodoInicio,
        lte: validatedParams.data.periodoFim,
      },
    };

    if (validatedParams.data.exercicio)
      where.exercicio = validatedParams.data.exercicio;
    else if (resultPermissao.exercicio)
      where.exercicio = resultPermissao.exercicio;

    if (validatedParams.data.status && validatedParams.data.status.length > 0) {
      where.status = { in: validatedParams.data.status };
    }

    if (validatedParams.data.idFornecedor)
      where.idFornecedor = validatedParams.data.idFornecedor;

    const empenhos = await prisma.empenhos.findMany({
      where,
      include: {
        fornecedor: {
          select: {
            id: true,
            nome: true,
            cnpjCpf: true,
          },
        },
        dotacao: {
          select: {
            id: true,
            despesa: true,
            desc: true,
            valorAtual: true,
            secretariaId: true,
            departamentoId: true,
            subdepartamentoId: true,
            secretaria: {
              select: {
                id: true,
                nome: true,
              },
            },
            departamento: {
              select: {
                id: true,
                nome: true,
              },
            },
            subdepartamento: {
              select: {
                id: true,
                nome: true,
              },
            },
          },
        },
        liquidacoes: {
          where: { ativo: true },
          select: {
            id: true,
            valorTotal: true,
            data: true,
            status: true,
          },
        },
      },
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
    });

    // Filter by department hierarchy if specified
    let filteredEmpenhos = empenhos;
    if (
      validatedParams.data.idSecretaria ||
      validatedParams.data.idDepartamento ||
      validatedParams.data.idSubdepartamento
    ) {
      filteredEmpenhos = empenhos.filter((emp) => {
        if (!emp.dotacao) return false;

        if (
          validatedParams.data.idSecretaria &&
          emp.dotacao.secretariaId !== validatedParams.data.idSecretaria
        ) {
          return false;
        }

        if (
          validatedParams.data.idDepartamento &&
          emp.dotacao.departamentoId !== validatedParams.data.idDepartamento
        ) {
          return false;
        }

        if (
          validatedParams.data.idSubdepartamento &&
          emp.dotacao.subdepartamentoId !==
            validatedParams.data.idSubdepartamento
        ) {
          return false;
        }

        return true;
      });
    }

    // Check user access permissions
    if (!resultPermissao.gerente) {
      const acessos = await prisma.user_profiles_dotacoes.findMany({
        where: {
          uuidUsuario: await (async () => {
            const supabase = await createClientWithBearer(
              validatedParams.data.bearer || ''
            );
            const { data } = await supabase.auth.getUser();
            return data?.user?.id || undefined;
          })(),
        },
        select: {
          dotacoesIds: true,
          acessoTotal: true,
        },
      });

      // Check if user has total access
      const temAcessoTotal = acessos.some((a) => a.acessoTotal);

      // Get all accessible dotacoes IDs
      const dotacoesAcessiveis = temAcessoTotal
        ? [] // Empty array means all dotacoes are accessible when acessoTotal is true
        : acessos.flatMap((a) => a.dotacoesIds);
      filteredEmpenhos = filteredEmpenhos.filter(
        (emp) =>
          temAcessoTotal ||
          (emp.idDotacao && dotacoesAcessiveis.includes(emp.idDotacao))
      );
    }

    // Group by specified criteria
    const grupos = new Map<string, EmpenhoReportData[]>();

    filteredEmpenhos.forEach((emp) => {
      let chave: string;

      switch (validatedParams.data.agruparPor) {
        case 'mes':
          chave = new Date(emp.data).toLocaleDateString('pt-BR', {
            month: 'long',
            year: 'numeric',
          });
          break;
        case 'secretaria':
          chave = emp.dotacao?.secretaria?.nome || 'Sem Secretaria';
          break;
        case 'departamento':
          chave = emp.dotacao?.departamento?.nome || 'Sem Departamento';
          break;
        case 'fornecedor':
          chave = emp.fornecedor?.nome || 'Sem Fornecedor';
          break;
        default:
          chave = 'Todos';
      }

      if (!grupos.has(chave)) {
        grupos.set(chave, []);
      }

      const valorTotal = emp.valorTotal.toNumber();
      const valorLiquidado = emp.liquidacoes.reduce(
        (sum, liq) => sum + liq.valorTotal.toNumber(),
        0
      );
      const valorProcessado = emp.liquidacoes
        .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
        .reduce((sum, liq) => sum + liq.valorTotal.toNumber(), 0);
      const valorNaoProcessado = emp.liquidacoes
        .filter((liq) => liq.status === StatusLiquidacao.NAO_PROCESSADO)
        .reduce((sum, liq) => sum + liq.valorTotal.toNumber(), 0);

      grupos.get(chave)!.push({
        id: emp.id,
        numero: emp.numero,
        exercicio: emp.exercicio,
        resumo: emp.resumo,
        obs: emp.obs,
        fornecedor: emp.fornecedor
          ? {
              id: emp.fornecedor.id,
              nome: emp.fornecedor.nome,
              tipo:
                emp.fornecedor.cnpjCpf.length === 11 ? 'FISICA' : 'JURIDICA',
              documento: emp.fornecedor.cnpjCpf,
            }
          : null,
        dotacao: {
          id: emp.dotacao?.id || 0,
          despesa: emp.dotacao?.despesa || 0,
          desc: emp.dotacao?.desc || '',
          valorAtual: emp.dotacao?.valorAtual?.toNumber() || 0,
          secretaria: emp.dotacao?.secretaria,
          departamento: emp.dotacao?.departamento,
          subdepartamento: emp.dotacao?.subdepartamento,
          economica: null,
          funcional: null,
        },
        valorTotal,
        valorLiquidado,
        valorProcessado,
        valorNaoProcessado,
        saldoDisponivel: valorTotal - valorLiquidado,
        status: emp.status,
        data: emp.data,
        dataLiquidacao:
          emp.liquidacoes.length > 0
            ? emp.liquidacoes[emp.liquidacoes.length - 1].data
            : null,
        liquidacoes: [],
        valoresMensais: [],
      });
    });

    // Calculate summaries for each group
    const gruposComResumo = Array.from(grupos.entries()).map(
      ([chave, empenhos]) => {
        const resumo: EmpenhoReportSummary = {
          totalEmpenhos: empenhos.length,
          valorTotal: empenhos.reduce(
            (sum, emp) =>
              currency(sum, currencyOptionsNoSymbol).add(emp.valorTotal).value,
            0
          ),
          valorTotalLiquidado: empenhos.reduce(
            (sum, emp) =>
              currency(sum, currencyOptionsNoSymbol).add(emp.valorLiquidado)
                .value,
            0
          ),
          valorTotalProcessado: empenhos.reduce(
            (sum, emp) =>
              currency(sum, currencyOptionsNoSymbol).add(emp.valorProcessado)
                .value,
            0
          ),
          valorTotalNaoProcessado: empenhos.reduce(
            (sum, emp) =>
              currency(sum, currencyOptionsNoSymbol).add(emp.valorNaoProcessado)
                .value,
            0
          ),
          saldoTotalDisponivel: empenhos.reduce(
            (sum, emp) =>
              currency(sum, currencyOptionsNoSymbol).add(emp.saldoDisponivel)
                .value,
            0
          ),
        };

        return {
          chave,
          empenhos,
          resumo,
        };
      }
    );

    const totalGeral: EmpenhoReportSummary = {
      totalEmpenhos: filteredEmpenhos.length,
      valorTotal: gruposComResumo.reduce(
        (sum, grupo) =>
          currency(sum, currencyOptionsNoSymbol).add(grupo.resumo.valorTotal)
            .value,
        0
      ),
      valorTotalLiquidado: gruposComResumo.reduce(
        (sum, grupo) =>
          currency(sum, currencyOptionsNoSymbol).add(
            grupo.resumo.valorTotalLiquidado
          ).value,
        0
      ),
      valorTotalProcessado: gruposComResumo.reduce(
        (sum, grupo) =>
          currency(sum, currencyOptionsNoSymbol).add(
            grupo.resumo.valorTotalProcessado
          ).value,
        0
      ),
      valorTotalNaoProcessado: gruposComResumo.reduce(
        (sum, grupo) =>
          currency(sum, currencyOptionsNoSymbol).add(
            grupo.resumo.valorTotalNaoProcessado
          ).value,
        0
      ),
      saldoTotalDisponivel: gruposComResumo.reduce(
        (sum, grupo) =>
          currency(sum, currencyOptionsNoSymbol).add(
            grupo.resumo.saldoTotalDisponivel
          ).value,
        0
      ),
    };

    return {
      data: {
        grupos: gruposComResumo,
        totalGeral,
        agrupadoPor: validatedParams.data.agruparPor,
        periodo: {
          inicio: validatedParams.data.periodoInicio,
          fim: validatedParams.data.periodoFim,
        },
      },
    };
  } catch (e) {
    console.error('Erro ao gerar relatório de empenhos por período:', e);
    return { error: 'Erro ao gerar relatório de empenhos por período.' };
  }
};
