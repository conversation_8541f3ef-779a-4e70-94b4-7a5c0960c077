'use server';
import { z } from 'zod';
import { ensureAuth } from '@/lib/supabase/actions';
import { Error<PERSON>lert } from '@/components/error-alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { RelatorioDespesas } from '@/components/relatorios/despesas';
import { obterDotacoesParaRelatorio } from '@/lib/database/relatorios/dotacoes';
import {
  obterRelatorioDespesa,
  obterRelatorioDespesaPessoal,
} from '@/lib/database/relatorios/despesas';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

async function RelatorioDespesasContent() {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const result = await temPermissao({ ...parametrosPermissao, bearer: '' });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  // Obter dados para todos os tipos de relatório
  const params = {
    exercicio: result.exercicio,
    bearer: '',
  };

  const [dotacoes, despesas, despesaPessoal] = await Promise.all([
    obterDotacoesParaRelatorio(params),
    obterRelatorioDespesa({ ...params, tipo: '1' }),
    obterRelatorioDespesaPessoal(params),
  ]);

  if (dotacoes.error) {
    return <ErrorAlert error={dotacoes.error} />;
  }

  const filtros = {
    exercicio: params.exercicio || result.exercicio,
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Relatório de Despesas
          </h1>
          <p className='text-muted-foreground'>
            Visualize e exporte relatórios detalhados de despesas, incluindo
            empenhos, reservas e despesas pessoais
          </p>
        </div>
        <div className='flex gap-4'>
          <div className='flex flex-wrap justify-end gap-2'>
            <BotaoDownloadPDF
              url='/gerenciamento/despesas/imprimir'
              title='Imprimir Dotações'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
                tipo: 'detalhado',
              }}
            />
            <BotaoDownloadPDF
              url='/gerenciamento/despesas/imprimir'
              title='Imprimir Resumido'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
                tipo: 'resumido',
              }}
            />
            <BotaoDownloadPDF
              url='/gerenciamento/despesas/imprimir'
              title='Imprimir Despesas'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
                tipo: 'despesa-detalhado',
              }}
            />
            <BotaoDownloadPDF
              url='/gerenciamento/despesas/imprimir'
              title='Imprimir Valores'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
                tipo: 'valores',
              }}
            />
            <BotaoDownloadPDF
              url='/gerenciamento/despesas/imprimir'
              title='Imprimir Pessoal'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
                tipo: 'pessoal',
              }}
            />
          </div>
        </div>
      </div>

      <div className='rounded-lg bg-white p-6 shadow'>
        <Tabs defaultValue='dotacoes' className='w-full'>
          <TabsList className='grid w-full grid-cols-4'>
            <TabsTrigger value='dotacoes'>Dotações</TabsTrigger>
            <TabsTrigger value='despesas'>Despesas</TabsTrigger>
            <TabsTrigger value='valores'>Valores</TabsTrigger>
            <TabsTrigger value='pessoal'>Pessoal</TabsTrigger>
          </TabsList>

          <TabsContent value='dotacoes' className='space-y-4'>
            <div className='pt-4'>
              <h3 className='text-lg font-semibold'>Dotações Orçamentárias</h3>
              <p className='mb-4 text-sm text-gray-600'>
                Relatório detalhado de dotações orçamentárias por departamento e
                secretaria
              </p>
              <RelatorioDespesas
                dotacoes={dotacoes.data}
                titulo='Relatório de Dotações Orçamentárias'
                filtros={filtros}
                tipo='detalhado'
              />
            </div>
          </TabsContent>

          <TabsContent value='despesas' className='space-y-4'>
            <div className='pt-4'>
              <h3 className='text-lg font-semibold'>Despesas Detalhadas</h3>
              <p className='mb-4 text-sm text-gray-600'>
                Este relatório mostra todos os empenhos, reservas,
                transferências e estornos com detalhamento mensal
              </p>
              {!('error' in despesas) && 'data' in despesas && despesas.data ? (
                <RelatorioDespesas
                  despesas={despesas.data as any[]}
                  titulo='Relatório Detalhado de Despesas'
                  filtros={filtros}
                  tipo='despesa-detalhado'
                />
              ) : (
                <div className='rounded-lg bg-red-50 p-4'>
                  <p className='text-red-700'>
                    Erro ao carregar relatório de despesas:{' '}
                    {'error' in despesas ? despesas.error : 'Desconhecido'}
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value='valores' className='space-y-4'>
            <div className='pt-4'>
              <h3 className='text-lg font-semibold'>Valores e Métricas</h3>
              <p className='mb-4 text-sm text-gray-600'>
                Relatório com valores brutos para análise e processamento
              </p>
              {!('error' in despesas) && 'data' in despesas && despesas.data ? (
                <RelatorioDespesas
                  despesas={despesas.data as any[]}
                  titulo='Relatório de Valores'
                  filtros={filtros}
                  tipo='valores'
                />
              ) : (
                <div className='rounded-lg bg-red-50 p-4'>
                  <p className='text-red-700'>
                    Erro ao carregar relatório de valores:{' '}
                    {'error' in despesas ? despesas.error : 'Desconhecido'}
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value='pessoal' className='space-y-4'>
            <div className='pt-4'>
              <h3 className='text-lg font-semibold'>Despesa Pessoal</h3>
              <p className='mb-4 text-sm text-gray-600'>
                Relatório de despesas com códigos econômicos 3.1.90% e 3.1.91%
                (despesas com pessoal)
              </p>
              {!('error' in despesaPessoal) &&
              'data' in despesaPessoal &&
              despesaPessoal.data &&
              despesaPessoal.data.length > 0 ? (
                <RelatorioDespesas
                  despesaPessoal={despesaPessoal.data}
                  titulo='Relatório de Despesa Pessoal'
                  filtros={filtros}
                  tipo='pessoal'
                />
              ) : (
                <div className='rounded-lg bg-yellow-50 p-4'>
                  <p className='text-yellow-700'>
                    {'error' in despesaPessoal
                      ? despesaPessoal.error
                      : 'Nenhum dado de despesa pessoal encontrado para este exercício.'}
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default async function RelatorioDespesasPage() {
  await ensureAuth();

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de Despesas</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <RelatorioDespesasContent />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
