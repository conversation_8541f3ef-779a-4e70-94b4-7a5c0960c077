'use server';

import {
  AuditoriaMovimentoReservas,
  ErrosReserva,
  Permissoes,
  StatusReserva,
  StatusEmpenho,
  TiposAcesso,
  TiposAssinatura,
} from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  cancelarReservaSchema,
  criarReservaSchema,
  editarReservaSchema,
  idSchema,
  imprimirReservaSchema,
  permissaoSchema,
  solicitarAssinaturasReservaSchema,
} from '@/lib/validation';
import {
  listarIdsDotacoesUsuarioConectadoTemAcesso,
  obterIpUsuarioConectado,
  temPermissao,
} from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { redistribuirCotas } from '../gerenciamento/dotacoes';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { revalidatePath } from 'next/cache';
import currency from 'currency.js';
import { currencyOptionsNoSymbol, montarPathPdfReserva } from '@/lib/utils';
import { toCurrency } from '@/lib/serverUtils';
import { Prisma } from '@prisma/client';
import { createSuperClient } from '@/lib/supabase/server';

export const listarReservas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    let acessos;
    if (!resultPermissao.gerente) {
      acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

      if (acessos.error) {
        return {
          error: acessos.error,
        };
      }

      if (!acessos.data) {
        return {
          error: 'Não foi possível obter acessos do usuário.',
        };
      }
    }

    let where: Prisma.reservasWhereInput | null;

    if (resultPermissao.gerente || acessos?.data?.acessoTotal) {
      where = {
        exercicio: resultPermissao.exercicio,
      };
    } else {
      where = {
        exercicio: resultPermissao.exercicio,
        idDotacao: { in: acessos?.data?.dotacoes },
      };
    }

    const reservas = await prisma.reservas.findMany({
      select: {
        id: true,
        resumo: true,
        obs: true,
        usarTotal: true,
        usarMes1: true,
        usarMes2: true,
        usarMes3: true,
        usarMes4: true,
        usarMes5: true,
        usarMes6: true,
        usarMes7: true,
        usarMes8: true,
        usarMes9: true,
        usarMes10: true,
        usarMes11: true,
        usarMes12: true,
        pluUsarTotal: true,
        pluUsarMes1: true,
        pluUsarMes2: true,
        pluUsarMes3: true,
        pluUsarMes4: true,
        pluUsarMes5: true,
        pluUsarMes6: true,
        pluUsarMes7: true,
        pluUsarMes8: true,
        pluUsarMes9: true,
        pluUsarMes10: true,
        pluUsarMes11: true,
        pluUsarMes12: true,
        status: true,
        dotacao: {
          select: {
            despesa: true,
            desc: true,
            secretariaId: true,
            departamentoId: true,
            departamento: { select: { secretariaId: true } },
          },
        },
        protocolos: {
          select: {
            id: true,
            numero: true,
            status: true,
            ativo: true,
          },
          where: {
            ativo: true,
          },
          take: 1,
        },
      },
      where,
      orderBy: { id: 'desc' },
    });

    const secretariasIds = reservas.map(
      (reserva) => reserva.dotacao.secretariaId || 0
    );
    const departamentosIds = reservas.map(
      (reserva) => reserva.dotacao.departamentoId || 0
    );

    const secretariasPromise = prisma.secretarias.findMany({
      where: {
        id: {
          in: secretariasIds,
        },
        ativo: true,
      },
    });
    const departamentosPromise = prisma.departamentos.findMany({
      where: {
        id: {
          in: departamentosIds,
        },
        ativo: true,
      },
      include: { secretaria: true },
    });

    let [secretarias, departamentos] = await Promise.all([
      secretariasPromise,
      departamentosPromise,
    ]);

    if (!secretarias || !secretarias.length) {
      secretarias = departamentos.reduce(
        (acc, departamento) => {
          if (
            acc.find(
              (secretaria) => secretaria.id === departamento.secretaria.id
            )
          ) {
            return acc;
          }
          return [...acc, departamento.secretaria];
        },
        [] as typeof secretarias
      );
    }

    return {
      data: {
        reservas: reservas.map((reserva) => {
          const protocolo = reserva.protocolos[0] || null;
          return {
            ...reserva,
            usarMes1: reserva.usarMes1.toNumber(),
            usarMes2: reserva.usarMes2.toNumber(),
            usarMes3: reserva.usarMes3.toNumber(),
            usarMes4: reserva.usarMes4.toNumber(),
            usarMes5: reserva.usarMes5.toNumber(),
            usarMes6: reserva.usarMes6.toNumber(),
            usarMes7: reserva.usarMes7.toNumber(),
            usarMes8: reserva.usarMes8.toNumber(),
            usarMes9: reserva.usarMes9.toNumber(),
            usarMes10: reserva.usarMes10.toNumber(),
            usarMes11: reserva.usarMes11.toNumber(),
            usarMes12: reserva.usarMes12.toNumber(),
            usarTotal: reserva.usarTotal.toNumber(),
            pluUsarMes1: reserva.pluUsarMes1.toNumber(),
            pluUsarMes2: reserva.pluUsarMes2.toNumber(),
            pluUsarMes3: reserva.pluUsarMes3.toNumber(),
            pluUsarMes4: reserva.pluUsarMes4.toNumber(),
            pluUsarMes5: reserva.pluUsarMes5.toNumber(),
            pluUsarMes6: reserva.pluUsarMes6.toNumber(),
            pluUsarMes7: reserva.pluUsarMes7.toNumber(),
            pluUsarMes8: reserva.pluUsarMes8.toNumber(),
            pluUsarMes9: reserva.pluUsarMes9.toNumber(),
            pluUsarMes10: reserva.pluUsarMes10.toNumber(),
            pluUsarMes11: reserva.pluUsarMes11.toNumber(),
            pluUsarMes12: reserva.pluUsarMes12.toNumber(),
            pluUsarTotal: reserva.pluUsarTotal.toNumber(),
            secretariaId:
              reserva.dotacao.secretariaId ||
              reserva.dotacao.departamento?.secretariaId ||
              null,
            departamentoId: reserva.dotacao.departamentoId || null,
            protocolo: protocolo,
          };
        }),
        secretarias,
        departamentos,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter reservas.`,
    };
  }
};

export const buscarDespesa = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Despesa inválida.',
    };
  }

  const { id: despesa } = parsedParams.data;

  try {
    const acessosPromise = listarIdsDotacoesUsuarioConectadoTemAcesso();
    const dotacaoPromise = prisma.dotacoes.findFirst({
      select: {
        id: true,
        exercicio: true,
        despesa: true,
        desc: true,
        fonte: true,
        codAplicacao: true,
        economica: true,
        funcional: true,
        secretaria: true,
        departamento: {
          include: {
            secretaria: true,
          },
        },
        subdepartamento: {
          include: {
            departamento: {
              include: {
                secretaria: true,
              },
            },
          },
        },
        ativo: true,
      },
      where: {
        exercicio: resultPermissao.exercicio,
        despesa: despesa,
      },
    });

    const [acessos, dotacao] = await Promise.all([
      acessosPromise,
      dotacaoPromise,
    ]);

    if (acessos.error) {
      return {
        error: acessos.error,
      };
    }

    if (!acessos.data) {
      return {
        error: 'Não foi possível obter acessos do usuário.',
      };
    }

    if (!dotacao) {
      return {
        error: 'Despesa não encontrada.',
      };
    }

    if (!dotacao.ativo) {
      return {
        error: 'Despesa desativada.',
      };
    }

    const subdepartamento = dotacao?.subdepartamento || null;
    const departamento = subdepartamento
      ? dotacao?.subdepartamento?.departamento || null
      : dotacao?.departamento || null;
    const secretaria = subdepartamento
      ? dotacao?.subdepartamento?.departamento?.secretaria || null
      : departamento
        ? dotacao?.departamento?.secretaria || null
        : dotacao?.secretaria || null;

    if (!secretaria) {
      return {
        error: 'Falha ao determinar secretaria da despesa.',
      };
    }

    if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
      if (
        !acessos.data.dotacoes ||
        !acessos.data.dotacoes.includes(dotacao.id)
      ) {
        return {
          error: 'Usuário sem permissão para essa despesa.',
        };
      }
    }

    await redistribuirCotas({
      dotacaoId: dotacao.id,
      exercicio: resultPermissao.exercicio,
    });

    const cotasPromise = prisma.dotacoes.findFirst({
      select: {
        cotaMes1: true,
        cotaMes2: true,
        cotaMes3: true,
        cotaMes4: true,
        cotaMes5: true,
        cotaMes6: true,
        cotaMes7: true,
        cotaMes8: true,
        cotaMes9: true,
        cotaMes10: true,
        cotaMes11: true,
        cotaMes12: true,
        valorAtual: true,
      },
      where: {
        id: dotacao.id,
      },
    });

    const economicasItensPromise = prisma.economicas.findMany({
      where: {
        ativo: true,
        categoria: dotacao.economica.categoria,
        grupo: dotacao.economica.grupo,
        modalidade: dotacao.economica.modalidade,
        elemento: dotacao.economica.elemento,
      },
      orderBy: { subelemento: 'asc' },
    });

    const cargosSecretariosAssinantesPromise = prisma.cargos.findMany({
      where: {
        tipoAssinatura: TiposAssinatura.SECRETARIO,
        OR: [
          {
            AND: [
              {
                cargos_acessos: {
                  every: {
                    secretariaId: secretaria.id,
                  },
                },
              },
              {
                tipoAcesso: TiposAcesso.SECRETARIAS,
              },
            ],
          },
          {
            tipoAcesso: TiposAcesso.TODAS_SECRETARIAS,
          },
        ],
      },
      select: {
        user_profiles_cargos: {
          select: {
            usuario: {
              select: {
                id: true,
                nome: true,
              },
            },
          },
        },
      },
    });

    const cargosDiretoresAssinantesPromise = prisma.cargos.findMany({
      where: {
        tipoAssinatura: TiposAssinatura.DIRETOR,
        OR: [
          {
            AND: [
              {
                cargos_acessos: {
                  every: {
                    secretariaId: secretaria.id,
                  },
                },
              },
              {
                tipoAcesso: TiposAcesso.SECRETARIAS,
              },
            ],
          },
          {
            tipoAcesso: TiposAcesso.TODAS_SECRETARIAS,
          },
        ],
      },
      select: {
        user_profiles_cargos: {
          select: {
            usuario: {
              select: {
                id: true,
                nome: true,
              },
            },
          },
        },
      },
    });

    const cargosPrefeitosAssinantesPromise = prisma.cargos.findMany({
      where: {
        tipoAssinatura: TiposAssinatura.PREFEITO,
        OR: [
          {
            AND: [
              {
                cargos_acessos: {
                  every: {
                    secretariaId: secretaria.id,
                  },
                },
              },
              {
                tipoAcesso: TiposAcesso.SECRETARIAS,
              },
            ],
          },
          {
            tipoAcesso: TiposAcesso.TODAS_SECRETARIAS,
          },
        ],
      },
      select: {
        user_profiles_cargos: {
          select: {
            usuario: {
              select: {
                id: true,
                nome: true,
              },
            },
          },
        },
      },
    });

    const [
      cotas,
      economicasItens,
      cargosSecretariosAssinantes,
      cargosDiretoresAssinantes,
      cargosPrefeitosAssinantes,
    ] = await Promise.all([
      cotasPromise,
      economicasItensPromise,
      cargosSecretariosAssinantesPromise,
      cargosDiretoresAssinantesPromise,
      cargosPrefeitosAssinantesPromise,
    ]);

    if (!cotas) {
      return {
        error: 'Falha ao buscar cotas, tente novamente.',
      };
    }

    if (!economicasItens) {
      return {
        error: 'Nenhuma econômica encontrada.',
      };
    }

    if (!cargosSecretariosAssinantes) {
      return {
        error: 'Nenhum secretário configurado para assinar.',
      };
    }

    if (!cargosDiretoresAssinantes) {
      return {
        error: 'Nenhum diretor configurado para assinar.',
      };
    }

    if (!cargosPrefeitosAssinantes) {
      return {
        error: 'Nenhum prefeito configurado para assinar.',
      };
    }

    const secretariosAssinantes = cargosSecretariosAssinantes
      .map((cargo) => cargo.user_profiles_cargos.map((cargo) => cargo.usuario))
      .flat();

    const diretoresAssinantes = cargosDiretoresAssinantes
      .map((cargo) => cargo.user_profiles_cargos.map((cargo) => cargo.usuario))
      .flat();

    const prefeitosAssinantes = cargosPrefeitosAssinantes
      .map((cargo) => cargo.user_profiles_cargos.map((cargo) => cargo.usuario))
      .flat();

    return {
      data: {
        ...dotacao,
        cotaMes1: cotas.cotaMes1.toNumber(),
        cotaMes2: cotas.cotaMes2.toNumber(),
        cotaMes3: cotas.cotaMes3.toNumber(),
        cotaMes4: cotas.cotaMes4.toNumber(),
        cotaMes5: cotas.cotaMes5.toNumber(),
        cotaMes6: cotas.cotaMes6.toNumber(),
        cotaMes7: cotas.cotaMes7.toNumber(),
        cotaMes8: cotas.cotaMes8.toNumber(),
        cotaMes9: cotas.cotaMes9.toNumber(),
        cotaMes10: cotas.cotaMes10.toNumber(),
        cotaMes11: cotas.cotaMes11.toNumber(),
        cotaMes12: cotas.cotaMes12.toNumber(),
        valorAtual: cotas.valorAtual.toNumber(),
        secretariosAssinantes,
        diretoresAssinantes,
        prefeitosAssinantes,
        economicasItens,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao buscar despesa.`,
    };
  }
};

export const buscarReservaParaEmpenho = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_EMPENHO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'ID da reserva inválido.',
    };
  }

  const { id: reservaId } = parsedParams.data;

  try {
    // Get reserva data with monthly values
    const reserva = await prisma.reservas.findUnique({
      where: {
        id: reservaId,
        exercicio: resultPermissao.exercicio,
        status: StatusReserva.Reservado,
      },
      select: {
        id: true,
        resumo: true,
        usarMes1: true,
        usarMes2: true,
        usarMes3: true,
        usarMes4: true,
        usarMes5: true,
        usarMes6: true,
        usarMes7: true,
        usarMes8: true,
        usarMes9: true,
        usarMes10: true,
        usarMes11: true,
        usarMes12: true,
        usarTotal: true,
        dotacao: {
          select: {
            despesa: true,
            desc: true,
          },
        },
      },
    });

    if (!reserva) {
      return {
        error: 'Reserva não encontrada ou não disponível para empenho.',
      };
    }

    // Calculate already empenhado amounts for this reserva
    const empenhosExistentes = await prisma.empenhos.aggregate({
      where: {
        idReserva: reservaId,
        status: {
          in: [StatusEmpenho.EMPENHADO, StatusEmpenho.LIQUIDADO],
        },
      },
      _sum: {
        usarMes1: true,
        usarMes2: true,
        usarMes3: true,
        usarMes4: true,
        usarMes5: true,
        usarMes6: true,
        usarMes7: true,
        usarMes8: true,
        usarMes9: true,
        usarMes10: true,
        usarMes11: true,
        usarMes12: true,
        valorTotal: true,
      },
    });

    // Calculate available balance per month
    const jaEmpenhado = {
      mes1: empenhosExistentes._sum.usarMes1?.toNumber() || 0,
      mes2: empenhosExistentes._sum.usarMes2?.toNumber() || 0,
      mes3: empenhosExistentes._sum.usarMes3?.toNumber() || 0,
      mes4: empenhosExistentes._sum.usarMes4?.toNumber() || 0,
      mes5: empenhosExistentes._sum.usarMes5?.toNumber() || 0,
      mes6: empenhosExistentes._sum.usarMes6?.toNumber() || 0,
      mes7: empenhosExistentes._sum.usarMes7?.toNumber() || 0,
      mes8: empenhosExistentes._sum.usarMes8?.toNumber() || 0,
      mes9: empenhosExistentes._sum.usarMes9?.toNumber() || 0,
      mes10: empenhosExistentes._sum.usarMes10?.toNumber() || 0,
      mes11: empenhosExistentes._sum.usarMes11?.toNumber() || 0,
      mes12: empenhosExistentes._sum.usarMes12?.toNumber() || 0,
      total: empenhosExistentes._sum.valorTotal?.toNumber() || 0,
    };

    const saldoDisponivel = {
      mes1: reserva.usarMes1.toNumber() - jaEmpenhado.mes1,
      mes2: reserva.usarMes2.toNumber() - jaEmpenhado.mes2,
      mes3: reserva.usarMes3.toNumber() - jaEmpenhado.mes3,
      mes4: reserva.usarMes4.toNumber() - jaEmpenhado.mes4,
      mes5: reserva.usarMes5.toNumber() - jaEmpenhado.mes5,
      mes6: reserva.usarMes6.toNumber() - jaEmpenhado.mes6,
      mes7: reserva.usarMes7.toNumber() - jaEmpenhado.mes7,
      mes8: reserva.usarMes8.toNumber() - jaEmpenhado.mes8,
      mes9: reserva.usarMes9.toNumber() - jaEmpenhado.mes9,
      mes10: reserva.usarMes10.toNumber() - jaEmpenhado.mes10,
      mes11: reserva.usarMes11.toNumber() - jaEmpenhado.mes11,
      mes12: reserva.usarMes12.toNumber() - jaEmpenhado.mes12,
      total: reserva.usarTotal.toNumber() - jaEmpenhado.total,
    };

    return {
      data: {
        reserva: {
          id: reserva.id,
          resumo: reserva.resumo,
          despesa: reserva.dotacao.despesa,
          descricaoDespesa: reserva.dotacao.desc,
          valoresOriginais: {
            mes1: reserva.usarMes1.toNumber(),
            mes2: reserva.usarMes2.toNumber(),
            mes3: reserva.usarMes3.toNumber(),
            mes4: reserva.usarMes4.toNumber(),
            mes5: reserva.usarMes5.toNumber(),
            mes6: reserva.usarMes6.toNumber(),
            mes7: reserva.usarMes7.toNumber(),
            mes8: reserva.usarMes8.toNumber(),
            mes9: reserva.usarMes9.toNumber(),
            mes10: reserva.usarMes10.toNumber(),
            mes11: reserva.usarMes11.toNumber(),
            mes12: reserva.usarMes12.toNumber(),
            total: reserva.usarTotal.toNumber(),
          },
          jaEmpenhado,
          saldoDisponivel,
        },
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_EMPENHO,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao buscar dados da reserva.',
    };
  }
};

export const criarReserva = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = criarReservaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    despesa,
    obs,
    resumo,
    itens,
    pedidoPlurianual,
    valorDisponivelCota,
    usarMes1,
    usarMes2,
    usarMes3,
    usarMes4,
    usarMes5,
    usarMes6,
    usarMes7,
    usarMes8,
    usarMes9,
    usarMes10,
    usarMes11,
    usarMes12,
    pluUsarMes1,
    pluUsarMes2,
    pluUsarMes3,
    pluUsarMes4,
    pluUsarMes5,
    pluUsarMes6,
    pluUsarMes7,
    pluUsarMes8,
    pluUsarMes9,
    pluUsarMes10,
    pluUsarMes11,
    pluUsarMes12,
    idSecretario,
    idDiretor,
    idPrefeito,
    economicaItemId,
  } = parsedParams.data;

  if (!itens || !itens.length) {
    return {
      error: 'Reserva sem itens.',
    };
  }

  let idReserva = 0;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    const configsExercicio = await prisma.configuracoesExercicio.findUnique({
      where: {
        exercicio: resultPermissao.exercicio,
      },
    });

    await prisma.$transaction(async (tx) => {
      const dotacao = await tx.dotacoes.findFirst({
        where: {
          exercicio: resultPermissao.exercicio!,
          despesa,
        },
      });

      if (!dotacao) {
        throw new Error(ErrosReserva.DESPESA_NAO_ENCONTRADA);
      }

      const somaCotas = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes1.toNumber())
        .add(dotacao.cotaMes2.toNumber())
        .add(dotacao.cotaMes3.toNumber())
        .add(dotacao.cotaMes4.toNumber())
        .add(dotacao.cotaMes5.toNumber())
        .add(dotacao.cotaMes6.toNumber())
        .add(dotacao.cotaMes7.toNumber())
        .add(dotacao.cotaMes8.toNumber())
        .add(dotacao.cotaMes9.toNumber())
        .add(dotacao.cotaMes10.toNumber())
        .add(dotacao.cotaMes11.toNumber())
        .add(dotacao.cotaMes12.toNumber());

      if (somaCotas.value !== dotacao.valorAtual.toNumber()) {
        throw new Error(ErrosReserva.COTAS_INCONSISTENTES);
      }

      if (dotacao.valorAtual.toNumber() !== valorDisponivelCota) {
        throw new Error(ErrosReserva.VALOR_LIBERADO_DIFERENTE);
      }

      const usarTotal = currency(0, currencyOptionsNoSymbol)
        .add(usarMes1)
        .add(usarMes2)
        .add(usarMes3)
        .add(usarMes4)
        .add(usarMes5)
        .add(usarMes6)
        .add(usarMes7)
        .add(usarMes8)
        .add(usarMes9)
        .add(usarMes10)
        .add(usarMes11)
        .add(usarMes12);

      if (usarTotal.value > dotacao.valorLiberado.toNumber()) {
        throw new Error(ErrosReserva.VALOR_TOTAL_RESERVA_MAIOR_LIBERADO);
      }

      if (usarMes1 > dotacao.cotaMes1.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_JANEIRO_MAIOR_COTA);
      }

      if (usarMes2 > dotacao.cotaMes2.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_FEVEREIRO_MAIOR_COTA);
      }

      if (usarMes3 > dotacao.cotaMes3.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_MARCO_MAIOR_COTA);
      }

      if (usarMes4 > dotacao.cotaMes4.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_ABRIL_MAIOR_COTA);
      }

      if (usarMes5 > dotacao.cotaMes5.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_MAIO_MAIOR_COTA);
      }

      if (usarMes6 > dotacao.cotaMes6.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_JUNHO_MAIOR_COTA);
      }

      if (usarMes7 > dotacao.cotaMes7.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_JULHO_MAIOR_COTA);
      }

      if (usarMes8 > dotacao.cotaMes8.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_AGOSTO_MAIOR_COTA);
      }

      if (usarMes9 > dotacao.cotaMes9.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_SETEMBRO_MAIOR_COTA);
      }

      if (usarMes10 > dotacao.cotaMes10.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_OUTUBRO_MAIOR_COTA);
      }

      if (usarMes11 > dotacao.cotaMes11.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_NOVEMBRO_MAIOR_COTA);
      }

      if (usarMes12 > dotacao.cotaMes12.toNumber()) {
        throw new Error(ErrosReserva.VALOR_RESERVA_DEZEMBRO_MAIOR_COTA);
      }

      const valorTotalItens =
        itens?.reduce(
          (acumulador, item) => {
            return currency(0, currencyOptionsNoSymbol)
              .add(item.valor)
              .multiply(item.quantidade)
              .add(acumulador);
          },
          currency(0, currencyOptionsNoSymbol)
        ).value || 0;

      if (valorTotalItens !== usarTotal.value) {
        throw new Error(ErrosReserva.VALOR_TOTAL_ITENS_DIFERENTE_RESERVA);
      }

      const cotaMes1Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes1.toNumber())
        .subtract(usarMes1);

      const cotaMes2Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes2.toNumber())
        .subtract(usarMes2);

      const cotaMes3Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes3.toNumber())
        .subtract(usarMes3);

      const cotaMes4Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes4.toNumber())
        .subtract(usarMes4);

      const cotaMes5Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes5.toNumber())
        .subtract(usarMes5);

      const cotaMes6Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes6.toNumber())
        .subtract(usarMes6);

      const cotaMes7Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes7.toNumber())
        .subtract(usarMes7);

      const cotaMes8Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes8.toNumber())
        .subtract(usarMes8);

      const cotaMes9Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes9.toNumber())
        .subtract(usarMes9);

      const cotaMes10Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes10.toNumber())
        .subtract(usarMes10);

      const cotaMes11Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes11.toNumber())
        .subtract(usarMes11);

      const cotaMes12Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes12.toNumber())
        .subtract(usarMes12);

      const valorAtualFinal = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.valorAtual.toNumber())
        .subtract(usarTotal);

      //Validações excessivas, mas vai que

      if (cotaMes1Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_JANEIRO_MENOR_ZERO);
      }

      if (cotaMes2Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_FEVEREIRO_MENOR_ZERO);
      }

      if (cotaMes3Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_MARCO_MENOR_ZERO);
      }

      if (cotaMes4Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_ABRIL_MENOR_ZERO);
      }

      if (cotaMes5Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_MAIO_MENOR_ZERO);
      }

      if (cotaMes6Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_JUNHO_MENOR_ZERO);
      }

      if (cotaMes7Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_JULHO_MENOR_ZERO);
      }

      if (cotaMes8Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_AGOSTO_MENOR_ZERO);
      }

      if (cotaMes9Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_SETEMBRO_MENOR_ZERO);
      }

      if (cotaMes10Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_OUTUBRO_MENOR_ZERO);
      }

      if (cotaMes11Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_NOVEMBRO_MENOR_ZERO);
      }

      if (cotaMes12Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_DEZEMBRO_MENOR_ZERO);
      }

      if (valorAtualFinal.value < 0) {
        throw new Error(ErrosReserva.VALOR_ATUAL_RESTANTE_MENOR_ZERO);
      }

      const pluUsarTotal = currency(0, currencyOptionsNoSymbol)
        .add(pluUsarMes1)
        .add(pluUsarMes2)
        .add(pluUsarMes3)
        .add(pluUsarMes4)
        .add(pluUsarMes5)
        .add(pluUsarMes6)
        .add(pluUsarMes7)
        .add(pluUsarMes8)
        .add(pluUsarMes9)
        .add(pluUsarMes10)
        .add(pluUsarMes11)
        .add(pluUsarMes12);

      const criarReservaPromise = tx.reservas.create({
        data: {
          exercicio: resultPermissao.exercicio!,
          reserva_itens: {
            createMany: {
              data: itens.map((item) => ({
                quantidade: item.quantidade,
                unidade: item.unidade,
                desc: item.desc,
                valor: item.valor,
              })),
            },
          },
          obs: obs || '',
          resumo: resumo || '',
          pedidoPlurianual,
          usarMes1,
          usarMes2,
          usarMes3,
          usarMes4,
          usarMes5,
          usarMes6,
          usarMes7,
          usarMes8,
          usarMes9,
          usarMes10,
          usarMes11,
          usarMes12,
          usarTotal: usarTotal.value,
          pluUsarMes1,
          pluUsarMes2,
          pluUsarMes3,
          pluUsarMes4,
          pluUsarMes5,
          pluUsarMes6,
          pluUsarMes7,
          pluUsarMes8,
          pluUsarMes9,
          pluUsarMes10,
          pluUsarMes11,
          pluUsarMes12,
          pluUsarTotal: pluUsarTotal.value,
          idDotacao: dotacao.id,
          idSecretario,
          idDiretor,
          idPrefeito,
          idEconomicaItem: economicaItemId,
          idGestor: resultPermissao.idUsuario!,
          data:
            configsExercicio &&
            configsExercicio.travarDataPedidos &&
            configsExercicio.dataPedidos
              ? configsExercicio.dataPedidos
              : new Date(),
        },
      });

      const atualizarDotacaoPromise = tx.dotacoes.update({
        where: {
          id: dotacao.id,
        },
        data: {
          valorAtual: valorAtualFinal.value,
          cotaMes1: cotaMes1Final.value,
          cotaMes2: cotaMes2Final.value,
          cotaMes3: cotaMes3Final.value,
          cotaMes4: cotaMes4Final.value,
          cotaMes5: cotaMes5Final.value,
          cotaMes6: cotaMes6Final.value,
          cotaMes7: cotaMes7Final.value,
          cotaMes8: cotaMes8Final.value,
          cotaMes9: cotaMes9Final.value,
          cotaMes10: cotaMes10Final.value,
          cotaMes11: cotaMes11Final.value,
          cotaMes12: cotaMes12Final.value,
        },
      });

      const [criarReserva] = await Promise.all([
        criarReservaPromise,
        atualizarDotacaoPromise,
      ]);

      const reservaAuditPromise = tx.reservas_audit.create({
        data: {
          idReserva: criarReserva.id,
          ip,
          exercicio: resultPermissao.exercicio!,
          acao: AuditoriaMovimentoReservas.CRIAR_RESERVA,
          obs: obs || '',
          resumo: resumo || '',
          pedidoPlurianual,
          usarMes1,
          usarMes2,
          usarMes3,
          usarMes4,
          usarMes5,
          usarMes6,
          usarMes7,
          usarMes8,
          usarMes9,
          usarMes10,
          usarMes11,
          usarMes12,
          usarTotal: usarTotal.value,
          pluUsarMes1,
          pluUsarMes2,
          pluUsarMes3,
          pluUsarMes4,
          pluUsarMes5,
          pluUsarMes6,
          pluUsarMes7,
          pluUsarMes8,
          pluUsarMes9,
          pluUsarMes10,
          pluUsarMes11,
          pluUsarMes12,
          pluUsarTotal: pluUsarTotal.value,
          idSecretario,
          idDiretor,
          idPrefeito,
          idEconomicaItem: economicaItemId,
          idUsuario: resultPermissao.idUsuario!,
          cotaMes1: dotacao.cotaMes1,
          cotaMes2: dotacao.cotaMes2,
          cotaMes3: dotacao.cotaMes3,
          cotaMes4: dotacao.cotaMes4,
          cotaMes5: dotacao.cotaMes5,
          cotaMes6: dotacao.cotaMes6,
          cotaMes7: dotacao.cotaMes7,
          cotaMes8: dotacao.cotaMes8,
          cotaMes9: dotacao.cotaMes9,
          cotaMes10: dotacao.cotaMes10,
          cotaMes11: dotacao.cotaMes11,
          cotaMes12: dotacao.cotaMes12,
          valorTotalCota: dotacao.valorAtual,
        },
      });

      const reservaItensAuditPromise = tx.reserva_itens_audit.createMany({
        data: itens.map((item) => ({
          idReserva: criarReserva.id,
          unidade: item.unidade,
          quantidade: item.quantidade,
          desc: item.desc,
          valor: item.valor,
          idUsuario: resultPermissao.idUsuario!,
          ip,
          acao: AuditoriaMovimentoReservas.CRIAR_RESERVA,
        })),
      });

      idReserva = criarReserva.id;
      await Promise.all([reservaAuditPromise, reservaItensAuditPromise]);
    });
    revalidatePath('/movimento/reservas');
    return {
      data: {
        idReserva,
      },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosReserva).includes(e.message as ErrosReserva)) {
        return {
          error: e.message,
        };
      }
    }
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Reserva já existe.`,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar reserva.`,
    };
  }
};

export const obterReserva = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { id } = parsedParams.data;

  try {
    const acessosPromise = listarIdsDotacoesUsuarioConectadoTemAcesso();
    const reservaPromise = prisma.reservas.findUnique({
      where: { id },
      include: { reserva_itens: true, dotacao: { select: { despesa: true } } },
    });

    const [acessos, reserva] = await Promise.all([
      acessosPromise,
      reservaPromise,
    ]);

    if (acessos.error) {
      return {
        error: acessos.error,
      };
    }

    if (!acessos.data) {
      return {
        error: 'Não foi possível obter acessos do usuário.',
      };
    }

    if (!reserva) {
      return {
        error: 'Reserva não encontrada.',
      };
    }

    if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
      const temAcesso = acessos.data.dotacoes?.includes(reserva.idDotacao);
      if (!temAcesso) {
        return {
          error: 'Usuário não pode acessar essa reserva.',
        };
      }
    }

    return {
      data: {
        ...reserva,
        usarMes1: reserva.usarMes1.toNumber(),
        usarMes2: reserva.usarMes2.toNumber(),
        usarMes3: reserva.usarMes3.toNumber(),
        usarMes4: reserva.usarMes4.toNumber(),
        usarMes5: reserva.usarMes5.toNumber(),
        usarMes6: reserva.usarMes6.toNumber(),
        usarMes7: reserva.usarMes7.toNumber(),
        usarMes8: reserva.usarMes8.toNumber(),
        usarMes9: reserva.usarMes9.toNumber(),
        usarMes10: reserva.usarMes10.toNumber(),
        usarMes11: reserva.usarMes11.toNumber(),
        usarMes12: reserva.usarMes12.toNumber(),
        usarTotal: reserva.usarTotal.toNumber(),
        pluUsarMes1: reserva.pluUsarMes1.toNumber(),
        pluUsarMes2: reserva.pluUsarMes2.toNumber(),
        pluUsarMes3: reserva.pluUsarMes3.toNumber(),
        pluUsarMes4: reserva.pluUsarMes4.toNumber(),
        pluUsarMes5: reserva.pluUsarMes5.toNumber(),
        pluUsarMes6: reserva.pluUsarMes6.toNumber(),
        pluUsarMes7: reserva.pluUsarMes7.toNumber(),
        pluUsarMes8: reserva.pluUsarMes8.toNumber(),
        pluUsarMes9: reserva.pluUsarMes9.toNumber(),
        pluUsarMes10: reserva.pluUsarMes10.toNumber(),
        pluUsarMes11: reserva.pluUsarMes11.toNumber(),
        pluUsarMes12: reserva.pluUsarMes12.toNumber(),
        pluUsarTotal: reserva.pluUsarTotal.toNumber(),
        reserva_itens: reserva.reserva_itens.map((item) => ({
          ...item,
          quantidade: item.quantidade.toNumber(),
          valor: item.valor.toNumber(),
        })),
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter reserva.`,
    };
  }
};

export const editarReserva = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = editarReservaSchema.safeParse(params);

  if (!parsedParams.success) {
    console.log(parsedParams.error);
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    id,
    obs,
    resumo,
    itens,
    pedidoPlurianual,
    valorDisponivelCota,
    usarMes1,
    usarMes2,
    usarMes3,
    usarMes4,
    usarMes5,
    usarMes6,
    usarMes7,
    usarMes8,
    usarMes9,
    usarMes10,
    usarMes11,
    usarMes12,
    pluUsarMes1,
    pluUsarMes2,
    pluUsarMes3,
    pluUsarMes4,
    pluUsarMes5,
    pluUsarMes6,
    pluUsarMes7,
    pluUsarMes8,
    pluUsarMes9,
    pluUsarMes10,
    pluUsarMes11,
    pluUsarMes12,
    idSecretario,
    idDiretor,
    idPrefeito,
    economicaItemId,
  } = parsedParams.data;

  if (!itens || !itens.length) {
    return {
      error: 'Reserva sem itens.',
    };
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const reservaAntiga = await tx.reservas.findUnique({
        where: {
          id,
        },
        include: {
          dotacao: {
            select: {
              despesa: true,
            },
          },
        },
      });

      if (!reservaAntiga) {
        throw new Error(ErrosReserva.RESERVA_NAO_ENCONTRADA);
      }

      if (reservaAntiga.status === StatusReserva.Cancelado) {
        throw new Error(ErrosReserva.RESERVA_CANCELADA);
      }

      if (reservaAntiga.status === StatusReserva.Empenho) {
        throw new Error(ErrosReserva.RESERVA_EMPENHO);
      }

      if (reservaAntiga.status === StatusReserva.Assinado) {
        throw new Error(ErrosReserva.RESERVA_ASSINADA);
      }

      if (
        reservaAntiga.status !== StatusReserva['Reservado'] &&
        reservaAntiga.status !== StatusReserva['Devolvido']
      ) {
        throw new Error(ErrosReserva.STATUS_INVALIDO);
      }

      const dotacao = await tx.dotacoes.findFirst({
        where: {
          exercicio: resultPermissao.exercicio!,
          despesa: reservaAntiga.dotacao.despesa,
        },
      });

      if (!dotacao) {
        throw new Error(ErrosReserva.DESPESA_NAO_ENCONTRADA);
      }

      const somaCotas = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes1.toNumber())
        .add(dotacao.cotaMes2.toNumber())
        .add(dotacao.cotaMes3.toNumber())
        .add(dotacao.cotaMes4.toNumber())
        .add(dotacao.cotaMes5.toNumber())
        .add(dotacao.cotaMes6.toNumber())
        .add(dotacao.cotaMes7.toNumber())
        .add(dotacao.cotaMes8.toNumber())
        .add(dotacao.cotaMes9.toNumber())
        .add(dotacao.cotaMes10.toNumber())
        .add(dotacao.cotaMes11.toNumber())
        .add(dotacao.cotaMes12.toNumber());

      if (somaCotas.value !== dotacao.valorAtual.toNumber()) {
        throw new Error(ErrosReserva.COTAS_INCONSISTENTES);
      }

      if (dotacao.valorAtual.toNumber() !== valorDisponivelCota) {
        throw new Error(ErrosReserva.VALOR_LIBERADO_DIFERENTE);
      }

      const usarTotal = currency(0, currencyOptionsNoSymbol)
        .add(usarMes1)
        .add(usarMes2)
        .add(usarMes3)
        .add(usarMes4)
        .add(usarMes5)
        .add(usarMes6)
        .add(usarMes7)
        .add(usarMes8)
        .add(usarMes9)
        .add(usarMes10)
        .add(usarMes11)
        .add(usarMes12);

      if (
        usarTotal.value >
        toCurrency(dotacao.valorLiberado.toNumber()).add(
          reservaAntiga.usarTotal.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_TOTAL_RESERVA_MAIOR_LIBERADO);
      }

      if (
        usarMes1 >
        toCurrency(dotacao.cotaMes1.toNumber()).add(
          reservaAntiga.usarMes1.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_JANEIRO_MAIOR_COTA);
      }

      if (
        usarMes2 >
        toCurrency(dotacao.cotaMes2.toNumber()).add(
          reservaAntiga.usarMes2.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_FEVEREIRO_MAIOR_COTA);
      }

      if (
        usarMes3 >
        toCurrency(dotacao.cotaMes3.toNumber()).add(
          reservaAntiga.usarMes3.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_MARCO_MAIOR_COTA);
      }

      if (
        usarMes4 >
        toCurrency(dotacao.cotaMes4.toNumber()).add(
          reservaAntiga.usarMes4.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_ABRIL_MAIOR_COTA);
      }

      if (
        usarMes5 >
        toCurrency(dotacao.cotaMes5.toNumber()).add(
          reservaAntiga.usarMes5.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_MAIO_MAIOR_COTA);
      }

      if (
        usarMes6 >
        toCurrency(dotacao.cotaMes6.toNumber()).add(
          reservaAntiga.usarMes6.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_JUNHO_MAIOR_COTA);
      }

      if (
        usarMes7 >
        toCurrency(dotacao.cotaMes7.toNumber()).add(
          reservaAntiga.usarMes7.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_JULHO_MAIOR_COTA);
      }

      if (
        usarMes8 >
        toCurrency(dotacao.cotaMes8.toNumber()).add(
          reservaAntiga.usarMes8.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_AGOSTO_MAIOR_COTA);
      }

      if (
        usarMes9 >
        toCurrency(dotacao.cotaMes9.toNumber()).add(
          reservaAntiga.usarMes9.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_SETEMBRO_MAIOR_COTA);
      }

      if (
        usarMes10 >
        toCurrency(dotacao.cotaMes10.toNumber()).add(
          reservaAntiga.usarMes10.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_OUTUBRO_MAIOR_COTA);
      }

      if (
        usarMes11 >
        toCurrency(dotacao.cotaMes11.toNumber()).add(
          reservaAntiga.usarMes11.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_NOVEMBRO_MAIOR_COTA);
      }

      if (
        usarMes12 >
        toCurrency(dotacao.cotaMes12.toNumber()).add(
          reservaAntiga.usarMes12.toNumber()
        ).value
      ) {
        throw new Error(ErrosReserva.VALOR_RESERVA_DEZEMBRO_MAIOR_COTA);
      }

      const valorTotalItens =
        itens?.reduce(
          (acumulador, item) => {
            return currency(0, currencyOptionsNoSymbol)
              .add(item.valor)
              .multiply(item.quantidade)
              .add(acumulador);
          },
          currency(0, currencyOptionsNoSymbol)
        ).value || 0;

      if (valorTotalItens !== usarTotal.value) {
        throw new Error(ErrosReserva.VALOR_TOTAL_ITENS_DIFERENTE_RESERVA);
      }

      const cotaMes1Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes1.toNumber())
        .add(reservaAntiga.usarMes1.toNumber())
        .subtract(usarMes1);

      const cotaMes2Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes2.toNumber())
        .add(reservaAntiga.usarMes2.toNumber())
        .subtract(usarMes2);

      const cotaMes3Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes3.toNumber())
        .add(reservaAntiga.usarMes3.toNumber())
        .subtract(usarMes3);

      const cotaMes4Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes4.toNumber())
        .add(reservaAntiga.usarMes4.toNumber())
        .subtract(usarMes4);

      const cotaMes5Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes5.toNumber())
        .add(reservaAntiga.usarMes5.toNumber())
        .subtract(usarMes5);

      const cotaMes6Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes6.toNumber())
        .add(reservaAntiga.usarMes6.toNumber())
        .subtract(usarMes6);

      const cotaMes7Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes7.toNumber())
        .add(reservaAntiga.usarMes7.toNumber())
        .subtract(usarMes7);

      const cotaMes8Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes8.toNumber())
        .add(reservaAntiga.usarMes8.toNumber())
        .subtract(usarMes8);

      const cotaMes9Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes9.toNumber())
        .add(reservaAntiga.usarMes9.toNumber())
        .subtract(usarMes9);

      const cotaMes10Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes10.toNumber())
        .add(reservaAntiga.usarMes10.toNumber())
        .subtract(usarMes10);

      const cotaMes11Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes11.toNumber())
        .add(reservaAntiga.usarMes11.toNumber())
        .subtract(usarMes11);

      const cotaMes12Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes12.toNumber())
        .add(reservaAntiga.usarMes12.toNumber())
        .subtract(usarMes12);

      const valorAtualFinal = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.valorAtual.toNumber())
        .add(reservaAntiga.usarTotal.toNumber())
        .subtract(usarTotal);

      //Validações excessivas, mas vai que

      if (cotaMes1Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_JANEIRO_MENOR_ZERO);
      }

      if (cotaMes2Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_FEVEREIRO_MENOR_ZERO);
      }

      if (cotaMes3Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_MARCO_MENOR_ZERO);
      }

      if (cotaMes4Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_ABRIL_MENOR_ZERO);
      }

      if (cotaMes5Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_MAIO_MENOR_ZERO);
      }

      if (cotaMes6Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_JUNHO_MENOR_ZERO);
      }

      if (cotaMes7Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_JULHO_MENOR_ZERO);
      }

      if (cotaMes8Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_AGOSTO_MENOR_ZERO);
      }

      if (cotaMes9Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_SETEMBRO_MENOR_ZERO);
      }

      if (cotaMes10Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_OUTUBRO_MENOR_ZERO);
      }

      if (cotaMes11Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_NOVEMBRO_MENOR_ZERO);
      }

      if (cotaMes12Final.value < 0) {
        throw new Error(ErrosReserva.VALOR_RESTANTE_DEZEMBRO_MENOR_ZERO);
      }

      if (valorAtualFinal.value < 0) {
        throw new Error(ErrosReserva.VALOR_ATUAL_RESTANTE_MENOR_ZERO);
      }

      const pluUsarTotal = currency(0, currencyOptionsNoSymbol)
        .add(pluUsarMes1)
        .add(pluUsarMes2)
        .add(pluUsarMes3)
        .add(pluUsarMes4)
        .add(pluUsarMes5)
        .add(pluUsarMes6)
        .add(pluUsarMes7)
        .add(pluUsarMes8)
        .add(pluUsarMes9)
        .add(pluUsarMes10)
        .add(pluUsarMes11)
        .add(pluUsarMes12);

      const atualizarReservaPromise = tx.reservas.update({
        where: { id },
        data: {
          reserva_itens: {
            // NOTE:
            // The order of props matters. We first want to delete all existing relations
            // before creating new ones
            deleteMany: {},
            createMany: {
              data: itens.map((item) => ({
                quantidade: item.quantidade,
                unidade: item.unidade,
                desc: item.desc,
                valor: item.valor,
              })),
            },
          },
          obs: obs || '',
          resumo: resumo || '',
          pedidoPlurianual,
          usarMes1,
          usarMes2,
          usarMes3,
          usarMes4,
          usarMes5,
          usarMes6,
          usarMes7,
          usarMes8,
          usarMes9,
          usarMes10,
          usarMes11,
          usarMes12,
          usarTotal: usarTotal.value,
          pluUsarMes1,
          pluUsarMes2,
          pluUsarMes3,
          pluUsarMes4,
          pluUsarMes5,
          pluUsarMes6,
          pluUsarMes7,
          pluUsarMes8,
          pluUsarMes9,
          pluUsarMes10,
          pluUsarMes11,
          pluUsarMes12,
          pluUsarTotal: pluUsarTotal.value,
          idDotacao: dotacao.id,
          idSecretario,
          idDiretor,
          idPrefeito,
          idEconomicaItem: economicaItemId,
          idGestor: resultPermissao.idUsuario!,
          ultimaModificacao: new Date(),
          status: StatusReserva.Reservado,
        },
      });

      const atualizarDotacaoPromise = tx.dotacoes.update({
        where: {
          id: dotacao.id,
        },
        data: {
          valorAtual: valorAtualFinal.value,
          cotaMes1: cotaMes1Final.value,
          cotaMes2: cotaMes2Final.value,
          cotaMes3: cotaMes3Final.value,
          cotaMes4: cotaMes4Final.value,
          cotaMes5: cotaMes5Final.value,
          cotaMes6: cotaMes6Final.value,
          cotaMes7: cotaMes7Final.value,
          cotaMes8: cotaMes8Final.value,
          cotaMes9: cotaMes9Final.value,
          cotaMes10: cotaMes10Final.value,
          cotaMes11: cotaMes11Final.value,
          cotaMes12: cotaMes12Final.value,
        },
      });

      const reservaAuditPromise = tx.reservas_audit.create({
        data: {
          idReserva: reservaAntiga.id,
          ip,
          exercicio: resultPermissao.exercicio!,
          acao: AuditoriaMovimentoReservas.EDITAR_RESERVA,
          obs: obs || '',
          resumo: resumo || '',
          pedidoPlurianual,
          usarMes1,
          usarMes2,
          usarMes3,
          usarMes4,
          usarMes5,
          usarMes6,
          usarMes7,
          usarMes8,
          usarMes9,
          usarMes10,
          usarMes11,
          usarMes12,
          usarTotal: usarTotal.value,
          pluUsarMes1,
          pluUsarMes2,
          pluUsarMes3,
          pluUsarMes4,
          pluUsarMes5,
          pluUsarMes6,
          pluUsarMes7,
          pluUsarMes8,
          pluUsarMes9,
          pluUsarMes10,
          pluUsarMes11,
          pluUsarMes12,
          pluUsarTotal: pluUsarTotal.value,
          idSecretario,
          idPrefeito,
          idDiretor,
          idEconomicaItem: economicaItemId,
          idUsuario: resultPermissao.idUsuario!,
          cotaMes1: dotacao.cotaMes1,
          cotaMes2: dotacao.cotaMes2,
          cotaMes3: dotacao.cotaMes3,
          cotaMes4: dotacao.cotaMes4,
          cotaMes5: dotacao.cotaMes5,
          cotaMes6: dotacao.cotaMes6,
          cotaMes7: dotacao.cotaMes7,
          cotaMes8: dotacao.cotaMes8,
          cotaMes9: dotacao.cotaMes9,
          cotaMes10: dotacao.cotaMes10,
          cotaMes11: dotacao.cotaMes11,
          cotaMes12: dotacao.cotaMes12,
          valorTotalCota: dotacao.valorAtual,
        },
      });

      const reservaItensAuditPromise = tx.reserva_itens_audit.createMany({
        data: itens.map((item) => ({
          idReserva: reservaAntiga.id,
          unidade: item.unidade,
          quantidade: item.quantidade,
          desc: item.desc,
          valor: item.valor,
          idUsuario: resultPermissao.idUsuario!,
          ip,
          acao: AuditoriaMovimentoReservas.EDITAR_RESERVA,
        })),
      });

      await Promise.all([
        reservaAuditPromise,
        reservaItensAuditPromise,
        atualizarReservaPromise,
        atualizarDotacaoPromise,
      ]);
    });
    revalidatePath('/movimento/reservas');
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosReserva).includes(e.message as ErrosReserva)) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar reserva.`,
    };
  }
};

export const obterReservaParaRelatorio = async (params: unknown) => {
  const parsedParams = imprimirReservaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { id, bearer, incluirGestor } = parsedParams.data;
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
    bearer,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Exercício não encontrado.',
    };
  }

  try {
    const acessosPromise = listarIdsDotacoesUsuarioConectadoTemAcesso({
      bearer,
    });
    const reservaPromise = prisma.reservas.findUnique({
      where: { id },
      include: {
        reserva_itens: true,
        economicaItem: true,
        gestor: true,
        secretario: true,
        diretor: true,
        prefeito: true,
        dotacao: {
          select: {
            id: true,
            fonte: true,
            codAplicacao: true,
            despesa: true,
            funcional: true,
            economica: true,
            secretaria: true,
            departamento: {
              include: {
                secretaria: true,
              },
            },
            subdepartamento: {
              include: {
                departamento: {
                  include: {
                    secretaria: true,
                  },
                },
              },
            },
          },
        },
        reservas_assinaturas: {
          select: {
            id: true,
            assinado: true,
            usuario: true,
          },
        },
        protocolos: {
          select: {
            id: true,
            numero: true,
            status: true,
            ativo: true,
          },
          where: {
            ativo: true,
          },
          take: 1,
        },
      },
    });
    const configsExercicioPromise = prisma.configuracoesExercicio.findUnique({
      where: {
        exercicio: resultPermissao.exercicio!,
      },
    });

    const [reserva, configsExercicio, acessos] = await Promise.all([
      reservaPromise,
      configsExercicioPromise,
      acessosPromise,
    ]);

    if (acessos.error) {
      return {
        error: acessos.error,
      };
    }

    if (!acessos.data) {
      return {
        error: 'Não foi possível obter acessos do usuário.',
      };
    }

    if (!reserva) {
      return {
        error: 'Reserva não encontrada.',
      };
    }

    if (!configsExercicio) {
      return {
        error: 'Configurações do exercício não encontradas.',
      };
    }

    if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
      const temAcesso = acessos.data.dotacoes?.includes(reserva.idDotacao);
      if (!temAcesso) {
        return {
          error: 'Usuário não pode acessar essa reserva.',
        };
      }
    }

    return {
      data: {
        ...reserva,
        usarMes1: reserva.usarMes1.toNumber(),
        usarMes2: reserva.usarMes2.toNumber(),
        usarMes3: reserva.usarMes3.toNumber(),
        usarMes4: reserva.usarMes4.toNumber(),
        usarMes5: reserva.usarMes5.toNumber(),
        usarMes6: reserva.usarMes6.toNumber(),
        usarMes7: reserva.usarMes7.toNumber(),
        usarMes8: reserva.usarMes8.toNumber(),
        usarMes9: reserva.usarMes9.toNumber(),
        usarMes10: reserva.usarMes10.toNumber(),
        usarMes11: reserva.usarMes11.toNumber(),
        usarMes12: reserva.usarMes12.toNumber(),
        usarTotal: reserva.usarTotal.toNumber(),
        pluUsarMes1: reserva.pluUsarMes1.toNumber(),
        pluUsarMes2: reserva.pluUsarMes2.toNumber(),
        pluUsarMes3: reserva.pluUsarMes3.toNumber(),
        pluUsarMes4: reserva.pluUsarMes4.toNumber(),
        pluUsarMes5: reserva.pluUsarMes5.toNumber(),
        pluUsarMes6: reserva.pluUsarMes6.toNumber(),
        pluUsarMes7: reserva.pluUsarMes7.toNumber(),
        pluUsarMes8: reserva.pluUsarMes8.toNumber(),
        pluUsarMes9: reserva.pluUsarMes9.toNumber(),
        pluUsarMes10: reserva.pluUsarMes10.toNumber(),
        pluUsarMes11: reserva.pluUsarMes11.toNumber(),
        pluUsarMes12: reserva.pluUsarMes12.toNumber(),
        pluUsarTotal: reserva.pluUsarTotal.toNumber(),
        reserva_itens: reserva.reserva_itens.map((item) => ({
          ...item,
          quantidade: item.quantidade.toNumber(),
          valor: item.valor.toNumber(),
        })),
        configsExercicio,
        incluirGestor,
        gerente: resultPermissao.gerente,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter reserva.`,
    };
  }
};

export const cancelarReserva = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = cancelarReservaSchema.safeParse(params);

  if (!parsedParams.success) {
    console.log(parsedParams.error);
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, motivo } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    let idReserva: number | null = null;
    let idDotacao: number | null = null;
    let exercicio: number | null = null;
    let modificado: Date | null = null;
    await prisma.$transaction(async (tx) => {
      const reservaAntiga = await tx.reservas.findUnique({
        where: {
          id,
        },
        include: {
          dotacao: {
            select: {
              despesa: true,
            },
          },
        },
      });

      if (!reservaAntiga) {
        throw new Error(ErrosReserva.RESERVA_NAO_ENCONTRADA);
      }

      const dotacao = await tx.dotacoes.findFirst({
        where: {
          exercicio: resultPermissao.exercicio!,
          despesa: reservaAntiga.dotacao.despesa,
        },
      });

      if (!dotacao) {
        throw new Error(ErrosReserva.DESPESA_NAO_ENCONTRADA);
      }

      if (reservaAntiga.status === StatusReserva.Cancelado) {
        throw new Error(ErrosReserva.RESERVA_CANCELADA);
      }

      const somaCotas = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes1.toNumber())
        .add(dotacao.cotaMes2.toNumber())
        .add(dotacao.cotaMes3.toNumber())
        .add(dotacao.cotaMes4.toNumber())
        .add(dotacao.cotaMes5.toNumber())
        .add(dotacao.cotaMes6.toNumber())
        .add(dotacao.cotaMes7.toNumber())
        .add(dotacao.cotaMes8.toNumber())
        .add(dotacao.cotaMes9.toNumber())
        .add(dotacao.cotaMes10.toNumber())
        .add(dotacao.cotaMes11.toNumber())
        .add(dotacao.cotaMes12.toNumber());

      if (somaCotas.value !== dotacao.valorAtual.toNumber()) {
        throw new Error(ErrosReserva.COTAS_INCONSISTENTES);
      }

      const cotaMes1Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes1.toNumber())
        .add(reservaAntiga.usarMes1.toNumber());

      const cotaMes2Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes2.toNumber())
        .add(reservaAntiga.usarMes2.toNumber());

      const cotaMes3Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes3.toNumber())
        .add(reservaAntiga.usarMes3.toNumber());

      const cotaMes4Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes4.toNumber())
        .add(reservaAntiga.usarMes4.toNumber());

      const cotaMes5Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes5.toNumber())
        .add(reservaAntiga.usarMes5.toNumber());

      const cotaMes6Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes6.toNumber())
        .add(reservaAntiga.usarMes6.toNumber());

      const cotaMes7Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes7.toNumber())
        .add(reservaAntiga.usarMes7.toNumber());

      const cotaMes8Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes8.toNumber())
        .add(reservaAntiga.usarMes8.toNumber());

      const cotaMes9Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes9.toNumber())
        .add(reservaAntiga.usarMes9.toNumber());

      const cotaMes10Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes10.toNumber())
        .add(reservaAntiga.usarMes10.toNumber());

      const cotaMes11Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes11.toNumber())
        .add(reservaAntiga.usarMes11.toNumber());

      const cotaMes12Final = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.cotaMes12.toNumber())
        .add(reservaAntiga.usarMes12.toNumber());

      const valorAtualFinal = currency(0, currencyOptionsNoSymbol)
        .add(dotacao.valorAtual.toNumber())
        .add(reservaAntiga.usarTotal.toNumber());

      const atualizarReservaPromise = tx.reservas.update({
        where: { id },
        data: {
          status: StatusReserva.Cancelado,
          ultimaModificacao: new Date(),
          motivoCancelamento: motivo,
          reservas_assinaturas: { deleteMany: {} },
        },
      });

      const atualizarDotacaoPromise = tx.dotacoes.update({
        where: {
          id: dotacao.id,
        },
        data: {
          valorAtual: valorAtualFinal.value,
          cotaMes1: cotaMes1Final.value,
          cotaMes2: cotaMes2Final.value,
          cotaMes3: cotaMes3Final.value,
          cotaMes4: cotaMes4Final.value,
          cotaMes5: cotaMes5Final.value,
          cotaMes6: cotaMes6Final.value,
          cotaMes7: cotaMes7Final.value,
          cotaMes8: cotaMes8Final.value,
          cotaMes9: cotaMes9Final.value,
          cotaMes10: cotaMes10Final.value,
          cotaMes11: cotaMes11Final.value,
          cotaMes12: cotaMes12Final.value,
        },
      });

      const reservaAuditPromise = tx.reservas_audit.create({
        data: {
          idReserva: reservaAntiga.id,
          ip,
          exercicio: resultPermissao.exercicio!,
          acao: AuditoriaMovimentoReservas.CANCELAR_RESERVA,
          obs: reservaAntiga.obs,
          resumo: reservaAntiga.resumo,
          pedidoPlurianual: reservaAntiga.pedidoPlurianual,
          usarMes1: reservaAntiga.usarMes1,
          usarMes2: reservaAntiga.usarMes2,
          usarMes3: reservaAntiga.usarMes3,
          usarMes4: reservaAntiga.usarMes4,
          usarMes5: reservaAntiga.usarMes5,
          usarMes6: reservaAntiga.usarMes6,
          usarMes7: reservaAntiga.usarMes7,
          usarMes8: reservaAntiga.usarMes8,
          usarMes9: reservaAntiga.usarMes9,
          usarMes10: reservaAntiga.usarMes10,
          usarMes11: reservaAntiga.usarMes11,
          usarMes12: reservaAntiga.usarMes12,
          usarTotal: reservaAntiga.usarTotal,
          pluUsarMes1: reservaAntiga.pluUsarMes1,
          pluUsarMes2: reservaAntiga.pluUsarMes2,
          pluUsarMes3: reservaAntiga.pluUsarMes3,
          pluUsarMes4: reservaAntiga.pluUsarMes4,
          pluUsarMes5: reservaAntiga.pluUsarMes5,
          pluUsarMes6: reservaAntiga.pluUsarMes6,
          pluUsarMes7: reservaAntiga.pluUsarMes7,
          pluUsarMes8: reservaAntiga.pluUsarMes8,
          pluUsarMes9: reservaAntiga.pluUsarMes9,
          pluUsarMes10: reservaAntiga.pluUsarMes10,
          pluUsarMes11: reservaAntiga.pluUsarMes11,
          pluUsarMes12: reservaAntiga.pluUsarMes12,
          pluUsarTotal: reservaAntiga.pluUsarTotal,
          idSecretario: reservaAntiga.idSecretario,
          idDiretor: reservaAntiga.idDiretor,
          idPrefeito: reservaAntiga.idPrefeito,
          idEconomicaItem: reservaAntiga.idEconomicaItem,
          idUsuario: resultPermissao.idUsuario!,
          cotaMes1: dotacao.cotaMes1,
          cotaMes2: dotacao.cotaMes2,
          cotaMes3: dotacao.cotaMes3,
          cotaMes4: dotacao.cotaMes4,
          cotaMes5: dotacao.cotaMes5,
          cotaMes6: dotacao.cotaMes6,
          cotaMes7: dotacao.cotaMes7,
          cotaMes8: dotacao.cotaMes8,
          cotaMes9: dotacao.cotaMes9,
          cotaMes10: dotacao.cotaMes10,
          cotaMes11: dotacao.cotaMes11,
          cotaMes12: dotacao.cotaMes12,
          valorTotalCota: dotacao.valorAtual,
        },
      });

      await Promise.all([
        reservaAuditPromise,
        atualizarReservaPromise,
        atualizarDotacaoPromise,
      ]);

      idReserva = reservaAntiga.id;
      idDotacao = dotacao.id;
      exercicio = reservaAntiga.exercicio;
      modificado = reservaAntiga.ultimaModificacao;
    });
    if (idReserva && idDotacao && exercicio && modificado) {
      const supabase = createSuperClient();
      const deletarPdf = await supabase.storage
        .from('reservas')
        .remove([
          montarPathPdfReserva(idReserva, idDotacao, exercicio, modificado),
        ]);
      console.log(JSON.stringify(deletarPdf));
      //TODO: Deletar outros anexos?
    }
    revalidatePath('/movimento/reservas');
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosReserva).includes(e.message as ErrosReserva)) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao cancelar reserva.`,
    };
  }
};

export const solicitarAssinaturasReserva = async (params: unknown) => {
  const parsedParams = solicitarAssinaturasReservaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { idReserva, gestor, secretario, diretor, prefeito } =
    parsedParams.data;

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.ALTERAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const posicoes = prefeito
    ? {
        gestor: { top: 15.2, left: 3.3 },
        secretario: { top: 17.5, left: 12.8 },
        diretor: { top: 17.8, left: 3.3 },
        prefeito: { top: 17.4, left: 22 },
      }
    : {
        gestor: { top: 15.2, left: 5.6 },
        secretario: { top: 17.5, left: 19.4 },
        diretor: { top: 17.8, left: 5.6 },
      };

  const dados: Prisma.reservas_assinaturasCreateManyInput[] = [];

  if (gestor) {
    dados.push({
      idReserva,
      idUsuario: gestor,
      top: posicoes.gestor.top,
      left: posicoes.gestor.left,
    });
  }

  if (secretario) {
    dados.push({
      idReserva,
      idUsuario: secretario,
      top: posicoes.secretario.top,
      left: posicoes.secretario.left,
    });
  }

  if (diretor) {
    dados.push({
      idReserva,
      idUsuario: diretor,
      top: posicoes.diretor.top,
      left: posicoes.diretor.left,
    });
  }

  if (prefeito && posicoes.prefeito) {
    dados.push({
      idReserva,
      idUsuario: prefeito,
      top: posicoes.prefeito.top,
      left: posicoes.prefeito.left,
    });
  }

  try {
    await prisma.$transaction(async (tx) => {
      const reserva = await tx.reservas.findUnique({
        where: {
          id: idReserva,
        },
      });

      if (!reserva) {
        throw new Error(ErrosReserva.RESERVA_NAO_ENCONTRADA);
      }

      if (reserva.status !== StatusReserva.Reservado) {
        throw new Error(ErrosReserva.STATUS_INVALIDO);
      }

      const createPromise = tx.reservas_assinaturas.createMany({
        data: dados,
      });

      const updatePromise = tx.reservas.update({
        where: {
          id: idReserva,
        },
        data: {
          status: StatusReserva['Aguardando Assinaturas'],
        },
      });

      await Promise.all([createPromise, updatePromise]);
    });
    revalidatePath('/movimento/reservas');
    revalidatePath('/assinatura/reservas');
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosReserva).includes(e.message as ErrosReserva)) {
        return {
          error: e.message,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_RESERVA,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao solicitar assinaturas da reserva.`,
    };
  }
};
