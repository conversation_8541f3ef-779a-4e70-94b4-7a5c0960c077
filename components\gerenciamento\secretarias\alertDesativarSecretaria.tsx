import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { desativarSecretaria } from '@/lib/database/gerenciamento/secretarias';
import { codigoSecretariaMask, toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Ban } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertDesativarSecretaria({
  idSecretaria,
  nomeSecretaria,
  codigoSecretaria,
}: {
  idSecretaria: number;
  nomeSecretaria: string;
  codigoSecretaria: number;
}) {
  const [loading, setLoading] = useState(false);

  const desativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idSecretaria,
      };

      const res = await desativarSecretaria(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Secretaria desativada.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline' className='text-red-800'>
          <Ban className='mr-2 size-4' /> Desativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Desativar Secretaria?</AlertDialogTitle>
          <AlertDialogDescription>
            A secretaria{' '}
            <span className='font-bold'>
              {codigoSecretariaMask(codigoSecretaria.toString())}.00.00 -{' '}
              {nomeSecretaria}
            </span>{' '}
            será desativada.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              desativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Desativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
