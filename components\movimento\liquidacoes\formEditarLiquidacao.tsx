'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Loader2, Save } from 'lucide-react';
import { cn, currencyOptionsNoSymbol, toastAlgoDeuErrado } from '@/lib/utils';
import { editarLiquidacaoSchema } from '@/lib/validation';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  editarLiquidacao,
  consultarSaldoEmpenhoParaLiquidacao,
  obterLiquidacao,
} from '@/lib/database/movimento/liquidacoes';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { FormAdicionarDocumento } from './formAdicionarDocumento';
import { DocumentosDataTable } from './documentosDataTable';
import currency from 'currency.js';

interface Documento {
  numeroDocumento: string;
  dataEmissao: Date;
  dataRecebimento: Date;
  dataMaterialServico?: Date;
  valorDocumento: number;
  // Optional file fields (for edit mode when files are uploaded)
  nomeArquivo?: string;
  caminhoArquivo?: string;
  tamanho?: number;
}

interface FormEditarLiquidacaoProps {
  liquidacao: NonNullable<Awaited<ReturnType<typeof obterLiquidacao>>['data']>;
}

export function FormEditarLiquidacao({
  liquidacao,
}: FormEditarLiquidacaoProps) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [empenhoData, setEmpenhoData] = useState<Awaited<
    ReturnType<typeof consultarSaldoEmpenhoParaLiquidacao>
  > | null>(null);
  const [documentos, setDocumentos] = useState<Documento[]>([]);

  const valorTotalDocumentos = documentos.reduce(
    (total, doc) => {
      return currency(total, currencyOptionsNoSymbol).add(doc.valorDocumento);
    },
    currency(0, currencyOptionsNoSymbol)
  );

  const form = useForm<z.infer<typeof editarLiquidacaoSchema>>({
    resolver: zodResolver(editarLiquidacaoSchema),
    defaultValues: {
      id: liquidacao.id,
      resumo: liquidacao.resumo || '',
      obs: liquidacao.obs || '',
      mesReferencia: liquidacao.mesReferencia || '',
      documentos: [],
    },
  });

  // Sync documents with form data
  useEffect(() => {
    form.setValue('documentos', documentos);
  }, [documentos, form]);

  // Load initial data
  useEffect(() => {
    // Load empenho data
    buscarEmpenho(liquidacao.empenho.id);

    // Load existing documents
    const existingDocuments: Documento[] =
      liquidacao.liquidacoes_documentos.map((doc) => ({
        numeroDocumento: doc.numeroDocumento,
        dataEmissao: doc.dataEmissao,
        dataRecebimento: doc.dataRecebimento,
        dataMaterialServico: doc.dataMaterialServico || undefined,
        valorDocumento: doc.valorDocumento,
        nomeArquivo: doc.nomeArquivo || undefined,
        caminhoArquivo: doc.caminhoArquivo || undefined,
        tamanho: doc.tamanho || undefined,
      }));
    setDocumentos(existingDocuments);
  }, [liquidacao]);

  const buscarEmpenho = async (idEmpenho: number) => {
    try {
      setLoading(true);
      const res = await consultarSaldoEmpenhoParaLiquidacao({ id: idEmpenho });
      if (res?.error) {
        setLoading(false);
        toast.error(res.error);
        setEmpenhoData(null);
      } else {
        setLoading(false);
        setEmpenhoData(res);
      }
    } catch (error: any) {
      setLoading(false);
      console.log(JSON.stringify(error));
      toast.error(toastAlgoDeuErrado);
    }
  };

  const adicionarDocumento = (documento: Documento) => {
    setDocumentos([...documentos, documento]);
  };

  const removerDocumento = (index: number) => {
    setDocumentos(documentos.filter((_, i) => i !== index));
  };

  const onSubmit = async (values: z.infer<typeof editarLiquidacaoSchema>) => {
    try {
      setLoading(true);
      const res = await editarLiquidacao({
        ...values,
        documentos: documentos,
      });
      if (res?.error) {
        setLoading(false);
        toast.error(res.error, { duration: 10000 });
      } else {
        toast.success('Liquidação editada com sucesso!');
        router.push(`/movimento/liquidacoes/visualizar/${liquidacao.id}`);
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  // Calculate available balance (add back current liquidacao value)
  const saldoDisponivel = empenhoData?.data
    ? empenhoData.data.saldo.saldoDisponivel + liquidacao.valorTotal
    : 0;

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            {empenhoData?.data && (
              <>
                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Empenho</Label>
                    <Input
                      disabled
                      value={`${empenhoData.data.empenho.numero}/${empenhoData.data.empenho.exercicio}`}
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Fornecedor</Label>
                    <Input
                      disabled
                      value={empenhoData.data.empenho.fornecedor?.nome || ''}
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Despesa</Label>
                    <Input
                      disabled
                      value={`${empenhoData.data.empenho.despesa} - ${empenhoData.data.empenho.descricaoDespesa}`}
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>
                      Saldo Disponível (incluindo esta liquidação)
                    </Label>
                    <Input
                      disabled
                      value={currency(saldoDisponivel, currencyOptionsNoSymbol)
                        .format()
                        .replace(/0$/, '')}
                      className={cn(
                        'text-right',
                        saldoDisponivel > 0 ? 'text-green-600' : 'text-red-600'
                      )}
                    />
                  </div>
                </div>

                <Separator className='my-8' />

                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <FormField
                    control={form.control}
                    name='resumo'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='w-full text-left'>
                          Resumo
                        </FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='mesReferencia'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='w-full text-left'>
                          Mês de Referência
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='Ex: Janeiro/2024' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='mt-4'>
                  <FormField
                    control={form.control}
                    name='obs'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='w-full text-left'>
                          Observações
                        </FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Separator className='my-8' />

                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>Documentos</h3>
                    <FormAdicionarDocumento
                      onAdicionarDocumento={adicionarDocumento}
                      saldoDisponivel={saldoDisponivel}
                      valorJaAdicionado={valorTotalDocumentos.value}
                      idLiquidacao={liquidacao.id}
                      exercicio={liquidacao.exercicio}
                      idDotacao={liquidacao.empenho.reserva?.dotacao.id}
                      idEmpenho={liquidacao.empenho.id}
                    />
                  </div>

                  {documentos.length > 0 && (
                    <DocumentosDataTable
                      documentos={documentos}
                      onRemoverDocumento={removerDocumento}
                    />
                  )}
                </div>

                <div className='mt-8 flex gap-4'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => router.back()}
                  >
                    <ArrowLeft className='mr-2 size-4' />
                    Voltar
                  </Button>
                  <Button
                    type='submit'
                    disabled={
                      loading ||
                      documentos.length === 0 ||
                      valorTotalDocumentos.value > saldoDisponivel ||
                      valorTotalDocumentos.value <= 0
                    }
                  >
                    {loading ? (
                      <Loader2 className='mr-2 size-4 animate-spin' />
                    ) : (
                      <Save className='mr-2 size-4' />
                    )}
                    Salvar Alterações
                  </Button>
                </div>
              </>
            )}
          </form>
        </Form>
      </div>
    </>
  );
}
