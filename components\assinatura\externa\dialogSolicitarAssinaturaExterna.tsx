'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Plus } from 'lucide-react';
import { toast } from 'sonner';
import { ComboboxSelecionarUsuarioExterno } from './comboboxSelecionarUsuarioExterno';
import { ComboboxSelecionarTemplate } from './comboboxSelecionarTemplate';
import { EditorTemplate } from '@/components/gerenciamento/templatesDocumentos/editorTemplate';
import { criarDocumentoExterno } from '@/lib/database/assinatura/documentosExternos';
import { TipoOrigemDocumento } from '@/lib/enums';
import { toastAlgoDeuErrado } from '@/lib/utils';
import Link from 'next/link';

const formSchema = z.object({
  idUsuarioExterno: z.coerce.number().min(1, 'Selecione um usuário externo'),
  idTemplate: z.coerce.number().min(1, 'Selecione um template'),
  nomeDocumento: z.string().min(2, 'Nome do documento é obrigatório').max(255),
  descricaoDocumento: z.string().max(500).optional(),
  instrucoes: z.string().max(1000).optional(),
  conteudoEditado: z.string().min(1, 'Conteúdo do documento é obrigatório'),
});

interface DialogSolicitarAssinaturaExternaProps {
  isOpen: boolean;
  onClose: () => void;
  idReserva: number;
  reserva: {
    id: number;
    resumo: string;
    dotacao: {
      id: number;
      despesa: number;
    };
    exercicio: number;
  };
  onSuccess: () => void;
}

export function DialogSolicitarAssinaturaExterna({
  isOpen,
  onClose,
  idReserva,
  reserva,
  onSuccess,
}: DialogSolicitarAssinaturaExternaProps) {
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'selecao' | 'edicao'>('selecao');
  const [templateSelecionado, setTemplateSelecionado] = useState<any>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nomeDocumento: `Documento - Reserva ${reserva.id}`,
      descricaoDocumento: reserva.resumo,
      instrucoes:
        'Por favor, assine digitalmente este documento e faça o upload da versão assinada.',
      conteudoEditado: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setLoading(true);

      const resultado = await criarDocumentoExterno({
        idOrigemTipo: TipoOrigemDocumento.Reserva,
        idOrigemRegistro: idReserva,
        idUsuarioExterno: values.idUsuarioExterno,
        idTemplate: values.idTemplate,
        nomeDocumento: values.nomeDocumento,
        descricaoDocumento: values.descricaoDocumento,
        instrucoes: values.instrucoes,
        variaveisPersonalizadas: {
          '{{NUMERO_RESERVA}}': reserva.id.toString(),
          '{{RESUMO_RESERVA}}': reserva.resumo,
          '{{DESPESA}}': reserva.dotacao.despesa.toString(),
          '{{EXERCICIO}}': reserva.exercicio.toString(),
        },
      });

      if (resultado.error) {
        toast.error(resultado.error);
        return;
      }

      toast.success('Solicitação de assinatura externa criada com sucesso!');
      onSuccess();
    } catch (error) {
      console.error(error);
      toast.error(toastAlgoDeuErrado);
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template: any) => {
    setTemplateSelecionado(template);
    form.setValue('conteudoEditado', template.conteudoHtml);
    setStep('edicao');
  };

  const handleVoltar = () => {
    setStep('selecao');
    setTemplateSelecionado(null);
  };

  const handleClose = () => {
    if (!loading) {
      setStep('selecao');
      setTemplateSelecionado(null);
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>
            {step === 'selecao'
              ? 'Solicitar Assinatura Externa'
              : 'Editar Documento'}
          </DialogTitle>
          <DialogDescription>
            {step === 'selecao'
              ? 'Selecione o usuário externo e o template para o documento.'
              : 'Edite o conteúdo do documento antes de enviar.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            {step === 'selecao' && (
              <>
                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <FormField
                    control={form.control}
                    name='idUsuarioExterno'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Usuário Externo</FormLabel>
                        <div className='flex gap-2'>
                          <FormControl className='flex-1'>
                            <ComboboxSelecionarUsuarioExterno
                              value={field.value}
                              onChange={field.onChange}
                            />
                          </FormControl>
                          <Link
                            href='/gerenciamento/usuarios-externos/novo'
                            target='_blank'
                          >
                            <Button type='button' variant='outline' size='icon'>
                              <Plus className='h-4 w-4' />
                            </Button>
                          </Link>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='idTemplate'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Template</FormLabel>
                        <FormControl>
                          <ComboboxSelecionarTemplate
                            value={field.value}
                            onChange={field.onChange}
                            onTemplateSelect={handleTemplateSelect}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name='nomeDocumento'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome do Documento</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='descricaoDocumento'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição (Opcional)</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={2} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='instrucoes'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Instruções para o Usuário Externo</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={3} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {step === 'edicao' && templateSelecionado && (
              <FormField
                control={form.control}
                name='conteudoEditado'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conteúdo do Documento</FormLabel>
                    <FormControl>
                      <EditorTemplate
                        value={field.value}
                        onChange={field.onChange}
                        height={400}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              {step === 'edicao' && (
                <Button
                  type='button'
                  variant='outline'
                  onClick={handleVoltar}
                  disabled={loading}
                >
                  Voltar
                </Button>
              )}

              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={loading}
              >
                Cancelar
              </Button>

              {step === 'selecao' && (
                <Button
                  type='button'
                  onClick={() => {
                    const templateId = form.getValues('idTemplate');
                    if (templateId) {
                      // Buscar template e ir para edição
                      setStep('edicao');
                    } else {
                      toast.error('Selecione um template primeiro');
                    }
                  }}
                  disabled={loading || !form.getValues('idTemplate')}
                >
                  Próximo
                </Button>
              )}

              {step === 'edicao' && (
                <Button type='submit' disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Criando...
                    </>
                  ) : (
                    'Solicitar Assinatura'
                  )}
                </Button>
              )}
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
