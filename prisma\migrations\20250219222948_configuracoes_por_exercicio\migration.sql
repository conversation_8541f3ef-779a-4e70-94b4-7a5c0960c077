/*
  Warnings:

  - The primary key for the `configuracoes` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `config` on the `configuracoes` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[exercicio]` on the table `configuracoes` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `exercicio` to the `configuracoes` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "configuracoes" DROP CONSTRAINT "configuracoes_pkey",
DROP COLUMN "config",
ADD COLUMN     "exercicio" SMALLINT NOT NULL,
ADD COLUMN     "id" SERIAL NOT NULL,
ADD CONSTRAINT "configuracoes_pkey" PRIMARY KEY ("id");

-- CreateIndex
CREATE UNIQUE INDEX "configuracoes_exercicio_key" ON "configuracoes"("exercicio");
