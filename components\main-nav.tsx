'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { siteConfig } from '@/config/site';
import { cn } from '@/lib/utils';
import { Avatar, AvatarImage } from './ui/avatar';

export function MainNav() {
  const pathname = usePathname();

  return (
    <div className='mr-4 flex'>
      <Link href='/dashboard' className='mr-6 flex items-center space-x-2'>
        <Avatar>
          <AvatarImage src='/gestao-digital.svg' className='dark:invert' />
        </Avatar>
        <span className='hidden font-bold sm:inline-block'>
          {siteConfig.name}
        </span>
      </Link>
      <nav className='flex items-center space-x-6 text-sm font-medium'>
        <Link
          href='/documentacao'
          className={cn(
            'hover:text-foreground/80 transition-colors',
            pathname === '/documentacao'
              ? 'text-foreground'
              : 'text-foreground/60'
          )}
        >
          Guia do Usuário
        </Link>
      </nav>
      <nav className='ml-4 flex items-center space-x-6 text-sm font-medium'>
        <Link
          href='/docs'
          className={cn(
            'hover:text-foreground/80 transition-colors',
            pathname === '/docs' ? 'text-foreground' : 'text-foreground/60'
          )}
        >
          Suporte
        </Link>
      </nav>
    </div>
  );
}
