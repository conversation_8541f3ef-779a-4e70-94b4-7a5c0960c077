'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Check, Loader2, Save } from 'lucide-react';
import {
  cn,
  codigoSecretariaMask,
  currencyOptionsNoSymbol,
  economicaMask,
  moneyMask,
  moneyUnmask,
  numeroMask,
  obterAnoAtual,
  obterMesAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { criarReservaSchema, idSchema } from '@/lib/validation';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON> } from '@/lib/enums';
import { toast } from 'sonner';
import {
  buscarDespesa,
  editarReserva,
  obterReserva,
} from '@/lib/database/movimento/reservas';
import { ComboboxSelecionarEconomicaItem } from './comboboxSelecionarEconomicaItem';
import currency from 'currency.js';
import { Separator } from '@/components/ui/separator';
import { FormAdicionarItem } from './formAdicionarItem';
import { Item } from '@/types/app';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
export default function FormEditarReserva({
  reserva,
}: {
  reserva: Awaited<ReturnType<typeof obterReserva>>;
}) {
  const router = useRouter();

  const dadosReserva = reserva.data!;

  const [loading, setLoading] = useState(false);
  const [despesa, setDespesa] = useState<Awaited<
    ReturnType<typeof buscarDespesa>
  > | null>(null);
  const [itensConfigurados, setItensConfigurados] = useState<Item[]>(
    dadosReserva.reserva_itens
  );
  const [pedidoPlurianual, setPedidoPlurianual] = useState(
    dadosReserva.pedidoPlurianual
  );

  const [aumentarDecimais, setAumentarDecimais] = useState(false);
  const decimais = aumentarDecimais ? 3 : 2;

  const valorTotalItens = itensConfigurados.reduce(
    (total, item) => {
      const itemTotal = currency(item.valor, currencyOptionsNoSymbol).multiply(
        item.quantidade
      );
      return itemTotal.add(total);
    },
    currency(0, currencyOptionsNoSymbol)
  );

  let {
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
  } = despesa?.data || {};

  const anoAtual = obterAnoAtual();
  const mesAtual = dadosReserva.exercicio < anoAtual ? 12 : obterMesAtual();

  cotaMes1 = currency(cotaMes1 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes1
  ).value;
  cotaMes2 = currency(cotaMes2 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes2
  ).value;
  cotaMes3 = currency(cotaMes3 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes3
  ).value;
  cotaMes4 = currency(cotaMes4 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes4
  ).value;
  cotaMes5 = currency(cotaMes5 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes5
  ).value;
  cotaMes6 = currency(cotaMes6 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes6
  ).value;
  cotaMes7 = currency(cotaMes7 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes7
  ).value;
  cotaMes8 = currency(cotaMes8 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes8
  ).value;
  cotaMes9 = currency(cotaMes9 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes9
  ).value;
  cotaMes10 = currency(cotaMes10 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes10
  ).value;
  cotaMes11 = currency(cotaMes11 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes11
  ).value;
  cotaMes12 = currency(cotaMes12 || 0, currencyOptionsNoSymbol).add(
    dadosReserva.usarMes12
  ).value;

  const [mes1BRL, setMes1BRL] = useState(
    currency(dadosReserva.usarMes1, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes2BRL, setMes2BRL] = useState(
    currency(dadosReserva.usarMes2, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes3BRL, setMes3BRL] = useState(
    currency(dadosReserva.usarMes3, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes4BRL, setMes4BRL] = useState(
    currency(dadosReserva.usarMes4, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes5BRL, setMes5BRL] = useState(
    currency(dadosReserva.usarMes5, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes6BRL, setMes6BRL] = useState(
    currency(dadosReserva.usarMes6, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes7BRL, setMes7BRL] = useState(
    currency(dadosReserva.usarMes7, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes8BRL, setMes8BRL] = useState(
    currency(dadosReserva.usarMes8, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes9BRL, setMes9BRL] = useState(
    currency(dadosReserva.usarMes9, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes10BRL, setMes10BRL] = useState(
    currency(dadosReserva.usarMes10, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes11BRL, setMes11BRL] = useState(
    currency(dadosReserva.usarMes11, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes12BRL, setMes12BRL] = useState(
    currency(dadosReserva.usarMes12, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );

  const total = currency(mes1BRL, currencyOptionsNoSymbol)
    .add(mes2BRL)
    .add(mes3BRL)
    .add(mes4BRL)
    .add(mes5BRL)
    .add(mes6BRL)
    .add(mes7BRL)
    .add(mes8BRL)
    .add(mes9BRL)
    .add(mes10BRL)
    .add(mes11BRL)
    .add(mes12BRL);

  const [mes1BRLPluri, setMes1BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes1, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes2BRLPluri, setMes2BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes2, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes3BRLPluri, setMes3BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes3, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes4BRLPluri, setMes4BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes4, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes5BRLPluri, setMes5BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes5, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes6BRLPluri, setMes6BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes6, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes7BRLPluri, setMes7BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes7, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes8BRLPluri, setMes8BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes8, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes9BRLPluri, setMes9BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes9, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes10BRLPluri, setMes10BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes10, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes11BRLPluri, setMes11BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes11, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );
  const [mes12BRLPluri, setMes12BRLPluri] = useState(
    currency(dadosReserva.pluUsarMes12, currencyOptionsNoSymbol)
      .format()
      .replace(/0$/, '')
  );

  const totalPluri = currency(mes1BRLPluri, currencyOptionsNoSymbol)
    .add(mes2BRLPluri)
    .add(mes3BRLPluri)
    .add(mes4BRLPluri)
    .add(mes5BRLPluri)
    .add(mes6BRLPluri)
    .add(mes7BRLPluri)
    .add(mes8BRLPluri)
    .add(mes9BRLPluri)
    .add(mes10BRLPluri)
    .add(mes11BRLPluri)
    .add(mes12BRLPluri);

  const valorItensEmAberto = total.subtract(valorTotalItens);

  const restante = currency(
    despesa?.data?.valorAtual || 0,
    currencyOptionsNoSymbol
  ).subtract(total);

  const subdepartamento = despesa?.data?.subdepartamento || null;
  const departamento = subdepartamento
    ? despesa?.data?.subdepartamento?.departamento || null
    : despesa?.data?.departamento || null;
  const secretaria = subdepartamento
    ? despesa?.data?.subdepartamento?.departamento?.secretaria || null
    : departamento
      ? despesa?.data?.departamento?.secretaria || null
      : despesa?.data?.secretaria || null;

  const secretarios = despesa?.data?.secretariosAssinantes || [];
  const diretores = despesa?.data?.diretoresAssinantes || [];
  const prefeitos = despesa?.data?.prefeitosAssinantes || [];

  const form = useForm<z.infer<typeof criarReservaSchema>>({
    resolver: zodResolver(criarReservaSchema),
    defaultValues: {
      //@ts-ignore
      despesa: dadosReserva.dotacao.despesa || null,
      obs: dadosReserva.obs || '',
      resumo: dadosReserva.resumo || '',
      valorDisponivelCota: 0,
      usarMes1: dadosReserva.usarMes1,
      usarMes2: dadosReserva.usarMes2,
      usarMes3: dadosReserva.usarMes3,
      usarMes4: dadosReserva.usarMes4,
      usarMes5: dadosReserva.usarMes5,
      usarMes6: dadosReserva.usarMes6,
      usarMes7: dadosReserva.usarMes7,
      usarMes8: dadosReserva.usarMes8,
      usarMes9: dadosReserva.usarMes9,
      usarMes10: dadosReserva.usarMes10,
      usarMes11: dadosReserva.usarMes11,
      usarMes12: dadosReserva.usarMes12,

      pedidoPlurianual: dadosReserva.pedidoPlurianual,

      pluUsarMes1: dadosReserva.pluUsarMes1,
      pluUsarMes2: dadosReserva.pluUsarMes2,
      pluUsarMes3: dadosReserva.pluUsarMes3,
      pluUsarMes4: dadosReserva.pluUsarMes4,
      pluUsarMes5: dadosReserva.pluUsarMes5,
      pluUsarMes6: dadosReserva.pluUsarMes6,
      pluUsarMes7: dadosReserva.pluUsarMes7,
      pluUsarMes8: dadosReserva.pluUsarMes8,
      pluUsarMes9: dadosReserva.pluUsarMes9,
      pluUsarMes10: dadosReserva.pluUsarMes10,
      pluUsarMes11: dadosReserva.pluUsarMes11,
      pluUsarMes12: dadosReserva.pluUsarMes12,

      idSecretario: dadosReserva.idSecretario,
      idDiretor: dadosReserva.idDiretor,
      idPrefeito: dadosReserva.idPrefeito,
      economicaItemId: dadosReserva.idEconomicaItem,
    },
  });

  const subMes1 = currency(cotaMes1 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes1'))
    .format()
    .replace(/0$/, '');
  const subMes2 = currency(cotaMes2 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes2'))
    .format()
    .replace(/0$/, '');
  const subMes3 = currency(cotaMes3 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes3'))
    .format()
    .replace(/0$/, '');
  const subMes4 = currency(cotaMes4 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes4'))
    .format()
    .replace(/0$/, '');
  const subMes5 = currency(cotaMes5 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes5'))
    .format()
    .replace(/0$/, '');
  const subMes6 = currency(cotaMes6 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes6'))
    .format()
    .replace(/0$/, '');
  const subMes7 = currency(cotaMes7 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes7'))
    .format()
    .replace(/0$/, '');
  const subMes8 = currency(cotaMes8 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes8'))
    .format()
    .replace(/0$/, '');
  const subMes9 = currency(cotaMes9 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes9'))
    .format()
    .replace(/0$/, '');
  const subMes10 = currency(cotaMes10 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes10'))
    .format()
    .replace(/0$/, '');
  const subMes11 = currency(cotaMes11 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes11'))
    .format()
    .replace(/0$/, '');
  const subMes12 = currency(cotaMes12 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('usarMes12'))
    .format()
    .replace(/0$/, '');

  // const resetForm = () => {
  //   form.reset();
  //   setMes1BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes2BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes3BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes4BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes5BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes6BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes7BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes8BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes9BRL(currency(0, currencyOptionsNoSymbol).format().replace(/0$/, ''));
  //   setMes10BRL(
  //     currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  //   );
  //   setMes11BRL(
  //     currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  //   );
  //   setMes12BRL(
  //     currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  //   );
  // };

  useEffect(() => {
    const buscarDespesa_ = async () => {
      try {
        setLoading(true);
        const data: z.infer<typeof idSchema> = {
          id: form.getValues().despesa, //usando id mas com numero da despesa
        };
        const res = await buscarDespesa(data);
        if (res?.error) {
          setLoading(false);
          toast.error(res.error);
          setDespesa(null);
        } else {
          setLoading(false);
          setDespesa(res);
          form.resetField('economicaItemId');
          form.setValue('valorDisponivelCota', res?.data?.valorAtual || 0);
        }
      } catch (error: any) {
        setLoading(false);
        toast.error(toastAlgoDeuErrado);
      }
    };
    buscarDespesa_();
  }, [form]);

  useEffect(() => {
    const valores = [
      mes1BRL,
      mes2BRL,
      mes3BRL,
      mes4BRL,
      mes5BRL,
      mes6BRL,
      mes7BRL,
      mes8BRL,
      mes9BRL,
      mes10BRL,
      mes11BRL,
      mes12BRL,
    ];
    for (const mes of valores) {
      if (mes.split(',')[1].length > 2) {
        setAumentarDecimais(true);
        break;
      }
    }
  }, [
    mes1BRL,
    mes2BRL,
    mes3BRL,
    mes4BRL,
    mes5BRL,
    mes6BRL,
    mes7BRL,
    mes8BRL,
    mes9BRL,
    mes10BRL,
    mes11BRL,
    mes12BRL,
  ]);

  const onSubmit = async (values: z.infer<typeof criarReservaSchema>) => {
    try {
      setLoading(true);
      const res = await editarReserva({
        ...values,
        itens: itensConfigurados,
        id: dadosReserva.id,
      });
      if (res?.error) {
        setLoading(false);
        toast(res.error, { duration: 10000 });
      } else {
        toast.success('Reserva atualizada.');
        router.push('/movimento/reservas');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex gap-2'>
              <FormField
                control={form.control}
                name='despesa'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Despesa
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        onChange={(e) =>
                          form.setValue(
                            'despesa',
                            Number(numeroMask(e.target.value))
                          )
                        }
                        className='w-[72px]'
                        disabled
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormItem>
                <FormLabel className='flex w-full text-left'>&nbsp;</FormLabel>
                <Button
                  className='mr-8'
                  aria-description='Estado da busca da despesa'
                  disabled={true}
                  variant={'outline'}
                >
                  {loading ? (
                    <Loader2 className='size-4 animate-spin' />
                  ) : (
                    <Check className='size-4' />
                  )}
                </Button>
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Descrição
                </FormLabel>
                <Input disabled={true} value={despesa?.data?.desc || ''} />
              </FormItem>
            </div>
            <div className='mt-12 flex flex-wrap gap-4'>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Secretaria
                </FormLabel>
                <Input
                  disabled={true}
                  value={
                    secretaria
                      ? `${codigoSecretariaMask(secretaria.codigo.toString())}.00.00 - ${secretaria.nome}`
                      : ''
                  }
                />
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Departamento
                </FormLabel>
                <Input
                  disabled={true}
                  value={
                    secretaria && departamento
                      ? `${codigoSecretariaMask(secretaria.codigo.toString())}.${codigoSecretariaMask(departamento.codigo.toString())}.00 - ${departamento.nome}`
                      : ''
                  }
                />
              </FormItem>
              {secretarios.length > 1 && (
                <FormField
                  control={form.control}
                  name='idSecretario'
                  render={({ field }) => (
                    <FormItem className='w-full'>
                      <FormLabel className='w-full text-left'>
                        Secretário
                      </FormLabel>
                      <Select
                        onValueChange={(e) => {
                          form.setValue('idSecretario', Number(e), {
                            shouldDirty: true,
                          });
                        }}
                        value={`${field.value}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Selecione o secretário que irá assinar...' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {despesa?.data?.secretariosAssinantes.map(
                            (secretario) => (
                              <SelectItem
                                key={secretario.id}
                                value={`${secretario.id}`}
                              >
                                {secretario.nome}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              )}
              {diretores.length > 1 && (
                <FormField
                  control={form.control}
                  name='idDiretor'
                  render={({ field }) => (
                    <FormItem className='w-full'>
                      <FormLabel className='w-full text-left'>
                        Diretor
                      </FormLabel>
                      <Select
                        onValueChange={(e) => {
                          form.setValue('idDiretor', Number(e), {
                            shouldDirty: true,
                          });
                        }}
                        value={`${field.value}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Selecione o diretor que irá assinar...' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {despesa?.data?.diretoresAssinantes.map(
                            (diretores) => (
                              <SelectItem
                                key={diretores.id}
                                value={`${diretores.id}`}
                              >
                                {diretores.nome}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              )}
              {prefeitos.length > 1 && (
                <FormField
                  control={form.control}
                  name='idPrefeito'
                  render={({ field }) => (
                    <FormItem className='w-full'>
                      <FormLabel className='w-full text-left'>
                        Prefeito
                      </FormLabel>
                      <Select
                        onValueChange={(e) => {
                          form.setValue('idPrefeito', Number(e), {
                            shouldDirty: true,
                          });
                        }}
                        value={`${field.value}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Selecione o prefeito que irá assinar...' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {despesa?.data?.prefeitosAssinantes.map(
                            (prefeito) => (
                              <SelectItem
                                key={prefeito.id}
                                value={`${prefeito.id}`}
                              >
                                {prefeito.nome}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              )}
              {subdepartamento && (
                <FormItem className='w-full'>
                  <FormLabel className='flex w-full text-left'>
                    Departamento
                  </FormLabel>
                  <Input
                    disabled={true}
                    value={
                      secretaria && departamento && subdepartamento
                        ? `${codigoSecretariaMask(secretaria.codigo.toString())}.${codigoSecretariaMask(departamento.codigo.toString())}.${codigoSecretariaMask(subdepartamento.codigo.toString())} - ${subdepartamento.nome}`
                        : ''
                    }
                  />
                </FormItem>
              )}
            </div>

            <div className='mt-12 flex flex-wrap gap-4'>
              <Label className='w-full text-left'>Econômica</Label>
              <Input
                disabled={true}
                value={
                  despesa
                    ? `${economicaMask(despesa.data?.economica.codigo || '')} - ${despesa.data?.economica.desc}`
                    : ''
                }
              />

              <Label className='w-full text-left'>Item</Label>
              <ComboboxSelecionarEconomicaItem despesa={despesa} form={form} />
            </div>
            <div className='mt-12 flex flex-wrap gap-2'>
              <Label className='w-full text-left'>Funcional</Label>
              <Input
                disabled={true}
                value={
                  despesa
                    ? `${despesa.data?.funcional.codigo || ''} - ${despesa.data?.funcional.desc}`
                    : ''
                }
              />
            </div>

            <div className='mt-12 flex gap-2'>
              <FormItem className='flex w-full flex-wrap'>
                <FormLabel className='w-full text-left'>Fonte</FormLabel>
                <Input
                  disabled={true}
                  value={
                    despesa
                      ? `${despesa.data?.fonte} - ${Fontes[despesa.data?.fonte || 0]}`
                      : ''
                  }
                />
              </FormItem>
              <FormItem className='flex w-full flex-wrap'>
                <FormLabel className='w-full text-left'>
                  Cód. Aplicação
                </FormLabel>
                <Input
                  disabled={true}
                  value={despesa ? `${despesa.data?.codAplicacao}` : ''}
                />
              </FormItem>
            </div>

            <div className='mt-12 flex gap-2'>
              <FormField
                control={form.control}
                name='obs'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>Obs</FormLabel>
                    <FormControl>
                      <Input {...field} className='w-full' />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='resumo'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>
                      Resumo
                    </FormLabel>
                    <FormControl>
                      <Input {...field} className='w-full' />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <Separator className='my-12' />
            <span className='text-lg'>Valores</span>
            <div className='mt-8 flex items-center space-x-2'>
              <Switch
                id='aumentar-decimais'
                checked={aumentarDecimais}
                onCheckedChange={() => {
                  setAumentarDecimais(!aumentarDecimais);
                }}
                disabled={!despesa}
              />
              <Label htmlFor='aumentar-decimais' className='text-lg'>
                Aumentar Decimais
              </Label>
            </div>
            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              <FormField
                name='usarMes1'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Janeiro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes1') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes1) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes1}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes1BRL}
                        onChange={(e) => {
                          setMes1BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes1',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes1') === 0 && mesAtual > 1)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes2'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Fevereiro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes2') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes2) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes2}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes2BRL}
                        onChange={(e) => {
                          setMes2BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes2',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes2') === 0 && mesAtual > 2)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes3'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Março
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes3') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes3) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes3}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes3BRL}
                        onChange={(e) => {
                          setMes3BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes3',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes3') === 0 && mesAtual > 3)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes4'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Abril
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes4') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes4) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes4}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes4BRL}
                        onChange={(e) => {
                          setMes4BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes4',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes4') === 0 && mesAtual > 4)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes5'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Maio
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes5') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes5) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes5}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes5BRL}
                        onChange={(e) => {
                          setMes5BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes5',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes5') === 0 && mesAtual > 5)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes6'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Junho
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes6') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes6) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes6}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes6BRL}
                        onChange={(e) => {
                          setMes6BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes6',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes6') === 0 && mesAtual > 6)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes7'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Julho
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes7') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes7) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes7}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes7BRL}
                        onChange={(e) => {
                          setMes7BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes7',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes7') === 0 && mesAtual > 7)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes8'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Agosto
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes8') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes8) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes8}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes8BRL}
                        onChange={(e) => {
                          setMes8BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes8',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes8') === 0 && mesAtual > 8)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes9'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Setembro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes9') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes9) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes9}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes9BRL}
                        onChange={(e) => {
                          setMes9BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes9',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes9') === 0 && mesAtual > 9)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes10'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Outubro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes10') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes10) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes10}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes10BRL}
                        onChange={(e) => {
                          setMes10BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes10',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={!despesa || mesAtual > 10}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes11'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Novembro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes11') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes11) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes11}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes11BRL}
                        onChange={(e) => {
                          setMes11BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes11',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          !despesa ||
                          (form.watch('usarMes11') === 0 && mesAtual > 11)
                        }
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='usarMes12'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Dezembro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('usarMes12') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(subMes12) < 0 &&
                          'text-red-600 disabled:opacity-100'
                      )}
                      value={subMes12}
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes12BRL}
                        onChange={(e) => {
                          setMes12BRL(moneyMask(e.target.value, decimais));
                          form.setValue(
                            'usarMes12',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={!despesa}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-12 flex flex-wrap justify-between gap-6'>
              <FormField
                name='cotaRestante'
                render={({ field }) => (
                  <FormItem className='w-[150px]'>
                    <FormLabel className='flex text-left'>
                      Total Cota Restante
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'text-right text-base disabled:opacity-100',
                          restante.value < 0 ||
                            (despesa?.data?.valorAtual &&
                              total.value > despesa.data.valorAtual)
                            ? 'text-red-600'
                            : 'text-green-600'
                        )}
                        value={restante.format().replace(/0$/, '')}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='total'
                render={({ field }) => (
                  <FormItem className='w-[150px]'>
                    <FormLabel className='flex text-left'>
                      Reserva Total
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'text-right text-base disabled:opacity-100',
                          total.value === 0 ||
                            (despesa?.data?.valorAtual &&
                              total.value > despesa.data.valorAtual)
                            ? 'text-red-600'
                            : 'text-green-600'
                        )}
                        value={total.format().replace(/0$/, '')}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-8 flex items-center space-x-2'>
              <Switch
                id='ativar-plurianual'
                checked={pedidoPlurianual}
                onCheckedChange={() => {
                  setPedidoPlurianual(!pedidoPlurianual);
                  form.setValue('pedidoPlurianual', !pedidoPlurianual);
                }}
                disabled={!despesa}
              />
              <Label htmlFor='ativar-plurianual' className='text-lg'>
                Reserva Plurianual
              </Label>
            </div>
            {pedidoPlurianual && (
              <>
                <div className='mt-8 flex flex-wrap justify-between gap-6'>
                  <FormField
                    name='pluUsarMes1'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Janeiro
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes1BRLPluri}
                            onChange={(e) => {
                              setMes1BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes1',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes2'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Fevereiro
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes2BRLPluri}
                            onChange={(e) => {
                              setMes2BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes2',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes3'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Março
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes3BRLPluri}
                            onChange={(e) => {
                              setMes3BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes3',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes4'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Abril
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes4BRLPluri}
                            onChange={(e) => {
                              setMes4BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes4',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes5'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Maio
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes5BRLPluri}
                            onChange={(e) => {
                              setMes5BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes5',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes6'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Junho
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes6BRLPluri}
                            onChange={(e) => {
                              setMes6BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes6',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes7'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Julho
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes7BRLPluri}
                            onChange={(e) => {
                              setMes7BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes7',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes8'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Agosto
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes8BRLPluri}
                            onChange={(e) => {
                              setMes8BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes8',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes9'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Setembro
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes9BRLPluri}
                            onChange={(e) => {
                              setMes9BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes9',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes10'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Outubro
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes10BRLPluri}
                            onChange={(e) => {
                              setMes10BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes10',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes11'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Novembro
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='max-w-[150px] text-right'
                            value={mes11BRLPluri}
                            onChange={(e) => {
                              setMes11BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes11',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name='pluUsarMes12'
                    render={({ field }) => (
                      <FormItem className='flex max-w-[160px] flex-wrap'>
                        <FormLabel className='flex w-full text-left'>
                          Dezembro
                        </FormLabel>

                        <FormControl>
                          <Input
                            {...field}
                            className='w-[150px] text-right'
                            value={mes12BRLPluri}
                            onChange={(e) => {
                              setMes12BRLPluri(
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                'pluUsarMes12',
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <div className='mt-12 flex flex-wrap justify-between gap-6'>
                  <FormField
                    name='totalPluri'
                    render={({ field }) => (
                      <FormItem className='w-[150px]'>
                        <FormLabel className='flex text-left'>Total</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className={'text-right disabled:opacity-100'}
                            value={totalPluri.format().replace(/0$/, '')}
                            disabled={true}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </>
            )}
          </form>
          <div className='grid w-full gap-4 py-8'>
            <Separator className='my-12' />
            <span className='text-lg'>Itens</span>
            <FormAdicionarItem
              data={itensConfigurados}
              setData={setItensConfigurados}
              decimais={decimais}
            />
            <div className='flex flex-wrap justify-between gap-6'>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>
                  Valor em Aberto
                </FormLabel>
                <FormControl>
                  <Input
                    className={cn(
                      'text-right text-base disabled:opacity-100',
                      total.value !== valorTotalItens.value
                        ? 'text-red-600'
                        : 'text-green-600'
                    )}
                    value={valorItensEmAberto.format().replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>

              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>
                  Valor Total Itens
                </FormLabel>
                <FormControl>
                  <Input
                    className={cn(
                      'text-right text-base disabled:opacity-100',
                      total.value !== valorTotalItens.value
                        ? 'text-red-600'
                        : 'text-green-600'
                    )}
                    value={valorTotalItens.format().replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
            </div>
          </div>
          <FormMessage>
            {form.formState.errors.economicaItemId
              ? 'Selecione o Item da Econômica'
              : ''}
          </FormMessage>
        </Form>
      </div>

      <div className='mt-12 flex w-full justify-between'>
        <Button
          variant={'destructive'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/movimento/reservas');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={
            loading ||
            !despesa ||
            valorTotalItens.value !== total.value ||
            restante.value < 0 ||
            itensConfigurados.length === 0
          }
        >
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <Save className='mr-2 h-4 w-4' /> Salvar Reserva
            </>
          )}
        </Button>
      </div>
    </>
  );
}
