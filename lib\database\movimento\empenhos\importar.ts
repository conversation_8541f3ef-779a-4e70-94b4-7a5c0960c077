'use server';

import { prisma } from '@/lib/prisma';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { toCurrency } from '@/lib/serverUtils';
import {
  StatusEmpenho,
  StatusReserva,
  AuditoriaMovimentoEmpenhos,
  ErrosEmpenho,
  Permissoes,
} from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  importarEmpenhosSchema,
  empenhoCSVRowSchema,
  type EmpenhoCSVRow,
} from '@/lib/validation';
import {
  temPermissao,
  listarIdsDotacoesUsuarioConectadoTemAcesso,
  obterIpUsuarioConectado,
} from '../../usuarios';
import { inserirErroAudit } from '../../auditoria/erro';
import { revalidatePath } from 'next/cache';

const MODULE = Modulos.MOVIMENTO_EMPENHO;
const ROUTE = '/movimento/empenhos';

export interface ImportarResultado {
  sucesso: boolean;
  empenhosCriados: number;
  empenhosAtualizados: number;
  erros: ImportarErro[];
  tempoTotal: number;
}

export interface ImportarErro {
  linha: number;
  tipo: 'validacao' | 'banco' | 'negocio' | 'sistema';
  mensagem: string;
  dados?: EmpenhoCSVRow;
  recuperavel?: boolean;
}

export interface ImportarProgresso {
  totalLinhas: number;
  linhaAtual: number;
  empenhosCriados: number;
  empenhosAtualizados: number;
  erros: ImportarErro[];
  status: 'iniciando' | 'processando' | 'finalizando' | 'concluido' | 'erro';
}

export async function parseCSVFile(file: File): Promise<EmpenhoCSVRow[]> {
  try {
    const text = await file.text();
    const lines = text.split('\n').filter((line) => line.trim());

    if (lines.length === 0) {
      throw new Error('Arquivo CSV está vazio');
    }

    // Detectar cabeçalho
    const headers = lines[0].split(',').map((h) => h.trim().replace(/"/g, ''));
    const dataLines = lines.slice(1);

    // Mapear colunas para padrão esperado
    const columnMap: Record<string, keyof EmpenhoCSVRow> = {
      numero: 'numero',
      exercicio: 'exercicio',
      id_reserva: 'idReserva',
      id_dotacao: 'idDotacao',
      id_fornecedor: 'idFornecedor',
      fornecedor_nome: 'fornecedorNome',
      fornecedor_cnpj_cpf: 'fornecedorCnpjCpf',
      resumo: 'resumo',
      obs: 'obs',
      valor_total: 'valorTotal',
      usar_mes1: 'usarMes1',
      usar_mes2: 'usarMes2',
      usar_mes3: 'usarMes3',
      usar_mes4: 'usarMes4',
      usar_mes5: 'usarMes5',
      usar_mes6: 'usarMes6',
      usar_mes7: 'usarMes7',
      usar_mes8: 'usarMes8',
      usar_mes9: 'usarMes9',
      usar_mes10: 'usarMes10',
      usar_mes11: 'usarMes11',
      usar_mes12: 'usarMes12',
      data: 'data',
    };

    const rows: EmpenhoCSVRow[] = [];

    for (let i = 0; i < dataLines.length; i++) {
      const line = dataLines[i];
      const values = line.split(',').map((v) => v.trim().replace(/"/g, ''));

      const row: EmpenhoCSVRow = { rowNumber: i + 2 };

      headers.forEach((header, index) => {
        const key = columnMap[header.toLowerCase()];
        if (key && values[index]) {
          (row as any)[key] = values[index];
        }
      });

      rows.push(row);
    }

    return rows;
  } catch (error) {
    console.error('Erro ao parsear CSV:', error);
    throw new Error('Formato de arquivo CSV inválido');
  }
}

export async function validarLinhaEmpenho(
  linha: EmpenhoCSVRow,
  opcoes: {
    criarFornecedores: boolean;
    ignorarErros: boolean;
    atualizarExistentes: boolean;
    validarDisponibilidade: boolean;
  }
): Promise<{ valido: boolean; erros: ImportarErro[]; dados?: any }> {
  const erros: ImportarErro[] = [];

  try {
    // Validar estrutura dos dados
    const parsed = empenhoCSVRowSchema.safeParse(linha);
    if (!parsed.success) {
      parsed.error.errors.forEach((err) => {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'validacao',
          mensagem: `${err.path.join('.')}: ${err.message}`,
          dados: linha,
          recuperavel: false,
        });
      });
      return { valido: false, erros };
    }

    const dados = parsed.data;

    // Verificar exercício do usuário
    const parametrosPermissao = {
      modulo: MODULE,
      permissao: Permissoes.ACESSAR,
      retornarExercicioUsuario: true,
    };
    const resultPermissao = await temPermissao(parametrosPermissao);

    if (
      resultPermissao.error ||
      !resultPermissao.temPermissao ||
      !resultPermissao.exercicio
    ) {
      erros.push({
        linha: linha.rowNumber || 0,
        tipo: 'sistema',
        mensagem: 'Erro ao verificar permissões do usuário',
        dados: linha,
        recuperavel: false,
      });
      return { valido: false, erros };
    }

    // Usar exercício do usuário se não informado
    if (!dados.exercicio) {
      dados.exercicio = resultPermissao.exercicio;
    }

    // Verificar se tem acesso à dotação
    if (dados.idDotacao) {
      const acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();
      if (acessos.error || !acessos.data) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'sistema',
          mensagem: 'Erro ao verificar acessos do usuário',
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }

      const temAcesso = acessos.data.dotacoes?.includes(dados.idDotacao);
      if (!acessos.data.acessoTotal && !temAcesso) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'negocio',
          mensagem: 'Usuário não tem acesso a esta dotação',
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }
    }

    // Validar reserva
    if (dados.idReserva) {
      const reserva = await prisma.reservas.findUnique({
        where: { id: dados.idReserva },
        include: { dotacao: true },
      });

      if (!reserva) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'negocio',
          mensagem: 'Reserva não encontrada',
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }

      if (reserva.status !== StatusReserva.Reservado) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'negocio',
          mensagem: 'Reserva não está com status Reservado',
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }

      // Usar dotação da reserva se não informada
      if (!dados.idDotacao) {
        dados.idDotacao = reserva.idDotacao;
      }
    }

    // Validar fornecedor
    let fornecedor = null;
    if (dados.idFornecedor) {
      fornecedor = await prisma.fornecedores.findUnique({
        where: { id: dados.idFornecedor, ativo: true },
      });

      if (!fornecedor) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'negocio',
          mensagem: 'Fornecedor não encontrado',
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }
    } else if (
      dados.fornecedorNome &&
      dados.fornecedorCnpjCpf &&
      opcoes.criarFornecedores
    ) {
      // Verificar se fornecedor já existe pelo CNPJ/CPF
      const cnpjCpfLimpo = dados.fornecedorCnpjCpf.replace(/[^\d]/g, '');
      fornecedor = await prisma.fornecedores.findFirst({
        where: {
          cnpjCpf: cnpjCpfLimpo,
          ativo: true,
        },
      });

      if (!fornecedor) {
        // Criar novo fornecedor
        try {
          // Obter último código para gerar um novo
          const ultimoFornecedor = await prisma.fornecedores.findFirst({
            orderBy: { codigo: 'desc' },
          });

          const novoCodigo = (ultimoFornecedor?.codigo || 0) + 1;

          fornecedor = await prisma.fornecedores.create({
            data: {
              codigo: novoCodigo,
              nome: dados.fornecedorNome!,
              cnpjCpf: cnpjCpfLimpo,
              email: '',
              phone: '',
            },
          });
        } catch (error) {
          erros.push({
            linha: linha.rowNumber || 0,
            tipo: 'banco',
            mensagem: 'Erro ao criar fornecedor',
            dados: linha,
            recuperavel: false,
          });
          return { valido: false, erros };
        }
      }
    } else if (
      !dados.idFornecedor &&
      (!dados.fornecedorNome || !dados.fornecedorCnpjCpf)
    ) {
      erros.push({
        linha: linha.rowNumber || 0,
        tipo: 'validacao',
        mensagem: 'É necessário informar ID do fornecedor ou Nome + CNPJ/CPF',
        dados: linha,
        recuperavel: false,
      });
      return { valido: false, erros };
    }

    // Validar disponibilidade orçamentária
    if (opcoes.validarDisponibilidade && dados.idDotacao && dados.valorTotal) {
      const dotacao = await prisma.dotacoes.findUnique({
        where: { id: dados.idDotacao },
      });

      if (!dotacao) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'negocio',
          mensagem: 'Dotação não encontrada',
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }

      const saldoDisponivel = toCurrency(dotacao.valorAtual);
      const valorSolicitado = currency(
        dados.valorTotal,
        currencyOptionsNoSymbol
      );

      if (valorSolicitado.value > saldoDisponivel.value) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'negocio',
          mensagem: `Saldo insuficiente na dotação. Disponível: R$ ${saldoDisponivel.value.toFixed(2)}, Solicitado: R$ ${valorSolicitado.value.toFixed(2)}`,
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }
    }

    // Verificar duplicidade
    if (dados.numero && dados.exercicio) {
      const empenhoExistente = await prisma.empenhos.findFirst({
        where: {
          numero: parseInt(dados.numero),
          exercicio: dados.exercicio,
          ativo: true,
        },
      });

      if (empenhoExistente && !opcoes.atualizarExistentes) {
        erros.push({
          linha: linha.rowNumber || 0,
          tipo: 'negocio',
          mensagem: `Empenho ${dados.numero}/${dados.exercicio} já existe`,
          dados: linha,
          recuperavel: false,
        });
        return { valido: false, erros };
      }
    }

    return {
      valido: true,
      erros,
      dados: { ...dados, idFornecedor: fornecedor?.id },
    };
  } catch (error) {
    console.error('Erro na validação da linha:', error);
    erros.push({
      linha: linha.rowNumber || 0,
      tipo: 'sistema',
      mensagem: 'Erro interno durante validação',
      dados: linha,
      recuperavel: false,
    });
    return { valido: false, erros };
  }
}

export async function importarEmpenhos(
  params: unknown,
  onProgress?: (progresso: ImportarProgresso) => void
): Promise<ImportarResultado> {
  const inicio = Date.now();

  try {
    const parametrosPermissao = {
      modulo: MODULE,
      permissao: Permissoes.CRIAR,
      retornarIdUsuario: true,
      retornarExercicioUsuario: true,
    };

    const resultPermissao = await temPermissao(parametrosPermissao);

    if (
      !resultPermissao ||
      resultPermissao.error ||
      !resultPermissao.temPermissao
    ) {
      return {
        sucesso: false,
        empenhosCriados: 0,
        empenhosAtualizados: 0,
        erros: [
          {
            linha: 0,
            tipo: 'sistema',
            mensagem: 'Usuário sem permissão para importar empenhos',
          },
        ],
        tempoTotal: Date.now() - inicio,
      };
    }

    const parsedParams = importarEmpenhosSchema.safeParse(params);
    if (!parsedParams.success) {
      return {
        sucesso: false,
        empenhosCriados: 0,
        empenhosAtualizados: 0,
        erros: [
          {
            linha: 0,
            tipo: 'validacao',
            mensagem: 'Parâmetros inválidos',
          },
        ],
        tempoTotal: Date.now() - inicio,
      };
    }

    const {
      arquivo,
      opcoes = {
        criarFornecedores: false,
        ignorarErros: false,
        atualizarExistentes: false,
        validarDisponibilidade: true,
      },
    } = parsedParams.data;

    onProgress?.({
      totalLinhas: 0,
      linhaAtual: 0,
      empenhosCriados: 0,
      empenhosAtualizados: 0,
      erros: [],
      status: 'iniciando',
    });

    // Parsear CSV
    const linhas = await parseCSVFile(arquivo);

    onProgress?.({
      totalLinhas: linhas.length,
      linhaAtual: 0,
      empenhosCriados: 0,
      empenhosAtualizados: 0,
      erros: [],
      status: 'processando',
    });

    const erros: ImportarErro[] = [];
    let empenhosCriados = 0;
    let empenhosAtualizados = 0;
    const ip = (await obterIpUsuarioConectado()).data;
    const idUsuario = resultPermissao.idUsuario!;

    // Validar todas as linhas primeiro
    const validacoes = await Promise.all(
      linhas.map((linha) => validarLinhaEmpenho(linha, opcoes))
    );

    // Coletar todos os erros de validação
    validacoes.forEach((validacao, _index) => {
      if (!validacao.valido) {
        erros.push(...validacao.erros);
      }
    });

    // Se há erros e não deve ignorar, retornar
    if (erros.length > 0 && !opcoes.ignorarErros) {
      return {
        sucesso: false,
        empenhosCriados: 0,
        empenhosAtualizados: 0,
        erros,
        tempoTotal: Date.now() - inicio,
      };
    }

    // Processar apenas as linhas válidas
    const linhasValidas = validacoes
      .map((validacao, index) =>
        validacao.valido
          ? { ...validacao.dados!, linhaOriginal: linhas[index] }
          : null
      )
      .filter(Boolean);

    // Processar em lote para melhor performance
    for (let i = 0; i < linhasValidas.length; i++) {
      const dados = linhasValidas[i];

      try {
        await prisma.$transaction(async (tx) => {
          // Verificar se já existe
          const empenhoExistente =
            dados.numero && dados.exercicio
              ? await tx.empenhos.findFirst({
                  where: {
                    numero: parseInt(dados.numero),
                    exercicio: dados.exercicio,
                    ativo: true,
                  },
                })
              : null;

          if (empenhoExistente && opcoes.atualizarExistentes) {
            // Atualizar empenho existente
            await tx.empenhos.update({
              where: { id: empenhoExistente.id },
              data: {
                resumo: dados.resumo,
                obs: dados.obs,
                valorTotal: dados.valorTotal!,
                usarMes1: dados.usarMes1 || 0,
                usarMes2: dados.usarMes2 || 0,
                usarMes3: dados.usarMes3 || 0,
                usarMes4: dados.usarMes4 || 0,
                usarMes5: dados.usarMes5 || 0,
                usarMes6: dados.usarMes6 || 0,
                usarMes7: dados.usarMes7 || 0,
                usarMes8: dados.usarMes8 || 0,
                usarMes9: dados.usarMes9 || 0,
                usarMes10: dados.usarMes10 || 0,
                usarMes11: dados.usarMes11 || 0,
                usarMes12: dados.usarMes12 || 0,
              },
            });

            // Registrar auditoria
            await tx.empenhos_audit.create({
              data: {
                idEmpenho: empenhoExistente.id,
                idUsuario,
                acao: AuditoriaMovimentoEmpenhos.ALTERAR_EMPENHO,
                ip,
                obs: `Empenho atualizado via importação. Linha: ${dados.linhaOriginal.rowNumber}`,
              },
            });

            empenhosAtualizados++;
          } else if (!empenhoExistente) {
            // Criar novo empenho
            let idDotacaoFinal = dados.idDotacao!;
            let reserva = null;

            // Se tem reserva, usar dotação da reserva
            if (dados.idReserva) {
              reserva = await tx.reservas.findUnique({
                where: { id: dados.idReserva },
                include: { dotacao: true },
              });

              if (reserva) {
                idDotacaoFinal = reserva.idDotacao;

                // Devolver valor da reserva para a dotação
                const valorReserva = toCurrency(reserva.usarTotal);
                const dotacaoComReservaDevolvida = toCurrency(
                  reserva.dotacao.valorAtual
                ).add(valorReserva);

                // Validar empenho contra o novo saldo
                const valorEmpenho = currency(
                  dados.valorTotal!,
                  currencyOptionsNoSymbol
                );
                if (valorEmpenho.value > dotacaoComReservaDevolvida.value) {
                  throw new Error(
                    ErrosEmpenho[ErrosEmpenho.SALDO_INSUFICIENTE]
                  );
                }

                // Atualizar dotação com valor final
                const valorAtualFinalDotacao =
                  dotacaoComReservaDevolvida.subtract(valorEmpenho);

                await tx.dotacoes.update({
                  where: { id: idDotacaoFinal },
                  data: {
                    valorAtual: valorAtualFinalDotacao.value,
                    cotaMes1: toCurrency(reserva.dotacao.cotaMes1)
                      .add(reserva.usarMes1.toNumber())
                      .subtract(dados.usarMes1 || 0).value,
                    cotaMes2: toCurrency(reserva.dotacao.cotaMes2)
                      .add(reserva.usarMes2.toNumber())
                      .subtract(dados.usarMes2 || 0).value,
                    cotaMes3: toCurrency(reserva.dotacao.cotaMes3)
                      .add(reserva.usarMes3.toNumber())
                      .subtract(dados.usarMes3 || 0).value,
                    cotaMes4: toCurrency(reserva.dotacao.cotaMes4)
                      .add(reserva.usarMes4.toNumber())
                      .subtract(dados.usarMes4 || 0).value,
                    cotaMes5: toCurrency(reserva.dotacao.cotaMes5)
                      .add(reserva.usarMes5.toNumber())
                      .subtract(dados.usarMes5 || 0).value,
                    cotaMes6: toCurrency(reserva.dotacao.cotaMes6)
                      .add(reserva.usarMes6.toNumber())
                      .subtract(dados.usarMes6 || 0).value,
                    cotaMes7: toCurrency(reserva.dotacao.cotaMes7)
                      .add(reserva.usarMes7.toNumber())
                      .subtract(dados.usarMes7 || 0).value,
                    cotaMes8: toCurrency(reserva.dotacao.cotaMes8)
                      .add(reserva.usarMes8.toNumber())
                      .subtract(dados.usarMes8 || 0).value,
                    cotaMes9: toCurrency(reserva.dotacao.cotaMes9)
                      .add(reserva.usarMes9.toNumber())
                      .subtract(dados.usarMes9 || 0).value,
                    cotaMes10: toCurrency(reserva.dotacao.cotaMes10)
                      .add(reserva.usarMes10.toNumber())
                      .subtract(dados.usarMes10 || 0).value,
                    cotaMes11: toCurrency(reserva.dotacao.cotaMes11)
                      .add(reserva.usarMes11.toNumber())
                      .subtract(dados.usarMes11 || 0).value,
                    cotaMes12: toCurrency(reserva.dotacao.cotaMes12)
                      .add(reserva.usarMes12.toNumber())
                      .subtract(dados.usarMes12 || 0).value,
                  },
                });

                // Atualizar status da reserva
                await tx.reservas.update({
                  where: { id: dados.idReserva },
                  data: { status: StatusReserva.Empenho },
                });
              }
            } else {
              // Validar e atualizar dotação diretamente
              const dotacao = await tx.dotacoes.findUnique({
                where: { id: idDotacaoFinal },
              });

              if (!dotacao) {
                throw new Error('Dotação não encontrada');
              }

              const valorEmpenho = currency(
                dados.valorTotal!,
                currencyOptionsNoSymbol
              );
              const saldoAtual = toCurrency(dotacao.valorAtual);

              if (valorEmpenho.value > saldoAtual.value) {
                throw new Error(ErrosEmpenho[ErrosEmpenho.SALDO_INSUFICIENTE]);
              }

              const novoSaldo = saldoAtual.subtract(valorEmpenho);

              await tx.dotacoes.update({
                where: { id: idDotacaoFinal },
                data: {
                  valorAtual: novoSaldo.value,
                  cotaMes1: toCurrency(dotacao.cotaMes1).subtract(
                    dados.usarMes1 || 0
                  ).value,
                  cotaMes2: toCurrency(dotacao.cotaMes2).subtract(
                    dados.usarMes2 || 0
                  ).value,
                  cotaMes3: toCurrency(dotacao.cotaMes3).subtract(
                    dados.usarMes3 || 0
                  ).value,
                  cotaMes4: toCurrency(dotacao.cotaMes4).subtract(
                    dados.usarMes4 || 0
                  ).value,
                  cotaMes5: toCurrency(dotacao.cotaMes5).subtract(
                    dados.usarMes5 || 0
                  ).value,
                  cotaMes6: toCurrency(dotacao.cotaMes6).subtract(
                    dados.usarMes6 || 0
                  ).value,
                  cotaMes7: toCurrency(dotacao.cotaMes7).subtract(
                    dados.usarMes7 || 0
                  ).value,
                  cotaMes8: toCurrency(dotacao.cotaMes8).subtract(
                    dados.usarMes8 || 0
                  ).value,
                  cotaMes9: toCurrency(dotacao.cotaMes9).subtract(
                    dados.usarMes9 || 0
                  ).value,
                  cotaMes10: toCurrency(dotacao.cotaMes10).subtract(
                    dados.usarMes10 || 0
                  ).value,
                  cotaMes11: toCurrency(dotacao.cotaMes11).subtract(
                    dados.usarMes11 || 0
                  ).value,
                  cotaMes12: toCurrency(dotacao.cotaMes12).subtract(
                    dados.usarMes12 || 0
                  ).value,
                },
              });
            }

            // Gerar número do empenho se não informado
            let numeroEmpenho = dados.numero ? parseInt(dados.numero) : null;
            if (!numeroEmpenho) {
              const ultimoEmpenho = await tx.empenhos.findFirst({
                where: { exercicio: dados.exercicio! },
                orderBy: { numero: 'desc' },
                select: { numero: true },
              });
              numeroEmpenho = (ultimoEmpenho?.numero || 0) + 1;
            }

            // Criar empenho
            const empenho = await tx.empenhos.create({
              data: {
                exercicio: dados.exercicio!,
                numero: numeroEmpenho,
                idReserva: dados.idReserva,
                idDotacao: idDotacaoFinal,
                idFornecedor: dados.idFornecedor!,
                resumo: dados.resumo || '',
                obs: dados.obs || '',
                valorTotal: dados.valorTotal!,
                status: StatusEmpenho.EMPENHADO,
                data: dados.data || new Date(),
                usarMes1: dados.usarMes1 || 0,
                usarMes2: dados.usarMes2 || 0,
                usarMes3: dados.usarMes3 || 0,
                usarMes4: dados.usarMes4 || 0,
                usarMes5: dados.usarMes5 || 0,
                usarMes6: dados.usarMes6 || 0,
                usarMes7: dados.usarMes7 || 0,
                usarMes8: dados.usarMes8 || 0,
                usarMes9: dados.usarMes9 || 0,
                usarMes10: dados.usarMes10 || 0,
                usarMes11: dados.usarMes11 || 0,
                usarMes12: dados.usarMes12 || 0,
              },
            });

            // Registrar auditoria
            await tx.empenhos_audit.create({
              data: {
                idEmpenho: empenho.id,
                idUsuario,
                acao: AuditoriaMovimentoEmpenhos.CRIAR_EMPENHO,
                ip,
                obs: `Empenho criado via importação. Linha: ${dados.linhaOriginal.rowNumber}`,
              },
            });

            empenhosCriados++;
          }
        });

        // Atualizar progresso
        onProgress?.({
          totalLinhas: linhas.length,
          linhaAtual: i + 1,
          empenhosCriados,
          empenhosAtualizados,
          erros,
          status:
            i === linhasValidas.length - 1 ? 'finalizando' : 'processando',
        });
      } catch (error) {
        console.error(`Erro ao processar linha ${i + 1}:`, error);

        const mensagem =
          error instanceof Error ? error.message : 'Erro desconhecido';
        const tipoErro = mensagem.includes('SALDO_INSUFICIENTE')
          ? 'negocio'
          : 'banco';

        erros.push({
          linha: dados.linhaOriginal.rowNumber || i + 1,
          tipo: tipoErro,
          mensagem: `Erro ao processar empenho: ${mensagem}`,
          dados: dados.linhaOriginal,
          recuperavel: opcoes.ignorarErros,
        });

        // Se não deve ignorar erros, parar o processamento
        if (!opcoes.ignorarErros) {
          break;
        }
      }
    }

    onProgress?.({
      totalLinhas: linhas.length,
      linhaAtual: linhas.length,
      empenhosCriados,
      empenhosAtualizados,
      erros,
      status: 'concluido',
    });

    revalidatePath(ROUTE);

    return {
      sucesso: erros.length === 0 || opcoes.ignorarErros,
      empenhosCriados,
      empenhosAtualizados,
      erros,
      tempoTotal: Date.now() - inicio,
    };
  } catch (error) {
    console.error('Erro na importação:', error);

    const auditData = {
      erro: JSON.stringify(error),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);

    return {
      sucesso: false,
      empenhosCriados: 0,
      empenhosAtualizados: 0,
      erros: [
        {
          linha: 0,
          tipo: 'sistema',
          mensagem: 'Erro interno durante importação',
        },
      ],
      tempoTotal: Date.now() - inicio,
    };
  }
}

export async function gerarModeloCSV(): Promise<string> {
  const headers = [
    'numero',
    'exercicio',
    'id_reserva',
    'id_dotacao',
    'id_fornecedor',
    'fornecedor_nome',
    'fornecedor_cnpj_cpf',
    'resumo',
    'obs',
    'valor_total',
    'usar_mes1',
    'usar_mes2',
    'usar_mes3',
    'usar_mes4',
    'usar_mes5',
    'usar_mes6',
    'usar_mes7',
    'usar_mes8',
    'usar_mes9',
    'usar_mes10',
    'usar_mes11',
    'usar_mes12',
    'data',
  ];

  const exemplo = [
    '',
    '2024',
    '123', // id_reserva (opcional)
    '456', // id_dotacao (obrigatório se não tiver reserva)
    '789', // id_fornecedor (opcional)
    'Fornecedor Exemplo Ltda', // fornecedor_nome (obrigatório se não tiver id_fornecedor)
    '12.345.678/0001-00', // fornecedor_cnpj_cpf (obrigatório se não tiver id_fornecedor)
    'Descrição do empenho',
    'Observações',
    '1000.00',
    '100.00', // usar_mes1
    '100.00', // usar_mes2
    '100.00', // usar_mes3
    '100.00', // usar_mes4
    '100.00', // usar_mes5
    '100.00', // usar_mes6
    '100.00', // usar_mes7
    '100.00', // usar_mes8
    '100.00', // usar_mes9
    '100.00', // usar_mes10
    '100.00', // usar_mes11
    '100.00', // usar_mes12
    '2024-01-15', // data (opcional)
  ];

  return [headers.join(','), exemplo.join(',')].join('\n');
}
