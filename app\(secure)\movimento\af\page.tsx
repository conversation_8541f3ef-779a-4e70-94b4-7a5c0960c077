import { z } from 'zod';
import AfsDatatableWrapper from '@/components/af/afsDatatableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusCircle, FileText } from 'lucide-react';
import Link from 'next/link';
import { Suspense } from 'react';
import { ensureAuth } from '@/lib/supabase/actions';
import { temPermissao } from '@/lib/database/usuarios';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';

async function VerRelatorioButton() {
  'use server';

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_AFS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result || result.error || !result.temPermissao) {
    return null;
  }

  return (
    <Link href='/relatorios/movimento/af/lista'>
      <Button variant='outline'>
        <FileText className='mr-1 h-4 w-4' />
        Ver Relatório
      </Button>
    </Link>
  );
}

export default async function AFPage() {
  await ensureAuth();

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Movimento de AFs</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='flex w-full justify-between pr-6'>
            <div className='flex gap-2'>
              <Link href='/movimento/af/novo'>
                <Button>
                  Nova AF <PlusCircle className='ml-1 h-4 w-4' />
                </Button>
              </Link>
              <VerRelatorioButton />
            </div>
          </div>
          <AfsDatatableWrapper />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
