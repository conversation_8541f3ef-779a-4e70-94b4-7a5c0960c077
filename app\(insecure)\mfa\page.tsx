'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState, useEffect } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { toastAlgoDeuErrado } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { validateToptp } from '@/lib/supabase/actions';
import { mfaSchema } from '@/lib/validation';

export default function LoginDialog() {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);
  const [isMfaOpen] = useState(true);

  const [loading, setLoading] = useState(false);

  const mfaForm = useForm<z.infer<typeof mfaSchema>>({
    resolver: zodResolver(mfaSchema),
  });

  const onSubmitMfa = async (values: z.infer<typeof mfaSchema>) => {
    try {
      setLoading(true);
      router.prefetch('/dashboard');
      const res = await validateToptp(`${values.token}`);
      if (res?.error) {
        setLoading(false);
        if (res.error.status === 400) {
          toast.error('Token inválido.');
        } else {
          toast.error(toastAlgoDeuErrado);
        }
      } else {
        router.replace('/dashboard');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted && isMfaOpen}>
        <AlertDialogContent
          className='sm:max-w-[425px]'
          onOpenAutoFocus={(e) => {
            e.preventDefault();
            mfaForm.setFocus('token');
          }}
        >
          <AlertDialogHeader>
            <AlertDialogTitle>Autenticação Multifator</AlertDialogTitle>
          </AlertDialogHeader>
          <Form {...mfaForm}>
            <form onSubmit={mfaForm.handleSubmit(onSubmitMfa)}>
              <div className='grid gap-8 py-4'>
                <FormField
                  control={mfaForm.control}
                  name='token'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Código</FormLabel>
                      <FormControl>
                        <Input
                          className='col-span-3'
                          {...field}
                          type='number'
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <AlertDialogFooter>
                <Button type='submit' disabled={loading}>
                  {loading ? (
                    <>
                      <Icons.loader className='mr-2 h-4 w-4 animate-spin' />{' '}
                      Aguarde...
                    </>
                  ) : (
                    <>
                      <Icons.logIn className='mr-2 h-4 w-4' /> Logar
                    </>
                  )}
                </Button>
              </AlertDialogFooter>
            </form>
          </Form>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
