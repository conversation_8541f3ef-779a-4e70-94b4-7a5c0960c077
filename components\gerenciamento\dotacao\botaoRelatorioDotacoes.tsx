'use client';

import { useState } from 'react';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { toast } from 'sonner';
import { createClient } from '@/lib/supabase/client';
import { Loader2, Printer } from 'lucide-react';
import { siteConfig } from '@/config/site';
import { Button } from '@/components/ui/button';
import { CHAVE_API, URL_SERVER_IMPRESSAO } from '@/lib/consts';

interface BotaoRelatorioDotacoesProps {
  tipo: 'dotacoes' | 'cotas' | 'despesas' | 'despesasResumido';
  filtros?: {
    exercicio?: number;
    secretaria?: number;
    secretariaNome?: string;
    departamento?: number;
    departamentoNome?: string;
    subdepartamento?: number;
    subdepartamentoNome?: string;
    despesa?: number;
    codAplicacaoInicial?: string;
    codAplicacaoFinal?: string;
    economicaInicial?: string;
    economicaFinal?: string;
    fonte?: number;
    reducao?: boolean;
  };
  titulo?: string;
}

export const BotaoRelatorioDotacoes = ({
  tipo,
  filtros = {},
  titulo,
}: BotaoRelatorioDotacoesProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const getRelatorioUrl = () => {
    const baseUrl = siteConfig.url + '/impressao/gerenciamento';

    switch (tipo) {
      case 'dotacoes':
        return `${baseUrl}/dotacoes/imprimir`;
      case 'cotas':
        return `${baseUrl}/cotas/imprimir`;
      case 'despesas':
        return `${baseUrl}/despesas/imprimir`;
      case 'despesasResumido':
        return `${baseUrl}/despesas/imprimir?tipo=resumido`;
      default:
        return `${baseUrl}/dotacoes/imprimir`;
    }
  };

  const getTitulo = () => {
    if (titulo) return titulo;

    switch (tipo) {
      case 'dotacoes':
        return 'Relatório de Dotações Atuais';
      case 'cotas':
        return 'Relatório de Cotas e Reduções';
      case 'despesas':
        return 'Relatório Detalhado de Despesas';
      case 'despesasResumido':
        return 'Relatório Resumido de Despesas';
      default:
        return 'Relatório';
    }
  };

  const onClick = async () => {
    if (isLoading) return null;
    setIsLoading(true);

    function popUpWasBlocked(popUp: Window | null) {
      return !popUp || popUp.closed || typeof popUp.closed === 'undefined';
    }

    const w = window.open('/carregando', '_blank');

    try {
      const supabase = createClient();
      const session = await supabase.auth.getSession();
      if (!session) {
        setIsLoading(false);
        return toast.error(toastAlgoDeuErrado);
      }

      if (!w || popUpWasBlocked(w)) {
        setIsLoading(false);
        return toast.error(
          'Janela popup bloqueada. Verifique as configurações do seu navegador.'
        );
      }

      // Construir URL com parâmetros
      const url = new URL(getRelatorioUrl());

      // Adicionar parâmetros de filtro
      Object.entries(filtros).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (
            key === 'exercicio' ||
            key === 'secretaria' ||
            key === 'departamento' ||
            key === 'subdepartamento' ||
            key === 'despesa' ||
            key === 'fonte'
          ) {
            url.searchParams.append(key, value.toString());
          } else if (key === 'reducao') {
            url.searchParams.append('tipo', value ? 'reducao' : 'cota');
          } else if (key.includes('Nome')) {
            url.searchParams.append(key, value as string);
          }
        }
      });

      // Adicionar parâmetro tipo para despesas resumido
      if (tipo === 'despesasResumido') {
        url.searchParams.set('tipo', 'resumido');
      }

      const pdf = await fetch(URL_SERVER_IMPRESSAO, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.data.session?.access_token}`,
          url: url.toString(),
          'Content-Type': 'application/json',
          chave: CHAVE_API,
        },
        body: JSON.stringify({
          landscape: tipo === 'dotacoes' || tipo === 'despesas',
        }),
      });

      if (!pdf.ok) {
        setIsLoading(false);
        w.close();
        return toast.error(toastAlgoDeuErrado);
      }

      const fileURL = URL.createObjectURL(await pdf.blob());
      w.location.href = fileURL;
    } catch (e) {
      setIsLoading(false);
      if (w) {
        w.close();
      }
      toast.error(toastAlgoDeuErrado);
      const env = process.env.NODE_ENV;
      if (env == 'development') {
        console.log(e);
      }
    }
    setIsLoading(false);
  };

  return (
    <Button onClick={onClick} disabled={isLoading} variant='outline'>
      {isLoading ? (
        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
      ) : (
        <Printer className='mr-2 h-4 w-4' />
      )}
      {getTitulo()}
    </Button>
  );
};
