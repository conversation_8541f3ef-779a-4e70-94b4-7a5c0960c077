import DOMPurify from 'isomorphic-dompurify';

/**
 * Sanitiza HTML de templates para prevenir XSS
 * Permite apenas tags seguras para documentos
 */
export const sanitizarHtmlTemplate = (html: string): string => {
  // Configuração restritiva para templates de documentos
  const config = {
    ALLOWED_TAGS: [
      // Estrutura básica
      'div',
      'span',
      'p',
      'br',
      'hr',
      // Formatação de texto
      'strong',
      'b',
      'em',
      'i',
      'u',
      's',
      // Listas
      'ul',
      'ol',
      'li',
      // Tabelas
      'table',
      'thead',
      'tbody',
      'tr',
      'td',
      'th',
      // Cabeçalhos
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      // Outros elementos seguros
      'blockquote',
      'pre',
      'code',
    ],
    ALLOWED_ATTR: [
      // Atributos de estilo seguros
      'class',
      'style',
      // Atributos de tabela
      'colspan',
      'rowspan',
      // Atributos de alinhamento
      'align',
      'valign',
    ],
    ALLOWED_URI_REGEXP:
      /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
    // Remover scripts e eventos
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
    FORBID_ATTR: [
      'onerror',
      'onload',
      'onclick',
      'onmouseover',
      'onfocus',
      'onblur',
    ],
  };

  try {
    const sanitizedHtml = DOMPurify.sanitize(html, config);

    if (process.env.NODE_ENV === 'development' && html !== sanitizedHtml) {
      console.warn('HTML foi sanitizado:', {
        original: html.length,
        sanitized: sanitizedHtml.length,
      });
    }

    return sanitizedHtml;
  } catch (error) {
    console.error('Erro ao sanitizar HTML:', error);
    // Em caso de erro, retornar string vazia por segurança
    return '';
  }
};

export const validarHtmlTemplate = (
  html: string
): { valido: boolean; erros: string[] } => {
  const erros: string[] = [];

  // Verificar tags perigosas
  const tagsPerigosas =
    /<(script|object|embed|form|input|button|iframe|frame)/gi;
  if (tagsPerigosas.test(html)) {
    erros.push(
      'Template contém tags não permitidas (script, object, embed, form, etc.)'
    );
  }

  // Verificar eventos JavaScript
  const eventosJs = /on\w+\s*=/gi;
  if (eventosJs.test(html)) {
    erros.push('Template contém eventos JavaScript não permitidos');
  }

  // Verificar javascript: URLs
  const jsUrls = /javascript:/gi;
  if (jsUrls.test(html)) {
    erros.push('Template contém URLs javascript: não permitidas');
  }

  return {
    valido: erros.length === 0,
    erros,
  };
};

export const processarVariaveisTemplate = (
  conteudoHtml: string,
  variaveis: Record<string, string>
): string => {
  let htmlProcessado = conteudoHtml;

  // Substituir variáveis padrão do sistema
  const variaveisPadrao = {
    '{{DATA_ATUAL}}': new Date().toLocaleDateString('pt-BR'),
    '{{HORA_ATUAL}}': new Date().toLocaleTimeString('pt-BR'),
    '{{ANO_ATUAL}}': new Date().getFullYear().toString(),
    '{{MES_ATUAL}}': (new Date().getMonth() + 1).toString().padStart(2, '0'),
    ...variaveis, // Variáveis específicas do contexto
  };

  Object.entries(variaveisPadrao).forEach(([variavel, valor]) => {
    const regex = new RegExp(variavel.replace(/[{}]/g, '\\$&'), 'g');
    htmlProcessado = htmlProcessado.replace(regex, valor);
  });

  return htmlProcessado;
};
