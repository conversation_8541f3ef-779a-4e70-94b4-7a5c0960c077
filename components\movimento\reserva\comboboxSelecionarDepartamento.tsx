'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn, montarCodigoDepartamento } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarReservas } from '@/lib/database/movimento/reservas';

export function ComboboxSelecionaDepartamento({
  departamentos,
  departamentoId,
  setDepartamentoId,
}: {
  departamentos: Required<
    Awaited<ReturnType<typeof listarReservas>>
  >['data']['departamentos'];
  departamentoId: number | null;
  setDepartamentoId: React.Dispatch<React.SetStateAction<number | null>>;
}) {
  const [open, setOpen] = React.useState(false);

  const departamentoSelecionado =
    departamentos.find((departamento) => {
      return departamentoId === departamento.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {departamentoSelecionado
              ? montarCodigoDepartamento(
                  departamentoSelecionado.secretaria.codigo,
                  departamentoSelecionado.codigo
                ) +
                ' - ' +
                departamentoSelecionado.nome
              : 'Selecione o departamento...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar departamento...' />
            <CommandEmpty>Departamento não encontrado.</CommandEmpty>
            <CommandList>
              <CommandItem
                key={0}
                value={`Todos os departamentos`}
                onSelect={() => {
                  setDepartamentoId(0);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    departamentoId === 0 ? 'opacity-100' : 'opacity-0'
                  )}
                />
                Todos os departamentos
              </CommandItem>
              {departamentos.map((sec) => (
                <CommandItem
                  key={sec.id}
                  value={`${montarCodigoDepartamento(sec.secretaria.codigo, sec.codigo)} - ${sec.nome}`}
                  onSelect={() => {
                    setDepartamentoId(sec.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      departamentoId === sec.id ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  {montarCodigoDepartamento(sec.secretaria.codigo, sec.codigo)}{' '}
                  - {sec.nome}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
