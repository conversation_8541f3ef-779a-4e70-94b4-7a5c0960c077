'use server';
import { z } from 'zod';

import { ErrorAlert } from '@/components/error-alert';
import { temPermissao } from '@/lib/database/usuarios';
import { Permissoes } from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import { isNumeric } from '@/lib/utils';
import { permissaoSchema } from '@/lib/validation';

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_ALTERACAO_ORCAMENTARIA,
    permissao: Permissoes.ACESSAR,
  };

  const { id } = await params;
  if (!isNumeric(id)) return <ErrorAlert error='ID inválido.' />;

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  if (result.error) {
    return <ErrorAlert error={result.error} />;
  }

  if (!result.temPermissao) {
    return <ErrorAlert error='Usuário sem permissão para criar.' />;
  }

  return <>{children}</>;
}
