import ReservasDatatableWrapper from '@/components/movimento/reserva/reservasDatatableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { Suspense } from 'react';

export default function ReservaPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Movimento de Reservas</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='flex w-full justify-end pr-6'>
            <Link href='/movimento/reservas/novo'>
              <Button>
                Nova Reserva <PlusCircle className='ml-1 h-4 w-4' />
              </Button>
            </Link>
          </div>
          <ReservasDatatableWrapper />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
