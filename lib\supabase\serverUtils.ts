'use server';
import { createClient } from './server';
import { z } from 'zod';
import { decryptFile, validarCertificado } from './serverOnlyUtils';

export const validarCertificadoUsuarioConectado = async (params: unknown) => {
  const parsedParams = z.object({ senha: z.string() }).safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { senha } = parsedParams.data;
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.auth.getUser();
    if (error || !data?.user) {
      return {
        error: 'Usuário não autenticado.',
      };
    }
    //Acesso controlado via supabase RLS, baseado no id do usuário autenticado
    const { data: certificado, error: errorCertificado } =
      await supabase.storage
        .from('certificados')
        .download(`${data.user.id}/cert.pfx.enc`);
    if (errorCertificado) {
      return {
        error: 'Erro ao buscar certificado.',
      };
    }
    if (!certificado) {
      return {
        error: 'Certificado não cadastrado.',
      };
    }
    const certificadoBuffer = Buffer.from(await certificado.arrayBuffer());
    let decrypted;
    try {
      decrypted = await decryptFile(certificadoBuffer, senha);
    } catch (e) {
      return {
        error: 'Senha incorreta ou arquivo inválido.',
      };
    }

    const validacao = validarCertificado(decrypted, senha);

    if (validacao.error) {
      return {
        error: validacao.error,
      };
    }

    return {
      data: validacao.data,
    };
  } catch (e: any) {
    console.log(e.message);
    return {
      error: 'Erro ao validar certificado.',
    };
  }
};
