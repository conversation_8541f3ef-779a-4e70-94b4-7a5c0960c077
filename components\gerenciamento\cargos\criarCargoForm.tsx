'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { cargoFormSchema, cargoSchema } from '@/lib/validation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import { AcessoCargo, ConfigModulo } from '@/types/app';
import { ComboboxSelecionarModulo } from '@/components/gerenciamento/cargos/comboboxSelecionarModulo';
import ConfigModulosDatatable from '@/components/gerenciamento/cargos/configModulosDatatable';
import { toastAlgoDeuErrado } from '@/lib/utils';
import {
  criarCargo,
  listarDepartamentosAtivos,
  listarSubdepartamentosAtivos,
} from '@/lib/database/gerenciamento/cargos';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  TiposAcesso,
  TiposAcessoDesc,
  TiposAssinatura,
  TiposAssinaturaDesc,
} from '@/lib/enums';
import ConfigAcessosDatatable from './configAcessosDatatable';
import { listarSecretariasAtivas } from '@/lib/database/gerenciamento/cargos';
import { ComboboxSelecionarSecretaria } from './comboboxSelecionarSecretaria';
import { ComboboxSelecionarDepartamento } from './comboboxSelecionarDepartamento';
import { ComboboxSelecionarSubdepartamento } from './comboboxSelecionarSubdepartamento';

export default function CriarCargoForm({
  secretarias,
  departamentos,
  subdepartamentos,
}: {
  secretarias: Awaited<ReturnType<typeof listarSecretariasAtivas>>;
  departamentos: Awaited<ReturnType<typeof listarDepartamentosAtivos>>;
  subdepartamentos: Awaited<ReturnType<typeof listarSubdepartamentosAtivos>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [modulosConfigurados, setModulosConfigurados] = useState<
    ConfigModulo[]
  >([]);
  const [acessosConfigurados, setAcessosConfigurados] = useState<AcessoCargo[]>(
    []
  );
  const [tipoAcesso, setTipoAcesso] = useState(TiposAcesso.NENHUM);

  useEffect(() => {
    setAcessosConfigurados([]);
  }, [tipoAcesso, setAcessosConfigurados]);

  const form = useForm<z.infer<typeof cargoFormSchema>>({
    resolver: zodResolver(cargoFormSchema),
    defaultValues: {
      nome: '',
      //@ts-ignore
      tipoAcesso: '',
      //@ts-ignore
      tipoAssinatura: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof cargoFormSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof cargoSchema> = {
        nome: values.nome,
        permissions: modulosConfigurados.map((modulo) => {
          return {
            moduloId: Number(modulo.id),
            read: modulo.read,
            write: modulo.write,
            update: modulo.update,
            delete: modulo.delete,
          };
        }),
        tipoAcesso: values.tipoAcesso,
        tipoAssinatura: values.tipoAssinatura,
        acessos: acessosConfigurados.map((acesso) => acesso.id),
      };
      const res = await criarCargo(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Cargo adicionado.');
        router.push('/gerenciamento/cargos');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='grid gap-8 py-4'>
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Nome do Cargo
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Digite...'
                    maxLength={100}
                    minLength={2}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className='grid gap-4 py-8'>
          <FormLabel className='text-left'>
            Selecione o módulo, configure as permissões e adicione. Repita para
            todos os módulos desejados.
          </FormLabel>
          <ComboboxSelecionarModulo
            modulosConfigurados={modulosConfigurados}
            setModulosConfigurados={setModulosConfigurados}
          />
          <ConfigModulosDatatable
            data={modulosConfigurados}
            setModulosConfigurados={setModulosConfigurados}
          />
        </div>
        <div className='grid gap-4 py-8'>
          <span className='mt-4 text-lg'>Secretarias e Departamentos</span>
          <FormField
            control={form.control}
            name='tipoAcesso'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Tipo de Acesso
                </FormLabel>
                <Select
                  onValueChange={(e) => {
                    field.onChange(e);
                    setTipoAcesso(Number(e));
                    form.setValue('tipoAcesso', Number(e));
                    form.setValue('tipoAssinatura', TiposAssinatura.NAOASSINA);
                  }}
                  //defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Selecione...' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.keys(TiposAcessoDesc)
                      .filter((v) => !isNaN(Number(v)) && Number(v) > 0)
                      .map((value) => (
                        <SelectItem key={value} value={`${value}`}>
                          {TiposAcessoDesc[Number(value)]}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
          {tipoAcesso === TiposAcesso.SECRETARIAS && (
            <ComboboxSelecionarSecretaria
              acessosConfigurados={acessosConfigurados}
              setAcessosConfigurados={setAcessosConfigurados}
              secretarias={secretarias}
            />
          )}
          {tipoAcesso === TiposAcesso.DEPARTAMENTOS && (
            <ComboboxSelecionarDepartamento
              acessosConfigurados={acessosConfigurados}
              setAcessosConfigurados={setAcessosConfigurados}
              departamentos={departamentos}
            />
          )}
          {tipoAcesso === TiposAcesso.SUBDEPARTAMENTOS && (
            <ComboboxSelecionarSubdepartamento
              acessosConfigurados={acessosConfigurados}
              setAcessosConfigurados={setAcessosConfigurados}
              subdepartamentos={subdepartamentos}
            />
          )}
          {tipoAcesso > TiposAcesso.TODAS_SECRETARIAS && (
            <ConfigAcessosDatatable
              data={acessosConfigurados}
              setAcessosConfigurados={setAcessosConfigurados}
            />
          )}
        </div>
        <div className='grid gap-4 py-8'>
          <span className='mt-4 text-lg'>Assinatura</span>
          <FormField
            control={form.control}
            name='tipoAssinatura'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Usuários com esse cargo assinam documentos dos órgãos que
                  possuem acesso?
                </FormLabel>
                <Select
                  onValueChange={(e) => {
                    field.onChange(e);
                    form.setValue('tipoAssinatura', Number(e));
                  }}
                >
                  <FormControl>
                    <SelectTrigger disabled={tipoAcesso === TiposAcesso.NENHUM}>
                      <SelectValue placeholder='Selecione...' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.keys(TiposAssinaturaDesc)
                      .filter((v) => !isNaN(Number(v)))
                      .filter((v) => {
                        switch (tipoAcesso) {
                          case TiposAcesso.SECRETARIAS:
                            return (
                              Number(v) === TiposAssinatura.SECRETARIO ||
                              Number(v) === TiposAssinatura.DIRETOR ||
                              Number(v) === TiposAssinatura.PREFEITO ||
                              Number(v) === TiposAssinatura.NAOASSINA
                            );
                          case TiposAcesso.DEPARTAMENTOS:
                            return (
                              Number(v) === TiposAssinatura.GESTOR ||
                              Number(v) === TiposAssinatura.NAOASSINA
                            );
                          case TiposAcesso.SUBDEPARTAMENTOS:
                            return (
                              Number(v) === TiposAssinatura.GESTOR ||
                              Number(v) === TiposAssinatura.NAOASSINA
                            );
                          case TiposAcesso.TODAS_SECRETARIAS:
                            return (
                              Number(v) === TiposAssinatura.SECRETARIO ||
                              Number(v) === TiposAssinatura.DIRETOR ||
                              Number(v) === TiposAssinatura.PREFEITO ||
                              Number(v) === TiposAssinatura.NAOASSINA
                            );
                          default:
                            return true;
                        }
                      })
                      .map((value) => (
                        <SelectItem key={value} value={`${value}`}>
                          {TiposAssinaturaDesc[Number(value)]}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
        </div>
        <div className='mt-12 flex justify-between'>
          <Button
            variant={'destructive'}
            disabled={loading}
            onClick={(e) => {
              e.preventDefault();
              router.push('/gerenciamento/cargos');
            }}
          >
            <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
          </Button>
          <Button
            type='submit'
            disabled={loading || modulosConfigurados.length === 0}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' /> Criar Cargo
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
