'use server';
import { z } from 'zod';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioDotacaoAtual } from '@/components/relatorios/dotacaoAtual';
import { obterDotacoesParaRelatorio } from '@/lib/database/relatorios/dotacoes';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permisso<PERSON> } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function RelatorioDotacoesPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const currSearchParams = await searchParams;
  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }
  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();
  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }
  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const params = {
    exercicio: currSearchParams.exercicio
      ? parseInt(currSearchParams.exercicio as string)
      : result.exercicio,
    idSecretaria: currSearchParams.secretaria
      ? parseInt(currSearchParams.secretaria as string)
      : undefined,
    idDepartamento: currSearchParams.departamento
      ? parseInt(currSearchParams.departamento as string)
      : undefined,
    idSubdepartamento: currSearchParams.subdepartamento
      ? parseInt(currSearchParams.subdepartamento as string)
      : undefined,
    despesa: currSearchParams.despesa
      ? parseInt(currSearchParams.despesa as string)
      : undefined,
    codAplicacaoInicial: currSearchParams.codAplicacaoInicial as string,
    codAplicacaoFinal: currSearchParams.codAplicacaoFinal as string,
    economicaInicial: currSearchParams.economicaInicial as string,
    economicaFinal: currSearchParams.economicaFinal as string,
    fonte: currSearchParams.fonte
      ? parseInt(currSearchParams.fonte as string)
      : undefined,
    bearer,
  };

  const dotacoes = await obterDotacoesParaRelatorio(params);

  if (dotacoes.error) {
    return <ErrorAlert error={dotacoes.error} />;
  }

  if (!dotacoes.data) {
    return <ErrorAlert error='Falha ao obter dotações.' />;
  }

  const filtros = {
    exercicio: params.exercicio || result.exercicio,
    secretaria: currSearchParams.secretariaNome as string,
    departamento: currSearchParams.departamentoNome as string,
    subdepartamento: currSearchParams.subdepartamentoNome as string,
    fonte: params.fonte,
    codAplicacaoInicial: params.codAplicacaoInicial,
    codAplicacaoFinal: params.codAplicacaoFinal,
    economicaInicial: params.economicaInicial,
    economicaFinal: params.economicaFinal,
  };

  return (
    <>
      <RelatorioDotacaoAtual dotacoes={dotacoes.data} filtros={filtros} />
      <ClientCompletionTrigger />
    </>
  );
}
