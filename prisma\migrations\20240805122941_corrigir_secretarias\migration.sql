-- DropForeignKey
ALTER TABLE "departamentos_gestores" DROP CONSTRAINT "departamentos_gestores_departamentoId_fkey";

-- DropForeignKey
ALTER TABLE "subdepartamentos_gestores" DROP CONSTRAINT "subdepartamentos_gestores_subdepartamentoId_fkey";

-- AlterTable
ALTER TABLE "departamentos_gestores" ADD COLUMN     "acessoTotalSubDeps" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "secretarias_secretarios" ADD COLUMN     "acessoTotalDeps" BOOLEAN NOT NULL DEFAULT false;

-- AddForeignKey
ALTER TABLE "departamentos_gestores" ADD CONSTRAINT "departamentos_gestores_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "departamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "subdepartamentos_gestores" ADD CONSTRAINT "subdepartamentos_gestores_subdepartamentoId_fkey" FOREIGN KEY ("subdepartamentoId") REFERENCES "subdepartamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
