'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { ErrorAlert } from '@/components/error-alert';
import { obterEmpenho } from '@/lib/database/movimento/empenhos';
import FormEditarEmpenho from '@/components/movimento/empenhos/formEditarEmpenho';

export default async function EditarEmpenhoPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const empenho = await obterEmpenho({
    id: Number(id),
  });

  if (empenho.error) {
    return <ErrorAlert error={empenho.error} />;
  }
  if (!empenho.data) {
    return <ErrorAlert error='Falha ao obter empenho.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Editar Empenho N° {empenho.data.id}</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormEditarEmpenho empenho={empenho.data} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
