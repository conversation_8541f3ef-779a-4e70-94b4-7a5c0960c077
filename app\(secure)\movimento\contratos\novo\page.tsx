'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import FormCriarContrato from '@/components/movimento/contrato/formCriarContrato';

export default async function NovoContratoPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Novo Contrato</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormCriarContrato />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
