'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn, montarCodigoSubdepartamento } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarSubdepartamentosAtivos } from '@/lib/database/gerenciamento/dotacoes';

export function ComboboxSelecionaSubdepartamento({
  subdepartamentos,
  subdepartamentoId,
  setSubdepartamentoId,
}: {
  subdepartamentos: Awaited<ReturnType<typeof listarSubdepartamentosAtivos>>;
  subdepartamentoId: number | null;
  setSubdepartamentoId: React.Dispatch<React.SetStateAction<number | null>>;
}) {
  const [open, setOpen] = React.useState(false);

  const subdepartamentoSelecionado =
    subdepartamentos.data?.find((subdepartamento) => {
      return subdepartamentoId === subdepartamento.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {subdepartamentoSelecionado
              ? montarCodigoSubdepartamento(
                  subdepartamentoSelecionado.departamento.secretaria.codigo,
                  subdepartamentoSelecionado.departamento.codigo,
                  subdepartamentoSelecionado.codigo
                ) +
                ' - ' +
                subdepartamentoSelecionado.nome
              : 'Selecione o subdepartamento...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar subdepartamento...' />
            <CommandEmpty>Subdepartamento não encontrado.</CommandEmpty>
            <CommandList>
              {subdepartamentos.data?.map((subdep) => (
                <CommandItem
                  key={subdep.id}
                  value={`${montarCodigoSubdepartamento(subdep.departamento.secretaria.codigo, subdep.departamento.codigo, subdep.codigo)} - ${subdep.nome}`}
                  onSelect={() => {
                    setSubdepartamentoId(subdep.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      subdepartamentoId === subdep.id
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  {montarCodigoSubdepartamento(
                    subdep.departamento.secretaria.codigo,
                    subdep.departamento.codigo,
                    subdep.codigo
                  )}{' '}
                  - {subdep.nome}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
