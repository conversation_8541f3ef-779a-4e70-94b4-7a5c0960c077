'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn, montarCodigoDepartamento } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarDepartamentosAtivos } from '@/lib/database/gerenciamento/dotacoes';
import { UseFormReturn } from 'react-hook-form';
import { alteracaoOrcamentariaSchema } from '@/lib/validation';
import { z } from 'zod';

export function ComboboxSelecionaDepartamento({
  departamentos,
  // departamentoId,
  // setDepartamentoId,
  habilitado = false,
  form,
}: {
  departamentos: Awaited<ReturnType<typeof listarDepartamentosAtivos>>;
  // departamentoId: number | null;
  // setDepartamentoId: React.Dispatch<React.SetStateAction<number | null>>;
  form: UseFormReturn<z.infer<typeof alteracaoOrcamentariaSchema>>;
  habilitado?: boolean;
}) {
  const [open, setOpen] = React.useState(false);

  const departamentoSelecionado =
    departamentos.data?.find((departamento) => {
      // return departamentoId === departamento.id;
      return form.getValues('idDepto') === departamento.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
            disabled={habilitado}
          >
            {departamentoSelecionado
              ? montarCodigoDepartamento(
                  departamentoSelecionado.secretaria.codigo,
                  departamentoSelecionado.codigo
                ) +
                ' - ' +
                departamentoSelecionado.nome
              : 'Selecione o departamento...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar departamento...' />
            <CommandEmpty>Departamento não encontrado.</CommandEmpty>
            <CommandList>
              {departamentos.data?.map((dep) => (
                <CommandItem
                  key={dep.id}
                  value={`${montarCodigoDepartamento(dep.secretaria.codigo, dep.codigo)} - ${dep.nome}`}
                  onSelect={() => {
                    form.setValue('idDepto', dep.id);
                    // setDepartamentoId(dep.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      // departamentoId === dep.id ? 'opacity-100' : 'opacity-0'
                      form.getValues('idDepto') === dep.id
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  {montarCodigoDepartamento(dep.secretaria.codigo, dep.codigo)}{' '}
                  - {dep.nome}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
