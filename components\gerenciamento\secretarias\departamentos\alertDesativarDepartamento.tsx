import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  desativarDepartamento,
  revalidateDepartamentos,
} from '@/lib/database/gerenciamento/departamentos';
import { codigoSecretariaMask, toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Ban } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertDesativarDepartamento({
  idSecretaria,
  idDepartamento,
  nomeDepartamento,
  codigoDepartamento,
  codigoSecretaria,
}: {
  idDepartamento: number;
  idSecretaria: number;
  nomeDepartamento: string;
  codigoDepartamento: number;
  codigoSecretaria: number;
}) {
  const [loading, setLoading] = useState(false);

  const desativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idDepartamento,
      };

      const res = await desativarDepartamento(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        await revalidateDepartamentos({
          id: idSecretaria,
        });
        toast.success('Departamento Desativado.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='min-w-[115px] text-red-800'
        >
          <Ban className='mr-2 size-4' /> Desativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Desativar Departamento?</AlertDialogTitle>
          <AlertDialogDescription>
            O Departamento{' '}
            <span className='font-bold'>
              {codigoSecretariaMask(codigoSecretaria.toString())}.
              {codigoSecretariaMask(codigoDepartamento.toString())}.00 -{' '}
              {nomeDepartamento}
            </span>{' '}
            será desativado.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              desativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Desativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
