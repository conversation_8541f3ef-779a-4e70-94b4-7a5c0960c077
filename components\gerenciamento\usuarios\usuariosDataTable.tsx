'use client';
import { ColumnDef } from '@tanstack/react-table';
import { UserCog } from 'lucide-react';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { listarPerfisUsuarios } from '@/lib/database/gerenciamento/usuarios';
import { AlertDesativarUsuario } from './alertDesativarUsuario';
import { AlertAtivarUsuario } from './alertAtivarUsuario';

export default function UsuariosDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarPerfisUsuarios>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'email',
      header: 'Email',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link href={`/gerenciamento/usuarios/editar/${row.original.id}`}>
            <Button type='button' variant='outline'>
              <UserCog className='mr-2 size-4' /> Cargos
            </Button>
          </Link>
          {row.original.ativo ? (
            <AlertDesativarUsuario
              idUsuario={row.original.id}
              nomeUsuario={row.original.nome}
            />
          ) : (
            <AlertAtivarUsuario
              idUsuario={row.original.id}
              nomeUsuario={row.original.nome}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatable columns={columns} data={data.data} />
    </div>
  );
}
