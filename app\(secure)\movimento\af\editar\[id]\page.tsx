import FormEditarAF from '@/components/af/formEditarAF';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';

export default async function EditarAFPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Editar AF</PageTitle>
      </PageHeader>
      <PageContent>
        <FormEditarAF id={parseInt((await params).id)} />
      </PageContent>
    </PageWrapper>
  );
}
