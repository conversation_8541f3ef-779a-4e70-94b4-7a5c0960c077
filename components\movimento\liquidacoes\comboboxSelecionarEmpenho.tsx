'use client';

import { useState, useEffect } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn, currencyOptionsNoSymbol } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { UseFormReturn } from 'react-hook-form';
import { criarLiquidacaoInicialSchema } from '@/lib/validation';
import * as z from 'zod';
import currency from 'currency.js';
import { listarEmpenhosParaLiquidacao } from '@/lib/database/movimento/liquidacoes';
import { toast } from 'sonner';
import { toastAlgoDeuErrado } from '@/lib/utils';

interface ComboboxSelecionarEmpenhoProps {
  form: UseFormReturn<z.infer<typeof criarLiquidacaoInicialSchema>>;
  onEmpenhoSelect: (idEmpenho: number) => void;
}

export function ComboboxSelecionarEmpenho({
  form,
  onEmpenhoSelect,
}: ComboboxSelecionarEmpenhoProps) {
  const [open, setOpen] = useState(false);
  const [empenhos, setEmpenhos] = useState<
    | Required<
        Awaited<ReturnType<typeof listarEmpenhosParaLiquidacao>>
      >['data']['empenhos']
    | []
  >([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const carregarEmpenhos = async () => {
      setLoading(true);
      try {
        const result = await listarEmpenhosParaLiquidacao();
        if (result.data && result.data.empenhos) {
          setEmpenhos(result.data.empenhos);
        } else if (result.error) {
          toast.error(result.error);
        }
      } catch (error) {
        console.error('Erro ao carregar empenhos:', error);
        toast.error(toastAlgoDeuErrado);
      } finally {
        setLoading(false);
      }
    };

    carregarEmpenhos();
  }, []);

  const filteredEmpenhos = empenhos.filter((empenho) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      empenho.numero.toString().includes(searchLower) ||
      empenho.resumo?.toLowerCase().includes(searchLower) ||
      empenho.fornecedor?.nome.toLowerCase().includes(searchLower) ||
      empenho.fornecedor?.cnpjCpf.includes(searchLower) ||
      String(empenho.despesa).includes(searchLower)
    );
  });

  const selectedEmpenho = empenhos.find(
    (empenho) => empenho.id === form.watch('idEmpenho')
  );

  return (
    <FormField
      control={form.control}
      name='idEmpenho'
      render={({ field }) => (
        <FormItem className='flex flex-col'>
          <FormLabel className='w-full text-left'>Empenho *</FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant='outline'
                  role='combobox'
                  aria-expanded={open}
                  className={cn(
                    'justify-between',
                    !field.value && 'text-muted-foreground'
                  )}
                  disabled={loading}
                >
                  {loading ? (
                    'Carregando empenhos...'
                  ) : selectedEmpenho ? (
                    <div className='flex items-center gap-2 truncate'>
                      <span className='font-medium'>
                        {selectedEmpenho.numero}/{selectedEmpenho.exercicio}
                      </span>
                      <span className='text-muted-foreground'>-</span>
                      <span className='truncate'>
                        {selectedEmpenho.fornecedor?.nome}
                      </span>
                      <span className='text-muted-foreground'>-</span>
                      <span className='font-medium text-green-600'>
                        {currency(
                          selectedEmpenho.valorTotal,
                          currencyOptionsNoSymbol
                        )
                          .format()
                          .replace(/0$/, '')}
                      </span>
                    </div>
                  ) : (
                    'Selecione um empenho...'
                  )}
                  <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className='w-[600px] p-0'>
              <Command>
                <CommandInput
                  placeholder='Buscar por número, fornecedor, despesa...'
                  value={searchTerm}
                  onValueChange={setSearchTerm}
                />
                <CommandList>
                  <CommandEmpty>Nenhum empenho encontrado.</CommandEmpty>
                  <CommandGroup>
                    {filteredEmpenhos.map((empenho) => (
                      <CommandItem
                        key={empenho.id}
                        value={empenho.id.toString()}
                        onSelect={() => {
                          form.setValue('idEmpenho', empenho.id);
                          onEmpenhoSelect(empenho.id);
                          setOpen(false);
                        }}
                        className='flex items-center gap-2 p-3'
                      >
                        <Check
                          className={cn(
                            'mr-2 h-4 w-4',
                            field.value === empenho.id
                              ? 'opacity-100'
                              : 'opacity-0'
                          )}
                        />
                        <div className='flex-1 space-y-1'>
                          <div className='flex items-center gap-2'>
                            <span className='font-medium'>
                              {empenho.numero}/{empenho.exercicio}
                            </span>
                            <span className='text-muted-foreground'>-</span>
                            <span className='font-medium text-green-600'>
                              {currency(
                                empenho.valorTotal,
                                currencyOptionsNoSymbol
                              ).format()}
                            </span>
                          </div>
                          <div className='text-muted-foreground text-sm'>
                            <div>{empenho.fornecedor?.nome}</div>
                            <div>
                              {empenho.despesa} - {empenho.descricaoDespesa}
                            </div>
                            <div className='truncate'>{empenho.resumo}</div>
                          </div>
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
