-- CreateTable
CREATE TABLE "external_users" (
    "id" SERIAL NOT NULL,
    "user_id" UUID NOT NULL,
    "nome" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "external_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "external_users_user_id_key" ON "external_users"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "external_users_email_key" ON "external_users"("email");
