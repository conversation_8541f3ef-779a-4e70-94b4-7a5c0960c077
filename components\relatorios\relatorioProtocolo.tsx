import { siteConfig } from '@/config/site';
import { StatusProtocolo } from '@/lib/enums';
import { toCurrency } from '@/lib/serverUtils';
import { ProtocoloWithRelations } from '@/lib/database/movimento/protocolos';

interface RelatorioProtocoloProps {
  protocolo: ProtocoloWithRelations;
}

export const RelatorioProtocolo = ({ protocolo }: RelatorioProtocoloProps) => {
  return (
    <div className='mx-auto max-w-[900px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>RELATÓRIO DE PROTOCOLO</h2>
        <div className='text-sm text-gray-600'>
          <p>Data: {new Date().toLocaleDateString('pt-BR')}</p>
          <p>Hora: {new Date().toLocaleTimeString('pt-BR')}</p>
        </div>
      </div>

      {/* Informações do Protocolo */}
      <div className='mb-6'>
        <h3 className='mb-3 border-b pb-1 font-semibold'>Dados do Protocolo</h3>
        <div className='grid grid-cols-2 gap-3 text-sm'>
          <div>
            <span className='font-medium'>Número:</span> #{protocolo.id}
          </div>
          <div>
            <span className='font-medium'>Protocolo:</span> {protocolo.numero}
          </div>
          <div>
            <span className='font-medium'>Exercício:</span>{' '}
            {protocolo.exercicio}
          </div>
          <div>
            <span className='font-medium'>Status:</span>{' '}
            {StatusProtocolo[protocolo.status]?.replace(/_/g, ' ')}
          </div>
          <div className='col-span-2'>
            <span className='font-medium'>Data de Abertura:</span>{' '}
            {new Date(protocolo.dataAbertura).toLocaleDateString('pt-BR')}
          </div>
        </div>
      </div>

      {/* Informações da Reserva */}
      {protocolo.reserva ? (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Informações da Reserva
          </h3>
          <div className='grid grid-cols-2 gap-3 text-sm'>
            <div>
              <span className='font-medium'>Reserva:</span> #
              {protocolo.reserva.id}
            </div>
            <div>
              <span className='font-medium'>Valor:</span>{' '}
              {toCurrency(Number(protocolo.reserva.usarTotal)).value}
            </div>
            <div>
              <span className='font-medium'>Secretaria:</span>{' '}
              {protocolo.reserva.dotacao?.secretaria?.nome || 'N/A'}
            </div>
            <div>
              <span className='font-medium'>Departamento:</span>{' '}
              {protocolo.reserva.dotacao?.departamento?.nome || 'N/A'}
            </div>
            <div className='col-span-2'>
              <span className='font-medium'>Despesa:</span>{' '}
              {protocolo.reserva.dotacao?.despesa || 'N/A'}
            </div>
          </div>
        </div>
      ) : (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Informações da Reserva
          </h3>
          <div className='py-3 text-center text-sm text-gray-600'>
            Nenhuma reserva associada a este protocolo
          </div>
        </div>
      )}

      {/* Descrição */}
      <div className='mb-6'>
        <h3 className='mb-3 border-b pb-1 font-semibold'>Descrição</h3>
        <div className='space-y-2 text-sm'>
          <div>
            <span className='font-medium'>Resumo:</span> {protocolo.resumo}
          </div>
          {protocolo.obs && (
            <div>
              <span className='font-medium'>Observações:</span> {protocolo.obs}
            </div>
          )}
        </div>
      </div>

      {/* Autorização de Fornecimento */}
      {protocolo.numeroAF && (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Autorização de Fornecimento (AF)
          </h3>
          <div className='text-sm'>
            <span className='font-medium'>Número:</span> {protocolo.numeroAF}/
            {protocolo.exercicioAF}
          </div>
        </div>
      )}

      {/* Empenho */}
      {protocolo.numeroEmpenho && (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Empenho</h3>
          <div className='text-sm'>
            <span className='font-medium'>Número:</span>{' '}
            {protocolo.numeroEmpenho}/{protocolo.exercicioEmpenho}
          </div>
        </div>
      )}

      {/* Histórico de Alterações */}
      {protocolo.protocolos_audit && protocolo.protocolos_audit.length > 0 && (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Histórico de Alterações
          </h3>
          <div className='space-y-2 text-sm'>
            {protocolo.protocolos_audit.map((audit) => (
              <div key={audit.id} className='border-b pb-2'>
                <div className='flex justify-between'>
                  <span className='font-medium'>
                    {StatusProtocolo[audit.deStatus]?.replace(/_/g, ' ')} →{' '}
                    {StatusProtocolo[audit.paraStatus]?.replace(/_/g, ' ')}
                  </span>
                  <span className='text-gray-600'>
                    {new Date(audit.data).toLocaleDateString('pt-BR')}
                  </span>
                </div>
                {audit.obs && <p className='mt-1 text-gray-600'>{audit.obs}</p>}
                <p className='text-xs text-gray-500'>
                  Por: {audit.usuario?.nome || 'Desconhecido'}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Rodapé */}
      <div className='mt-8 border-t pt-4 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p className='mt-1'>{siteConfig.name}</p>
      </div>
    </div>
  );
};
