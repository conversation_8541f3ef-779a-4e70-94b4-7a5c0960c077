'use client';
import { ColumnDef } from '@tanstack/react-table';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { ConfigModulo } from '@/types/app';
import { Check, Trash, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dispatch } from 'react';

export default function ConfigModulosDatatable({
  data,
  setModulosConfigurados,
}: {
  data: ConfigModulo[];
  setModulosConfigurados: Dispatch<React.SetStateAction<ConfigModulo[]>>;
}) {
  if (!data) return null;
  const columns: ColumnDef<(typeof data)[0]>[] = [
    {
      accessorKey: 'nome',
      header: 'Módulo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'read',
      header: 'Acessar',
      cell: ({ row }) =>
        row.getValue('read') ? (
          <Check className='mx-auto size-4 text-green-800' />
        ) : (
          <X className='mx-auto size-4 text-red-800' />
        ),
    },
    {
      accessorKey: 'write',
      header: 'Criar',
      cell: ({ row }) =>
        row.getValue('write') ? (
          <Check className='mx-auto size-4 text-green-800' />
        ) : (
          <X className='mx-auto size-4 text-red-800' />
        ),
    },
    {
      accessorKey: 'update',
      header: 'Alterar',
      cell: ({ row }) =>
        row.getValue('update') ? (
          <Check className='mx-auto size-4 text-green-800' />
        ) : (
          <X className='mx-auto size-4 text-red-800' />
        ),
    },
    {
      accessorKey: 'delete',
      header: 'Excluir',
      cell: ({ row }) =>
        row.getValue('delete') ? (
          <Check className='mx-auto size-4 text-green-800' />
        ) : (
          <X className='mx-auto size-4 text-red-800' />
        ),
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <Button
          type='button'
          variant='ghost'
          aria-description='Remover Módulo'
          onClick={() => {
            setModulosConfigurados(
              data.filter((mod) => mod.id !== row.getValue('id'))
            );
          }}
        >
          <Trash className='size-4' />
        </Button>
      ),
    },
  ];
  return (
    <div className='overflow-x-scroll'>
      <ReusableDatatable columns={columns} data={data} />
    </div>
  );
}
