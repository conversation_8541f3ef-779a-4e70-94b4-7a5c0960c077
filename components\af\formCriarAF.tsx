'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Loader2 } from 'lucide-react';
import {
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { criarAFSchema } from '@/lib/validation';
import { toast } from 'sonner';
import { criarAF, obterEmpenhoParaAF } from '@/lib/database/movimento/afs';
import currency from 'currency.js';
import { ComboboxSelecionarEmpenho } from './comboboxSelecionarEmpenho';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default function FormCriarAF({ idEmpenho }: { idEmpenho?: number }) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [loading, setLoading] = useState(false);
  const [empenho, setEmpenho] = useState<Awaited<
    ReturnType<typeof obterEmpenhoParaAF>
  > | null>(null);

  const form = useForm<z.infer<typeof criarAFSchema>>({
    resolver: zodResolver(criarAFSchema),
    defaultValues: {
      idEmpenho: idEmpenho || 0,
      resumo: '',
      obs: '',
      valorTotal: 0,
    },
  });

  const [valorTotalBRL, setValorTotalBRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );

  const loadEmpenho = useCallback(
    async (empId: number) => {
      try {
        const result = await obterEmpenhoParaAF(empId);
        if (result.error) {
          toast.error(result.error);
        } else {
          setEmpenho(result);
          form.setValue('idEmpenho', empId);
        }
      } catch (error) {
        toast.error('Erro ao carregar empenho');
      }
    },
    [form]
  );

  useEffect(() => {
    const empenhoFromUrl = searchParams?.get('empenho');
    const empenhoId = empenhoFromUrl ? parseInt(empenhoFromUrl) : idEmpenho;

    if (empenhoId) {
      loadEmpenho(empenhoId);
      form.setValue('idEmpenho', empenhoId);
    }
  }, [form, idEmpenho, loadEmpenho, searchParams]);

  const onSubmit = async (values: z.infer<typeof criarAFSchema>) => {
    setLoading(true);
    try {
      const result = await criarAF({
        idEmpenho: values.idEmpenho,
        resumo: values.resumo,
        obs: values.obs,
        valorTotal: values.valorTotal,
        dataEmissao: values.dataEmissao,
        dataVencimento: values.dataVencimento,
      });

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF criada com sucesso!');
        // Redirect to the AF details page where user can upload documents
        router.push(`/movimento/af/visualizar/${result.data!.id}`);
      }
    } catch (error) {
      toast.error(toastAlgoDeuErrado);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Button variant='ghost' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='mr-1 h-4 w-4' />
            Voltar
          </Button>
          <h1 className='text-2xl font-bold'>Criar AF</h1>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <FormField
              control={form.control}
              name='idEmpenho'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Empenho</FormLabel>
                  <FormControl>
                    <ComboboxSelecionarEmpenho
                      empenhoId={field.value || null}
                      setEmpenhoId={(id) => {
                        field.onChange(id);
                        loadEmpenho(id);
                      }}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {empenho?.data && (
            <div className='rounded-lg bg-gray-50 p-4'>
              <h3 className='mb-2 font-semibold'>Empenho Selecionado</h3>
              <div className='grid grid-cols-1 gap-2 text-sm md:grid-cols-2'>
                <div>
                  <span className='font-medium'>Empenho:</span> #
                  {empenho.data.empenho.id}
                </div>
                <div>
                  <span className='font-medium'>Despesa:</span>{' '}
                  {empenho.data.empenho.dotacao?.despesa}
                </div>
                <div>
                  <span className='font-medium'>Fornecedor:</span>{' '}
                  {empenho.data.empenho.fornecedor?.nome}
                </div>
                <div>
                  <span className='font-medium'>Saldo Disponível:</span>{' '}
                  {currency(
                    empenho.data.saldoDisponivel,
                    currencyOptionsNoSymbol
                  )
                    .format()
                    .replace(/0$/, '')}
                </div>
              </div>
            </div>
          )}

          {/* Campos de data */}
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <FormField
              control={form.control}
              name='dataEmissao'
              render={({ field }) => (
                <FormItem className='flex flex-col'>
                  <FormLabel>Data de Emissão</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={'outline'}
                          className={'w-full pl-3 text-left font-normal'}
                        >
                          {field.value ? (
                            format(field.value, 'dd/MM/yyyy', { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className='w-auto p-0' align='start'>
                      <Calendar
                        mode='single'
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date > new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='dataVencimento'
              render={({ field }) => (
                <FormItem className='flex flex-col'>
                  <FormLabel>Data de Vencimento</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={'outline'}
                          className={'w-full pl-3 text-left font-normal'}
                        >
                          {field.value ? (
                            format(field.value, 'dd/MM/yyyy', { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className='w-auto p-0' align='start'>
                      <Calendar
                        mode='single'
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => {
                          const dataEmissao = form.getValues('dataEmissao');
                          return dataEmissao ? date < dataEmissao : false;
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='resumo'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Resumo</FormLabel>
                <FormControl>
                  <Input placeholder='Digite um resumo para a AF' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='obs'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Observações</FormLabel>
                <FormControl>
                  <Input placeholder='Observações (opcional)' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='valorTotal'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Valor Total</FormLabel>
                <FormControl>
                  <Input
                    placeholder='0,00'
                    value={valorTotalBRL}
                    onChange={(e) => {
                      const maskedValue = moneyMask(e.target.value);
                      setValorTotalBRL(maskedValue);
                      field.onChange(moneyUnmask(maskedValue));
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className='flex justify-end gap-2'>
            <Button
              type='button'
              variant='outline'
              onClick={() => router.back()}
            >
              Cancelar
            </Button>
            <Button type='submit' disabled={loading}>
              {loading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
              Criar AF
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
