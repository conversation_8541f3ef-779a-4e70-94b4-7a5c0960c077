'use server';

import * as z from 'zod';
import {
  AuditoriaMovimentoEmpenhos,
  ErrosEmpenho,
  Permissoes,
  StatusEmpenho,
  StatusReserva,
  StatusLiquidacao,
  StatusEmpenhoDesc,
} from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import { auditoriaErroSchema, permissaoSchema } from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../../usuarios';
import { createClient } from '@/lib/supabase/server';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../../auditoria/erro';
import { revalidatePath } from 'next/cache';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { toCurrency } from '@/lib/serverUtils';

const MODULE = Modulos.MOVIMENTO_EMPENHO;
const ROUTE = '/movimento/empenhos';

// Interfaces para operações em lote
export interface BatchOperationParams {
  ids: number[];
  motivo?: string;
  valorAnulacao?: number;
  valorLiquidacao?: number;
  novoStatus?: StatusEmpenho;
}

export interface BatchOperationResult {
  sucesso: number;
  falhas: number;
  erros: BatchOperationError[];
  detalhes: BatchOperationDetail[];
}

export interface BatchOperationError {
  id: number;
  erro: string;
  motivo: string;
}

export interface BatchOperationDetail {
  id: number;
  numero: number;
  status: string;
  acao: string;
  valorAfetado?: number;
  observacao?: string;
}

// Schema para validação de operações em lote
export const batchOperationSchema = z
  .object({
    ids: z.array(z.coerce.number().int().min(1)).min(1).max(100),
    operacao: z.enum([
      'cancelar',
      'anular_parcial',
      'anular_total',
      'atualizar_status',
      'liquidar',
    ]),
    motivo: z.string().min(5).max(500).optional(),
    valorAnulacao: z.coerce.number().positive().optional(),
    valorLiquidacao: z.coerce.number().positive().optional(),
    novoStatus: z.nativeEnum(StatusEmpenho).optional(),
  })
  .refine(
    (data) => {
      if (data.operacao === 'anular_parcial' && !data.valorAnulacao) {
        return false;
      }
      if (data.operacao === 'liquidar' && !data.valorLiquidacao) {
        return false;
      }
      if (data.operacao === 'atualizar_status' && !data.novoStatus) {
        return false;
      }
      return true;
    },
    {
      message: 'Parâmetros inválidos para a operação selecionada',
      path: ['operacao'],
    }
  );

// Função principal de validação em lote
export async function validarOperacaoLote(
  params: BatchOperationParams,
  operacao: string
) {
  // Obter UUID do usuário
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    throw new Error('Usuário não autenticado.');
  }

  const userUuid = user.id;

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao:
      operacao === 'cancelar' ? Permissoes.DELETAR : Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (
    !resultPermissao ||
    resultPermissao.error ||
    !resultPermissao.temPermissao
  ) {
    throw new Error('Usuário sem permissão para realizar operações em lote.');
  }

  if (!resultPermissao.idUsuario || !resultPermissao.exercicio) {
    throw new Error('Erro ao obter dados do usuário.');
  }

  // Validar quantidade de empenhos
  if (params.ids.length > 100) {
    throw new Error(
      'Não é possível processar mais de 100 empenhos em uma única operação.'
    );
  }

  // Buscar empenhos com informações completas
  const empenhos = await prisma.empenhos.findMany({
    where: {
      id: { in: params.ids },
      ativo: true,
    },
    include: {
      dotacao: true,
      liquidacoes: {
        where: {
          ativo: true,
          status: { not: StatusLiquidacao.ESTORNADA },
        },
        select: { valorTotal: true },
      },
      empenhos_anulacoes: {
        where: { ativo: true },
        select: { valorAnulado: true },
      },
    },
  });

  if (empenhos.length !== params.ids.length) {
    const encontrados = empenhos.map((e) => e.id);
    const naoEncontrados = params.ids.filter((id) => !encontrados.includes(id));
    throw new Error(`Empenhos não encontrados: ${naoEncontrados.join(', ')}`);
  }

  // Validar acesso a todos os empenhos
  if (!resultPermissao.gerente) {
    const acessos = await prisma.user_profiles_dotacoes.findMany({
      where: {
        uuidUsuario: userUuid,
      },
    });

    const dotacoesAcessiveis = new Set();
    acessos.forEach((acesso) => {
      if (acesso.acessoTotal) {
        // Se tem acesso total, não precisa validar dotacoesIds
        return;
      }
      acesso.dotacoesIds.forEach((dotacaoId) =>
        dotacoesAcessiveis.add(dotacaoId)
      );
    });

    const empenhosSemAcesso = empenhos.filter(
      (e) => !dotacoesAcessiveis.has(e.idDotacao)
    );

    if (empenhosSemAcesso.length > 0) {
      throw new Error(
        `Usuário não tem acesso aos empenhos: ${empenhosSemAcesso.map((e) => e.id).join(', ')}`
      );
    }
  }

  return {
    empenhos,
    usuario: resultPermissao,
    ip: (await obterIpUsuarioConectado()).data,
  };
}

// Operação em lote: Cancelar empenhos
export async function cancelarEmpenhosEmLote(
  params: BatchOperationParams
): Promise<BatchOperationResult> {
  try {
    const { empenhos, usuario, ip } = await validarOperacaoLote(
      params,
      'cancelar'
    );

    if (!params.motivo) {
      throw new Error(
        'Motivo do cancelamento é obrigatório para operações em lote.'
      );
    }

    const resultado: BatchOperationResult = {
      sucesso: 0,
      falhas: 0,
      erros: [],
      detalhes: [],
    };

    for (const empenho of empenhos) {
      try {
        // Validar status do empenho
        if (empenho.status !== StatusEmpenho.EMPENHADO) {
          throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ATIVO]);
        }

        // Verificar liquidações
        const valorLiquidado = empenho.liquidacoes.reduce(
          (acc, liq) => acc.add(liq.valorTotal.toNumber()),
          currency(0, currencyOptionsNoSymbol)
        );

        if (valorLiquidado.value > 0) {
          throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_COM_LIQUIDACOES]);
        }

        // Devolver valor para dotação
        const valorEmpenho = toCurrency(empenho.valorTotal);

        await prisma.$transaction(async (tx) => {
          const novoValorAtualDotacao = toCurrency(
            empenho.dotacao.valorAtual
          ).add(valorEmpenho);

          await tx.dotacoes.update({
            where: { id: empenho.idDotacao },
            data: {
              valorAtual: novoValorAtualDotacao.value,
              cotaMes1: toCurrency(empenho.dotacao.cotaMes1).add(
                empenho.usarMes1.toNumber()
              ).value,
              cotaMes2: toCurrency(empenho.dotacao.cotaMes2).add(
                empenho.usarMes2.toNumber()
              ).value,
              cotaMes3: toCurrency(empenho.dotacao.cotaMes3).add(
                empenho.usarMes3.toNumber()
              ).value,
              cotaMes4: toCurrency(empenho.dotacao.cotaMes4).add(
                empenho.usarMes4.toNumber()
              ).value,
              cotaMes5: toCurrency(empenho.dotacao.cotaMes5).add(
                empenho.usarMes5.toNumber()
              ).value,
              cotaMes6: toCurrency(empenho.dotacao.cotaMes6).add(
                empenho.usarMes6.toNumber()
              ).value,
              cotaMes7: toCurrency(empenho.dotacao.cotaMes7).add(
                empenho.usarMes7.toNumber()
              ).value,
              cotaMes8: toCurrency(empenho.dotacao.cotaMes8).add(
                empenho.usarMes8.toNumber()
              ).value,
              cotaMes9: toCurrency(empenho.dotacao.cotaMes9).add(
                empenho.usarMes9.toNumber()
              ).value,
              cotaMes10: toCurrency(empenho.dotacao.cotaMes10).add(
                empenho.usarMes10.toNumber()
              ).value,
              cotaMes11: toCurrency(empenho.dotacao.cotaMes11).add(
                empenho.usarMes11.toNumber()
              ).value,
              cotaMes12: toCurrency(empenho.dotacao.cotaMes12).add(
                empenho.usarMes12.toNumber()
              ).value,
            },
          });

          // Cancelar empenho
          await tx.empenhos.update({
            where: { id: empenho.id },
            data: {
              status: StatusEmpenho.CANCELADO,
              obs: empenho.obs
                ? `${empenho.obs}\n\nMOTIVO CANCELAMENTO EM LOTE: ${params.motivo}`
                : `MOTIVO CANCELAMENTO EM LOTE: ${params.motivo}`,
            },
          });

          // Reverter reserva se existir
          if (empenho.idReserva) {
            await tx.reservas.update({
              where: { id: empenho.idReserva },
              data: { status: StatusReserva.Reservado },
            });
          }

          // Registrar auditoria
          await tx.empenhos_audit.create({
            data: {
              idEmpenho: empenho.id,
              idUsuario: usuario.idUsuario!,
              acao: AuditoriaMovimentoEmpenhos.CANCELAR_EMPENHO,
              ip,
              obs: `Cancelamento em lote. Motivo: ${params.motivo}`,
            },
          });
        });

        resultado.sucesso++;
        resultado.detalhes.push({
          id: empenho.id,
          numero: empenho.numero,
          status: StatusEmpenhoDesc[StatusEmpenho.CANCELADO],
          acao: 'Cancelado',
          valorAfetado: valorEmpenho.value,
          observacao: params.motivo,
        });
      } catch (error) {
        resultado.falhas++;
        resultado.erros.push({
          id: empenho.id,
          erro: error instanceof Error ? error.message : 'Erro desconhecido',
          motivo: params.motivo || '',
        });
      }
    }

    revalidatePath(ROUTE);
    return resultado;
  } catch (error) {
    console.error('Erro em cancelarEmpenhosEmLote:', error);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(error),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);

    return {
      sucesso: 0,
      falhas: params.ids.length,
      erros: params.ids.map((id) => ({
        id,
        erro: error instanceof Error ? error.message : 'Erro ao processar lote',
        motivo: params.motivo || '',
      })),
      detalhes: [],
    };
  }
}

// Operação em lote: Anular parcialmente
export async function anularParcialmenteEmpenhosEmLote(
  params: BatchOperationParams
): Promise<BatchOperationResult> {
  try {
    if (!params.valorAnulacao || params.valorAnulacao <= 0) {
      throw new Error('Valor de anulação é obrigatório para anulação parcial.');
    }

    const { empenhos, usuario, ip } = await validarOperacaoLote(
      params,
      'anular_parcial'
    );

    if (!params.motivo) {
      throw new Error(
        'Motivo da anulação é obrigatório para operações em lote.'
      );
    }

    const resultado: BatchOperationResult = {
      sucesso: 0,
      falhas: 0,
      erros: [],
      detalhes: [],
    };

    for (const empenho of empenhos) {
      try {
        // Validar status do empenho
        if (empenho.status !== StatusEmpenho.EMPENHADO) {
          throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ATIVO]);
        }

        // Calcular saldo disponível
        const valorOriginal = currency(0, currencyOptionsNoSymbol)
          .add(empenho.usarMes1.toNumber())
          .add(empenho.usarMes2.toNumber())
          .add(empenho.usarMes3.toNumber())
          .add(empenho.usarMes4.toNumber())
          .add(empenho.usarMes5.toNumber())
          .add(empenho.usarMes6.toNumber())
          .add(empenho.usarMes7.toNumber())
          .add(empenho.usarMes8.toNumber())
          .add(empenho.usarMes9.toNumber())
          .add(empenho.usarMes10.toNumber())
          .add(empenho.usarMes11.toNumber())
          .add(empenho.usarMes12.toNumber());

        const valorLiquidado = empenho.liquidacoes.reduce(
          (acc, liq) => acc.add(liq.valorTotal.toNumber()),
          currency(0, currencyOptionsNoSymbol)
        );

        const valorAnuladoExistente = empenho.empenhos_anulacoes.reduce(
          (acc, anul) => acc.add(anul.valorAnulado.toNumber()),
          currency(0, currencyOptionsNoSymbol)
        );

        const saldoAtual = valorOriginal
          .subtract(valorLiquidado)
          .subtract(valorAnuladoExistente);

        // Validar valor de anulação
        const valorAnular = currency(
          params.valorAnulacao,
          currencyOptionsNoSymbol
        );
        if (valorAnular.value > saldoAtual.value) {
          throw new Error(
            ErrosEmpenho[ErrosEmpenho.VALOR_ANULACAO_MAIOR_SALDO]
          );
        }

        // Calcular novo valor total
        const novoValorTotal = valorOriginal.subtract(valorAnular);

        await prisma.$transaction(async (tx) => {
          const fatorReducao = novoValorTotal.divide(valorOriginal);

          const novasCotasMensais = {
            usarMes1: toCurrency(empenho.usarMes1).multiply(fatorReducao).value,
            usarMes2: toCurrency(empenho.usarMes2).multiply(fatorReducao).value,
            usarMes3: toCurrency(empenho.usarMes3).multiply(fatorReducao).value,
            usarMes4: toCurrency(empenho.usarMes4).multiply(fatorReducao).value,
            usarMes5: toCurrency(empenho.usarMes5).multiply(fatorReducao).value,
            usarMes6: toCurrency(empenho.usarMes6).multiply(fatorReducao).value,
            usarMes7: toCurrency(empenho.usarMes7).multiply(fatorReducao).value,
            usarMes8: toCurrency(empenho.usarMes8).multiply(fatorReducao).value,
            usarMes9: toCurrency(empenho.usarMes9).multiply(fatorReducao).value,
            usarMes10: toCurrency(empenho.usarMes10).multiply(fatorReducao)
              .value,
            usarMes11: toCurrency(empenho.usarMes11).multiply(fatorReducao)
              .value,
            usarMes12: toCurrency(empenho.usarMes12).multiply(fatorReducao)
              .value,
          };

          // Devolver valor anulado para dotação
          const fatorAnulacao = valorAnular.divide(valorOriginal);
          const cotasAnuladas = {
            usarMes1: toCurrency(empenho.usarMes1).multiply(fatorAnulacao)
              .value,
            usarMes2: toCurrency(empenho.usarMes2).multiply(fatorAnulacao)
              .value,
            usarMes3: toCurrency(empenho.usarMes3).multiply(fatorAnulacao)
              .value,
            usarMes4: toCurrency(empenho.usarMes4).multiply(fatorAnulacao)
              .value,
            usarMes5: toCurrency(empenho.usarMes5).multiply(fatorAnulacao)
              .value,
            usarMes6: toCurrency(empenho.usarMes6).multiply(fatorAnulacao)
              .value,
            usarMes7: toCurrency(empenho.usarMes7).multiply(fatorAnulacao)
              .value,
            usarMes8: toCurrency(empenho.usarMes8).multiply(fatorAnulacao)
              .value,
            usarMes9: toCurrency(empenho.usarMes9).multiply(fatorAnulacao)
              .value,
            usarMes10: toCurrency(empenho.usarMes10).multiply(fatorAnulacao)
              .value,
            usarMes11: toCurrency(empenho.usarMes11).multiply(fatorAnulacao)
              .value,
            usarMes12: toCurrency(empenho.usarMes12).multiply(fatorAnulacao)
              .value,
          };

          await tx.dotacoes.update({
            where: { id: empenho.idDotacao },
            data: {
              valorAtual: toCurrency(empenho.dotacao.valorAtual).add(
                valorAnular
              ).value,
              cotaMes1: toCurrency(empenho.dotacao.cotaMes1).add(
                cotasAnuladas.usarMes1
              ).value,
              cotaMes2: toCurrency(empenho.dotacao.cotaMes2).add(
                cotasAnuladas.usarMes2
              ).value,
              cotaMes3: toCurrency(empenho.dotacao.cotaMes3).add(
                cotasAnuladas.usarMes3
              ).value,
              cotaMes4: toCurrency(empenho.dotacao.cotaMes4).add(
                cotasAnuladas.usarMes4
              ).value,
              cotaMes5: toCurrency(empenho.dotacao.cotaMes5).add(
                cotasAnuladas.usarMes5
              ).value,
              cotaMes6: toCurrency(empenho.dotacao.cotaMes6).add(
                cotasAnuladas.usarMes6
              ).value,
              cotaMes7: toCurrency(empenho.dotacao.cotaMes7).add(
                cotasAnuladas.usarMes7
              ).value,
              cotaMes8: toCurrency(empenho.dotacao.cotaMes8).add(
                cotasAnuladas.usarMes8
              ).value,
              cotaMes9: toCurrency(empenho.dotacao.cotaMes9).add(
                cotasAnuladas.usarMes9
              ).value,
              cotaMes10: toCurrency(empenho.dotacao.cotaMes10).add(
                cotasAnuladas.usarMes10
              ).value,
              cotaMes11: toCurrency(empenho.dotacao.cotaMes11).add(
                cotasAnuladas.usarMes11
              ).value,
              cotaMes12: toCurrency(empenho.dotacao.cotaMes12).add(
                cotasAnuladas.usarMes12
              ).value,
            },
          });

          // Atualizar empenho
          await tx.empenhos.update({
            where: { id: empenho.id },
            data: {
              valorTotal: novoValorTotal.value,
              ...novasCotasMensais,
              status:
                novoValorTotal.value === 0
                  ? StatusEmpenho.ANULADO
                  : StatusEmpenho.EMPENHADO,
            },
          });

          // Registrar anulação
          await tx.empenhos_anulacoes.create({
            data: {
              idEmpenho: empenho.id,
              valorAnulado: valorAnular.value,
              motivo: params.motivo!,
              idUsuario: usuario.idUsuario!,
              ip,
            },
          });

          // Registrar auditoria
          await tx.empenhos_audit.create({
            data: {
              idEmpenho: empenho.id,
              idUsuario: usuario.idUsuario!,
              acao: AuditoriaMovimentoEmpenhos.ANULAR_EMPENHO_PARCIAL,
              ip,
              obs: `Anulação parcial em lote: R$ ${valorAnular.format()} - ${params.motivo}`,
            },
          });
        });

        resultado.sucesso++;
        resultado.detalhes.push({
          id: empenho.id,
          numero: empenho.numero,
          status:
            novoValorTotal.value === 0
              ? StatusEmpenhoDesc[StatusEmpenho.ANULADO]
              : StatusEmpenhoDesc[StatusEmpenho.EMPENHADO],
          acao: 'Anulado Parcialmente',
          valorAfetado: valorAnular.value,
          observacao: params.motivo,
        });
      } catch (error) {
        resultado.falhas++;
        resultado.erros.push({
          id: empenho.id,
          erro: error instanceof Error ? error.message : 'Erro desconhecido',
          motivo: params.motivo || '',
        });
      }
    }

    revalidatePath(ROUTE);
    return resultado;
  } catch (error) {
    console.error('Erro em anularParcialmenteEmpenhosEmLote:', error);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(error),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);

    return {
      sucesso: 0,
      falhas: params.ids.length,
      erros: params.ids.map((id) => ({
        id,
        erro: error instanceof Error ? error.message : 'Erro ao processar lote',
        motivo: params.motivo || '',
      })),
      detalhes: [],
    };
  }
}

// Operação em lote: Atualizar status
export async function atualizarStatusEmpenhosEmLote(
  params: BatchOperationParams
): Promise<BatchOperationResult> {
  try {
    if (!params.novoStatus) {
      throw new Error('Novo status é obrigatório para atualização em lote.');
    }

    const { empenhos, usuario, ip } = await validarOperacaoLote(
      params,
      'atualizar_status'
    );

    const resultado: BatchOperationResult = {
      sucesso: 0,
      falhas: 0,
      erros: [],
      detalhes: [],
    };

    for (const empenho of empenhos) {
      try {
        // Validar transição de status
        const statusTransicoesPermitidas = {
          [StatusEmpenho.CANCELADO]: [StatusEmpenho.EMPENHADO],
          [StatusEmpenho.EMPENHADO]: [
            StatusEmpenho.LIQUIDADO,
            StatusEmpenho.CANCELADO,
          ],
          [StatusEmpenho.LIQUIDADO]: [StatusEmpenho.EMPENHADO],
          [StatusEmpenho.ANULADO]: [StatusEmpenho.EMPENHADO],
          [StatusEmpenho.ANULADO_PARCIAL]: [StatusEmpenho.EMPENHADO],
        };

        const transicoesPermitidas =
          statusTransicoesPermitidas[
            empenho.status as keyof typeof statusTransicoesPermitidas
          ];
        if (!transicoesPermitidas.includes(params.novoStatus)) {
          throw new Error(
            `Não é possível alterar o status de ${StatusEmpenhoDesc[empenho.status as keyof typeof StatusEmpenhoDesc]} para ${StatusEmpenhoDesc[params.novoStatus as keyof typeof StatusEmpenhoDesc]}`
          );
        }

        await prisma.$transaction(async (tx) => {
          // Atualizar status
          await tx.empenhos.update({
            where: { id: empenho.id },
            data: {
              status: params.novoStatus,
              obs: params.motivo
                ? `${empenho.obs}\n\nMOTIVO ALTERAÇÃO STATUS EM LOTE: ${params.motivo}`
                : empenho.obs,
            },
          });

          // Registrar auditoria
          await tx.empenhos_audit.create({
            data: {
              idEmpenho: empenho.id,
              idUsuario: usuario.idUsuario!,
              acao: AuditoriaMovimentoEmpenhos.ALTERAR_EMPENHO,
              ip,
              obs: `Alteração de status em lote: ${StatusEmpenhoDesc[empenho.status as keyof typeof StatusEmpenhoDesc]} → ${StatusEmpenhoDesc[params.novoStatus as keyof typeof StatusEmpenhoDesc]}${params.motivo ? ` - ${params.motivo}` : ''}`,
            },
          });
        });

        resultado.sucesso++;
        resultado.detalhes.push({
          id: empenho.id,
          numero: empenho.numero,
          status:
            StatusEmpenhoDesc[
              params.novoStatus as keyof typeof StatusEmpenhoDesc
            ],
          acao: 'Status Atualizado',
          observacao: params.motivo,
        });
      } catch (error) {
        resultado.falhas++;
        resultado.erros.push({
          id: empenho.id,
          erro: error instanceof Error ? error.message : 'Erro desconhecido',
          motivo: params.motivo || '',
        });
      }
    }

    revalidatePath(ROUTE);
    return resultado;
  } catch (error) {
    console.error('Erro em atualizarStatusEmpenhosEmLote:', error);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(error),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);

    return {
      sucesso: 0,
      falhas: params.ids.length,
      erros: params.ids.map((id) => ({
        id,
        erro: error instanceof Error ? error.message : 'Erro ao processar lote',
        motivo: params.motivo || '',
      })),
      detalhes: [],
    };
  }
}

// Operação em lote: Liquidar empenhos
export async function liquidarEmpenhosEmLote(
  params: BatchOperationParams
): Promise<BatchOperationResult> {
  try {
    if (!params.valorLiquidacao || params.valorLiquidacao <= 0) {
      throw new Error(
        'Valor de liquidação é obrigatório para liquidação em lote.'
      );
    }

    const { empenhos, usuario, ip } = await validarOperacaoLote(
      params,
      'liquidar'
    );

    if (!params.motivo) {
      throw new Error(
        'Motivo da liquidação é obrigatório para operações em lote.'
      );
    }

    const resultado: BatchOperationResult = {
      sucesso: 0,
      falhas: 0,
      erros: [],
      detalhes: [],
    };

    for (const empenho of empenhos) {
      try {
        // Validar status do empenho
        if (empenho.status !== StatusEmpenho.EMPENHADO) {
          throw new Error(ErrosEmpenho[ErrosEmpenho.EMPENHO_NAO_ATIVO]);
        }

        // Calcular saldo disponível
        const valorOriginal = currency(0, currencyOptionsNoSymbol)
          .add(empenho.usarMes1.toNumber())
          .add(empenho.usarMes2.toNumber())
          .add(empenho.usarMes3.toNumber())
          .add(empenho.usarMes4.toNumber())
          .add(empenho.usarMes5.toNumber())
          .add(empenho.usarMes6.toNumber())
          .add(empenho.usarMes7.toNumber())
          .add(empenho.usarMes8.toNumber())
          .add(empenho.usarMes9.toNumber())
          .add(empenho.usarMes10.toNumber())
          .add(empenho.usarMes11.toNumber())
          .add(empenho.usarMes12.toNumber());

        const valorLiquidado = empenho.liquidacoes.reduce(
          (acc, liq) => acc.add(liq.valorTotal.toNumber()),
          currency(0, currencyOptionsNoSymbol)
        );

        const valorAnuladoExistente = empenho.empenhos_anulacoes.reduce(
          (acc, anul) => acc.add(anul.valorAnulado.toNumber()),
          currency(0, currencyOptionsNoSymbol)
        );

        const saldoAtual = valorOriginal
          .subtract(valorLiquidado)
          .subtract(valorAnuladoExistente);

        // Validar valor de liquidação
        const valorLiquidar = currency(
          params.valorLiquidacao,
          currencyOptionsNoSymbol
        );
        if (valorLiquidar.value > saldoAtual.value) {
          throw new Error(
            'Valor de liquidação maior que o saldo disponível do empenho.'
          );
        }

        // Calcular novo saldo após liquidação
        const novoSaldo = saldoAtual.subtract(valorLiquidar);

        await prisma.$transaction(async (tx) => {
          // Obter próximo número de liquidação
          const ultimaLiquidacao = await tx.liquidacoes.findFirst({
            where: { exercicio: empenho.exercicio },
            orderBy: { numero: 'desc' },
            select: { numero: true },
          });
          const numeroLiquidacao = (ultimaLiquidacao?.numero || 0) + 1;

          // Criar liquidação
          await tx.liquidacoes.create({
            data: {
              idEmpenho: empenho.id,
              exercicio: empenho.exercicio,
              numero: numeroLiquidacao,
              valorTotal: valorLiquidar.value,
              status: StatusLiquidacao.LIQUIDADA,
              obs: params.motivo!,
            },
          });

          // Atualizar status do empenho se estiver totalmente liquidado
          if (novoSaldo.value === 0) {
            await tx.empenhos.update({
              where: { id: empenho.id },
              data: { status: StatusEmpenho.LIQUIDADO },
            });
          }

          // Registrar auditoria
          await tx.empenhos_audit.create({
            data: {
              idEmpenho: empenho.id,
              idUsuario: usuario.idUsuario!,
              acao: AuditoriaMovimentoEmpenhos.ALTERAR_EMPENHO,
              ip,
              obs: `Liquidação em lote: R$ ${valorLiquidar.format()} - ${params.motivo}`,
            },
          });
        });

        resultado.sucesso++;
        resultado.detalhes.push({
          id: empenho.id,
          numero: empenho.numero,
          status:
            novoSaldo.value === 0
              ? StatusEmpenhoDesc[
                  StatusEmpenho.LIQUIDADO as keyof typeof StatusEmpenhoDesc
                ]
              : StatusEmpenhoDesc[
                  StatusEmpenho.EMPENHADO as keyof typeof StatusEmpenhoDesc
                ],
          acao: 'Liquidado',
          valorAfetado: valorLiquidar.value,
          observacao: params.motivo,
        });
      } catch (error) {
        resultado.falhas++;
        resultado.erros.push({
          id: empenho.id,
          erro: error instanceof Error ? error.message : 'Erro desconhecido',
          motivo: params.motivo || '',
        });
      }
    }

    revalidatePath(ROUTE);
    return resultado;
  } catch (error) {
    console.error('Erro em liquidarEmpenhosEmLote:', error);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(error),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);

    return {
      sucesso: 0,
      falhas: params.ids.length,
      erros: params.ids.map((id) => ({
        id,
        erro: error instanceof Error ? error.message : 'Erro ao processar lote',
        motivo: params.motivo || '',
      })),
      detalhes: [],
    };
  }
}

// Função principal para executar operações em lote
export async function executarOperacaoLote(
  params: unknown
): Promise<{ data?: BatchOperationResult; error?: string }> {
  try {
    const parsedParams = batchOperationSchema.safeParse(params);

    if (!parsedParams.success) {
      return { error: 'Parâmetros inválidos para operação em lote.' };
    }

    const {
      ids,
      operacao,
      motivo,
      valorAnulacao,
      valorLiquidacao,
      novoStatus,
    } = parsedParams.data;

    switch (operacao) {
      case 'cancelar':
        return { data: await cancelarEmpenhosEmLote({ ids, motivo }) };

      case 'anular_parcial':
        return {
          data: await anularParcialmenteEmpenhosEmLote({
            ids,
            motivo,
            valorAnulacao,
          }),
        };

      case 'liquidar':
        return {
          data: await liquidarEmpenhosEmLote({ ids, motivo, valorLiquidacao }),
        };

      case 'atualizar_status':
        return {
          data: await atualizarStatusEmpenhosEmLote({
            ids,
            motivo,
            novoStatus,
          }),
        };

      default:
        return { error: 'Operação em lote não implementada.' };
    }
  } catch (error) {
    console.error('Erro em executarOperacaoLote:', error);
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Erro ao executar operação em lote',
    };
  }
}

// Função para pré-validação de operações em lote (para UI)
export async function preValidarOperacaoLote(ids: number[], operacao: string) {
  try {
    const params: BatchOperationParams = { ids };
    const { empenhos } = await validarOperacaoLote(params, operacao);

    const validacoes = empenhos.map((empenho) => {
      const erros: string[] = [];

      // Validar status
      if (empenho.status !== StatusEmpenho.EMPENHADO) {
        erros.push(`Empenho ${empenho.numero} não está ativo`);
      }

      // Validar liquidações
      const valorLiquidado = empenho.liquidacoes.reduce(
        (acc, liq) => acc.add(liq.valorTotal.toNumber()),
        currency(0, currencyOptionsNoSymbol)
      );

      if (valorLiquidado.value > 0) {
        erros.push(`Empenho ${empenho.numero} possui liquidações`);
      }

      return {
        id: empenho.id,
        numero: empenho.numero,
        status: empenho.status,
        valorTotal: empenho.valorTotal,
        valorLiquidado: valorLiquidado.value,
        podeProcessar: erros.length === 0,
        erros,
      };
    });

    return {
      data: {
        total: empenhos.length,
        podeProcessar: validacoes.filter((v) => v.podeProcessar).length,
        naoPodeProcessar: validacoes.filter((v) => !v.podeProcessar).length,
        validacoes,
      },
    };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Erro ao validar operação em lote',
    };
  }
}
