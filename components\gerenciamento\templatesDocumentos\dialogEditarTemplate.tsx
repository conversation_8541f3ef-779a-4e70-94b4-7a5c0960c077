'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  <PERSON><PERSON>Trigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { editarTemplateDocumentoSchema } from '@/lib/validation';
import { editarTemplateDocumento } from '@/lib/database/gerenciamento/templatesDocumentos';
import { EditorTemplate } from './editorTemplate';
import { toast } from 'sonner';
import { Icons } from '@/components/icons';
import { Pencil } from 'lucide-react';
import * as z from 'zod';

interface DialogEditarTemplateProps {
  template: {
    id: number;
    nome: string;
    descricao: string | null;
    conteudoHtml: string;
  };
  children: React.ReactNode;
}

type FormData = z.infer<typeof editarTemplateDocumentoSchema>;

export function DialogEditarTemplate({
  template,
  children,
}: DialogEditarTemplateProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(editarTemplateDocumentoSchema),
    defaultValues: {
      id: template.id,
      nome: template.nome,
      descricao: template.descricao || '',
      conteudoHtml: template.conteudoHtml,
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    try {
      const result = await editarTemplateDocumento(data);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      toast.success('Template editado com sucesso!');
      setIsOpen(false);
      form.reset();
    } catch (error) {
      console.error('Erro ao editar template:', error);
      toast.error('Erro inesperado ao editar template');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset({
        id: template.id,
        nome: template.nome,
        descricao: template.descricao || '',
        conteudoHtml: template.conteudoHtml,
      });
    }
    setIsOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Pencil className='h-5 w-5' />
            Editar Template
          </DialogTitle>
          <DialogDescription>
            Edite as informações do template de documento.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='nome'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Template *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Digite o nome do template...'
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='descricao'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Digite uma descrição para o template...'
                      {...field}
                      disabled={isLoading}
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='conteudoHtml'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conteúdo do Template *</FormLabel>
                  <FormControl>
                    <EditorTemplate
                      value={field.value}
                      onChange={field.onChange}
                      disabled={isLoading}
                      height={400}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => setIsOpen(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading && (
                  <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                )}
                Salvar Alterações
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
