'use client';

import { StatusProtocolo, StatusProtocoloDesc } from '@/lib/enums';
import { cn } from '@/lib/utils';

interface IndicadorStatusProps {
  status: number;
  className?: string;
}

export function IndicadorStatus({ status, className }: IndicadorStatusProps) {
  const getStatusColor = (status: number) => {
    // Status iniciais - cinza
    if (status === StatusProtocolo.Nenhum) {
      return 'bg-gray-100 text-gray-800 border-gray-300';
    }

    // Status em andamento - azul
    if (
      status === StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS ||
      status === StatusProtocolo.RECEBIMENTO_EM_COMPRAS ||
      status === StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE ||
      status === StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE ||
      status === StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE ||
      status === StatusProtocolo.RECEBIMENTO_EM_GABINETE
    ) {
      return 'bg-blue-100 text-blue-800 border-blue-300';
    }

    // Status concluídos com sucesso - verde (usando status existentes)
    if (
      status === StatusProtocolo.AF_ENCAMINHADA ||
      status === StatusProtocolo.ENTRADANOCOMPRAS ||
      status === StatusProtocolo.CONTROLE_INTERNO
    ) {
      return 'bg-green-100 text-green-800 border-green-300';
    }

    // Status de cancelamento/estorno - laranja (usando status existentes)
    if (
      status ===
      StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM_PARA_CORRECAO_PEDIDO_COMPRA
    ) {
      return 'bg-orange-100 text-orange-800 border-orange-300';
    }

    // Status de devolução - amarelo (usando status existentes)
    if (
      status === StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM ||
      status === StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS ||
      status === StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS ||
      status === StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS ||
      status === StatusProtocolo.DEVOLUCAO_DE_SAUDE_PARA_FINANCAS ||
      status === StatusProtocolo.DEVOLUCAO_DE_ESPORTES_PARA_FINANCAS ||
      status === StatusProtocolo.DEVOLUCAO_DE_EDUCACAO_PARA_FINANCAS
    ) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    }

    // Status de erro/rejeição - vermelho (usando status existentes)
    if (status === StatusProtocolo.OUTROS) {
      return 'bg-red-100 text-red-800 border-red-300';
    }

    // Status final - roxo (usando status existentes)
    if (
      status === StatusProtocolo.ENTRADA_NO_DPAO ||
      status === StatusProtocolo.ENVIADOPARADPAO ||
      status === StatusProtocolo.DEPARTAMENTO_INFORMATICA
    ) {
      return 'bg-purple-100 text-purple-800 border-purple-300';
    }

    // Padrão - cinza
    return 'bg-gray-100 text-gray-800 border-gray-300';
  };

  return (
    <span
      className={cn(
        'inline-flex items-center rounded-sm border px-2.5 py-0.5 text-xs font-medium',
        getStatusColor(status),
        className
      )}
    >
      {StatusProtocoloDesc[status as StatusProtocolo]}
    </span>
  );
}
