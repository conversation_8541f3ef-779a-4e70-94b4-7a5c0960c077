'use client';
import { ColumnDef } from '@tanstack/react-table';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { ConfigCargo } from '@/types/app';
import { Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dispatch } from 'react';

export default function ConfigCargosDatatable({
  data,
  setCargosConfigurados,
}: {
  data: ConfigCargo[];
  setCargosConfigurados: Dispatch<React.SetStateAction<ConfigCargo[]>>;
}) {
  if (!data) return null;
  const columns: ColumnDef<(typeof data)[0]>[] = [
    {
      accessorKey: 'nome',
      header: 'Cargo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <Button
          type='button'
          variant='ghost'
          aria-description='Remover Cargo'
          onClick={() => {
            setCargosConfigurados(
              data.filter((mod) => mod.id !== row.getValue('id'))
            );
          }}
        >
          <Trash className='size-4' />
        </Button>
      ),
    },
  ];
  return (
    <div className='overflow-x-scroll'>
      <ReusableDatatable columns={columns} data={data} />
    </div>
  );
}
