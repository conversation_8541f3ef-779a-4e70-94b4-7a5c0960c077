'use server';

import { revalidatePath } from 'next/cache';
import { prisma } from '../../prisma';
import {
  auditoriaErroSchema,
  editarfornecedorSchema,
  fornecedorSchema,
  idSchema,
  permissaoSchema,
} from '../../validation';
import { Modulos } from '@/lib/modulos';
import { AuditoriaGerenciamentoFornecedores, Permissoes } from '@/lib/enums';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { inserirErroAudit } from '../auditoria/erro';

export const listarFornecedores = async () => {
  try {
    const fornecedores = await prisma.fornecedores.findMany();
    return {
      data: fornecedores,
    };
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao obter Fornecedores.`,
    };
  }
};

export const criarFornecedor = async (params: unknown) => {
  // TO DO criar auditoria

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = fornecedorSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { codigo, nome, cnpjCpf, email, phone } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    // const pedacoCpf = cpf && cpf !== '' ? cpf.substring(3, 12) : '' // analisar Criptografar
    // const cpfMascarado = pedacoCpf ? `***${pedacoCpf}**` : '' // analisar Criptografar
    //Prisma ainda não implementou deep nested queries, fazer via transação e loops
    await prisma.$transaction(async (tx) => {
      const result = await tx.fornecedores.create({
        data: {
          codigo: codigo,
          nome: nome.toUpperCase(),
          cnpjCpf: cnpjCpf!,
          email: email && email !== '' ? email : '',
          phone: phone && phone !== '' ? phone : '',
        },
      });

      await tx.gerenciamentoFornecedores_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idFornecedor: result.id,
          acao: AuditoriaGerenciamentoFornecedores.CRIAR_FORNECEDOR,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/fornecedores');
  } catch (e: unknown) {
    console.log(e);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Código do Fornecedor já existe.`,
        };
      }
    }
    return {
      error: `Erro ao criar fornecedor.`,
    };
  }
};

export const deletarFornecedor = async (id: number) => {
  try {
    const fornecedor = await prisma.fornecedores.delete({
      where: {
        id,
      },
    });
    revalidatePath('/gerenciamento/fornecedores');
    return {
      data: fornecedor,
    };
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao deletar fornecedor.`,
    };
  }
};

export const editarFornecedor = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = editarfornecedorSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id, nome, cnpjCpf, email, phone } = parsedParams.data;

  try {
    // const pedacoCpf = cpf && cpf !== '' ? cpf.substring(3, 12) : '' // analisar Criptografar
    // const cpfMascarado = pedacoCpf ? `***${pedacoCpf}**` : '' // analisar Criptografar
    //Prisma ainda não implementou deep nested queries, fazer via transação e loops
    const ip = (await obterIpUsuarioConectado()).data;

    const fornecedorAntigo = await prisma.fornecedores.findUnique({
      where: {
        id: id,
      },
      select: {
        codigo: true,
        nome: true,
        cnpjCpf: true,
        email: true,
        phone: true,
      },
    });

    const dataAudit = await criarAuditAlter(
      fornecedorAntigo,
      parsedParams,
      resultPermissao.idUsuario!,
      id,
      ip
    );

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.fornecedores.update({
        where: {
          id: id,
        },
        data: {
          // codigo: codigo, verificar se poderá ser alterado
          nome: nome.toUpperCase(),
          cnpjCpf: cnpjCpf && cnpjCpf !== '' ? cnpjCpf : '',
          email: email && email !== '' ? email : '', //analisar para não nulo
          phone: phone && phone !== '' ? phone : '', //analisar para não nulo
        },
      });

      const auditPromise = tx.gerenciamentoFornecedores_audit.createMany({
        data: dataAudit,
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/fornecedores');
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao Editar fornecedor.`,
    };
  }
};

const criarAuditAlter = (
  obj: any,
  params: any,
  idUsuario: number,
  idfornecedor: number,
  ip: string
) => {
  const data = params.data;
  let arr: any[] = []; // let arr: [] = []

  Object.keys(obj).map((key) => {
    Object.keys(data).map((key1) => {
      if (key == key1) {
        if (obj[key] != data[key1]) {
          let objAux = {
            idUsuario: idUsuario,
            idFornecedor: idfornecedor,
            acao: AuditoriaGerenciamentoFornecedores.ALTERAR_FORNECEDOR,
            de: obj[key],
            para: data[key1],
            ip,
          };
          arr.push(objAux);
        }
      }
    });
  });

  return arr;
};

export const obterFornecedor = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;
  try {
    const fornecedor = await prisma.fornecedores.findFirst({
      where: {
        id: id,
      },
    });
    return {
      data: fornecedor,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao obter Fornecedor.',
    };
  }
};

export const ativarFornecedor = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.fornecedores.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });
      const auditPromise = tx.gerenciamentoFornecedores_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idFornecedor: id,
          acao: AuditoriaGerenciamentoFornecedores.ATIVAR_FORNECEDOR,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/fornecedores');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar fornecedor.`,
    };
  }
};

export const desativarFornecedor = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.fornecedores.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });
      const auditPromise = tx.gerenciamentoFornecedores_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idFornecedor: id,
          acao: AuditoriaGerenciamentoFornecedores.DESATIVAR_FORNECEDOR,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/fornecedores');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FORNECEDORES,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar fornecedor.`,
    };
  }
};
