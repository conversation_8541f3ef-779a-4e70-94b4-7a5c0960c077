'use client';
import { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useRouter } from 'next/navigation';
import { Icons } from '@/components/icons';
import { createClient } from '@/lib/supabase/client';

export default function CadastroPage() {
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setIsMounted(true);
    router.prefetch('/dashboard');
    router.prefetch('/login');
    async function checkAuth() {
      const supabase = createClient();
      const user = await supabase.auth.getUser();
      if (user.error || !user.data) {
        router.replace('/login');
      } else {
        router.replace('/dashboard');
      }
    }
    checkAuth();
  }, [router]);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Aguarde</AlertDialogTitle>
          </AlertDialogHeader>
          <div className='flex items-center justify-start'>
            Verificando usuário...{' '}
            <Icons.loader className='ml-2 size-4 animate-spin' />
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
