import { Skeleton } from '@/components/ui/skeleton';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';

export default function ProtocoloVisualizarLoading() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Visualizar Protocolo</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='space-y-6'>
          {/* Header Info */}
          <div className='grid grid-cols-1 gap-4 md:grid-cols-4'>
            <div className='md:col-span-3'>
              <Skeleton className='mb-2 h-8 w-48' />
              <Skeleton className='h-4 w-64' />
            </div>
            <div className='flex justify-end'>
              <Skeleton className='h-10 w-32' />
            </div>
          </div>

          {/* Status and Basic Info */}
          <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
            <div className='space-y-4'>
              <Skeleton className='h-6 w-24' />
              <div className='space-y-2'>
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-4 w-3/4' />
                <Skeleton className='h-4 w-5/6' />
              </div>
            </div>
            <div className='space-y-4'>
              <Skeleton className='h-6 w-24' />
              <div className='space-y-2'>
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-4 w-2/3' />
              </div>
            </div>
            <div className='space-y-4'>
              <Skeleton className='h-6 w-24' />
              <div className='space-y-2'>
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-4 w-4/5' />
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className='space-y-4'>
            <div className='flex space-x-4 border-b'>
              <Skeleton className='h-10 w-24' />
              <Skeleton className='h-10 w-32' />
              <Skeleton className='h-10 w-28' />
              <Skeleton className='h-10 w-20' />
            </div>

            {/* Tab Content */}
            <div className='space-y-4'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-20' />
                  <Skeleton className='h-10 w-full' />
                </div>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-24' />
                  <Skeleton className='h-10 w-full' />
                </div>
              </div>

              <div className='space-y-2'>
                <Skeleton className='h-4 w-16' />
                <Skeleton className='h-20 w-full' />
              </div>

              <div className='space-y-2'>
                <Skeleton className='h-4 w-20' />
                <Skeleton className='h-32 w-full' />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className='flex justify-end space-x-2'>
            <Skeleton className='h-10 w-24' />
            <Skeleton className='h-10 w-20' />
            <Skeleton className='h-10 w-16' />
          </div>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
