'use client';
import { ColumnDef } from '@tanstack/react-table';
import { Eye, Pencil, FileText, XCircle, Plus, Receipt } from 'lucide-react';
import { listarEmpenhos } from '@/lib/database/movimento/empenhos';
import { StatusEmpenho, StatusProtocoloDesc } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { ReusableDatatableEmpenhos } from '@/components/datatable/reusableDatatableEmpenhos';
import { useState } from 'react';
import {
  DatatableActionsDropdown,
  DatatableAction,
} from '@/components/datatable/DatatableActionsDropdown';
import { BotaoVincularEmpenhoProtocolo } from '@/components/movimento/botaoVincularEmpenhoProtocolo';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

interface EnhancedEmpenhosDatatableProps {
  data: Awaited<ReturnType<typeof listarEmpenhos>>;
}

export default function EnhancedEmpenhosDatatable({
  data,
}: EnhancedEmpenhosDatatableProps) {
  const [isVincularModalOpen, setIsVincularModalOpen] = useState<{
    empenhoId: number;
    exercicio: number;
  } | null>(null);

  if (!data.data?.empenhos) return null;

  const handleVincularProtocolo = (empenhoId: number, exercicio: number) => {
    setIsVincularModalOpen({ empenhoId, exercicio });
  };

  const handleCloseVincularModal = () => {
    setIsVincularModalOpen(null);
  };

  const columns: ColumnDef<(typeof data.data.empenhos)[0]>[] = [
    {
      accessorKey: 'id',
      header: 'Empenho',
      cell: ({ row }) => <div className='font-medium'>{row.original.id}</div>,
    },
    {
      accessorKey: 'dotacao.despesa',
      id: 'despesa',
      header: 'Despesa',
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='text-xs font-medium'>
            {row.original.dotacao?.despesa}
          </span>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className='text-muted-foreground max-w-[150px] cursor-help truncate text-xs'>
                {row.original.dotacao?.desc}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className='max-w-xs'>{row.original.dotacao?.desc}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      ),
    },
    {
      accessorKey: 'fornecedor.nome',
      header: 'Fornecedor',
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='text-sm font-medium'>
            {row.original.fornecedor?.nome}
          </span>
          {row.original.fornecedor?.cnpjCpf && (
            <span className='text-muted-foreground text-xs'>
              {row.original.fornecedor.cnpjCpf}
            </span>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'resumo',
      header: 'Resumo',
      cell: ({ row }) => (
        <div className='max-w-xs'>
          <span className='text-sm'>{row.original.resumo}</span>
        </div>
      ),
    },
    {
      accessorKey: 'obs',
      header: 'Obs',
      cell: ({ row }) => (
        <div className='max-w-xs'>
          <span className='text-muted-foreground text-sm'>
            {row.original.obs || '-'}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const empenhoStatus = row.original.status;
        const protocolo = row.original.protocolo;

        const getStatusBadge = (status: StatusEmpenho) => {
          const variants: Record<
            StatusEmpenho,
            'default' | 'secondary' | 'destructive' | 'outline'
          > = {
            [StatusEmpenho.EMPENHADO]: 'default',
            [StatusEmpenho.LIQUIDADO]: 'secondary',
            [StatusEmpenho.ANULADO]: 'destructive',
            [StatusEmpenho.ANULADO_PARCIAL]: 'destructive',
            [StatusEmpenho.CANCELADO]: 'outline',
          };

          return (
            <Badge variant={variants[status]} className='text-xs'>
              {StatusEmpenho[status]}
            </Badge>
          );
        };

        if (protocolo) {
          return (
            <div className='space-y-1'>
              <div>{getStatusBadge(empenhoStatus)}</div>
              <div className='text-xs text-blue-600'>
                Protocolo #{protocolo.numero}:{' '}
                {
                  StatusProtocoloDesc[
                    protocolo.status as keyof typeof StatusProtocoloDesc
                  ]
                }
              </div>
            </div>
          );
        }

        return getStatusBadge(empenhoStatus);
      },
    },
    {
      accessorKey: 'valorTotal',
      header: 'Valor Total',
      cell: ({ getValue }) => {
        const value = getValue<number>();
        return (
          <div className='text-right font-medium'>
            {currency(value, currencyOptionsNoSymbol)
              .format()
              .replace(/0$/, '')}
          </div>
        );
      },
    },
    {
      accessorKey: 'saldo',
      header: 'Saldo',
      cell: ({ row }) => {
        const saldo = row.original.saldo;
        const saldoClass =
          saldo > 0
            ? 'text-green-600'
            : saldo < 0
              ? 'text-red-600'
              : 'text-gray-600';
        return (
          <div className={`text-right font-medium ${saldoClass}`}>
            {currency(saldo, currencyOptionsNoSymbol)
              .format()
              .replace(/0$/, '')}
          </div>
        );
      },
    },
    {
      accessorKey: 'data',
      header: 'Data',
      cell: ({ getValue }) => {
        const date = getValue<Date>();
        return (
          <div className='text-sm'>{date.toLocaleDateString('pt-BR')}</div>
        );
      },
    },
    {
      accessorKey: 'secretariaId',
      header: 'Secretaria',
      cell: ({ row }) => {
        const secretariaNome = data.data.secretarias?.find(
          (s) => s.id === row.original.secretariaId
        )?.nome;
        return <div className='text-sm'>{secretariaNome || '-'}</div>;
      },
    },
    {
      accessorKey: 'departamentoId',
      header: 'Departamento',
      cell: ({ row }) => {
        const departamentoNome = data.data.departamentos?.find(
          (d) => d.id === row.original.departamentoId
        )?.nome;
        return <div className='text-sm'>{departamentoNome || '-'}</div>;
      },
    },
    {
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => {
        const actions: DatatableAction[] = [
          {
            label: 'Visualizar',
            icon: <Eye className='size-4' />,
            href: `/movimento/empenhos/visualizar/${row.original.id}`,
            show: true,
          },
          {
            label: 'Editar',
            icon: <Pencil className='size-4' />,
            href: `/movimento/empenhos/editar/${row.original.id}`,
            show:
              row.original.status === StatusEmpenho.EMPENHADO ||
              row.original.status === StatusEmpenho.ANULADO_PARCIAL,
          },
          {
            label: 'Criar AF',
            icon: <Plus className='size-4' />,
            href: `/movimento/af/novo?empenho=${row.original.id}`,
            show: row.original.status === StatusEmpenho.EMPENHADO,
          },
          {
            label: 'Liquidação',
            icon: <Receipt className='size-4' />,
            href: `/movimento/liquidacoes/novo?empenho=${row.original.id}`,
            show:
              row.original.status === StatusEmpenho.EMPENHADO ||
              row.original.status === StatusEmpenho.LIQUIDADO,
          },
          {
            label: 'Vincular a Protocolo',
            icon: <FileText className='size-4' />,
            onClick: () =>
              handleVincularProtocolo(row.original.id, row.original.exercicio),
            show:
              row.original.status === StatusEmpenho.EMPENHADO &&
              !row.original.protocolo,
          },
          {
            label: 'Ver Protocolo',
            icon: <Eye className='size-4' />,
            href: `/movimento/protocolo/visualizar/${row.original.protocolo?.id}`,
            show: !!row.original.protocolo,
          },
          {
            label: 'Anular',
            icon: <XCircle className='size-4' />,
            href: `/movimento/empenhos/anular/${row.original.id}`,
            variant: 'destructive',
            show:
              row.original.status === StatusEmpenho.EMPENHADO ||
              row.original.status === StatusEmpenho.ANULADO_PARCIAL,
          },
        ];

        return (
          <div className='flex items-center gap-2'>
            <DatatableActionsDropdown actions={actions} />
          </div>
        );
      },
    },
  ];

  // Preparar dados para os filtros
  const secretarias = (data.data.secretarias || []).map((s) => ({
    id: s.id,
    nome: s.nome,
    ativo: true, // Assuming active since they're returned from the query
    codigo: s.id, // Using ID as code since it's not provided
  }));
  const departamentos = (data.data.departamentos || []).map((d) => ({
    id: d.id,
    nome: d.nome,
    secretariaId: d.secretariaId,
    ativo: true, // Assuming active since they're returned from the query
    codigo: d.id, // Using ID as code since it's not provided
    secretaria: {
      id: d.secretariaId,
      nome:
        data.data.secretarias?.find((s) => s.id === d.secretariaId)?.nome ||
        `Secretaria ${d.secretariaId}`,
      ativo: true,
      codigo: d.secretariaId,
    },
  }));
  const fornecedores = data.data.fornecedores || [];

  // Extrair exercícios disponíveis
  const exerciciosArray = data.data.empenhos.map((e) => e.exercicio);
  const exerciciosDisponiveis = Array.from(new Set(exerciciosArray)).sort(
    (a, b) => b - a
  );

  const handleFiltersChange = (filters: any) => {
    // Aqui você pode implementar lógica adicional quando os filtros mudam
    console.log('Filtros aplicados:', filters);
  };

  return (
    <TooltipProvider>
      <div className='container mx-auto'>
        <ReusableDatatableEmpenhos
          columns={columns}
          data={data.data.empenhos}
          secretarias={secretarias}
          departamentos={departamentos}
          fornecedores={fornecedores}
          exerciciosDisponiveis={exerciciosDisponiveis}
          onFiltersChange={handleFiltersChange}
        />

        {/* Modal for vincular empenho to protocolo */}
        {isVincularModalOpen && (
          <BotaoVincularEmpenhoProtocolo
            empenhoId={isVincularModalOpen.empenhoId}
            exercicio={isVincularModalOpen.exercicio}
            defaultOpen
            onClose={handleCloseVincularModal}
          />
        )}
      </div>
    </TooltipProvider>
  );
}
