'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Download, X } from 'lucide-react';
import { StatusDocumentoExterno } from '@/lib/enums';
import { formatDataHora } from '@/lib/utils';
import { listarDocumentosExternosReserva } from '@/lib/database/assinatura/documentosExternos';

interface StatusAssinaturasExternasProps {
  idReserva: number;
}

export function StatusAssinaturasExternas({
  idReserva,
}: StatusAssinaturasExternasProps) {
  const [documentos, setDocumentos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const carregarDocumentos = async () => {
      try {
        const resultado = await listarDocumentosExternosReserva({ idReserva });
        if (resultado.data) {
          setDocumentos(resultado.data.documentos);
        }
      } catch (error) {
        console.error('Erro ao carregar documentos externos:', error);
      } finally {
        setLoading(false);
      }
    };

    carregarDocumentos();
  }, [idReserva]);

  const getStatusBadge = (status: number) => {
    switch (status) {
      case StatusDocumentoExterno['Aguardando Assinatura']:
        return <Badge variant='secondary'>Aguardando Assinatura</Badge>;
      case StatusDocumentoExterno['Assinado']:
        return <Badge variant='default'>Assinado</Badge>;
      case StatusDocumentoExterno['Rejeitado']:
        return <Badge variant='destructive'>Rejeitado</Badge>;
      case StatusDocumentoExterno['Cancelado']:
        return <Badge variant='outline'>Cancelado</Badge>;
      case StatusDocumentoExterno['Processando']:
        return <Badge variant='secondary'>Processando</Badge>;
      default:
        return <Badge variant='outline'>Desconhecido</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Assinaturas Externas</CardTitle>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>Carregando...</p>
        </CardContent>
      </Card>
    );
  }

  if (documentos.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Assinaturas Externas</CardTitle>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>
            Nenhuma assinatura externa solicitada.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Assinaturas Externas</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          {documentos.map((documento) => (
            <div
              key={documento.id}
              className='flex items-center justify-between rounded-lg border p-4'
            >
              <div className='flex-1'>
                <div className='mb-2 flex items-center gap-2'>
                  <h4 className='font-medium'>{documento.nomeDocumento}</h4>
                  {getStatusBadge(documento.status)}
                </div>
                <p className='text-muted-foreground mb-1 text-sm'>
                  Usuário: {documento.usuarioExterno.nome}
                </p>
                <p className='text-muted-foreground text-sm'>
                  Solicitado em: {formatDataHora(documento.dataCriacao)}
                </p>
                {documento.dataAssinatura && (
                  <p className='text-muted-foreground text-sm'>
                    Assinado em: {formatDataHora(documento.dataAssinatura)}
                  </p>
                )}
                {documento.motivoCancelamento && (
                  <p className='mt-1 text-sm text-red-600'>
                    Motivo: {documento.motivoCancelamento}
                  </p>
                )}
              </div>

              <div className='flex gap-2'>
                <Button variant='outline' size='sm'>
                  <Eye className='h-4 w-4' />
                </Button>

                {documento.pathDocumentoAssinado && (
                  <Button variant='outline' size='sm'>
                    <Download className='h-4 w-4' />
                  </Button>
                )}

                {documento.status ===
                  StatusDocumentoExterno['Aguardando Assinatura'] && (
                  <Button variant='outline' size='sm'>
                    <X className='h-4 w-4' />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
