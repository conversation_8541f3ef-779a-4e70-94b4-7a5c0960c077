'use client';
import { ColumnDef } from '@tanstack/react-table';
import { Pencil, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dispatch } from 'react';
import { Item } from '@/types/app';
import { ReusableDatatableFiltroDesc } from '@/components/datatable/reusableDatatableFiltroDesc';
import { Unidades, UnidadesDesc } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol, moneyMask } from '@/lib/utils';
import { UseFormReturn } from 'react-hook-form';
import { itemFormSchema } from '@/lib/validation';
import { z } from 'zod';

export default function CadastroItensDatatable({
  data,
  setData,
  form,
  setValorUnitarioBRL,
  decimais,
}: {
  data: Item[];
  setData: Dispatch<React.SetStateAction<Item[]>>;
  form: UseFormReturn<z.infer<typeof itemFormSchema>>;
  setValorUnitarioBRL: React.Dispatch<React.SetStateAction<string>>;
  decimais: number;
}) {
  if (!data) return null;
  const columns: ColumnDef<(typeof data)[0]>[] = [
    {
      accessorKey: 'quantidade',
      header: 'Quantidade',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'unidade',
      header: 'Unidade',
      filterFn: 'includesString',
      cell: ({ row }) => {
        const unidade = row.getValue('unidade') as number;
        return `${UnidadesDesc[unidade]} - ${Unidades[unidade]}`;
      },
    },
    {
      accessorKey: 'desc',
      header: 'Descrição',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'valor',
      header: 'Valor Unitário',
      filterFn: 'includesString',
      cell: ({ row }) => {
        const unidade = Number(row.getValue('valor'));
        return currency(unidade, currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'valorTotal',
      header: 'Valor Total',
      filterFn: 'includesString',
      cell: ({ row }) => {
        const unidade = Number(row.getValue('valor'));
        return currency(unidade, currencyOptionsNoSymbol)
          .multiply(row.getValue('quantidade'))
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div>
          <Button
            type='button'
            variant='ghost'
            aria-description='Editar Item'
            onClick={() => {
              const index = row.index;
              form.setValue('quantidade', row.getValue('quantidade'), {
                shouldDirty: true,
              });
              form.setValue('unidade', row.getValue('unidade'), {
                shouldDirty: true,
              });
              form.setValue('desc', row.getValue('desc'), {
                shouldDirty: true,
              });
              form.setValue('valor', row.getValue('valor'), {
                shouldDirty: true,
              });
              console.log(row.getValue('valor'));
              setValorUnitarioBRL(
                moneyMask(`${row.getValue('valor')}`, decimais)
              );
              setData(data.filter((mod, i) => i !== index));
            }}
            aria-label='Editar Item'
          >
            <Pencil className='size-4' />
          </Button>
          <Button
            type='button'
            variant='ghost'
            aria-description='Remover Item'
            onClick={() => {
              const index = row.index;
              setData(data.filter((mod, i) => i !== index));
            }}
            aria-label='Remover Item'
          >
            <Trash className='size-4' />
          </Button>
        </div>
      ),
    },
  ];
  return (
    <div className='overflow-x-scroll'>
      <ReusableDatatableFiltroDesc columns={columns} data={data} />
    </div>
  );
}
