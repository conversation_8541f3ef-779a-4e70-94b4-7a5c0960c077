'use server';
import { EmailOtpType } from '@supabase/supabase-js';
import { createClient } from './server';

export async function confirmOtp(
  token_hash: string | null,
  type: EmailOtpType | null
) {
  if (token_hash && type) {
    const supabase = await createClient();

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    });
    console.log(error);
    if (error) {
      return {
        error: 'Token inválido',
      };
    } else {
      return { data: 'ok' };
    }
  }

  return {
    error: 'Token inválido',
  };
}
