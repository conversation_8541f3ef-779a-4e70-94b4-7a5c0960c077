import AFView from '@/components/af/afView';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';

export default async function VisualizarAFPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Visualizar AF</PageTitle>
      </PageHeader>
      <PageContent>
        <AFView id={parseInt((await params).id)} />
      </PageContent>
    </PageWrapper>
  );
}
