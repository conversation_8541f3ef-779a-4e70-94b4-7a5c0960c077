'use client';
import { ColumnDef } from '@tanstack/react-table';
import { UserCog } from 'lucide-react';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { listarUsuariosExternos } from '@/lib/database/gerenciamento/usuariosExternos';
import { AlertDesativarUsuarioExterno } from './AlertDesativarUsuarioExterno';
import { AlertAtivarUsuarioExterno } from './AlertAtivarUsuarioExterno';

export default function ExternalUsersDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarUsuariosExternos>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'email',
      header: 'Email',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link
            href={`/gerenciamento/usuarios-externos/editar/${row.original.id}`}
          >
            <Button type='button' variant='outline'>
              <UserCog className='mr-2 size-4' /> Editar
            </Button>
          </Link>
          {row.original.ativo ? (
            <AlertDesativarUsuarioExterno
              idUsuario={row.original.id}
              nomeUsuario={row.original.nome}
            />
          ) : (
            <AlertAtivarUsuarioExterno
              idUsuario={row.original.id}
              nomeUsuario={row.original.nome}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatable columns={columns} data={data.data} />
    </div>
  );
}
