'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { estornarAnulacaoSchema } from '@/lib/validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, RotateCcw, AlertTriangle } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { estornarAnulacao } from '@/lib/database/movimento/empenhos';
import { DialogDescription } from '@radix-ui/react-dialog';
import { useRouter } from 'next/navigation';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';

interface UndoAnulacaoDialogProps {
  idEmpenho: number;
  valorAnulado: number;
  status: number;
  children?: React.ReactNode;
}

export function UndoAnulacaoDialog({
  idEmpenho,
  valorAnulado,
  status: _status,
  children,
}: UndoAnulacaoDialogProps) {
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<z.infer<typeof estornarAnulacaoSchema>>({
    resolver: zodResolver(estornarAnulacaoSchema),
    defaultValues: {
      id: idEmpenho,
      motivo: '',
    },
  });

  const router = useRouter();

  const onSubmit = async (values: z.infer<typeof estornarAnulacaoSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof estornarAnulacaoSchema> = {
        id: idEmpenho,
        motivo: values.motivo,
      };

      const res = await estornarAnulacao(data);

      if (res?.error) {
        setLoading(false);
        toast.error(res.error);
      } else {
        toast.success('Anulação estornada com sucesso.');
        setIsDialogOpen(false);
        router.refresh();
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const valorFormatado = currency(
    valorAnulado,
    currencyOptionsNoSymbol
  ).format();

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button type='button' variant='outline' size='sm'>
            <RotateCcw className='mr-2 size-4' />
            Estornar Anulação
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <RotateCcw className='size-5' />
            Estornar Anulação de Empenho
          </DialogTitle>
          <DialogDescription>
            Esta ação irá restaurar o valor anulado e reativar o empenho.
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* Alert with impact information */}
          <Alert className='border-amber-200 bg-amber-50'>
            <AlertTriangle className='h-4 w-4 text-amber-800' />
            <AlertDescription className='text-amber-800'>
              <div className='space-y-2'>
                <p className='font-medium'>Impacto da operação:</p>
                <div className='flex items-center gap-2'>
                  <Badge
                    variant='outline'
                    className='border-green-700 text-green-700'
                  >
                    +{valorFormatado}
                  </Badge>
                  <span className='text-sm'>
                    serão restaurados para o empenho
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Badge
                    variant='outline'
                    className='border-blue-700 text-blue-700'
                  >
                    Status: EMPENHADO
                  </Badge>
                  <span className='text-sm'>o empenho será reativado</span>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              {/* Hidden field for empenho ID */}
              <input type='hidden' {...form.register('id')} />

              {/* Motivo field */}
              <FormField
                control={form.control}
                name='motivo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Motivo do Estorno</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Descreva o motivo para estornar esta anulação...'
                        className='resize-none'
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <DialogFooter className='flex gap-2'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setIsDialogOpen(false)}
                  disabled={loading}
                >
                  Cancelar
                </Button>
                <Button
                  type='submit'
                  disabled={loading}
                  className='bg-green-600 hover:bg-green-700'
                >
                  {loading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
                  <RotateCcw className='mr-2 h-4 w-4' />
                  Confirmar Estorno
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
