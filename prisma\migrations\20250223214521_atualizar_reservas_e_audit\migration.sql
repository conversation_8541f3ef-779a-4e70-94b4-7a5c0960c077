/*
  Warnings:

  - You are about to drop the `reserva` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idDepto_fkey";

-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idDotacao_fkey";

-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idEconomica_fkey";

-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idFuncional_fkey";

-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idGestor_fkey";

-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idSecretaria_fkey";

-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idSecretario_fkey";

-- DropForeignKey
ALTER TABLE "reserva" DROP CONSTRAINT "reserva_idSubDepto_fkey";

-- DropTable
DROP TABLE "reserva";

-- CreateTable
CREATE TABLE "reservas" (
    "id" SERIAL NOT NULL,
    "idGestor" INTEGER NOT NULL,
    "idDotacao" INTEGER NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "idSecretario" INTEGER NOT NULL,
    "idEconomicaItem" INTEGER NOT NULL,
    "resumo" TEXT NOT NULL,
    "obs" TEXT NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "usarMes1" DECIMAL(18,2) NOT NULL,
    "usarMes2" DECIMAL(18,2) NOT NULL,
    "usarMes3" DECIMAL(18,2) NOT NULL,
    "usarMes4" DECIMAL(18,2) NOT NULL,
    "usarMes5" DECIMAL(18,2) NOT NULL,
    "usarMes6" DECIMAL(18,2) NOT NULL,
    "usarMes7" DECIMAL(18,2) NOT NULL,
    "usarMes8" DECIMAL(18,2) NOT NULL,
    "usarMes9" DECIMAL(18,2) NOT NULL,
    "usarMes10" DECIMAL(18,2) NOT NULL,
    "usarMes11" DECIMAL(18,2) NOT NULL,
    "usarMes12" DECIMAL(18,2) NOT NULL,
    "usarTotal" DECIMAL(18,2) NOT NULL,
    "pedidoPlurianual" BOOLEAN NOT NULL DEFAULT false,
    "pluUsarMes1" DECIMAL(18,2) NOT NULL,
    "pluUsarMes2" DECIMAL(18,2) NOT NULL,
    "pluUsarMes3" DECIMAL(18,2) NOT NULL,
    "pluUsarMes4" DECIMAL(18,2) NOT NULL,
    "pluUsarMes5" DECIMAL(18,2) NOT NULL,
    "pluUsarMes6" DECIMAL(18,2) NOT NULL,
    "pluUsarMes7" DECIMAL(18,2) NOT NULL,
    "pluUsarMes8" DECIMAL(18,2) NOT NULL,
    "pluUsarMes9" DECIMAL(18,2) NOT NULL,
    "pluUsarMes10" DECIMAL(18,2) NOT NULL,
    "pluUsarMes11" DECIMAL(18,2) NOT NULL,
    "pluUsarMes12" DECIMAL(18,2) NOT NULL,
    "pluUsarTotal" DECIMAL(18,2) NOT NULL,
    "cancelado" BOOLEAN NOT NULL DEFAULT false,
    "motivoCancelamento" TEXT,
    "dataCancelamento" TIMESTAMP(3),

    CONSTRAINT "reservas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reservas_audit" (
    "id" SERIAL NOT NULL,
    "idReserva" INTEGER NOT NULL,
    "idSecretario" INTEGER NOT NULL,
    "idEconomicaItem" INTEGER NOT NULL,
    "resumo" TEXT NOT NULL,
    "obs" TEXT NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "usarMes1" DECIMAL(18,2) NOT NULL,
    "usarMes2" DECIMAL(18,2) NOT NULL,
    "usarMes3" DECIMAL(18,2) NOT NULL,
    "usarMes4" DECIMAL(18,2) NOT NULL,
    "usarMes5" DECIMAL(18,2) NOT NULL,
    "usarMes6" DECIMAL(18,2) NOT NULL,
    "usarMes7" DECIMAL(18,2) NOT NULL,
    "usarMes8" DECIMAL(18,2) NOT NULL,
    "usarMes9" DECIMAL(18,2) NOT NULL,
    "usarMes10" DECIMAL(18,2) NOT NULL,
    "usarMes11" DECIMAL(18,2) NOT NULL,
    "usarMes12" DECIMAL(18,2) NOT NULL,
    "usarTotal" DECIMAL(18,2) NOT NULL,
    "pedidoPlurianual" BOOLEAN NOT NULL DEFAULT false,
    "pluUsarMes1" DECIMAL(18,2) NOT NULL,
    "pluUsarMes2" DECIMAL(18,2) NOT NULL,
    "pluUsarMes3" DECIMAL(18,2) NOT NULL,
    "pluUsarMes4" DECIMAL(18,2) NOT NULL,
    "pluUsarMes5" DECIMAL(18,2) NOT NULL,
    "pluUsarMes6" DECIMAL(18,2) NOT NULL,
    "pluUsarMes7" DECIMAL(18,2) NOT NULL,
    "pluUsarMes8" DECIMAL(18,2) NOT NULL,
    "pluUsarMes9" DECIMAL(18,2) NOT NULL,
    "pluUsarMes10" DECIMAL(18,2) NOT NULL,
    "pluUsarMes11" DECIMAL(18,2) NOT NULL,
    "pluUsarMes12" DECIMAL(18,2) NOT NULL,
    "pluUsarTotal" DECIMAL(18,2) NOT NULL,

    CONSTRAINT "reservas_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "reservas" ADD CONSTRAINT "reservas_idGestor_fkey" FOREIGN KEY ("idGestor") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas" ADD CONSTRAINT "reservas_idDotacao_fkey" FOREIGN KEY ("idDotacao") REFERENCES "dotacoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas" ADD CONSTRAINT "reservas_idSecretario_fkey" FOREIGN KEY ("idSecretario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas" ADD CONSTRAINT "reservas_idEconomicaItem_fkey" FOREIGN KEY ("idEconomicaItem") REFERENCES "economicas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas_audit" ADD CONSTRAINT "reservas_audit_idReserva_fkey" FOREIGN KEY ("idReserva") REFERENCES "reservas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas_audit" ADD CONSTRAINT "reservas_audit_idSecretario_fkey" FOREIGN KEY ("idSecretario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reservas_audit" ADD CONSTRAINT "reservas_audit_idEconomicaItem_fkey" FOREIGN KEY ("idEconomicaItem") REFERENCES "economicas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
