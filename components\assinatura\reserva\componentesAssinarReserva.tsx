'use client';

import { createContext, useContext, useState } from 'react';
import { Loader2, Signature } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { toast } from 'sonner';
import { validarCertificadoUsuarioConectado } from '@/lib/supabase/serverUtils';
import { assinarReserva } from '@/lib/database/assinatura/reservas';
import { createClient } from '@/lib/supabase/client';

interface AssinaturaContextType {
  isDialogOpen: boolean;
  setIsDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
  assinaturas: Assinatura[];
  setAssinaturas: React.Dispatch<React.SetStateAction<Assinatura[]>>;
  password: string;
  setPassword: React.Dispatch<React.SetStateAction<string>>;
  loading: boolean;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface Assinatura {
  idReserva: number;
  idAssinatura: number;
}

const AssinaturaContext = createContext<AssinaturaContextType | null>(null);

export const AssinaturaProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [assinaturas, setAssinaturas] = useState<Assinatura[]>([]);
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const value = {
    isDialogOpen,
    setIsDialogOpen,
    assinaturas,
    setAssinaturas,
    password,
    setPassword,
    loading,
    setLoading,
  };

  return (
    <AssinaturaContext.Provider value={value}>
      {children}
    </AssinaturaContext.Provider>
  );
};

export const useAssinatura = () => {
  const context = useContext(AssinaturaContext);
  if (context === null) {
    throw new Error('useAssinatura precisa estar dentro de AssinaturaProvider');
  }
  return context;
};

export const DialogAssinarReserva = () => {
  const {
    loading,
    setLoading,
    password,
    setPassword,
    isDialogOpen,
    setIsDialogOpen,
    assinaturas,
    setAssinaturas,
  } = useAssinatura();

  const [etapa, setEtapa] = useState('');

  const handleDialogSubmit = async () => {
    if (loading) return null;
    setLoading(true);
    try {
      setEtapa('Validando certificado...');
      const validacao = await validarCertificadoUsuarioConectado({
        senha: password,
      });
      if (validacao.error) {
        toast.error(validacao.error);
        return;
      }
      const supabase = createClient();
      for (const assinatura of assinaturas) {
        setEtapa(`Assinando reserva ${assinatura.idReserva}...`);
        await new Promise<void>(async (resolve) => {
          const realtimeChannel = supabase.channel(`${assinatura.idReserva}`);

          const userStatus = {
            online_at: new Date().toISOString(),
          };

          realtimeChannel.subscribe(async (status) => {
            if (status !== 'SUBSCRIBED') {
              return;
            }
            try {
              await realtimeChannel.track(userStatus);
              const state = realtimeChannel.presenceState();
              //TODO: Suficiente?
              if (Object.keys(state).length >= 1) {
                toast.error(
                  `Outro usuário está assinando a reserva ${assinatura.idReserva}. Aguarde alguns instantes.`
                );
                realtimeChannel.unsubscribe();
                return resolve();
              } else {
                const result = await assinarReserva({
                  idReserva: assinatura.idReserva,
                  idAssinatura: assinatura.idAssinatura,
                  senha: password,
                });

                if (result && result.error) {
                  toast.error(
                    `${result.error} - Reserva ${assinatura.idReserva}`
                  );
                } else {
                  toast.success(
                    `Reserva ${assinatura.idReserva} assinada com sucesso`
                  );
                }
                realtimeChannel.unsubscribe();
                return resolve();
              }
            } catch (e) {
              toast.error(`Erro ao assinar Reserva ${assinatura.idReserva}`);
              realtimeChannel.unsubscribe();
              return resolve();
            }
          });
        });

        setAssinaturas(
          assinaturas.filter((a) => a.idAssinatura !== assinatura.idAssinatura)
        );
      }
    } catch (e) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    } finally {
      setIsDialogOpen(false);
      setPassword('');
      setEtapa('');
      setLoading(false);
    }
  };

  return (
    <Dialog open={isDialogOpen || loading} onOpenChange={setIsDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Senha do certificado</DialogTitle>
          <DialogDescription>Insira a senha do certificado.</DialogDescription>
        </DialogHeader>
        <Input
          type='password'
          placeholder='Senha do certificado'
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleDialogSubmit()}
          autoFocus
          disabled={loading}
        />
        <DialogFooter>
          <Button
            variant='outline'
            onClick={() => {
              setIsDialogOpen(false);
              setPassword('');
            }}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button onClick={handleDialogSubmit} disabled={loading || !password}>
            {loading ? (
              <>
                <Loader2 className='size-4 animate-spin' />
                {etapa}
              </>
            ) : (
              `Assinar`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const BotaoAssinarReserva = ({
  idReserva,
  idAssinatura,
}: {
  idReserva: number;
  idAssinatura: number;
}) => {
  const { loading, setIsDialogOpen, setAssinaturas, isDialogOpen } =
    useAssinatura();
  const onClick = async () => {
    if (loading) return null;
    setAssinaturas([{ idReserva, idAssinatura }]);
    setIsDialogOpen(true);
  };

  return (
    <Button
      onClick={onClick}
      disabled={loading || isDialogOpen}
      className='w-[110px]'
    >
      {loading ? (
        <Loader2 className='size-4 animate-spin' />
      ) : (
        <Signature className='size-4' />
      )}
      Assinar
    </Button>
  );
};

export const BotaoAssinarReservasSelecionadas = ({
  assinaturas,
}: {
  assinaturas: Assinatura[];
}) => {
  const { loading, setIsDialogOpen, setAssinaturas, isDialogOpen } =
    useAssinatura();
  const onClick = async () => {
    if (loading) return null;
    setAssinaturas(assinaturas);
    setIsDialogOpen(true);
  };

  return (
    <Button
      onClick={onClick}
      disabled={loading || isDialogOpen || assinaturas.length === 0}
    >
      {loading ? (
        <Loader2 className='size-4 animate-spin' />
      ) : (
        <Signature className='size-4' />
      )}
      Assinar Selecionados
    </Button>
  );
};
