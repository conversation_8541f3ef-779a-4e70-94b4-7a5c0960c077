'use server';

import { prisma } from '@/lib/prisma';
import { <PERSON><PERSON><PERSON><PERSON>, StatusLiquidacao } from '@/lib/enums';
import { permissaoSchema } from '@/lib/validation';
import { z } from 'zod';

const relatorioLiquidacoesSchema = z
  .object({
    exercicio: z.coerce.number().int().min(2020).optional(),
    idSecretaria: z.coerce.number().int().min(1).optional(),
    idDepartamento: z.coerce.number().int().min(1).optional(),
    idSubdepartamento: z.coerce.number().int().min(1).optional(),
    status: z.array(z.coerce.number().int().min(1)).optional(),
    idFornecedor: z.coerce.number().int().min(1).optional(),
    dataInicio: z.coerce.date().optional(),
    dataFim: z.coerce.date().optional(),
    incluirProcessados: z.boolean().optional(),
    incluirNaoProcessados: z.boolean().optional(),
    bearer: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.dataInicio && data.dataFim) {
        return data.dataFim >= data.dataInicio;
      }
      return true;
    },
    {
      message: 'Data fim deve ser maior ou igual à data início',
      path: ['dataFim'],
    }
  );
import { Modulos } from '@/lib/modulos';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { Prisma } from '@prisma/client';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';

export interface LiquidacaoReportData {
  id: number;
  numero: number;
  exercicio: number;
  valorTotal: number;
  data: Date;
  dataMaterialServico: Date | null;
  mesReferencia: string | null;
  resumo: string | null;
  obs: string | null;
  marcaDocumento: string | null;
  empenhoOrigem: string;
  status: number;
  fornecedor: FornecedorData | null;
  empenho: EmpenhoData;
  secretaria: SecretariaData | null;
  departamento: DepartamentoData | null;
  subdepartamento: SubdepartamentoData | null;
  valoresMensais: MonthlyBreakdown[];
}

export interface FornecedorData {
  id: number;
  nome: string;
  tipo: 'FISICA' | 'JURIDICA';
  documento: string;
}

export interface EmpenhoData {
  id: number;
  numero: number;
  exercicio: number;
  resumo: string | null;
  status: number;
}

export interface SecretariaData {
  id: number;
  nome: string;
}

export interface DepartamentoData {
  id: number;
  nome: string;
}

export interface SubdepartamentoData {
  id: number;
  nome: string;
}

export interface MonthlyBreakdown {
  mes: string;
  valorLiquidado: number;
  valorProcessado: number;
  valorNaoProcessado: number;
}

export interface LiquidacaoReportSummary {
  totalLiquidacoes: number;
  valorTotal: number;
  valorTotalProcessado: number;
  valorTotalNaoProcessado: number;
  mediaValor: number;
}

export interface RelatorioLiquidacoesParams {
  exercicio?: number;
  idSecretaria?: number;
  idDepartamento?: number;
  idSubdepartamento?: number;
  status?: number[];
  idFornecedor?: number;
  dataInicio?: Date;
  dataFim?: Date;
  incluirProcessados?: boolean;
  incluirNaoProcessados?: boolean;
  bearer?: string;
}

export const gerarRelatorioLiquidacoes = async (
  params: RelatorioLiquidacoesParams
) => {
  const validatedParams = relatorioLiquidacoesSchema.safeParse(params);
  if (!validatedParams.success) {
    return { error: 'Parâmetros inválidos para o relatório de liquidações.' };
  }

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_LIQUIDACAO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao({
    ...parametrosPermissao,
    bearer: validatedParams.data.bearer || '',
  });
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const where: Prisma.liquidacoesWhereInput = { ativo: true };

    if (validatedParams.data.exercicio)
      where.exercicio = validatedParams.data.exercicio;
    else if (resultPermissao.exercicio)
      where.exercicio = resultPermissao.exercicio;

    if (validatedParams.data.status && validatedParams.data.status.length > 0) {
      where.status = { in: validatedParams.data.status };
    }

    if (validatedParams.data.idFornecedor) {
      where.empenho = {
        idFornecedor: validatedParams.data.idFornecedor,
      };
    }

    if (validatedParams.data.dataInicio || validatedParams.data.dataFim) {
      where.data = {};
      if (validatedParams.data.dataInicio)
        where.data.gte = validatedParams.data.dataInicio;
      if (validatedParams.data.dataFim)
        where.data.lte = validatedParams.data.dataFim;
    }

    const liquidacoes = await prisma.liquidacoes.findMany({
      where,
      include: {
        empenho: {
          include: {
            fornecedor: {
              select: {
                id: true,
                nome: true,
                cnpjCpf: true,
              },
            },
            dotacao: {
              include: {
                secretaria: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
                departamento: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
                subdepartamento: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
    });

    // Filter by department hierarchy if specified
    let filteredLiquidacoes = liquidacoes;
    if (
      validatedParams.data.idSecretaria ||
      validatedParams.data.idDepartamento ||
      validatedParams.data.idSubdepartamento
    ) {
      filteredLiquidacoes = liquidacoes.filter((liq) => {
        if (!liq.empenho?.dotacao) return false;

        if (
          validatedParams.data.idSecretaria &&
          liq.empenho.dotacao.secretariaId !== validatedParams.data.idSecretaria
        ) {
          return false;
        }

        if (
          validatedParams.data.idDepartamento &&
          liq.empenho.dotacao.departamentoId !==
            validatedParams.data.idDepartamento
        ) {
          return false;
        }

        if (
          validatedParams.data.idSubdepartamento &&
          liq.empenho.dotacao.subdepartamentoId !==
            validatedParams.data.idSubdepartamento
        ) {
          return false;
        }

        return true;
      });
    }

    // Filter by processing status if specified
    if (
      validatedParams.data.incluirProcessados !== undefined ||
      validatedParams.data.incluirNaoProcessados !== undefined
    ) {
      filteredLiquidacoes = filteredLiquidacoes.filter((liq) => {
        const isProcessado = liq.status === StatusLiquidacao.PROCESSADO;

        if (
          validatedParams.data.incluirProcessados &&
          validatedParams.data.incluirNaoProcessados
        ) {
          return true; // Include both
        } else if (validatedParams.data.incluirProcessados) {
          return isProcessado;
        } else if (validatedParams.data.incluirNaoProcessados) {
          return !isProcessado;
        }
        return true;
      });
    }

    // Check user access permissions
    if (!resultPermissao.gerente) {
      const acessos = await prisma.user_profiles_dotacoes.findMany({
        where: {
          uuidUsuario: await (async () => {
            const supabase = await createClientWithBearer(
              validatedParams.data.bearer || ''
            );
            const { data } = await supabase.auth.getUser();
            return data?.user?.id || undefined;
          })(),
        },
        select: {
          dotacoesIds: true,
          acessoTotal: true,
        },
      });

      // Check if user has total access
      const temAcessoTotal = acessos.some((a) => a.acessoTotal);

      // Get all accessible dotacoes IDs
      const dotacoesAcessiveis = temAcessoTotal
        ? [] // Empty array means all dotacoes are accessible when acessoTotal is true
        : acessos.flatMap((a) => a.dotacoesIds);
      filteredLiquidacoes = filteredLiquidacoes.filter(
        (liq) =>
          temAcessoTotal ||
          (liq.empenho?.idDotacao &&
            dotacoesAcessiveis.includes(liq.empenho.idDotacao))
      );
    }

    // Process data for report
    const reportData: LiquidacaoReportData[] = filteredLiquidacoes.map(
      (liq) => {
        const valorTotal = liq.valorTotal.toNumber();
        const isProcessado = liq.status === StatusLiquidacao.PROCESSADO;

        // Group by month for monthly breakdown
        const valoresMensais: MonthlyBreakdown[] = [];
        const meses = [
          'Jan',
          'Fev',
          'Mar',
          'Abr',
          'Mai',
          'Jun',
          'Jul',
          'Ago',
          'Set',
          'Out',
          'Nov',
          'Dez',
        ];

        meses.forEach((mes, index) => {
          const mesLiquidacao = liq.mesReferencia
            ? parseInt(liq.mesReferencia)
            : new Date(liq.data).getMonth() + 1;

          if (mesLiquidacao === index + 1) {
            const valorLiquidadoMes = isProcessado ? valorTotal : 0;
            const valorProcessadoMes = isProcessado ? valorTotal : 0;
            const valorNaoProcessadoMes = !isProcessado ? valorTotal : 0;

            valoresMensais.push({
              mes,
              valorLiquidado: valorLiquidadoMes,
              valorProcessado: valorProcessadoMes,
              valorNaoProcessado: valorNaoProcessadoMes,
            });
          }
        });

        return {
          id: liq.id,
          numero: liq.numero,
          exercicio: liq.exercicio,
          valorTotal,
          data: liq.data,
          dataMaterialServico: liq.dataMaterialServico,
          mesReferencia: liq.mesReferencia,
          resumo: liq.resumo,
          obs: liq.obs,
          marcaDocumento: liq.marca_documento,
          empenhoOrigem: `${liq.idEmpenho}/${liq.exercicio}`,
          status: liq.status,
          fornecedor: liq.empenho?.fornecedor
            ? {
                id: liq.empenho.fornecedor.id,
                nome: liq.empenho.fornecedor.nome,
                tipo:
                  liq.empenho.fornecedor.cnpjCpf.length === 11
                    ? 'FISICA'
                    : 'JURIDICA',
                documento: liq.empenho.fornecedor.cnpjCpf,
              }
            : null,
          empenho: {
            id: liq.empenho.id,
            numero: liq.empenho.numero,
            exercicio: liq.empenho.exercicio,
            resumo: liq.empenho.resumo,
            status: liq.empenho.status,
          },
          secretaria: liq.empenho?.dotacao?.secretaria,
          departamento: liq.empenho?.dotacao?.departamento,
          subdepartamento: liq.empenho?.dotacao?.subdepartamento,
          valoresMensais,
        };
      }
    );

    // Calculate summary statistics
    const summary: LiquidacaoReportSummary = {
      totalLiquidacoes: reportData.length,
      valorTotal: reportData.reduce(
        (sum, liq) =>
          currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal).value,
        0
      ),
      valorTotalProcessado: reportData
        .filter((liq) => liq.status === StatusLiquidacao.PROCESSADO)
        .reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal).value,
          0
        ),
      valorTotalNaoProcessado: reportData
        .filter((liq) => liq.status === StatusLiquidacao.NAO_PROCESSADO)
        .reduce(
          (sum, liq) =>
            currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal).value,
          0
        ),
      mediaValor:
        reportData.length > 0
          ? currency(
              reportData.reduce(
                (sum, liq) =>
                  currency(sum, currencyOptionsNoSymbol).add(liq.valorTotal)
                    .value,
                0
              ),
              currencyOptionsNoSymbol
            ).divide(reportData.length).value
          : 0,
    };

    return {
      data: {
        liquidacoes: reportData,
        resumo: summary,
      },
    };
  } catch (e) {
    console.error('Erro ao gerar relatório de liquidações:', e);
    return { error: 'Erro ao gerar relatório de liquidações.' };
  }
};

export const gerarRelatorioLiquidacoesPorPeriodo = async (
  params: RelatorioLiquidacoesParams & {
    periodoInicio: Date;
    periodoFim: Date;
  }
) => {
  const validatedParams = relatorioLiquidacoesSchema.safeParse(params);
  if (!validatedParams.success) {
    return {
      error:
        'Parâmetros inválidos para o relatório de liquidações por período.',
    };
  }

  return await gerarRelatorioLiquidacoes({
    ...validatedParams.data,
    dataInicio: params.periodoInicio,
    dataFim: params.periodoFim,
  });
};

export const gerarRelatorioLiquidacoesProcessados = async (
  params: RelatorioLiquidacoesParams
) => {
  const validatedParams = relatorioLiquidacoesSchema.safeParse(params);
  if (!validatedParams.success) {
    return {
      error:
        'Parâmetros inválidos para o relatório de liquidações processados.',
    };
  }

  return await gerarRelatorioLiquidacoes({
    ...validatedParams.data,
    incluirProcessados: true,
    incluirNaoProcessados: false,
  });
};

export const gerarRelatorioLiquidacoesNaoProcessados = async (
  params: RelatorioLiquidacoesParams
) => {
  const validatedParams = relatorioLiquidacoesSchema.safeParse(params);
  if (!validatedParams.success) {
    return {
      error:
        'Parâmetros inválidos para o relatório de liquidações não processados.',
    };
  }

  return await gerarRelatorioLiquidacoes({
    ...validatedParams.data,
    incluirProcessados: false,
    incluirNaoProcessados: true,
  });
};
