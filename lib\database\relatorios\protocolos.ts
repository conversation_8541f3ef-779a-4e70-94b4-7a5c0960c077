'use server';

import { StatusProtocolo } from '@/lib/enums';
import { prisma } from '@/lib/prisma';

export interface ProtocoloCombobox {
  id: number;
  numero: string;
  exercicio: number;
  dataAbertura: Date;
  resumo: string;
  status: string;
}

export async function listarProtocolosParaCombobox() {
  try {
    const protocolos = await prisma.protocolos.findMany({
      select: {
        id: true,
        numero: true,
        exercicio: true,
        dataAbertura: true,
        resumo: true,
        status: true,
      },
      where: {
        status: {
          not: StatusProtocolo.Nenhum,
        },
      },
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
      take: 100, // Limit to prevent too many results
    });

    return {
      data: protocolos.map((protocolo) => ({
        id: protocolo.id,
        numero: protocolo.numero,
        exercicio: protocolo.exercicio,
        dataAbertura: protocolo.dataAbertura.toISOString(),
        resumo: protocolo.resumo,
        status: protocolo.status,
      })),
    };
  } catch (error) {
    console.error('Erro ao listar protocolos:', error);
    return { error: 'Falha ao listar protocolos' };
  }
}
