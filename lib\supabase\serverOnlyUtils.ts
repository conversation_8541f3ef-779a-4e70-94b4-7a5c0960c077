//Não utilizar 'use server', pois assim as funções aqui podem ser exportadas
//e chamadas pelo servidor sem estarem disponíveis para o client e sem precisar de validação de parâmetros

import { createDecipheriv, pbkdf2Sync } from 'crypto';
import forge from 'node-forge';

function deriveAESKey(password: string, salt: Buffer): Buffer {
  //salt Buffer to BinaryLike
  const saltBinary = new Uint8Array(salt);
  return pbkdf2Sync(password, saltBinary, 100000, 32, 'sha256'); // 32 bytes for AES-256
}

export async function decryptFile(
  encryptedBuffer: Buffer,
  password: string
): Promise<Buffer> {
  try {
    // Extract salt, IV, authTag, and encrypted data
    const salt = encryptedBuffer.slice(0, 16);
    const iv = encryptedBuffer.slice(16, 28); // GCM usually uses 12-byte IV
    const authTag = encryptedBuffer.slice(28, 44); // Authentication tag
    const encryptedData = encryptedBuffer.slice(44); // Remaining data is the encrypted content

    // Derive AES key from password and salt
    const key = deriveAESKey(password, salt);

    // Create decipher for AES-GCM
    const decipher = createDecipheriv(
      'aes-256-gcm',
      new Uint8Array(key),
      new Uint8Array(iv)
    );
    decipher.setAuthTag(new Uint8Array(authTag));

    // Decrypt the data
    let decrypted = decipher.update(new Uint8Array(encryptedData));
    decrypted = Buffer.concat([
      new Uint8Array(decrypted),
      new Uint8Array(decipher.final()),
    ]);

    return decrypted;
  } catch (error) {
    throw error;
  }
}

export function validarCertificado(certificado: Buffer, senha: string) {
  const pfxAsn1 = forge.asn1.fromDer(
    forge.util.createBuffer(new Uint8Array(certificado))
  );
  const pfx = forge.pkcs12.pkcs12FromAsn1(pfxAsn1, senha);

  if (!pfx) {
    return {
      error: 'Senha incorreta ou arquivo inválido.',
    };
  }

  // Extract the certificate (assuming it's the first certificate in the PFX file)
  const bags = pfx.getBags({ bagType: forge.pki.oids.certBag });
  const certBag = bags[forge.pki.oids.certBag];

  // Check if certBag is defined
  if (certBag && certBag.length > 0) {
    const cert = certBag[0].cert;
    if (cert) {
      const validity = cert.validity;
      const currentDate = new Date();
      const expirationDate = validity.notAfter;
      const isExpired = currentDate > expirationDate;

      if (isExpired) {
        return {
          error: 'O certificado está expirado.',
        };
      } else {
        const subjectAttributes = cert.subject.attributes;
        const issuerAttributes = cert.issuer.attributes;
        return {
          data: {
            subjectAttributes,
            issuerAttributes,
            validity: {
              notBefore: validity.notBefore,
              notAfter: validity.notAfter,
            },
          },
        };
      }
    } else {
      return {
        error: 'Certificado não encontrado no PFX.',
      };
    }
  } else {
    return {
      error: 'Certificado não encontrado no PFX.',
    };
  }
}
