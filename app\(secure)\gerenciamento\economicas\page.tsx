import { DialogNovaEconomica } from '@/components/gerenciamento/economicas/dialogNovaEconomica';
import EconomicasDatatableWrapper from '@/components/gerenciamento/economicas/economicasDatatableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';

export default function GerenciamentoDeEconomicasPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Gerenciamento de Econômicas</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='flex w-full justify-end pr-6'>
            <DialogNovaEconomica />
          </div>
          <EconomicasDatatableWrapper />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
