import { ErrorAlert } from '@/components/error-alert';
import UsuariosDatatable from './usuariosDataTable';
import { listarPerfisUsuarios } from '@/lib/database/gerenciamento/usuarios';

export default async function UsuariosDatatableWrapper() {
  const result = await listarPerfisUsuarios();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Usuários não encontrados.'} />;

  return <UsuariosDatatable data={result} />;
}
