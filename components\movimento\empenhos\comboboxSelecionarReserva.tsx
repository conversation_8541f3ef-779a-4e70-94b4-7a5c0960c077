'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarReservasParaEmpenho } from '@/lib/database/movimento/empenhos';

interface ComboboxSelecionarReservaProps {
  reservaId: number | null;
  setReservaId: (id: number) => void;
  onReservaSelect?: (reserva: any) => void;
  disabled?: boolean;
}

export function ComboboxSelecionarReserva({
  reservaId,
  setReservaId,
  onReservaSelect,
  disabled = false,
}: ComboboxSelecionarReservaProps) {
  const [open, setOpen] = React.useState(false);
  const [reservas, setReservas] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    const carregarReservas = async () => {
      setLoading(true);
      try {
        const result = await listarReservasParaEmpenho();
        if (result.data && result.data.reservas) {
          setReservas(result.data.reservas);
        }
      } catch (error) {
        console.error('Erro ao carregar reservas:', error);
      } finally {
        setLoading(false);
      }
    };

    carregarReservas();
  }, []);

  const reservaSelecionada = reservas.find(
    (reserva) => reserva.id === reservaId
  );

  const handleSelect = (reserva: any) => {
    setReservaId(reserva.id);
    setOpen(false);
    if (onReservaSelect) {
      onReservaSelect(reserva);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
          disabled={loading || disabled}
        >
          {loading
            ? 'Carregando reservas...'
            : reservaSelecionada
              ? `Reserva ${reservaSelecionada.id} - ${reservaSelecionada.resumo}`
              : 'Selecione uma reserva para empenhar...'}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[500px] p-0'>
        <Command>
          <CommandInput placeholder='Buscar reserva por ID ou descrição...' />
          <CommandList>
            <CommandEmpty>Nenhuma reserva encontrada.</CommandEmpty>
            {reservas.map((reserva) => (
              <CommandItem
                key={reserva.id}
                value={`${reserva.id} ${reserva.resumo} ${reserva.dotacao.despesa}`}
                onSelect={() => handleSelect(reserva)}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    reserva.id === reservaId ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className='flex w-full flex-col'>
                  <span className='font-medium'>
                    Reserva #{reserva.id} - {reserva.resumo}
                  </span>
                  <div className='text-muted-foreground flex justify-between text-sm'>
                    <span>Despesa: {reserva.dotacao.despesa}</span>
                    <span>
                      Valor: R${' '}
                      {reserva.usarTotal.toLocaleString('pt-BR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </span>
                  </div>
                  {reserva.dotacao.desc && (
                    <span className='text-muted-foreground truncate text-xs'>
                      {reserva.dotacao.desc}
                    </span>
                  )}
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
