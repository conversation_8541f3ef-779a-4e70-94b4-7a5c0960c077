'use server';

import { AuditoriaGerenciamentoTemplates, Permisso<PERSON> } from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  templateDocumentoSchema,
  editarTemplateDocumentoSchema,
  idSchema,
  permissaoSchema,
} from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { revalidatePath } from 'next/cache';

import {
  validarHtmlTemplate,
  processarVariaveisTemplate,
} from '@/lib/utils/seguranca';

export const listarTemplatesDocumentos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const templates = await prisma.templates_documentos.findMany({
      where: {
        ativo: true,
      },
      orderBy: {
        nome: 'asc',
      },
    });

    return {
      data: { templates },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao listar templates de documentos.',
    };
  }
};

export const obterTemplateDocumento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const template = await prisma.templates_documentos.findUnique({
      where: {
        id: id,
        ativo: true,
      },
    });

    if (!template) {
      return {
        error: 'Template não encontrado.',
      };
    }

    return {
      data: template,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao obter template de documento.',
    };
  }
};

export const criarTemplateDocumento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = templateDocumentoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { nome, descricao, conteudoHtml } = parsedParams.data;

  // VALIDAÇÃO DE SEGURANÇA
  const validacaoHtml = validarHtmlTemplate(conteudoHtml);
  if (!validacaoHtml.valido) {
    return {
      error: `Template contém elementos não seguros: ${validacaoHtml.erros.join(', ')}`,
    };
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    const novoTemplate = await prisma.$transaction(async (tx) => {
      const template = await tx.templates_documentos.create({
        data: {
          nome,
          descricao,
          conteudoHtml,
          ativo: true,
        },
      });

      await tx.templates_documentos_audit.create({
        data: {
          idTemplate: template.id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaGerenciamentoTemplates.CRIAR_TEMPLATE,
          ip,
        },
      });

      return template;
    });

    revalidatePath('/gerenciamento/templates-documentos');
    return {
      data: novoTemplate,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao criar template de documento.',
    };
  }
};

export const editarTemplateDocumento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = editarTemplateDocumentoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id, nome, descricao, conteudoHtml } = parsedParams.data;

  // VALIDAÇÃO DE SEGURANÇA
  const validacaoHtml = validarHtmlTemplate(conteudoHtml);
  if (!validacaoHtml.valido) {
    return {
      error: `Template contém elementos não seguros: ${validacaoHtml.erros.join(', ')}`,
    };
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    const templateAtualizado = await prisma.$transaction(async (tx) => {
      // Verificar se o template existe
      const templateExistente = await tx.templates_documentos.findUnique({
        where: { id, ativo: true },
      });

      if (!templateExistente) {
        throw new Error('Template não encontrado');
      }

      const template = await tx.templates_documentos.update({
        where: { id },
        data: {
          nome,
          descricao,
          conteudoHtml,
        },
      });

      await tx.templates_documentos_audit.create({
        data: {
          idTemplate: template.id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaGerenciamentoTemplates.EDITAR_TEMPLATE,
          ip,
        },
      });

      return template;
    });

    revalidatePath('/gerenciamento/templates-documentos');
    return {
      data: templateAtualizado,
    };
  } catch (e) {
    console.log(e);
    if (e instanceof Error && e.message === 'Template não encontrado') {
      return {
        error: 'Template não encontrado.',
      };
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao editar template de documento.',
    };
  }
};

export const desativarTemplateDocumento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      // Verificar se o template existe
      const templateExistente = await tx.templates_documentos.findUnique({
        where: { id, ativo: true },
      });

      if (!templateExistente) {
        throw new Error('Template não encontrado');
      }

      await tx.templates_documentos.update({
        where: { id },
        data: { ativo: false },
      });

      await tx.templates_documentos_audit.create({
        data: {
          idTemplate: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaGerenciamentoTemplates.DESATIVAR_TEMPLATE,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/templates-documentos');
    return {
      data: true,
    };
  } catch (e) {
    console.log(e);
    if (e instanceof Error && e.message === 'Template não encontrado') {
      return {
        error: 'Template não encontrado.',
      };
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_TEMPLATES,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao desativar template de documento.',
    };
  }
};

export const obterTemplateProcessado = async (params: {
  id: number;
  variaveis: Record<string, string>;
  bearer?: string;
}) => {
  try {
    const template = await prisma.templates_documentos.findUnique({
      where: {
        id: params.id,
        ativo: true,
      },
    });

    if (!template) {
      return {
        error: 'Template não encontrado.',
      };
    }

    const conteudoProcessado = processarVariaveisTemplate(
      template.conteudoHtml,
      params.variaveis
    );

    return {
      data: {
        template,
        conteudoProcessado,
      },
    };
  } catch (error) {
    console.error('Erro ao obter template processado:', error);
    return {
      error: 'Erro ao processar template.',
    };
  }
};
