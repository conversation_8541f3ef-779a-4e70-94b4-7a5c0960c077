'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { RelatorioAF } from '@/components/relatorios/relatorioAF';
import { obterAFParaRelatorio } from '@/lib/database/relatorios/afs';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';
import { ComboboxSelecionarAF } from '@/components/relatorios/comboboxSelecionarAF';

interface AFOption {
  id: number;
  numero: number;
  exercicio: number;
  resumo: string | null;
  valorTotal: number;
  status: number;
  data: Date;
  empenho: {
    numero: number;
    exercicio: number;
    fornecedor: {
      nome: string;
    } | null;
  };
}

export default function RelatorioAFPage() {
  const [af, setAF] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const afId = searchParams?.get('af');

  useEffect(() => {
    const fetchAF = async (id: number) => {
      setLoading(true);
      setError(null);

      try {
        const result = await obterAFParaRelatorio({ id, bearer: '' });

        if (result.error) {
          setError(result.error);
        } else if (result.data) {
          setAF(result.data);
        } else {
          setAF(null);
        }
      } catch (err) {
        setError('Erro ao carregar AF.');
      } finally {
        setLoading(false);
      }
    };

    if (afId) {
      fetchAF(parseInt(afId));
    } else {
      setAF(null);
      setError(null);
    }
  }, [afId]);

  const handleAFChange = async (selectedAF: AFOption | null) => {
    if (selectedAF) {
      // Update URL without navigation
      const newParams = new URLSearchParams(searchParams?.toString() || '');
      newParams.set('af', selectedAF.id.toString());
      window.history.pushState({}, '', `?${newParams.toString()}`);
    } else {
      // Clear AF from URL
      const newParams = new URLSearchParams(searchParams?.toString() || '');
      newParams.delete('af');
      window.history.pushState({}, '', `?${newParams.toString()}`);
    }
  };

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de AF</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='space-y-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold tracking-tight'>
                Relatório de Autorização de Fornecimento
              </h1>
              <p className='text-muted-foreground'>
                Visualize e exporte o relatório de AFs
              </p>
            </div>
            <div className='flex gap-4'>
              {af && (
                <BotaoDownloadPDF
                  url={`/movimento/af/imprimir/${af.id}`}
                  title='Imprimir Relatório'
                />
              )}
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow'>
            <div className='space-y-6'>
              <div>
                <label className='mb-2 block text-sm font-medium text-gray-700'>
                  Selecione uma AF
                </label>
                <ComboboxSelecionarAF onAFChange={handleAFChange} />
              </div>

              {loading ? (
                <div className='flex justify-center py-8'>
                  <Skeleton className='h-[400px] w-full max-w-4xl' />
                </div>
              ) : error ? (
                <div className='py-8 text-center'>
                  <p className='text-red-500'>{error}</p>
                </div>
              ) : af ? (
                <div className='mt-6'>
                  <RelatorioAF af={af} />
                </div>
              ) : afId ? (
                <div className='py-8 text-center'>
                  <p className='text-gray-500'>
                    AF não encontrada ou não há dados disponíveis.
                  </p>
                </div>
              ) : (
                <div className='py-8 text-center'>
                  <p className='text-gray-500'>
                    Selecione uma AF acima para visualizar o relatório.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
