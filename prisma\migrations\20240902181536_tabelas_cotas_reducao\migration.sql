-- CreateTable
CREATE TABLE "cotasReducao" (
    "id" SERIAL NOT NULL,
    "idDotacao" INTEGER NOT NULL,
    "motivo" TEXT NOT NULL,
    "valorTotal" DECIMAL(18,2) NOT NULL,
    "valorMes1" DECIMAL(18,2) NOT NULL,
    "valorMes2" DECIMAL(18,2) NOT NULL,
    "valorMes3" DECIMAL(18,2) NOT NULL,
    "valorMes4" DECIMAL(18,2) NOT NULL,
    "valorMes5" DECIMAL(18,2) NOT NULL,
    "valorMes6" DECIMAL(18,2) NOT NULL,
    "valorMes7" DECIMAL(18,2) NOT NULL,
    "valorMes8" DECIMAL(18,2) NOT NULL,
    "valorMes9" DECIMAL(18,2) NOT NULL,
    "valorMes10" DECIMAL(18,2) NOT NULL,
    "valorMes11" DECIMAL(18,2) NOT NULL,
    "valorMes12" DECIMAL(18,2) NOT NULL,
    "reducao" BOOLEAN NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cotasReducao_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cotasReducao_audit" (
    "id" SERIAL NOT NULL,
    "idCotaReducao" INTEGER NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cotasReducao_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "cotasReducao" ADD CONSTRAINT "cotasReducao_idDotacao_fkey" FOREIGN KEY ("idDotacao") REFERENCES "dotacoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cotasReducao_audit" ADD CONSTRAINT "cotasReducao_audit_idCotaReducao_fkey" FOREIGN KEY ("idCotaReducao") REFERENCES "cotasReducao"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cotasReducao_audit" ADD CONSTRAINT "cotasReducao_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
