import '@/styles/globals-insecure.css'; //Tailwind não estava carregando o globals.css também usado no grupo (secure)

import { Metadata, Viewport } from 'next';

import { fontSans } from '@/lib/fonts';
import { cn } from '@/lib/utils';
import { TailwindIndicator } from '@/components/tailwind-indicator';
import { ThemeProvider } from '@/components/theme-provider';
import { siteConfig } from '@/config/site';
import { Toaster } from '@/components/ui/sonner';

export const metadata: Metadata = {
  title: siteConfig.name,
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='pt-BR' suppressHydrationWarning>
      <body
        className={cn(
          'bg-background min-h-screen font-sans antialiased',
          fontSans.variable
        )}
      >
        <ThemeProvider attribute='class' defaultTheme='system' enableSystem>
          <div className='h-full min-h-screen'>{children}</div>
          <TailwindIndicator />
          <Toaster richColors />
        </ThemeProvider>
      </body>
    </html>
  );
}
