'use client';
import { ColumnDef } from '@tanstack/react-table';
import { listarDotacoes } from '@/lib/database/gerenciamento/dotacoes';
import { AlertDesativarDotacao } from './alertDesativarDotacao';
import { AlertAtivarDotacao } from './alertAtivarDotacao';
import { DialogDescricaoDotacao } from './dialogDescricaoDotacao';
import { ReusableDatatableFiltroDespesaDesc } from '@/components/datatable/reusableDatatableFiltroDespesaDesc';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Diff, Percent } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import currency from 'currency.js';

export default function DotacoesDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarDotacoes>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'despesa',
      header: 'Despesa',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'desc',
      header: 'Descrição',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'economica.codigo',
      header: 'Economica',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'valorAtual',
      header: 'Valor Atual',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link href={`/gerenciamento/dotacoes/${row.original.id}/cotas`}>
            <Button type='button' variant='outline'>
              <Percent className='mr-2 size-4' /> Cotas
            </Button>
          </Link>
          <Link
            href={`/gerenciamento/dotacoes/${row.original.id}/cotas-de-reducao`}
          >
            <Button type='button' variant='outline'>
              <Diff className='mr-2 size-4' /> Cotas Redução
            </Button>
          </Link>
          <Separator orientation='vertical' />
          <DialogDescricaoDotacao
            idDotacao={row.original.id}
            descDotacao={row.original.desc}
            despesa={row.original.despesa}
          />
          {row.original.ativo ? (
            <AlertDesativarDotacao
              idDotacao={row.original.id}
              descDotacao={row.original.desc}
              despesa={row.original.despesa}
            />
          ) : (
            <AlertAtivarDotacao
              idDotacao={row.original.id}
              descDotacao={row.original.desc}
              despesa={row.original.despesa}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatableFiltroDespesaDesc columns={columns} data={data.data} />
    </div>
  );
}
