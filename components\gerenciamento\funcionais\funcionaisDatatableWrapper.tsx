import { ErrorAlert } from '@/components/error-alert';
import FuncionalsDatatable from './funcionaisDataTable';
import { listarFuncionais } from '@/lib/database/gerenciamento/funcionais';

export default async function FuncionalsDatatableWrapper() {
  const result = await listarFuncionais();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Cargos não encontrados.'} />;

  return <FuncionalsDatatable data={result} />;
}
