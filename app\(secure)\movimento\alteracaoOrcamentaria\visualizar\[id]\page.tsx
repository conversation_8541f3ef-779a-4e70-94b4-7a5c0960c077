'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { ErrorAlert } from '@/components/error-alert';
import { obterAlteracaoParaRelatorio } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { RelatorioAlteracaoOrcamentaria } from '@/components/movimento/alteracaoOrcamentaria/relatorioAlteracaoOrcamentaria';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { StatusAlteracaoOrcamentaria } from '@/lib/enums';
import { Checkbox } from '@/components/ui/checkbox';
import { usuarioEhGerente } from '@/lib/database/usuarios';
import { BotaoDownloadPDFAlteracaoOrcamentaria } from '@/components/movimento/alteracaoOrcamentaria/botaoDownloadPDF';

export default async function VisualizarAlteracaoOrcamentariaPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const gerentePromise = usuarioEhGerente();

  const alteracaoOrcamentariaPromise = obterAlteracaoParaRelatorio({
    id: Number(id),
  });

  const [alteracaoOrcamentaria, gerente] = await Promise.all([
    alteracaoOrcamentariaPromise,
    gerentePromise,
  ]);

  if (alteracaoOrcamentaria.error) {
    return <ErrorAlert error={alteracaoOrcamentaria.error} />;
  }
  if (!alteracaoOrcamentaria.data) {
    return <ErrorAlert error='Falha ao obter alteração orçamentária.' />;
  }
  if (gerente.error) {
    return <ErrorAlert error={gerente.error} />;
  }
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Visualizar Alteração Orçamentária</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='mx-6 flex w-full flex-wrap justify-between gap-4'>
          <Link href='/movimento/alteracaoOrcamentaria'>
            <Button className='mb-4' variant='secondary'>
              <ArrowLeft className='mr-2 size-4' /> Voltar
            </Button>
          </Link>
          <div className='flex flex-wrap gap-4'>
            {alteracaoOrcamentaria.data.status !==
              StatusAlteracaoOrcamentaria.Reprovado && (
              <BotaoDownloadPDFAlteracaoOrcamentaria
                url={`/movimento/alteracaoOrcamentaria/imprimir/${id}`}
              />
            )}
          </div>
        </div>
        {gerente.data && (
          <div className='flex items-center space-x-2'>
            <Checkbox id='incluirGestor' defaultChecked />
            <label
              htmlFor='incluirGestor'
              className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
            >
              Incluir assinatura gestor(a)
            </label>
          </div>
        )}
        <div className='flex w-full flex-wrap justify-center'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <RelatorioAlteracaoOrcamentaria
              alteracaoOrcamentaria={alteracaoOrcamentaria}
            />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
