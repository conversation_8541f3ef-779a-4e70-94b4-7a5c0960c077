'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Upload, FileText, Trash2, Eye } from 'lucide-react';
import { uploadPDF } from '@/lib/supabase/clientUtils';
import { TipoDocumentoAF, TipoDocumentoAFDesc } from '@/lib/enums';
import {
  uploadDocumentoAF,
  removerDocumentoAF,
  obterUrlDocumentoAF,
} from '@/lib/database/movimento/afs';

interface DocumentoAF {
  id: number;
  tipoDocumento: TipoDocumentoAF;
  nomeArquivo: string;
  caminhoArquivo: string;
  dataUpload: Date;
  tamanho: number;
}

interface DocumentUploadAFProps {
  idAF: number;
  exercicio?: number;
  idDotacao?: number;
  idEmpenho?: number;
  documentosExistentes?: DocumentoAF[];
  onUploadComplete?: (documento: DocumentoAF) => void;
  onRemoveComplete?: (documentoId: string) => void;
  readOnly?: boolean;
}

export default function DocumentUploadAF({
  idAF,
  exercicio,
  idDotacao,
  idEmpenho,
  documentosExistentes = [],
  onUploadComplete,
  onRemoveComplete,
  readOnly = false,
}: DocumentUploadAFProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedTipo, setSelectedTipo] = useState<TipoDocumentoAF | null>(
    null
  );
  const [documentos, setDocumentos] =
    useState<DocumentoAF[]>(documentosExistentes);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validar tipo de arquivo
      if (!file.type.includes('pdf') && !file.type.includes('image')) {
        toast.error('Apenas arquivos PDF e imagens são permitidos');
        return;
      }

      // Validar tamanho (10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Arquivo muito grande. Máximo 10MB');
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !selectedTipo) {
      toast.error('Selecione um arquivo e um tipo de documento');
      return;
    }

    if (!exercicio || !idDotacao || !idEmpenho) {
      toast.error('Dados incompletos para upload do documento');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      // Gerar nome do arquivo com padrão: AF_{idAF}_{tipo}_{timestamp}
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = selectedFile.name.split('.').pop();
      const fileName = `AF_${idAF}_${selectedTipo}_${timestamp}.${fileExtension}`;

      // Construir caminho completo: private/${exercicio}/dotacoes/${idDotacao}/empenhos/${idEmpenho}/afs/${fileName}
      const filePath = `private/${exercicio}/dotacoes/${idDotacao}/empenhos/${idEmpenho}/afs/${fileName}`;

      // Upload para Supabase
      const uploadResult = await uploadPDF(
        selectedFile,
        'reservas',
        filePath,
        (progress) => setUploadProgress(progress)
      );

      if (uploadResult.error) {
        toast.error(uploadResult.error);
        return;
      }

      // Criar registro no banco de dados
      const dbResult = await uploadDocumentoAF({
        idAF,
        tipoDocumento: selectedTipo,
        nomeArquivo: selectedFile.name,
        caminhoArquivo: uploadResult.data?.path || filePath,
        tamanho: selectedFile.size,
      });

      if ('error' in dbResult) {
        toast.error(dbResult.error);
        return;
      }

      const novoDocumento: DocumentoAF = {
        id: dbResult.data.id,
        tipoDocumento: selectedTipo,
        nomeArquivo: selectedFile.name,
        caminhoArquivo: uploadResult.data?.path || filePath,
        dataUpload: new Date(),
        tamanho: selectedFile.size,
      };

      setDocumentos([...documentos, novoDocumento]);
      onUploadComplete?.(novoDocumento);
      toast.success('Documento enviado com sucesso!');

      // Reset form
      setSelectedFile(null);
      setSelectedTipo(null);
      setUploadProgress(0);
    } catch (error) {
      console.error('Erro no upload:', error);
      toast.error('Erro ao enviar documento');
    } finally {
      setUploading(false);
    }
  };

  const handleRemove = async (documentoId: number) => {
    try {
      const result = await removerDocumentoAF({
        idAF,
        idDocumento: documentoId,
      });

      if ('error' in result) {
        toast.error(result.error);
        return;
      }

      setDocumentos(documentos.filter((doc) => doc.id !== documentoId));
      onRemoveComplete?.(documentoId.toString());
      toast.success('Documento removido com sucesso!');
    } catch (error) {
      console.error('Erro ao remover documento:', error);
      toast.error('Erro ao remover documento');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <FileText className='h-5 w-5' />
          Documentos da AF
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {!readOnly && (
          <div className='space-y-4 rounded-lg border p-4'>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div>
                <Label htmlFor='tipo-documento'>Tipo de Documento</Label>
                <Select
                  value={selectedTipo?.toString() || ''}
                  onValueChange={(value) =>
                    setSelectedTipo(parseInt(value) as TipoDocumentoAF)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Selecione o tipo' />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(TipoDocumentoAFDesc).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor='arquivo'>Arquivo PDF</Label>
                <Input
                  id='arquivo'
                  type='file'
                  accept='.pdf'
                  onChange={handleFileSelect}
                  disabled={uploading}
                />
              </div>
            </div>

            {selectedFile && (
              <div className='text-sm'>
                <p>
                  <strong>Arquivo selecionado:</strong> {selectedFile.name}
                </p>
                <p>
                  <strong>Tamanho:</strong> {formatFileSize(selectedFile.size)}
                </p>
              </div>
            )}

            {uploading && (
              <div className='space-y-2'>
                <Progress value={uploadProgress} className='w-full' />
                <p className='text-center text-sm'>
                  Enviando... {Math.round(uploadProgress)}%
                </p>
              </div>
            )}

            <Button
              onClick={handleUpload}
              disabled={!selectedFile || !selectedTipo || uploading}
              className='w-full'
            >
              {uploading ? (
                <>
                  <Upload className='mr-2 h-4 w-4 animate-pulse' />
                  Enviando...
                </>
              ) : (
                <>
                  <Upload className='mr-2 h-4 w-4' />
                  Enviar Documento
                </>
              )}
            </Button>
          </div>
        )}

        <div className='space-y-2'>
          <h3 className='font-semibold'>Documentos Anexados</h3>
          {documentos.length === 0 ? (
            <p className='text-muted-foreground text-sm'>
              Nenhum documento anexado
            </p>
          ) : (
            <div className='space-y-2'>
              {documentos.map((documento) => (
                <div
                  key={documento.id}
                  className='flex items-center justify-between rounded border p-3'
                >
                  <div className='flex items-center gap-3'>
                    <FileText className='h-4 w-4 text-blue-600' />
                    <div>
                      <p className='font-medium'>{documento.nomeArquivo}</p>
                      <div className='text-muted-foreground flex items-center gap-2 text-xs'>
                        <Badge variant='outline' className='text-xs'>
                          {TipoDocumentoAFDesc[documento.tipoDocumento]}
                        </Badge>
                        <span>{formatFileSize(documento.tamanho)}</span>
                        <span>
                          {new Date(documento.dataUpload).toLocaleDateString(
                            'pt-BR'
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={async () => {
                        try {
                          const result = await obterUrlDocumentoAF(
                            documento.id
                          );
                          if ('error' in result) {
                            toast.error(result.error);
                          } else {
                            window.open(result.data.url, '_blank');
                          }
                        } catch (error) {
                          toast.error('Erro ao abrir documento');
                        }
                      }}
                    >
                      <Eye className='h-4 w-4' />
                    </Button>
                    {!readOnly && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => handleRemove(documento.id)}
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
