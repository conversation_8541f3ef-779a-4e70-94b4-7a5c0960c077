'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import FormCriarEmpenho from '@/components/movimento/empenhos/formCriarEmpenho';

export default async function NovoEmpenhoPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Novo Empenho</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormCriarEmpenho />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
