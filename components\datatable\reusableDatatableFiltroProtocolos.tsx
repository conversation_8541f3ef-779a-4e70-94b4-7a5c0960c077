'use client';

import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons';

import {
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from '@tanstack/react-table';
import { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { ComboboxSelecionaSecretaria } from '../movimento/reserva/comboboxSelecionarSecretaria';
import { ComboboxSelecionaDepartamento } from '../movimento/reserva/comboboxSelecionarDepartamento';
import { StatusProtocolo } from '@/lib/enums';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  secretarias: Array<{ id: number; nome: string }>;
  departamentos: Array<{ id: number; nome: string; secretariaId: number }>;
}

export function ReusableDatatableFiltroProtocolos<TData, TValue>({
  columns,
  data,
  secretarias,
  departamentos,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // Filter states
  const [secretariaId, setSecretariaId] = useState<number | null>(null);
  const [departamentoId, setDepartamentoId] = useState<number | null>(null);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      columnVisibility: {
        secretariaId: false,
      },
    },
    enableMultiRowSelection: true,
  });

  // Handle secretaria change - apply filter to table
  useEffect(() => {
    if (secretariaId) {
      table.getColumn('secretariaId')?.setFilterValue(secretariaId);
      if (
        departamentoId &&
        departamentos.find((departamento) => departamento.id === departamentoId)
          ?.secretariaId !== secretariaId
      ) {
        setDepartamentoId(null);
      }
    } else {
      table.getColumn('secretariaId')?.setFilterValue(undefined);
    }
  }, [secretariaId, departamentoId, departamentos, table]);

  // Handle departamento change - apply filter to table
  useEffect(() => {
    if (departamentoId) {
      table.getColumn('departamentoId')?.setFilterValue(departamentoId);
    } else {
      table.getColumn('departamentoId')?.setFilterValue(undefined);
    }
  }, [departamentoId, table]);

  return (
    <>
      <div className='flex flex-wrap items-center gap-4 py-4'>
        <Input
          placeholder='Filtrar Protocolo...'
          value={(table.getColumn('id')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('id')?.setFilterValue(event.target.value)
          }
          className='max-w-[150px]'
        />
        <Input
          placeholder='Filtrar Reserva...'
          value={(table.getColumn('reserva')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('reserva')?.setFilterValue(event.target.value)
          }
          className='max-w-[150px]'
        />
        <Input
          placeholder='Filtrar Resumo...'
          value={(table.getColumn('resumo')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('resumo')?.setFilterValue(event.target.value)
          }
          className='max-w-[200px]'
        />
        <Select
          value={
            (table.getColumn('status')?.getFilterValue() as string) ?? 'all'
          }
          onValueChange={(value) =>
            table
              .getColumn('status')
              ?.setFilterValue(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className='ovberflow-hidden ellipsis max-w-[200px] whitespace-nowrap'>
            <SelectValue placeholder='Todos Status' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Todos Status</SelectItem>
            {Object.entries(StatusProtocolo)
              .filter(
                ([_key, value]) =>
                  typeof value === 'number' && value !== StatusProtocolo.Nenhum
              )
              .map(([key, value]) => (
                <SelectItem key={value} value={value.toString()}>
                  {key.replace(/_/g, ' ')}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
        {secretarias.length > 0 && (
          <div className='w-fit'>
            <ComboboxSelecionaSecretaria
              secretarias={secretarias.map((s) => ({
                ...s,
                ativo: true,
                codigo: s.id,
              }))}
              secretariaId={secretariaId}
              setSecretariaId={setSecretariaId}
            />
          </div>
        )}
        {departamentos.length > 0 && (
          <div className='w-fit'>
            <ComboboxSelecionaDepartamento
              departamentos={departamentos
                .filter((d) => !secretariaId || d.secretariaId === secretariaId)
                .map((d) => ({
                  ...d,
                  ativo: true,
                  codigo: d.id,
                  secretaria: (() => {
                    const foundSecretaria = secretarias.find(
                      (s) => s.id === d.secretariaId
                    );
                    return foundSecretaria
                      ? {
                          ...foundSecretaria,
                          ativo: true,
                          codigo: foundSecretaria.id,
                        }
                      : {
                          id: d.secretariaId,
                          nome: '',
                          ativo: true,
                          codigo: d.secretariaId,
                        };
                  })(),
                }))}
              departamentoId={departamentoId}
              setDepartamentoId={setDepartamentoId}
            />
          </div>
        )}
        <Input
          placeholder='Filtrar Despesa...'
          value={(table.getColumn('despesa')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('despesa')?.setFilterValue(event.target.value)
          }
          className='max-w-[200px]'
        />
      </div>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} {...cell.column.columnDef.meta}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  Nenhum Resultado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className='mr-4 flex items-center justify-end space-x-2 py-4'>
        <div className='flex w-[120px] items-center justify-center text-sm font-medium'>
          Página {table.getState().pagination.pageIndex + 1} de{' '}
          {table.getPageCount()}
        </div>
        <div className='flex items-center space-x-2'>
          <Button
            variant='outline'
            className='hidden h-8 w-8 p-0 lg:flex'
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <span className='sr-only'>Ir para a primeira página</span>
            <DoubleArrowLeftIcon className='h-4 w-4' />
          </Button>
          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <span className='sr-only'>Ir para a página anterior</span>
            <ChevronLeftIcon className='h-4 w-4' />
          </Button>
          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <span className='sr-only'>Ir para a próxima página</span>
            <ChevronRightIcon className='h-4 w-4' />
          </Button>
          <Button
            variant='outline'
            className='hidden h-8 w-8 p-0 lg:flex'
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <span className='sr-only'>Ir para a última página</span>
            <DoubleArrowRightIcon className='h-4 w-4' />
          </Button>
        </div>
      </div>
    </>
  );
}
