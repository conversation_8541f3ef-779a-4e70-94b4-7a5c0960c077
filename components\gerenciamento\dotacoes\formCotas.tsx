'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save } from 'lucide-react';
import {
  cn,
  currencyOptions,
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  obterAnoAtual,
  obterMesAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { alterarCotasFormSchema } from '@/lib/validation';
import {
  alterarCotas,
  obterCotasPorId,
} from '@/lib/database/gerenciamento/dotacoes';
import currency from 'currency.js';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
export default function FormCotas({
  dotacao,
}: {
  dotacao: Awaited<ReturnType<typeof obterCotasPorId>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);

  const {
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
  } = dotacao.data!;

  const [mes1BRL, setMes1BRL] = useState(
    currency(cotaMes1, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes2BRL, setMes2BRL] = useState(
    currency(cotaMes2, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes3BRL, setMes3BRL] = useState(
    currency(cotaMes3, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes4BRL, setMes4BRL] = useState(
    currency(cotaMes4, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes5BRL, setMes5BRL] = useState(
    currency(cotaMes5, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes6BRL, setMes6BRL] = useState(
    currency(cotaMes6, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes7BRL, setMes7BRL] = useState(
    currency(cotaMes7, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes8BRL, setMes8BRL] = useState(
    currency(cotaMes8, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes9BRL, setMes9BRL] = useState(
    currency(cotaMes9, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes10BRL, setMes10BRL] = useState(
    currency(cotaMes10, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes11BRL, setMes11BRL] = useState(
    currency(cotaMes11, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );
  const [mes12BRL, setMes12BRL] = useState(
    currency(cotaMes12, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );

  const form = useForm<z.infer<typeof alterarCotasFormSchema>>({
    resolver: zodResolver(alterarCotasFormSchema),
    defaultValues: {
      id: dotacao.data!.id,
      cotaMes1: Number(cotaMes1),
      cotaMes2: Number(cotaMes2),
      cotaMes3: Number(cotaMes3),
      cotaMes4: Number(cotaMes4),
      cotaMes5: Number(cotaMes5),
      cotaMes6: Number(cotaMes6),
      cotaMes7: Number(cotaMes7),
      cotaMes8: Number(cotaMes8),
      cotaMes9: Number(cotaMes9),
      cotaMes10: Number(cotaMes10),
      cotaMes11: Number(cotaMes11),
      cotaMes12: Number(cotaMes12),
    },
  });

  const total = currency(mes1BRL, currencyOptionsNoSymbol)
    .add(mes2BRL)
    .add(mes3BRL)
    .add(mes4BRL)
    .add(mes5BRL)
    .add(mes6BRL)
    .add(mes7BRL)
    .add(mes8BRL)
    .add(mes9BRL)
    .add(mes10BRL)
    .add(mes11BRL)
    .add(mes12BRL);

  const restante = currency(
    dotacao.data!.valorAtual,
    currencyOptionsNoSymbol
  ).subtract(total);

  const onSubmit = async (values: z.infer<typeof alterarCotasFormSchema>) => {
    try {
      setLoading(true);
      const res = await alterarCotas(values);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Cotas alteradas.');
        router.push('/gerenciamento/dotacoes');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };
  const anoAtual = obterAnoAtual();
  const mesAtual = dotacao.data!.exercicio < anoAtual ? 12 : obterMesAtual();

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='mt-8 flex flex-wrap justify-between gap-6 text-left'>
              <span>
                <Label>Valor Inicial:</Label>{' '}
                {currency(dotacao.data!.valorInicial, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Cota Redução Inicial:</Label>{' '}
                {currency(dotacao.data!.cotaReducaoInicial, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Cota Redução:</Label>{' '}
                {currency(dotacao.data!.cotaReducao, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Suplementação:</Label>{' '}
                {currency(dotacao.data!.suplementacao, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span>
                <Label>Anulação:</Label>{' '}
                {currency(dotacao.data!.anulacao, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
              <span className='w-full'>
                <Label>Valor Disponível:</Label>{' '}
                {currency(dotacao.data!.valorAtual, currencyOptions)
                  .format()
                  .replace(/0$/, '')}
              </span>
            </div>
            <Separator className='my-12' />
            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              <FormField
                name='janeiro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Janeiro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes1BRL}
                        onChange={(e) => {
                          setMes1BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes1',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 1}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='fevereiro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Fevereiro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes2BRL}
                        onChange={(e) => {
                          setMes2BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes2',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 2}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Março'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Março</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes3BRL}
                        onChange={(e) => {
                          setMes3BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes3',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 3}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Abril'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Abril</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes4BRL}
                        onChange={(e) => {
                          setMes4BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes4',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 4}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Maio'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Maio</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes5BRL}
                        onChange={(e) => {
                          setMes5BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes5',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 5}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Junho'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Junho</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes6BRL}
                        onChange={(e) => {
                          setMes6BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes6',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 6}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Julho'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Julho</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes7BRL}
                        onChange={(e) => {
                          setMes7BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes7',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 7}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Agosto'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Agosto</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes8BRL}
                        onChange={(e) => {
                          setMes8BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes8',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 8}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Setembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Setembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes9BRL}
                        onChange={(e) => {
                          setMes9BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes9',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 9}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Outubro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Outubro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes10BRL}
                        onChange={(e) => {
                          setMes10BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes10',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 10}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Novembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Novembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes11BRL}
                        onChange={(e) => {
                          setMes11BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes11',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={mesAtual > 11}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='Dezembro'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Dezembro</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={mes12BRL}
                        onChange={(e) => {
                          setMes12BRL(moneyMask(e.target.value));
                          form.setValue(
                            'cotaMes12',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <Separator className='my-12' />
            <div className='flex flex-wrap justify-between gap-6'>
              <FormField
                name='total'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Total</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'text-right text-base disabled:opacity-100',
                          dotacao.data!.valorAtual !== total.value
                            ? 'text-red-600'
                            : 'text-green-600'
                        )}
                        value={total.format().replace(/0$/, '')}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='restante'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Restante</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'text-right text-base disabled:opacity-100',
                          restante.value !== 0
                            ? 'text-red-600'
                            : 'text-green-600'
                        )}
                        value={restante.format().replace(/0$/, '')}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <div className='mt-12 flex w-full justify-between'>
        <Button
          variant={'destructive'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/gerenciamento/dotacoes');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={
            loading ||
            total.value !== Number(dotacao.data!.valorAtual) ||
            restante.value !== 0 ||
            Object.keys(form.formState.dirtyFields).length === 0
          }
        >
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <Save className='mr-2 h-4 w-4' /> Alterar Cotas
            </>
          )}
        </Button>
      </div>
    </>
  );
}
