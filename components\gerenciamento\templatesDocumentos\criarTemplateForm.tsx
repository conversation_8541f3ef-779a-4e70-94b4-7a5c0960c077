'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { templateDocumentoSchema } from '@/lib/validation';
import { criarTemplateDocumento } from '@/lib/database/gerenciamento/templatesDocumentos';
import { EditorTemplate } from './editorTemplate';
import { toast } from 'sonner';
import { Icons } from '@/components/icons';
import { ArrowLeft, Save } from 'lucide-react';
import * as z from 'zod';

type FormData = z.infer<typeof templateDocumentoSchema>;

export function CriarTemplateForm() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const form = useForm<FormData>({
    resolver: zodResolver(templateDocumentoSchema),
    defaultValues: {
      nome: '',
      descricao: '',
      conteudoHtml: '<p>Digite o conteúdo do template aqui...</p>',
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    try {
      const result = await criarTemplateDocumento(data);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      toast.success('Template criado com sucesso!');
      router.push('/gerenciamento/templates-documentos');
    } catch (error) {
      console.error('Erro ao criar template:', error);
      toast.error('Erro inesperado ao criar template');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoltar = () => {
    router.push('/gerenciamento/templates-documentos');
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center gap-4'>
        <Button
          type='button'
          variant='outline'
          onClick={handleVoltar}
          disabled={isLoading}
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Voltar
        </Button>
        <h1 className='text-2xl font-bold'>Criar Novo Template</h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <FormField
              control={form.control}
              name='nome'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Template *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Digite o nome do template...'
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='descricao'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Digite uma descrição para o template...'
                      {...field}
                      disabled={isLoading}
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='conteudoHtml'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Conteúdo do Template *</FormLabel>
                <FormControl>
                  <EditorTemplate
                    value={field.value}
                    onChange={field.onChange}
                    disabled={isLoading}
                    height={500}
                  />
                </FormControl>
                <FormMessage />
                <div className='text-muted-foreground text-sm'>
                  <strong>Variáveis disponíveis:</strong> {'{'}
                  {'{'}`NOME_USUARIO`{'}'}
                  {'}'}, {'{'}
                  {'{'}`DATA_ATUAL`{'}'}
                  {'}'}, {'{'}
                  {'{'}`NUMERO_DOCUMENTO`{'}'}
                  {'}'}, {'{'}
                  {'{'}`ANO_ATUAL`{'}'}
                  {'}'}, {'{'}
                  {'{'}`MES_ATUAL`{'}'}
                  {'}'}
                </div>
              </FormItem>
            )}
          />

          <div className='flex justify-end gap-4'>
            <Button
              type='button'
              variant='outline'
              onClick={handleVoltar}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button type='submit' disabled={isLoading}>
              {isLoading && (
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              )}
              <Save className='mr-2 h-4 w-4' />
              Criar Template
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
