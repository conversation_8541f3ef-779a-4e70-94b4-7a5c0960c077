'use server';

import { revalidatePath } from 'next/cache';
import { Modulos } from '@/lib/modulos';
import { Permissoes, StatusAF, AuditoriaAFs, StatusEmpenho } from '@/lib/enums';
import {
  auditoriaErroSchema,
  permissaoSchema,
  criarAFSchema,
  editarAFSchema,
  cancelarAFSchema,
  marcarAFComoUtilizadaSchema,
  reativarAFSchema,
  atualizarDatasAFSchema,
  listarAFsSchema,
  afIdSchema,
  numeroAFSchema,
  uploadDocumentoAFSchema,
  removerDocumentoAFSchema,
} from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { inserirErroAudit } from '../auditoria/erro';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { createSuperClient } from '@/lib/supabase/server';

// AFs serão tratadas como parte de Movimento de Empenhos para permissões
const MODULE = Modulos.MOVIMENTO_AFS;
const ROUTE = '/movimento/afs';

export const listarAFs = async (params?: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsedParams = listarAFsSchema.safeParse(params || {});
  if (!parsedParams.success) {
    console.log(parsedParams.error);
    return { error: 'Parâmetros inválidos.' };
  }

  try {
    const { idEmpenho, exercicio, status, numeroAF, dataInicio, dataFim } =
      parsedParams.data;

    const where: Prisma.afsWhereInput = { ativo: true };

    if (idEmpenho) where.idEmpenho = idEmpenho;
    if (exercicio) where.exercicio = exercicio;
    if (status !== undefined) where.status = status;
    if (numeroAF) where.numero = numeroAF;

    if (dataInicio || dataFim) {
      where.data = {};
      if (dataInicio) where.data.gte = dataInicio;
      if (dataFim) where.data.lte = dataFim;
    }

    const [afs, secretarias, departamentos] = await Promise.all([
      prisma.afs.findMany({
        where,
        select: {
          id: true,
          numero: true,
          exercicio: true,
          resumo: true,
          valorTotal: true,
          status: true,
          data: true,
          dataEmissao: true,
          dataVencimento: true,
          dataUtilizacao: true,
          dataCancelamento: true,
          dataReativacao: true,
          empenho: {
            include: {
              dotacao: {
                select: {
                  despesa: true,
                  desc: true,
                  secretaria: {
                    select: {
                      id: true,
                      nome: true,
                    },
                  },
                  departamento: {
                    select: {
                      id: true,
                      nome: true,
                    },
                  },
                },
              },
              fornecedor: {
                select: {
                  id: true,
                  nome: true,
                },
              },
              liquidacoes: true,
              empenhos_anulacoes: true,
            },
          },
        },
        orderBy: { data: 'desc' },
      }),
      prisma.secretarias.findMany({ where: { ativo: true } }),
      prisma.departamentos.findMany({
        where: { ativo: true },
        include: { secretaria: true },
      }),
    ]);

    const returnAfs = afs.map((af) => {
      // Calculate used and annulled values for this AF
      const valorUtilizado = af.empenho.liquidacoes.reduce(
        (sum, liquidacao) =>
          sum.add(
            currency(liquidacao.valorTotal.toNumber(), currencyOptionsNoSymbol)
          ),
        currency(0, currencyOptionsNoSymbol)
      );

      const valorAnulado = af.empenho.empenhos_anulacoes.reduce(
        (sum, anulacao) =>
          sum.add(
            currency(anulacao.valorAnulado.toNumber(), currencyOptionsNoSymbol)
          ),
        currency(0, currencyOptionsNoSymbol)
      );

      const saldo = currency(
        af.empenho.valorTotal.toNumber(),
        currencyOptionsNoSymbol
      )
        .subtract(valorUtilizado)
        .subtract(valorAnulado)
        .subtract(currency(af.valorTotal.toNumber(), currencyOptionsNoSymbol));

      // Convert Decimal fields to Number
      const returnObj = {
        ...af,
        valorTotal: af.valorTotal.toNumber(),
        valorUtilizado: valorUtilizado.value,
        saldo: saldo.value,
        empenho: {
          ...af.empenho,
          valorTotal: af.empenho.valorTotal.toNumber(),
          usarMes1: af.empenho.usarMes1.toNumber(),
          usarMes2: af.empenho.usarMes2.toNumber(),
          usarMes3: af.empenho.usarMes3.toNumber(),
          usarMes4: af.empenho.usarMes4.toNumber(),
          usarMes5: af.empenho.usarMes5.toNumber(),
          usarMes6: af.empenho.usarMes6.toNumber(),
          usarMes7: af.empenho.usarMes7.toNumber(),
          usarMes8: af.empenho.usarMes8.toNumber(),
          usarMes9: af.empenho.usarMes9.toNumber(),
          usarMes10: af.empenho.usarMes10.toNumber(),
          usarMes11: af.empenho.usarMes11.toNumber(),
          usarMes12: af.empenho.usarMes12.toNumber(),
        },
      };

      return returnObj;
    });

    return {
      data: {
        afs: returnAfs,
        secretarias,
        departamentos,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao listar AFs.' };
  }
};

export const obterAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsed = afIdSchema.safeParse(params);
  if (!parsed.success) return { error: 'Parâmetros inválidos.' };

  try {
    const af = await prisma.afs.findUnique({
      where: { id: parsed.data.id },
      include: {
        empenho: {
          include: {
            dotacao: {
              include: {
                secretaria: true,
                departamento: true,
                subdepartamento: true,
                economica: true,
                funcional: true,
              },
            },
            fornecedor: true,
            liquidacoes: {
              where: { ativo: true },
            },
            empenhos_anulacoes: {
              where: { ativo: true },
            },
          },
        },
        afs_audit: {
          include: {
            usuario: true,
          },
          orderBy: { data: 'desc' },
        },
        afs_documentos: {
          where: { ativo: true },
          orderBy: { dataUpload: 'desc' },
        },
      },
    });

    if (!af) return { error: 'AF não encontrada.' };

    // Calcular valor utilizado e saldo
    const valorUtilizado = af.empenho.liquidacoes.reduce(
      (sum, liquidacao) =>
        sum.add(
          currency(liquidacao.valorTotal.toNumber(), currencyOptionsNoSymbol)
        ),
      currency(0, currencyOptionsNoSymbol)
    );

    const valorAnulado = af.empenho.empenhos_anulacoes.reduce(
      (sum, anulacao) =>
        sum.add(
          currency(anulacao.valorAnulado.toNumber(), currencyOptionsNoSymbol)
        ),
      currency(0, currencyOptionsNoSymbol)
    );

    // FIXED: Use sum of monthly quotas instead of valorTotal (matching legacy logic)
    const valorOriginalEmpenho = currency(0, currencyOptionsNoSymbol)
      .add(af.empenho.usarMes1.toNumber())
      .add(af.empenho.usarMes2.toNumber())
      .add(af.empenho.usarMes3.toNumber())
      .add(af.empenho.usarMes4.toNumber())
      .add(af.empenho.usarMes5.toNumber())
      .add(af.empenho.usarMes6.toNumber())
      .add(af.empenho.usarMes7.toNumber())
      .add(af.empenho.usarMes8.toNumber())
      .add(af.empenho.usarMes9.toNumber())
      .add(af.empenho.usarMes10.toNumber())
      .add(af.empenho.usarMes11.toNumber())
      .add(af.empenho.usarMes12.toNumber());

    const saldo = valorOriginalEmpenho
      .subtract(valorUtilizado)
      .subtract(valorAnulado)
      .subtract(currency(af.valorTotal.toNumber(), currencyOptionsNoSymbol));

    // Convert Decimal fields to Number
    const returnObj = {
      id: af.id,
      exercicio: af.exercicio,
      numero: af.numero,
      idEmpenho: af.idEmpenho,
      resumo: af.resumo,
      obs: af.obs,
      valorTotal: af.valorTotal.toNumber(),
      status: af.status,
      data: af.data,
      dataEmissao: af.dataEmissao,
      dataVencimento: af.dataVencimento,
      dataUtilizacao: af.dataUtilizacao,
      dataCancelamento: af.dataCancelamento,
      dataReativacao: af.dataReativacao,
      ativo: af.ativo,
      valorUtilizado: valorUtilizado.value,
      saldo: saldo.value,
      auditoria: af.afs_audit,
      afs_documentos: af.afs_documentos,
      empenho: {
        ...af.empenho,
        valorTotal: af.empenho.valorTotal.toNumber(),
        usarMes1: af.empenho.usarMes1.toNumber(),
        usarMes2: af.empenho.usarMes2.toNumber(),
        usarMes3: af.empenho.usarMes3.toNumber(),
        usarMes4: af.empenho.usarMes4.toNumber(),
        usarMes5: af.empenho.usarMes5.toNumber(),
        usarMes6: af.empenho.usarMes6.toNumber(),
        usarMes7: af.empenho.usarMes7.toNumber(),
        usarMes8: af.empenho.usarMes8.toNumber(),
        usarMes9: af.empenho.usarMes9.toNumber(),
        usarMes10: af.empenho.usarMes10.toNumber(),
        usarMes11: af.empenho.usarMes11.toNumber(),
        usarMes12: af.empenho.usarMes12.toNumber(),
        dotacao: af.empenho.dotacao
          ? {
              ...af.empenho.dotacao,
              valorInicial: af.empenho.dotacao.valorInicial.toNumber(),
              cotaReducaoInicial:
                af.empenho.dotacao.cotaReducaoInicial.toNumber(),
              valorLiberado: af.empenho.dotacao.valorLiberado.toNumber(),
              cotaReducao: af.empenho.dotacao.cotaReducao.toNumber(),
              suplementacao: af.empenho.dotacao.suplementacao.toNumber(),
              anulacao: af.empenho.dotacao.anulacao.toNumber(),
              valorAtual: af.empenho.dotacao.valorAtual.toNumber(),
              cotaMes1: af.empenho.dotacao.cotaMes1.toNumber(),
              cotaMes2: af.empenho.dotacao.cotaMes2.toNumber(),
              cotaMes3: af.empenho.dotacao.cotaMes3.toNumber(),
              cotaMes4: af.empenho.dotacao.cotaMes4.toNumber(),
              cotaMes5: af.empenho.dotacao.cotaMes5.toNumber(),
              cotaMes6: af.empenho.dotacao.cotaMes6.toNumber(),
              cotaMes7: af.empenho.dotacao.cotaMes7.toNumber(),
              cotaMes8: af.empenho.dotacao.cotaMes8.toNumber(),
              cotaMes9: af.empenho.dotacao.cotaMes9.toNumber(),
              cotaMes10: af.empenho.dotacao.cotaMes10.toNumber(),
              cotaMes11: af.empenho.dotacao.cotaMes11.toNumber(),
              cotaMes12: af.empenho.dotacao.cotaMes12.toNumber(),
            }
          : null,
        liquidacoes: af.empenho.liquidacoes.map((liq) => ({
          ...liq,
          valorTotal: liq.valorTotal.toNumber(),
        })),
        empenhos_anulacoes: af.empenho.empenhos_anulacoes.map((anul) => ({
          ...anul,
          valorAnulado: anul.valorAnulado.toNumber(),
        })),
      },
    };

    return { data: returnObj };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao obter AF.' };
  }
};

export const listarAFsPorEmpenho = async (idEmpenho: number) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const afs = await prisma.afs.findMany({
      where: { idEmpenho, ativo: true },
      orderBy: { numero: 'asc' },
    });

    return { data: afs };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao listar AFs do empenho.' };
  }
};

export const calcularSaldoAF = async (idAF: number) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const af = await prisma.afs.findUnique({
      where: { id: idAF },
      include: {
        empenho: {
          include: {
            liquidacoes: {
              where: { ativo: true },
              select: { valorTotal: true },
            },
          },
        },
      },
    });

    if (!af) return { error: 'AF não encontrada.' };

    // Calcular total de liquidações relacionadas a esta AF
    const valorLiquidado = af.empenho.liquidacoes.reduce(
      (acc, liquidacao) =>
        acc.add(
          currency(liquidacao.valorTotal.toNumber(), currencyOptionsNoSymbol)
        ),
      currency(0, currencyOptionsNoSymbol)
    );

    const saldoDisponivel = currency(
      af.valorTotal.toNumber(),
      currencyOptionsNoSymbol
    ).subtract(valorLiquidado);

    return {
      data: {
        valorTotal: af.valorTotal.toNumber(),
        valorLiquidado: valorLiquidado.value,
        saldoDisponivel: saldoDisponivel.value,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao calcular saldo da AF.' };
  }
};

export const criarAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsedData = criarAFSchema.safeParse(params);
  if (!parsedData.success) return { error: 'Dados inválidos.' };

  const { idEmpenho, resumo, obs, valorTotal, dataEmissao, dataVencimento } =
    parsedData.data;

  try {
    // Validar empenho
    const empenho = await prisma.empenhos.findUnique({
      where: { id: idEmpenho, ativo: true },
    });

    if (!empenho) return { error: 'Empenho não encontrado.' };
    if (
      empenho.status !== StatusEmpenho.EMPENHADO &&
      empenho.status !== StatusEmpenho.ANULADO
    ) {
      return {
        error: 'Apenas empenhos ativos ou parcialmente anulados podem ter AFs.',
      };
    }

    // Validar limite de 4 AFs por empenho
    const totalAFs = await prisma.afs.count({
      where: { idEmpenho, ativo: true },
    });

    if (totalAFs >= 4) {
      return { error: 'Este empenho já possui o máximo de 4 AFs permitidas.' };
    }

    // Calcular valor total anulado
    const anulacoes = await prisma.empenhos_anulacoes.aggregate({
      _sum: {
        valorAnulado: true,
      },
      where: {
        idEmpenho: empenho.id,
        ativo: true,
      },
    });

    const valorAnulado = currency(
      anulacoes._sum.valorAnulado?.toNumber() || 0,
      currencyOptionsNoSymbol
    );

    // Validar disponibilidade de saldo
    const saldoAFs = await calcularTotalAFsPorEmpenho(idEmpenho);
    if (saldoAFs.error) return saldoAFs;

    const valorTotalAFs = currency(
      saldoAFs.data?.totalAFs || 0,
      currencyOptionsNoSymbol
    );
    const valorMaximoAF = currency(
      empenho.valorTotal.toNumber(),
      currencyOptionsNoSymbol
    )
      .subtract(valorAnulado)
      .subtract(valorTotalAFs);

    if (
      currency(valorTotal, currencyOptionsNoSymbol).value > valorMaximoAF.value
    ) {
      return { error: 'Valor da AF excede o saldo disponível do empenho.' };
    }

    // Gerar próximo número sequencial
    const proximoNumero = await gerarProximoNumeroAF(empenho.exercicio);

    const resultado = await prisma.$transaction(async (tx) => {
      const novaAF = await tx.afs.create({
        data: {
          exercicio: empenho.exercicio,
          numero: proximoNumero,
          idEmpenho,
          resumo,
          obs,
          valorTotal,
          status: StatusAF.ATIVA,
          dataEmissao,
          dataVencimento,
        },
        include: {
          empenho: {
            include: {
              dotacao: {
                select: {
                  despesa: true,
                  desc: true,
                  secretaria: {
                    select: {
                      id: true,
                      nome: true,
                    },
                  },
                  departamento: {
                    select: {
                      id: true,
                      nome: true,
                    },
                  },
                },
              },
              fornecedor: {
                select: {
                  id: true,
                  nome: true,
                },
              },
            },
          },
        },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF: novaAF.id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.CRIAR_AF,
          ip: (await obterIpUsuarioConectado()).data,
          obs: `AF criada: ${novaAF.numero}/${novaAF.exercicio}`,
        },
      });

      // Convert Decimal fields to Number before returning
      return {
        ...novaAF,
        valorTotal: novaAF.valorTotal.toNumber(),
        empenho: {
          ...novaAF.empenho,
          valorTotal: novaAF.empenho.valorTotal.toNumber(),
        },
      };
    });

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/empenhos/visualizar/${idEmpenho}`);

    return { data: resultado };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao criar AF.' };
  }
};

export const editarAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsedData = editarAFSchema.safeParse(params);
  if (!parsedData.success) return { error: 'Dados inválidos.' };

  const { id, resumo, obs, valorTotal, dataEmissao, dataVencimento } =
    parsedData.data;

  try {
    const afAtual = await prisma.afs.findUnique({
      include: {
        empenho: true,
      },
      where: { id, ativo: true },
    });

    if (!afAtual) return { error: 'AF não encontrada.' };
    if (afAtual.status !== StatusAF.ATIVA) {
      return { error: 'Apenas AFs ativas podem ser editadas.' };
    }

    // Calcular valor total anulado
    const anulacoes = await prisma.empenhos_anulacoes.aggregate({
      _sum: {
        valorAnulado: true,
      },
      where: {
        idEmpenho: afAtual.empenho.id,
        ativo: true,
      },
    });

    const valorAnulado = currency(
      anulacoes._sum.valorAnulado?.toNumber() || 0,
      currencyOptionsNoSymbol
    );

    // Validar disponibilidade de saldo
    const saldoAFs = await calcularTotalAFsPorEmpenho(afAtual.idEmpenho);
    if (saldoAFs.error) return saldoAFs;

    const valorTotalAFs = currency(
      saldoAFs.data?.totalAFs || 0,
      currencyOptionsNoSymbol
    ).subtract(
      currency(afAtual.valorTotal.toNumber(), currencyOptionsNoSymbol)
    );
    const valorMaximoAF = currency(
      afAtual.empenho.valorTotal.toNumber(),
      currencyOptionsNoSymbol
    )
      .subtract(valorAnulado)
      .subtract(valorTotalAFs);

    if (
      currency(valorTotal, currencyOptionsNoSymbol).value > valorMaximoAF.value
    ) {
      return { error: 'Valor da AF excede o saldo disponível do empenho.' };
    }

    const resultado = await prisma.$transaction(async (tx) => {
      const afAtualizada = await tx.afs.update({
        where: { id },
        data: {
          resumo,
          obs,
          valorTotal,
          dataEmissao,
          dataVencimento,
        },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.ALTERAR_AF,
          ip: (await obterIpUsuarioConectado()).data,
          obs: `AF alterada: ${afAtualizada.numero}/${afAtualizada.exercicio}`,
        },
      });

      return afAtualizada;
    });

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/empenhos/visualizar/${afAtual.idEmpenho}`);

    return { data: resultado, error: undefined };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao editar AF.' };
  }
};

export const cancelarAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsedData = cancelarAFSchema.safeParse(params);
  if (!parsedData.success) return { error: 'Dados inválidos.' };

  const { id, motivo } = parsedData.data;

  try {
    const afAtual = await prisma.afs.findUnique({
      where: { id, ativo: true },
    });

    if (!afAtual) return { error: 'AF não encontrada.' };
    if (afAtual.status !== StatusAF.ATIVA) {
      return { error: 'Apenas AFs ativas podem ser canceladas.' };
    }

    const resultado = await prisma.$transaction(async (tx) => {
      const afCancelada = await tx.afs.update({
        where: { id },
        data: {
          status: StatusAF.CANCELADA,
          ativo: false,
          dataCancelamento: new Date(), // Registra data de cancelamento
        },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.CANCELAR_AF,
          ip: (await obterIpUsuarioConectado()).data,
          obs: `AF cancelada: ${motivo}`,
        },
      });

      return afCancelada;
    });

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/empenhos/visualizar/${afAtual.idEmpenho}`);

    return { data: resultado };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao cancelar AF.' };
  }
};

export const reativarAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsedData = reativarAFSchema.safeParse(params);
  if (!parsedData.success) return { error: 'Dados inválidos.' };

  const { id, motivo } = parsedData.data;

  try {
    const afAtual = await prisma.afs.findUnique({
      where: { id },
    });

    if (!afAtual) return { error: 'AF não encontrada.' };
    if (afAtual.status !== StatusAF.CANCELADA) {
      return { error: 'Apenas AFs canceladas podem ser reativadas.' };
    }

    const resultado = await prisma.$transaction(async (tx) => {
      const afReativada = await tx.afs.update({
        where: { id },
        data: {
          status: StatusAF.ATIVA,
          ativo: true,
          dataReativacao: new Date(), // Registra data de reativação
        },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.REATIVAR_AF,
          ip: (await obterIpUsuarioConectado()).data,
          obs: `AF reativada: ${motivo}`,
        },
      });

      return afReativada;
    });

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/empenhos/visualizar/${afAtual.idEmpenho}`);

    return { data: resultado };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao reativar AF.' };
  }
};

export const atualizarDatasAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsedData = atualizarDatasAFSchema.safeParse(params);
  if (!parsedData.success) return { error: 'Dados inválidos.' };

  const { id, dataEmissao, dataVencimento, motivo } = parsedData.data;

  try {
    const afAtual = await prisma.afs.findUnique({
      where: { id, ativo: true },
    });

    if (!afAtual) return { error: 'AF não encontrada.' };
    if (afAtual.status !== StatusAF.ATIVA) {
      return { error: 'Apenas AFs ativas podem ter datas atualizadas.' };
    }

    const resultado = await prisma.$transaction(async (tx) => {
      const afAtualizada = await tx.afs.update({
        where: { id },
        data: {
          dataEmissao,
          dataVencimento,
        },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.ATUALIZAR_DATAS,
          ip: (await obterIpUsuarioConectado()).data,
          obs: `Datas atualizadas: ${motivo}`,
        },
      });

      return afAtualizada;
    });

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/empenhos/visualizar/${afAtual.idEmpenho}`);

    return { data: resultado };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao atualizar datas da AF.' };
  }
};

export const marcarAFComoUtilizada = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };
  const resultPermissao = await temPermissao(parametrosPermissao);
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  const parsedData = marcarAFComoUtilizadaSchema.safeParse(params);
  if (!parsedData.success) return { error: 'Dados inválidos.' };

  const { id, observacao } = parsedData.data;

  try {
    const afAtual = await prisma.afs.findUnique({
      where: { id, ativo: true },
    });

    if (!afAtual) return { error: 'AF não encontrada.' };
    if (afAtual.status !== StatusAF.ATIVA) {
      return { error: 'Apenas AFs ativas podem ser marcadas como utilizadas.' };
    }

    const resultado = await prisma.$transaction(async (tx) => {
      const afUtilizada = await tx.afs.update({
        where: { id },
        data: {
          status: StatusAF.UTILIZADA,
          dataUtilizacao: new Date(), // Registra data de utilização
        },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.MARCAR_UTILIZADA,
          ip: (await obterIpUsuarioConectado()).data,
          obs: `AF marcada como utilizada: ${observacao || 'Sem observação'}`,
        },
      });

      return afUtilizada;
    });

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/empenhos/visualizar/${afAtual.idEmpenho}`);

    return { data: resultado };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao marcar AF como utilizada.' };
  }
};

// Funções auxiliares
const gerarProximoNumeroAF = async (exercicio: number): Promise<number> => {
  const ultimaAF = await prisma.afs.findFirst({
    where: { exercicio },
    orderBy: { numero: 'desc' },
  });

  return (ultimaAF?.numero || 0) + 1;
};

const calcularTotalAFsPorEmpenho = async (idEmpenho: number) => {
  try {
    const afs = await prisma.afs.findMany({
      where: { idEmpenho, ativo: true, status: StatusAF.ATIVA },
      select: { valorTotal: true },
    });

    if (!afs) {
      return { error: 'AFs não encontradas.' };
    }

    const totalAFs = afs.reduce(
      (acc, af) =>
        acc.add(currency(af.valorTotal.toNumber(), currencyOptionsNoSymbol)),
      currency(0, currencyOptionsNoSymbol)
    );

    return { data: { totalAFs: totalAFs.value } };
  } catch (e) {
    console.log(e);
    return { error: 'Erro ao calcular total de AFs do empenho.' };
  }
};

export const validarNumeroAF = async (params: unknown) => {
  const parsedData = numeroAFSchema.safeParse(params);
  if (!parsedData.success) return { error: 'Parâmetros inválidos.' };

  const { numero, exercicio } = parsedData.data;

  try {
    const afExistente = await prisma.afs.findFirst({
      where: { numero, exercicio, ativo: true },
    });

    return { data: { disponivel: !afExistente } };
  } catch (e) {
    console.log(e);
    return { error: 'Erro ao validar número da AF.' };
  }
};

export const listarEmpenhosParaAF = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    const empenhos = await prisma.empenhos.findMany({
      where: {
        exercicio: resultPermissao.exercicio,
        status: StatusEmpenho.EMPENHADO,
        ativo: true,
      },
      select: {
        id: true,
        numero: true,
        exercicio: true,
        resumo: true,
        valorTotal: true,
        // Add monthly quota fields for accurate balance calculation
        usarMes1: true,
        usarMes2: true,
        usarMes3: true,
        usarMes4: true,
        usarMes5: true,
        usarMes6: true,
        usarMes7: true,
        usarMes8: true,
        usarMes9: true,
        usarMes10: true,
        usarMes11: true,
        usarMes12: true,
        fornecedor: {
          select: {
            id: true,
            nome: true,
          },
        },
        dotacao: {
          select: {
            despesa: true,
          },
        },
        liquidacoes: {
          where: { ativo: true },
          select: { valorTotal: true },
        },
        afs: {
          where: { ativo: true, status: StatusAF.ATIVA },
          select: { valorTotal: true },
        },
      },
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
    });

    const empenhosComSaldo = empenhos
      .map((empenho) => {
        const valorLiquidado = empenho.liquidacoes.reduce(
          (acc, liq) => acc.add(liq.valorTotal.toNumber()),
          currency(0, currencyOptionsNoSymbol)
        );

        const valorAFs = empenho.afs.reduce(
          (acc, af) => acc.add(af.valorTotal.toNumber()),
          currency(0, currencyOptionsNoSymbol)
        );

        // FIXED: Use sum of monthly quotas instead of valorTotal (matching legacy logic)
        const valorOriginalEmpenho = currency(0, currencyOptionsNoSymbol)
          .add(empenho.usarMes1.toNumber())
          .add(empenho.usarMes2.toNumber())
          .add(empenho.usarMes3.toNumber())
          .add(empenho.usarMes4.toNumber())
          .add(empenho.usarMes5.toNumber())
          .add(empenho.usarMes6.toNumber())
          .add(empenho.usarMes7.toNumber())
          .add(empenho.usarMes8.toNumber())
          .add(empenho.usarMes9.toNumber())
          .add(empenho.usarMes10.toNumber())
          .add(empenho.usarMes11.toNumber())
          .add(empenho.usarMes12.toNumber());

        const saldoDisponivel = valorOriginalEmpenho
          .subtract(valorLiquidado.value)
          .subtract(valorAFs.value);

        return {
          ...empenho,
          valorTotal: empenho.valorTotal.toNumber(),
          saldoDisponivel: saldoDisponivel.value,
        };
      })
      .filter((empenho) => empenho.saldoDisponivel > 0); // Only show empenhos with available balance

    return {
      data: {
        empenhos: empenhosComSaldo,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao obter empenhos disponíveis.',
    };
  }
};

export const obterEmpenhoParaAF = async (idEmpenho: number) => {
  try {
    const empenho = await prisma.empenhos.findFirst({
      where: { id: idEmpenho, ativo: true },
      include: {
        fornecedor: true,
        dotacao: {
          include: {
            secretaria: true,
            departamento: true,
          },
        },
        liquidacoes: {
          where: { ativo: true },
          select: { valorTotal: true },
        },
        afs: {
          where: { ativo: true, status: StatusAF.ATIVA },
          select: { valorTotal: true },
        },
      },
    });

    if (!empenho) {
      return { error: 'Empenho não encontrado.' };
    }

    // Calcular saldo disponível
    const valorLiquidado = empenho.liquidacoes.reduce(
      (acc, liquidacao) =>
        acc.add(
          currency(liquidacao.valorTotal.toNumber(), currencyOptionsNoSymbol)
        ),
      currency(0, currencyOptionsNoSymbol)
    );

    const valorAFs = empenho.afs.reduce(
      (acc, af) =>
        acc.add(currency(af.valorTotal.toNumber(), currencyOptionsNoSymbol)),
      currency(0, currencyOptionsNoSymbol)
    );

    const saldoDisponivel = currency(
      empenho.valorTotal.toNumber(),
      currencyOptionsNoSymbol
    )
      .subtract(valorLiquidado)
      .subtract(valorAFs);

    return {
      data: {
        empenho: {
          ...empenho,
          valorTotal: empenho.valorTotal.toNumber(),
          usarMes1: empenho.usarMes1.toNumber(),
          usarMes2: empenho.usarMes2.toNumber(),
          usarMes3: empenho.usarMes3.toNumber(),
          usarMes4: empenho.usarMes4.toNumber(),
          usarMes5: empenho.usarMes5.toNumber(),
          usarMes6: empenho.usarMes6.toNumber(),
          usarMes7: empenho.usarMes7.toNumber(),
          usarMes8: empenho.usarMes8.toNumber(),
          usarMes9: empenho.usarMes9.toNumber(),
          usarMes10: empenho.usarMes10.toNumber(),
          usarMes11: empenho.usarMes11.toNumber(),
          usarMes12: empenho.usarMes12.toNumber(),
          // Convert dotacao Decimal fields
          dotacao: empenho.dotacao
            ? {
                ...empenho.dotacao,
                valorInicial: empenho.dotacao.valorInicial.toNumber(),
                cotaReducaoInicial:
                  empenho.dotacao.cotaReducaoInicial.toNumber(),
                valorLiberado: empenho.dotacao.valorLiberado.toNumber(),
                cotaReducao: empenho.dotacao.cotaReducao.toNumber(),
                suplementacao: empenho.dotacao.suplementacao.toNumber(),
                anulacao: empenho.dotacao.anulacao.toNumber(),
                valorAtual: empenho.dotacao.valorAtual.toNumber(),
                cotaMes1: empenho.dotacao.cotaMes1.toNumber(),
                cotaMes2: empenho.dotacao.cotaMes2.toNumber(),
                cotaMes3: empenho.dotacao.cotaMes3.toNumber(),
                cotaMes4: empenho.dotacao.cotaMes4.toNumber(),
                cotaMes5: empenho.dotacao.cotaMes5.toNumber(),
                cotaMes6: empenho.dotacao.cotaMes6.toNumber(),
                cotaMes7: empenho.dotacao.cotaMes7.toNumber(),
                cotaMes8: empenho.dotacao.cotaMes8.toNumber(),
                cotaMes9: empenho.dotacao.cotaMes9.toNumber(),
                cotaMes10: empenho.dotacao.cotaMes10.toNumber(),
                cotaMes11: empenho.dotacao.cotaMes11.toNumber(),
                cotaMes12: empenho.dotacao.cotaMes12.toNumber(),
              }
            : null,
          // Convert liquidacoes Decimal fields
          liquidacoes: empenho.liquidacoes.map((liquidacao) => ({
            ...liquidacao,
            valorTotal: liquidacao.valorTotal.toNumber(),
          })),
          // Convert afs Decimal fields
          afs: empenho.afs.map((af) => ({
            ...af,
            valorTotal: af.valorTotal.toNumber(),
          })),
        },
        saldoDisponivel: saldoDisponivel.value,
      },
    };
  } catch (e) {
    console.log(e);
    return { error: 'Erro ao obter empenho para AF.' };
  }
};

// ==================== GESTÃO DE DOCUMENTOS AF ====================

export const uploadDocumentoAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);
  if (resultPermissao.error) return resultPermissao;

  const result = uploadDocumentoAFSchema.safeParse(params);
  if (!result.success) {
    return { error: result.error.errors[0].message };
  }

  const { idAF, tipoDocumento, nomeArquivo, tamanho, caminhoArquivo } =
    result.data;

  try {
    // Verificar se a AF existe e está ativa
    const af = await prisma.afs.findFirst({
      where: { id: idAF, ativo: true },
    });

    if (!af) {
      return { error: 'AF não encontrada.' };
    }

    const ip = (await obterIpUsuarioConectado()).data;

    const documento = await prisma.$transaction(async (tx) => {
      // Criar registro do documento
      const novoDocumento = await tx.afs_documentos.create({
        data: {
          idAF,
          tipoDocumento,
          nomeArquivo,
          caminhoArquivo,
          tamanho,
        },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.UPLOAD_DOCUMENTO,
          ip,
          obs: `Upload de documento: ${nomeArquivo} (${tipoDocumento})`,
        },
      });

      return novoDocumento;
    });

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/af/visualizar/${idAF}`);

    return { data: documento };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao fazer upload do documento.' };
  }
};

export const removerDocumentoAF = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);
  if (resultPermissao.error) return resultPermissao;

  const result = removerDocumentoAFSchema.safeParse(params);
  if (!result.success) {
    return { error: result.error.errors[0].message };
  }

  const { idAF, idDocumento } = result.data;

  try {
    // Verificar se o documento existe
    const documento = await prisma.afs_documentos.findFirst({
      where: { id: idDocumento, idAF, ativo: true },
      include: { af: true },
    });

    if (!documento) {
      return { error: 'Documento não encontrado.' };
    }

    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      // Marcar documento como inativo
      await tx.afs_documentos.update({
        where: { id: idDocumento },
        data: { ativo: false },
      });

      // Registrar auditoria
      await tx.afs_audit.create({
        data: {
          idAF,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaAFs.REMOVER_DOCUMENTO,
          ip,
          obs: `Remoção de documento: ${documento.nomeArquivo}`,
        },
      });
    });

    // Remover arquivo do Supabase
    try {
      const supabase = createSuperClient();
      await supabase.storage
        .from('reservas')
        .remove([documento.caminhoArquivo]);
    } catch (storageError) {
      console.log('Erro ao remover arquivo do storage:', storageError);
      // Não falhar a operação se houver erro no storage
    }

    revalidatePath(ROUTE);
    revalidatePath(`/movimento/af/visualizar/${idAF}`);

    return { data: true };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao remover documento.' };
  }
};

export const listarDocumentosAF = async (idAF: number) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);
  if (resultPermissao.error) return resultPermissao;

  try {
    const documentos = await prisma.afs_documentos.findMany({
      where: { idAF, ativo: true },
      orderBy: { dataUpload: 'desc' },
    });

    return { data: documentos };
  } catch (e) {
    console.log(e);
    return { error: 'Erro ao listar documentos da AF.' };
  }
};

export const obterUrlDocumentoAF = async (idDocumento: number) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);
  if (resultPermissao.error) return resultPermissao;

  try {
    const documento = await prisma.afs_documentos.findFirst({
      where: { id: idDocumento, ativo: true },
    });

    if (!documento) {
      return { error: 'Documento não encontrado.' };
    }

    // Gerar URL assinada temporária (válida por 1 hora)
    const supabase = createSuperClient();
    const { data, error } = await supabase.storage
      .from('reservas')
      .createSignedUrl(documento.caminhoArquivo, 3600);

    if (error) {
      return { error: 'Erro ao gerar URL do documento.' };
    }

    return {
      data: { url: data.signedUrl, nomeArquivo: documento.nomeArquivo },
    };
  } catch (e) {
    console.log(e);
    return { error: 'Erro ao obter URL do documento.' };
  }
};
