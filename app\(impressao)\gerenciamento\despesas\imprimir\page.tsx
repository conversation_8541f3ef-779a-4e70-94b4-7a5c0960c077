'use server';
import { z } from 'zod';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioDespesas } from '@/components/relatorios/despesas';
import { obterDotacoesParaRelatorio } from '@/lib/database/relatorios/dotacoes';
import {
  obterRelatorioDespesa,
  obterRelatorioDespesaPessoal,
} from '@/lib/database/relatorios/despesas';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function RelatorioDespesasPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const currSearchParams = await searchParams;
  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }
  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();
  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }
  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const params = {
    exercicio: currSearchParams.exercicio
      ? parseInt(currSearchParams.exercicio as string)
      : result.exercicio,
    idSecretaria: currSearchParams.secretaria
      ? parseInt(currSearchParams.secretaria as string)
      : undefined,
    idDepartamento: currSearchParams.departamento
      ? parseInt(currSearchParams.departamento as string)
      : undefined,
    idSubdepartamento: currSearchParams.subdepartamento
      ? parseInt(currSearchParams.subdepartamento as string)
      : undefined,
    bearer,
  };

  const tipo =
    (currSearchParams.tipo as
      | 'detalhado'
      | 'resumido'
      | 'despesa-detalhado'
      | 'valores'
      | 'pessoal') || 'detalhado';
  const despesa = currSearchParams.despesa
    ? parseInt(currSearchParams.despesa as string)
    : undefined;

  const filtros = {
    exercicio: params.exercicio,
    despesa,
    secretaria: currSearchParams.secretariaNome as string,
    departamento: currSearchParams.departamentoNome as string,
    subdepartamento: currSearchParams.subdepartamentoNome as string,
  };

  // Buscar dados conforme o tipo de relatório
  let reportContent;

  switch (tipo) {
    case 'despesa-detalhado':
    case 'valores': {
      const despesasResult = await obterRelatorioDespesa({
        ...params,
        tipo: tipo === 'despesa-detalhado' ? '1' : '2',
        despesa,
      });

      if ('error' in despesasResult && despesasResult.error) {
        return <ErrorAlert error={despesasResult.error} />;
      }

      const titulo =
        tipo === 'despesa-detalhado'
          ? 'Relatório Detalhado de Despesas'
          : 'Relatório de Valores de Despesas';

      const despesasData = 'data' in despesasResult ? despesasResult.data : [];
      reportContent = (
        <RelatorioDespesas
          despesas={despesasData as any[]}
          titulo={titulo}
          filtros={filtros}
          tipo={tipo}
        />
      );
      break;
    }

    case 'pessoal': {
      const pessoalResult = await obterRelatorioDespesaPessoal(params);

      if ('error' in pessoalResult && pessoalResult.error) {
        return <ErrorAlert error={pessoalResult.error} />;
      }

      reportContent = (
        <RelatorioDespesas
          despesaPessoal={'data' in pessoalResult ? pessoalResult.data : []}
          titulo='Relatório de Despesa Pessoal'
          filtros={filtros}
          tipo={tipo}
        />
      );
      break;
    }

    default: {
      // Relatórios originais (detalhado e resumido)
      const dotacoesResult = await obterDotacoesParaRelatorio(params);

      if ('error' in dotacoesResult && dotacoesResult.error) {
        return <ErrorAlert error={dotacoesResult.error} />;
      }

      if (!('data' in dotacoesResult) || !dotacoesResult.data) {
        return <ErrorAlert error='Falha ao obter dotações.' />;
      }

      const titulo =
        tipo === 'resumido'
          ? 'Relatório Resumido de Dotações'
          : 'Relatório Detalhado de Dotações';

      reportContent = (
        <RelatorioDespesas
          dotacoes={dotacoesResult.data}
          titulo={titulo}
          filtros={filtros}
          tipo={tipo}
        />
      );
      break;
    }
  }

  return (
    <>
      {reportContent}
      <ClientCompletionTrigger />
    </>
  );
}
