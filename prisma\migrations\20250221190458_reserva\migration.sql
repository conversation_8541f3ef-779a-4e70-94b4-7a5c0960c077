-- CreateTable
CREATE TABLE "reserva" (
    "id" SERIAL NOT NULL,
    "idGestor" INTEGER NOT NULL,
    "idDotacao" INTEGER NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "idSecretario" INTEGER NOT NULL,
    "idSecretaria" INTEGER NOT NULL,
    "idDepto" INTEGER,
    "idSubDepto" INTEGER,
    "idEconomica" INTEGER NOT NULL,
    "idFuncional" INTEGER NOT NULL,
    "resumo" TEXT NOT NULL,
    "obs" TEXT NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "usarMes1" DECIMAL(18,2) NOT NULL,
    "usarMes2" DECIMAL(18,2) NOT NULL,
    "usarMes3" DECIMAL(18,2) NOT NULL,
    "usarMes4" DECIMAL(18,2) NOT NULL,
    "usarMes5" DECIMAL(18,2) NOT NULL,
    "usarMes6" DECIMAL(18,2) NOT NULL,
    "usarMes7" DECIMAL(18,2) NOT NULL,
    "usarMes8" DECIMAL(18,2) NOT NULL,
    "usarMes9" DECIMAL(18,2) NOT NULL,
    "usarMes10" DECIMAL(18,2) NOT NULL,
    "usarMes11" DECIMAL(18,2) NOT NULL,
    "usarMes12" DECIMAL(18,2) NOT NULL,
    "usarTotal" DECIMAL(18,2) NOT NULL,
    "pedidoPlurianual" BOOLEAN NOT NULL DEFAULT false,
    "pluUsarMes1" DECIMAL(18,2) NOT NULL,
    "pluUsarMes2" DECIMAL(18,2) NOT NULL,
    "pluUsarMes3" DECIMAL(18,2) NOT NULL,
    "pluUsarMes4" DECIMAL(18,2) NOT NULL,
    "pluUsarMes5" DECIMAL(18,2) NOT NULL,
    "pluUsarMes6" DECIMAL(18,2) NOT NULL,
    "pluUsarMes7" DECIMAL(18,2) NOT NULL,
    "pluUsarMes8" DECIMAL(18,2) NOT NULL,
    "pluUsarMes9" DECIMAL(18,2) NOT NULL,
    "pluUsarMes10" DECIMAL(18,2) NOT NULL,
    "pluUsarMes11" DECIMAL(18,2) NOT NULL,
    "pluUsarMes12" DECIMAL(18,2) NOT NULL,
    "pluUsarTotal" DECIMAL(18,2) NOT NULL,
    "cancelado" BOOLEAN NOT NULL DEFAULT false,
    "motivoCancelamento" TEXT,
    "dataCancelamento" TIMESTAMP(3),

    CONSTRAINT "reserva_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idGestor_fkey" FOREIGN KEY ("idGestor") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idDotacao_fkey" FOREIGN KEY ("idDotacao") REFERENCES "dotacoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idSecretario_fkey" FOREIGN KEY ("idSecretario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idSecretaria_fkey" FOREIGN KEY ("idSecretaria") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idDepto_fkey" FOREIGN KEY ("idDepto") REFERENCES "departamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idSubDepto_fkey" FOREIGN KEY ("idSubDepto") REFERENCES "subdepartamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idEconomica_fkey" FOREIGN KEY ("idEconomica") REFERENCES "economicas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "reserva" ADD CONSTRAINT "reserva_idFuncional_fkey" FOREIGN KEY ("idFuncional") REFERENCES "funcionais"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
