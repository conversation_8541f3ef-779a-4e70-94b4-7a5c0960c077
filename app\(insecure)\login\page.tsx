'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState, useEffect } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { toastAlgoDeuErrado } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { isAuth, login } from '@/lib/supabase/actions';
import { SignInWithPasswordCredentials } from '@supabase/supabase-js';
import {
  atualizarExercicioUsuarioConectado,
  obterNomeUsuarioConectado,
  salvarDotacoesUsuarioConectado,
  verificarSeUsuarioAtivoPorEmail,
} from '@/lib/database/usuarios';
import Link from 'next/link';
import { EXERCICIO_INICIAL } from '@/lib/consts';

const formSchema = z.object({
  email: z.string().min(2).max(50),
  password: z.string().min(2),
  exercicio: z.number().min(2008).max(2280).int(),
});

export default function LoginDialog() {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);
  const [isOpen] = useState(true);

  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
      exercicio: Number(
        new Date().toLocaleDateString('pt-br', { year: 'numeric' })
      ),
    },
  });
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setLoading(true);
      router.prefetch('/dashboard');
      router.prefetch('/mfa');
      const credentials: SignInWithPasswordCredentials = {
        email: values.email,
        password: values.password,
      };
      const res = await login(credentials);
      if (res.error) {
        setLoading(false);
        if (JSON.parse(res.error).status === 400) {
          toast.error('Usuário e/ou senha inválidos.');
        } else {
          toast.error('Algo deu errado, por favor tente novamente mais tarde.');
        }
      } else {
        //Paralelismo não funciona no client
        const authed = await isAuth();
        if (!authed) {
          router.replace('/mfa');
          return;
        }
        await atualizarExercicioUsuarioConectado(values.exercicio);
        await salvarDotacoesUsuarioConectado();
        const nome = await obterNomeUsuarioConectado();

        if (nome.error || !nome.data || nome.data === '') {
          return router.replace('/cadastro/novo');
        }

        const usuarioAtivo = await verificarSeUsuarioAtivoPorEmail({
          email: values.email,
        });
        if (!usuarioAtivo.data) {
          return router.replace('/login/desativado');
        }

        router.replace('/dashboard');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const listaExercicios: number[] = [];
  for (
    let i = Number(
      new Date().toLocaleDateString('pt-br', {
        year: 'numeric',
      })
    );
    i >= EXERCICIO_INICIAL;
    i--
  ) {
    listaExercicios.push(i);
  }

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted && isOpen}>
        <AlertDialogContent
          className='sm:max-w-[425px]'
          onOpenAutoFocus={(e) => {
            e.preventDefault();
            form.setFocus('email');
          }}
        >
          <AlertDialogHeader>
            <AlertDialogTitle>Acesso ao sistema</AlertDialogTitle>
          </AlertDialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className='grid gap-8 py-4'>
                <FormField
                  control={form.control}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input className='col-span-3' {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='password'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Senha</FormLabel>
                      <FormControl>
                        <Input
                          type='password'
                          className='col-span-3'
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='exercicio'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Exercício</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={new Date().toLocaleDateString('pt-br', {
                          year: 'numeric',
                        })}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {listaExercicios.map((exercicio) => (
                            <SelectItem key={exercicio} value={`${exercicio}`}>
                              {exercicio}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>
              <AlertDialogFooter className='flex sm:justify-between'>
                <Link
                  href='/recuperar-conta'
                  className='hover:text-primary mt-4 text-left text-xs'
                >
                  Esqueci minha senha
                </Link>
                <Button type='submit' disabled={loading}>
                  {loading ? (
                    <>
                      <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                      Aguarde...
                    </>
                  ) : (
                    <>
                      <Icons.logIn className='mr-2 h-4 w-4' /> Logar
                    </>
                  )}
                </Button>
              </AlertDialogFooter>
            </form>
          </Form>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
