import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { obterLiquidacao } from '@/lib/database/movimento/liquidacoes';
import { FormEditarLiquidacao } from '@/components/movimento/liquidacoes/formEditarLiquidacao';
import { StatusLiquidacao } from '@/lib/enums';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface EditarLiquidacaoPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({
  params,
}: EditarLiquidacaoPageProps): Promise<Metadata> {
  const { id: idParam } = await params;
  const id = parseInt(idParam);

  if (isNaN(id)) {
    return {
      title: 'Liquidação não encontrada',
    };
  }

  const result = await obterLiquidacao({ id });

  if (result?.error || !result?.data) {
    return {
      title: 'Liquidação não encontrada',
    };
  }

  return {
    title: `Editar Liquidação ${result.data.numero}/${result.data.exercicio}`,
    description: `Editar liquidação ${result.data.numero}/${result.data.exercicio}`,
  };
}

export default async function EditarLiquidacaoPage({
  params,
}: EditarLiquidacaoPageProps) {
  const { id: idParam } = await params;
  const id = parseInt(idParam);

  if (isNaN(id)) {
    notFound();
  }

  const result = await obterLiquidacao({ id });

  if (result?.error || !result?.data) {
    notFound();
  }

  // Check if liquidacao can be edited
  if (result.data.status === StatusLiquidacao.ESTORNADA) {
    notFound();
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>
          Editar Liquidação {result.data.numero}/{result.data.exercicio}
        </PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormEditarLiquidacao liquidacao={result.data} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
