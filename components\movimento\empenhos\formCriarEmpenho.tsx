'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCallback, useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import {
  cn,
  codigoSecretariaMask,
  currencyOptionsNoSymbol,
  economicaMask,
  moneyMask,
  moneyUnmask,
  obterAnoAtual,
  obterMesAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { criarEmpenhoSchema } from '@/lib/validation';
import { Label } from '@/components/ui/label';
import { Fontes } from '@/lib/enums';
import { toast } from 'sonner';
import {
  criarEmpenho,
  listarReservasParaEmpenho,
} from '@/lib/database/movimento/empenhos';
import {
  buscarDespesa,
  buscarReservaParaEmpenho,
} from '@/lib/database/movimento/reservas';
import { listarFornecedores } from '@/lib/database/gerenciamento/fornecedores';
import { ComboboxSelecionarReserva } from './comboboxSelecionarReserva';
import { ComboboxSelecionarFornecedor } from './comboboxSelecionarFornecedor';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import currency from 'currency.js';

export default function FormCriarEmpenho({}: {}) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [loading, setLoading] = useState(false);

  // Create a form-specific schema that excludes valorTotal since it's calculated
  const formSchema = criarEmpenhoSchema._def.schema.omit({ valorTotal: true });
  type FormSchema = z.infer<typeof formSchema>;
  const [despesa, setDespesa] = useState<Awaited<
    ReturnType<typeof buscarDespesa>
  > | null>(null);
  const [reservaData, setReservaData] = useState<Awaited<
    ReturnType<typeof buscarReservaParaEmpenho>
  > | null>(null);
  const [reservasDisponiveis, setReservasDisponiveis] = useState<
    | Required<
        Awaited<ReturnType<typeof listarReservasParaEmpenho>>
      >['data']['reservas']
    | null
  >(null);
  const [fornecedores, setFornecedores] = useState<
    Awaited<ReturnType<typeof listarFornecedores>>['data'] | null
  >(null);
  const [aumentarDecimais, setAumentarDecimais] = useState(false);
  const decimais = aumentarDecimais ? 3 : 2;

  const anoAtual = obterAnoAtual();
  const mesAtual =
    despesa?.data?.exercicio && despesa?.data?.exercicio < anoAtual
      ? 12
      : obterMesAtual();

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      //@ts-ignore
      idReserva: '',
      obs: '',
      resumo: '',
      idFornecedor: 0,
      usarMes1: 0,
      usarMes2: 0,
      usarMes3: 0,
      usarMes4: 0,
      usarMes5: 0,
      usarMes6: 0,
      usarMes7: 0,
      usarMes8: 0,
      usarMes9: 0,
      usarMes10: 0,
      usarMes11: 0,
      usarMes12: 0,
    },
  });

  const [mesesBRL, setMesesBRL] = useState(() => {
    const initialMeses: Record<string, string> = {};
    for (let i = 1; i <= 12; i++) {
      initialMeses[`mes${i}`] = currency(0, currencyOptionsNoSymbol)
        .format()
        .replace(/0$/, '');
    }
    return initialMeses;
  });

  const updateMesBRL = (mesNum: number, value: string) => {
    setMesesBRL((prev) => ({ ...prev, [`mes${mesNum}`]: value }));
  };

  const total = Object.values(mesesBRL).reduce(
    (acc: currency, mesBRL) =>
      acc.add(currency(mesBRL, currencyOptionsNoSymbol)),
    currency(0, currencyOptionsNoSymbol)
  );

  const buscarDespesa_ = useCallback(async () => {
    try {
      setLoading(true);
      const reservaId = form.getValues().idReserva;

      // Load both despesa and reserva data
      const [despesaRes, reservaRes] = await Promise.all([
        buscarDespesa({ id: reservaId }),
        buscarReservaParaEmpenho({ id: reservaId }),
      ]);

      if (despesaRes?.error) {
        setLoading(false);
        toast.error(despesaRes.error);
        setDespesa(null);
        setReservaData(null);
        return;
      }

      if (reservaRes?.error) {
        setLoading(false);
        toast.error(reservaRes.error);
        setReservaData(null);
        // Still set despesa data if available
        setDespesa(despesaRes);
        return;
      }

      setLoading(false);
      setDespesa(despesaRes);
      setReservaData(reservaRes);

      // Auto-populate empenho fields with available balance
      if (reservaRes?.data?.reserva?.saldoDisponivel) {
        const saldo = reservaRes.data.reserva.saldoDisponivel;
        const mesesBRLUpdate: Record<string, string> = {};

        for (let i = 1; i <= 12; i++) {
          const saldoMes = saldo[`mes${i}` as keyof typeof saldo] as number;
          if (saldoMes > 0) {
            mesesBRLUpdate[`mes${i}`] = currency(
              saldoMes,
              currencyOptionsNoSymbol
            )
              .format()
              .replace(/0$/, '');
            form.setValue(
              `usarMes${i}` as `usarMes${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12}`,
              saldoMes
            );
          }
        }

        setMesesBRL((prev) => ({ ...prev, ...mesesBRLUpdate }));
      }
    } catch (error: any) {
      setLoading(false);
      console.log(JSON.stringify(error));
      toast.error(toastAlgoDeuErrado);
    }
  }, [form]);

  // Load available reservas on component mount
  useEffect(() => {
    const loadReservas = async () => {
      const result = await listarReservasParaEmpenho();
      if (result.data) {
        setReservasDisponiveis(result.data.reservas);
      }
    };
    loadReservas();
  }, []);

  // Load fornecedores on component mount
  useEffect(() => {
    const loadFornecedores = async () => {
      const result = await listarFornecedores();
      if (result.data) {
        setFornecedores(result.data);
      }
    };
    loadFornecedores();
  }, []);

  // Handle pre-selected reserva from URL params
  useEffect(() => {
    const preSelectedReserva = searchParams?.get('reserva');
    if (
      preSelectedReserva &&
      reservasDisponiveis &&
      reservasDisponiveis.length > 0
    ) {
      const reservaId = Number(preSelectedReserva);
      form.setValue('idReserva', reservaId);
      buscarDespesa_();
    }
  }, [searchParams, reservasDisponiveis, form, buscarDespesa_]);

  useEffect(() => {
    if (!searchParams?.get('reserva')) {
      form.setFocus('idReserva');
    }
  }, [form, searchParams]);

  const onSubmit = async (values: FormSchema) => {
    try {
      setLoading(true);

      // Calculate valorTotal from monthly values, ensuring it's a valid number
      const calculatedTotal = Object.values(mesesBRL).reduce(
        (acc: currency, mesBRL) =>
          acc.add(currency(mesBRL || '0', currencyOptionsNoSymbol)),
        currency(0, currencyOptionsNoSymbol)
      );
      const valorTotal = isNaN(calculatedTotal.value)
        ? 0
        : calculatedTotal.value;

      // Create the complete submission data
      const submissionData = {
        ...values,
        valorTotal: valorTotal,
      };

      console.log('Submitting empenho with values:', submissionData);
      const res = await criarEmpenho(submissionData);
      console.log('Response from criarEmpenho:', res);

      if (res?.error) {
        setLoading(false);
        toast(res.error, { duration: 10000 });
      } else {
        toast.success('Empenho criado', {
          description: 'Número do empenho: ' + res?.data?.idEmpenho,
          action: {
            label: 'Visualizar',
            onClick: () =>
              router.push(
                '/movimento/empenhos/visualizar/' + res?.data?.idEmpenho
              ),
          },
        });
        router.push('/movimento/empenhos');
      }
    } catch (error) {
      setLoading(false);
      console.error('Error in onSubmit:', error);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const subdepartamento = despesa?.data?.subdepartamento || null;
  const departamento = subdepartamento
    ? despesa?.data?.subdepartamento?.departamento || null
    : despesa?.data?.departamento || null;
  const secretaria = subdepartamento
    ? despesa?.data?.subdepartamento?.departamento?.secretaria || null
    : departamento
      ? despesa?.data?.departamento?.secretaria || null
      : despesa?.data?.secretaria || null;

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex gap-2'>
              <FormField
                control={form.control}
                name='idReserva'
                render={({ field }) => (
                  <FormItem className='flex-1'>
                    <FormLabel className='flex w-full text-left'>
                      Reserva
                    </FormLabel>
                    <FormControl>
                      <ComboboxSelecionarReserva
                        reservaId={field.value || null}
                        setReservaId={(id) => {
                          field.onChange(id);
                          // Auto-load reserva details when selected
                          setTimeout(() => buscarDespesa_(), 100);
                        }}
                        disabled={loading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {despesa && (
              <div className='bg-muted/50 mt-4 rounded-lg p-4'>
                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <div>
                    <Label className='text-sm font-medium'>Despesa</Label>
                    <p className='text-muted-foreground text-sm'>
                      {despesa.data?.despesa}
                    </p>
                  </div>
                  <div>
                    <Label className='text-sm font-medium'>Descrição</Label>
                    <p className='text-muted-foreground text-sm'>
                      {despesa.data?.desc}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className='mt-12 flex flex-wrap gap-4'>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Secretaria
                </FormLabel>
                <Input
                  disabled={true}
                  value={
                    secretaria
                      ? `${codigoSecretariaMask(secretaria.codigo.toString())}.00.00 - ${secretaria.nome}`
                      : ''
                  }
                />
              </FormItem>
              <FormItem className='w-full'>
                <FormLabel className='flex w-full text-left'>
                  Departamento
                </FormLabel>
                <Input
                  disabled={true}
                  value={
                    secretaria && departamento
                      ? `${codigoSecretariaMask(secretaria.codigo.toString())}.${codigoSecretariaMask(departamento.codigo.toString())}.00 - ${departamento.nome}`
                      : ''
                  }
                />
              </FormItem>
              {subdepartamento && (
                <FormItem className='w-full'>
                  <FormLabel className='flex w-full text-left'>
                    Subdepartamento
                  </FormLabel>
                  <Input
                    disabled={true}
                    value={
                      secretaria && departamento && subdepartamento
                        ? `${codigoSecretariaMask(secretaria.codigo.toString())}.${codigoSecretariaMask(departamento.codigo.toString())}.${codigoSecretariaMask(subdepartamento.codigo.toString())} - ${subdepartamento.nome}`
                        : ''
                    }
                  />
                </FormItem>
              )}
            </div>

            <div className='mt-12 flex flex-wrap gap-4'>
              <Label className='w-full text-left'>Econômica</Label>
              <Input
                disabled={true}
                value={
                  despesa
                    ? `${economicaMask(despesa.data?.economica.codigo || '')} - ${despesa.data?.economica.desc}`
                    : ''
                }
              />

              <Label className='w-full text-left'>Funcional</Label>
              <Input
                disabled={true}
                value={
                  despesa
                    ? `${despesa.data?.funcional.codigo || ''} - ${despesa.data?.funcional.desc}`
                    : ''
                }
              />
            </div>

            <div className='mt-12 flex gap-2'>
              <FormItem className='flex w-full flex-wrap'>
                <FormLabel className='w-full text-left'>Fonte</FormLabel>
                <Input
                  disabled={true}
                  value={
                    despesa
                      ? `${despesa.data?.fonte} - ${Fontes[despesa.data?.fonte || 0]}`
                      : ''
                  }
                />
              </FormItem>
              <FormItem className='flex w-full flex-wrap'>
                <FormLabel className='w-full text-left'>
                  Cód. Aplicação
                </FormLabel>
                <Input
                  disabled={true}
                  value={despesa ? `${despesa.data?.codAplicacao}` : ''}
                />
              </FormItem>
            </div>

            <div className='mt-12 flex gap-2'>
              <FormField
                control={form.control}
                name='idFornecedor'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>
                      Fornecedor
                    </FormLabel>
                    <FormControl>
                      <ComboboxSelecionarFornecedor
                        fornecedorId={field.value || null}
                        setFornecedorId={(id) => {
                          field.onChange(id);
                        }}
                        fornecedores={fornecedores || []}
                        disabled={loading || !despesa}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='obs'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>Obs</FormLabel>
                    <FormControl>
                      <Input {...field} className='w-full' />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className='mt-4 flex gap-2'>
              <FormField
                control={form.control}
                name='resumo'
                render={({ field }) => (
                  <FormItem className='flex w-full flex-wrap'>
                    <FormLabel className='flex w-full text-left'>
                      Resumo
                    </FormLabel>
                    <FormControl>
                      <Input {...field} className='w-full' />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Enhanced Reserva Values Display */}
            {reservaData?.data?.reserva && (
              <>
                <Separator className='my-12' />
                <div className='space-y-6'>
                  <div className='flex items-center justify-between'>
                    <span className='text-lg font-semibold'>
                      Valores da Reserva (Referência)
                    </span>
                    <div className='text-muted-foreground text-sm'>
                      Reserva #{reservaData.data.reserva.id} -{' '}
                      {reservaData.data.reserva.resumo}
                    </div>
                  </div>

                  {/* Summary Cards */}
                  <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
                    <div className='rounded-lg border border-blue-200 bg-blue-50 p-4'>
                      <div className='text-sm font-medium text-blue-800'>
                        Valor Original da Reserva
                      </div>
                      <div className='text-lg font-bold text-blue-900'>
                        {currency(
                          reservaData.data.reserva.valoresOriginais.total,
                          currencyOptionsNoSymbol
                        )
                          .format()
                          .replace(/0$/, '')}
                      </div>
                    </div>
                    <div className='rounded-lg border border-orange-200 bg-orange-50 p-4'>
                      <div className='text-sm font-medium text-orange-800'>
                        Já Empenhado
                      </div>
                      <div className='text-lg font-bold text-orange-900'>
                        {currency(
                          reservaData.data.reserva.jaEmpenhado.total,
                          currencyOptionsNoSymbol
                        )
                          .format()
                          .replace(/0$/, '')}
                      </div>
                    </div>
                    <div className='rounded-lg border border-green-200 bg-green-50 p-4'>
                      <div className='text-sm font-medium text-green-800'>
                        Saldo Disponível
                      </div>
                      <div className='text-lg font-bold text-green-900'>
                        {currency(
                          reservaData.data.reserva.saldoDisponivel.total,
                          currencyOptionsNoSymbol
                        )
                          .format()
                          .replace(/0$/, '')}
                      </div>
                    </div>
                  </div>

                  {/* Monthly Breakdown */}
                  <div className='space-y-4'>
                    <div className='text-base font-medium'>
                      Distribuição Mensal:
                    </div>
                    <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                      {Array.from({ length: 12 }, (_, i) => {
                        const mesNum = i + 1;
                        const mesNome = [
                          'Janeiro',
                          'Fevereiro',
                          'Março',
                          'Abril',
                          'Maio',
                          'Junho',
                          'Julho',
                          'Agosto',
                          'Setembro',
                          'Outubro',
                          'Novembro',
                          'Dezembro',
                        ][i];

                        const valorOriginal = reservaData.data.reserva
                          .valoresOriginais[
                          `mes${mesNum}` as keyof typeof reservaData.data.reserva.valoresOriginais
                        ] as number;
                        const jaEmpenhado = reservaData.data.reserva
                          .jaEmpenhado[
                          `mes${mesNum}` as keyof typeof reservaData.data.reserva.jaEmpenhado
                        ] as number;
                        const saldoDisponivel = reservaData.data.reserva
                          .saldoDisponivel[
                          `mes${mesNum}` as keyof typeof reservaData.data.reserva.saldoDisponivel
                        ] as number;

                        // Only show months with values
                        if (valorOriginal === 0) return null;

                        return (
                          <div
                            key={mesNum}
                            className='space-y-2 rounded-lg border bg-gray-50 p-3'
                          >
                            <div className='text-sm font-medium text-gray-700'>
                              {mesNome}
                            </div>
                            <div className='space-y-1 text-xs'>
                              <div className='flex justify-between'>
                                <span className='text-blue-600'>Original:</span>
                                <span className='font-medium text-blue-800'>
                                  {currency(
                                    valorOriginal,
                                    currencyOptionsNoSymbol
                                  )
                                    .format()
                                    .replace(/0$/, '')}
                                </span>
                              </div>
                              {jaEmpenhado > 0 && (
                                <div className='flex justify-between'>
                                  <span className='text-orange-600'>
                                    Empenhado:
                                  </span>
                                  <span className='font-medium text-orange-800'>
                                    {currency(
                                      jaEmpenhado,
                                      currencyOptionsNoSymbol
                                    )
                                      .format()
                                      .replace(/0$/, '')}
                                  </span>
                                </div>
                              )}
                              <div className='flex justify-between border-t pt-1'>
                                <span className='font-medium text-green-600'>
                                  Disponível:
                                </span>
                                <span
                                  className={cn(
                                    'font-bold',
                                    saldoDisponivel > 0
                                      ? 'text-green-800'
                                      : 'text-red-600'
                                  )}
                                >
                                  {currency(
                                    saldoDisponivel,
                                    currencyOptionsNoSymbol
                                  )
                                    .format()
                                    .replace(/0$/, '')}
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      }).filter(Boolean)}
                    </div>
                  </div>
                </div>
              </>
            )}

            <Separator className='my-12' />
            <div className='flex items-center justify-between'>
              <span className='text-lg'>Valores do Empenho</span>
              {reservaData?.data?.reserva && (
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={() => {
                    const saldo = reservaData.data.reserva.saldoDisponivel;
                    const mesesBRLUpdate: Record<string, string> = {};

                    for (let i = 1; i <= 12; i++) {
                      const saldoMes = saldo[
                        `mes${i}` as keyof typeof saldo
                      ] as number;
                      if (saldoMes > 0) {
                        mesesBRLUpdate[`mes${i}`] = currency(
                          saldoMes,
                          currencyOptionsNoSymbol
                        )
                          .format()
                          .replace(/0$/, '');
                        form.setValue(
                          `usarMes${i}` as `usarMes${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12}`,
                          saldoMes
                        );
                      } else {
                        mesesBRLUpdate[`mes${i}`] = currency(
                          0,
                          currencyOptionsNoSymbol
                        )
                          .format()
                          .replace(/0$/, '');
                        form.setValue(
                          `usarMes${i}` as `usarMes${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12}`,
                          0
                        );
                      }
                    }

                    setMesesBRL((prev) => ({ ...prev, ...mesesBRLUpdate }));
                    toast.success(
                      'Valores preenchidos com saldo disponível da reserva'
                    );
                  }}
                  disabled={loading}
                >
                  Usar Saldo Disponível
                </Button>
              )}
            </div>

            <div className='mt-8 flex items-center space-x-2'>
              <Switch
                id='aumentar-decimais'
                checked={aumentarDecimais}
                onCheckedChange={() => {
                  setAumentarDecimais(!aumentarDecimais);
                }}
                disabled={!despesa}
              />
              <Label htmlFor='aumentar-decimais' className='text-lg'>
                Aumentar Decimais
              </Label>
            </div>

            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              {Array.from({ length: 12 }, (_, i) => {
                const mesNum = i + 1;
                const mesNome = [
                  'Janeiro',
                  'Fevereiro',
                  'Março',
                  'Abril',
                  'Maio',
                  'Junho',
                  'Julho',
                  'Agosto',
                  'Setembro',
                  'Outubro',
                  'Novembro',
                  'Dezembro',
                ][i];

                const currentValue = Number(
                  moneyUnmask(mesesBRL[`mes${mesNum}`] || '0')
                );
                const saldoDisponivel =
                  (reservaData?.data?.reserva?.saldoDisponivel?.[
                    `mes${mesNum}` as keyof typeof reservaData.data.reserva.saldoDisponivel
                  ] as number) || 0;
                const isOverLimit = currentValue > saldoDisponivel;
                const hasReservaData = reservaData?.data?.reserva;

                return (
                  <FormField
                    key={mesNum}
                    name={`usarMes${mesNum}`}
                    render={({ field }) => (
                      <FormItem
                        className={cn(
                          'flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs',
                          isOverLimit && hasReservaData
                            ? 'border-red-300 bg-red-50'
                            : '',
                          currentValue > 0 ? 'border-green-300 bg-green-50' : ''
                        )}
                      >
                        <FormLabel className='flex w-full text-left'>
                          {mesNome}
                        </FormLabel>
                        {hasReservaData && (
                          <div className='text-muted-foreground mb-1 w-full text-xs'>
                            Disponível:{' '}
                            {currency(
                              saldoDisponivel,
                              currencyOptionsNoSymbol
                            ).format()}
                          </div>
                        )}
                        <FormLabel className='mt-2 flex w-full text-left'>
                          Usar
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className={cn(
                              'max-w-[150px] text-right',
                              isOverLimit && hasReservaData
                                ? 'border-red-500 text-red-700'
                                : '',
                              currentValue > 0
                                ? 'font-medium text-green-700'
                                : ''
                            )}
                            value={mesesBRL[`mes${mesNum}`]}
                            onChange={(e) => {
                              updateMesBRL(
                                mesNum,
                                moneyMask(e.target.value, decimais)
                              );
                              form.setValue(
                                `usarMes${mesNum}` as `usarMes${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12}`,
                                Number(moneyUnmask(e.target.value)),
                                { shouldDirty: true }
                              );
                            }}
                            onFocus={(e) => e.currentTarget.select()}
                            disabled={!despesa || mesAtual > mesNum}
                          />
                        </FormControl>
                        {isOverLimit && hasReservaData && (
                          <div className='mt-1 w-full text-xs text-red-600'>
                            Excede saldo disponível
                          </div>
                        )}
                      </FormItem>
                    )}
                  />
                );
              })}
            </div>

            <div className='mt-12 flex flex-wrap justify-between gap-6'>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>
                  Saldo Disponível
                </FormLabel>
                <FormControl>
                  <Input
                    className={cn(
                      'text-right text-base disabled:opacity-100',
                      (reservaData?.data?.reserva?.saldoDisponivel?.total ||
                        0) < total.value
                        ? 'text-red-600'
                        : 'text-green-600'
                    )}
                    value={currency(
                      reservaData?.data?.reserva?.saldoDisponivel?.total || 0,
                      currencyOptionsNoSymbol
                    )
                      .format()
                      .replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
              <FormItem className='w-[150px]'>
                <FormLabel className='flex text-left'>Valor Total</FormLabel>
                <FormControl>
                  <Input
                    className={cn(
                      'text-right text-base disabled:opacity-100',
                      total.value === 0 ? 'text-red-600' : 'text-green-600'
                    )}
                    value={total.format().replace(/0$/, '')}
                    disabled={true}
                  />
                </FormControl>
              </FormItem>
            </div>
          </form>
        </Form>
      </div>

      <div className='mt-12 flex w-full justify-between'>
        <Button
          variant={'destructive'}
          disabled={loading}
          onClick={(e) => {
            e.preventDefault();
            router.push('/movimento/empenhos');
          }}
        >
          <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={
            loading ||
            !despesa ||
            total.value === 0 ||
            (reservaData?.data?.reserva &&
              total.value > reservaData.data.reserva.saldoDisponivel.total) ||
            (form.watch('idFornecedor') || 0) < 1
          }
        >
          {loading ? (
            <>
              <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
              Aguarde...
            </>
          ) : (
            <>
              <PlusCircle className='mr-2 h-4 w-4' /> Criar Empenho
            </>
          )}
        </Button>
      </div>
    </>
  );
}
