'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarEmpenhosParaAF } from '@/lib/database/movimento/afs';

interface ComboboxSelecionarEmpenhoProps {
  empenhoId: number | null;
  setEmpenhoId: (id: number) => void;
  onEmpenhoSelect?: (empenho: any) => void;
  disabled?: boolean;
}

export function ComboboxSelecionarEmpenho({
  empenhoId,
  setEmpenhoId,
  onEmpenhoSelect,
  disabled = false,
}: ComboboxSelecionarEmpenhoProps) {
  const [open, setOpen] = React.useState(false);
  const [empenhos, setEmpenhos] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    const carregarEmpenhos = async () => {
      setLoading(true);
      try {
        const result = await listarEmpenhosParaAF();
        if (result.data && result.data.empenhos) {
          setEmpenhos(result.data.empenhos);
        }
      } catch (error) {
        console.error('Erro ao carregar empenhos:', error);
      } finally {
        setLoading(false);
      }
    };

    carregarEmpenhos();
  }, []);

  const empenhoSelecionado = empenhos.find(
    (empenho) => empenho.id === empenhoId
  );

  const handleSelect = (empenho: any) => {
    setEmpenhoId(empenho.id);
    setOpen(false);
    if (onEmpenhoSelect) {
      onEmpenhoSelect(empenho);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
          disabled={loading || disabled}
        >
          {loading
            ? 'Carregando empenhos...'
            : empenhoSelecionado
              ? `Empenho ${empenhoSelecionado.numero}/${empenhoSelecionado.exercicio} - ${empenhoSelecionado.resumo}`
              : 'Selecione um empenho...'}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[600px] p-0'>
        <Command>
          <CommandInput placeholder='Buscar empenho por número, exercício ou descrição...' />
          <CommandList>
            <CommandEmpty>Nenhum empenho encontrado.</CommandEmpty>
            {empenhos.map((empenho) => (
              <CommandItem
                key={empenho.id}
                value={`${empenho.numero} ${empenho.exercicio} ${empenho.resumo} ${empenho.fornecedor?.nome || ''}`}
                onSelect={() => handleSelect(empenho)}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    empenho.id === empenhoId ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className='flex w-full flex-col'>
                  <span className='font-medium'>
                    Empenho {empenho.numero}/{empenho.exercicio} -{' '}
                    {empenho.resumo}
                  </span>
                  <div className='text-muted-foreground flex justify-between text-sm'>
                    <span>Fornecedor: {empenho.fornecedor?.nome || 'N/A'}</span>
                    <span>
                      Saldo: R${' '}
                      {empenho.saldoDisponivel?.toLocaleString('pt-BR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }) || '0,00'}
                    </span>
                  </div>
                  <span className='text-muted-foreground text-xs'>
                    Despesa: {empenho.dotacao?.despesa || 'N/A'}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
