import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  ativarSubdepartamento,
  revalidateSubdepartamentos,
} from '@/lib/database/gerenciamento/subdepartamentos';
import { codigoSecretariaMask, toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema, secretariaEDepartamentoIdsSchema } from '@/lib/validation';
import { Check } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertAtivarSubdepartamento({
  idSubdepartamento,
  nomeSubdepartamento,
  codigoSubdepartamento,
  codigoSecretaria,
  idSecretaria,
  idDepartamento,
  codigoDepartamento,
}: {
  idSubdepartamento: number;
  nomeSubdepartamento: string;
  codigoSubdepartamento: number;
  codigoSecretaria: number;
  idSecretaria: number;
  idDepartamento: number;
  codigoDepartamento: number;
}) {
  const [loading, setLoading] = useState(false);

  const ativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idSubdepartamento,
      };

      const res = await ativarSubdepartamento(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        const data: z.infer<typeof secretariaEDepartamentoIdsSchema> = {
          idDepartamento: idDepartamento,
          idSecretaria: idSecretaria,
        };
        await revalidateSubdepartamentos(data);
        toast.success('Subdepartamento Ativado.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='min-w-[115px] text-green-800'
        >
          <Check className='mr-2 size-4' /> Ativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Ativar Subdepartamento?</AlertDialogTitle>
          <AlertDialogDescription>
            O Subdepartamento{' '}
            <span className='font-bold'>
              {codigoSecretariaMask(codigoSecretaria.toString())}.
              {codigoSecretariaMask(codigoDepartamento.toString())}.
              {codigoSecretariaMask(codigoSubdepartamento.toString())} -{' '}
              {nomeSubdepartamento}
            </span>{' '}
            será ativado.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              ativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Ativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
