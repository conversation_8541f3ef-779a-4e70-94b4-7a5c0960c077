import { siteConfig } from '@/config/site';
import { obterReservaParaRelatorio } from '@/lib/database/movimento/reservas';
import { Unidades } from '@/lib/enums';
import { fontMono } from '@/lib/fonts';
import { toCurrency } from '@/lib/serverUtils';
import {
  cn,
  montarCodigoDepartamento,
  montarCodigoSecretaria,
  splitString,
} from '@/lib/utils';
import { ItemRelatorio } from '@/types/app';
import Image from 'next/image';

export const RelatorioReserva = ({
  reserva,
  assinaturaDigital,
}: {
  reserva: Awaited<ReturnType<typeof obterReservaParaRelatorio>>;
  assinaturaDigital?: boolean;
}) => {
  const charactersPerLine = 56;
  const linesPerPage = 12;

  const dadosReserva = reserva.data!;
  const subdepartamento = dadosReserva.dotacao.subdepartamento || null;
  const departamento = subdepartamento
    ? dadosReserva.dotacao.subdepartamento?.departamento || null
    : dadosReserva.dotacao.departamento || null;
  const secretaria = subdepartamento
    ? dadosReserva.dotacao.subdepartamento?.departamento?.secretaria || null
    : departamento
      ? dadosReserva.dotacao.departamento?.secretaria || null
      : dadosReserva.dotacao.secretaria || null;

  if (!secretaria || !departamento) return null;

  const itemsList: ItemRelatorio[] = [];
  dadosReserva.reserva_itens.forEach((item, i) => {
    const desc = splitString(item.desc, charactersPerLine);
    itemsList.push({
      numero: i + 1,
      quantidade: item.quantidade,
      desc: desc[0].toUpperCase(),
      valor: item.valor,
      unidade: item.unidade,
    });
    for (let i = 1; i < desc.length; i++) {
      itemsList.push({
        numero: null,
        quantidade: null,
        desc: desc[i].toUpperCase(),
        valor: null,
        unidade: null,
      });
    }
  });

  const paginatedItems: ItemRelatorio[][] | [] = [];
  itemsList.forEach((item, index) => {
    const chunkIndex = Math.floor(index / linesPerPage);
    if (!paginatedItems[chunkIndex]) {
      paginatedItems[chunkIndex] = [item];
    } else {
      paginatedItems[chunkIndex].push(item);
    }
  });
  return (
    <>
      {paginatedItems.map((page, i) => (
        <div
          key={i}
          className={cn(
            'relative mt-8 text-center print:mt-0',
            fontMono.className
          )}
          style={{ breakAfter: 'always' }}
        >
          <div className='w-[277.5mm] border-[1px]'>
            <div className='flex h-[50px] border-b-[1px]'>
              <div className='flex justify-center border-r-[1px] align-middle'>
                <Image
                  className='p-1'
                  src='/imagens/brasao.jpg' //Sempre utilizar jpg para não ter problema com a transparência no PDF/A
                  width={46}
                  height={46}
                  alt='brasão'
                  priority={true}
                />
              </div>
              <div className='my-auto flex pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='mr-8 ml-8 text-[12pt] font-bold'>
                  {siteConfig.cliente.toUpperCase()}
                </span>
              </div>
              <div className='my-auto flex border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='ml-8 text-[10pt] font-bold'>
                  PEDIDO - Nº {dadosReserva.id}
                </span>
              </div>
              <div className='my-auto ml-auto flex border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='mr-2 ml-8 text-[10pt]'>
                  <strong>DATA </strong>
                  {dadosReserva.data.toLocaleDateString('pt-br', {})}
                </span>
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[50%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>SECRETARIA</span>
                <span>{montarCodigoSecretaria(secretaria.codigo)}</span> -{' '}
                <span
                  style={{
                    fontSize: secretaria.nome.length > 53 ? '7.5pt' : '10pt',
                  }}
                >
                  {secretaria.nome}
                </span>
              </div>
              <div className='w-[50%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>DEPARTAMENTO</span>
                <span>
                  {montarCodigoDepartamento(
                    secretaria.codigo,
                    departamento.codigo
                  )}
                </span>{' '}
                -{' '}
                <span
                  style={{
                    fontSize: departamento.nome.length > 53 ? '7.5pt' : '10pt',
                  }}
                >
                  {departamento.nome}
                </span>
              </div>
            </div>
            <div className='border-b-[1px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1 text-center text-[10pt] font-bold'>
              CLASSIFICAÇÃO ORÇAMENTÁRIA
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[21%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1 text-left'>
                <span className='mr-2 font-bold'>ECONÔMICA</span>
                <span>{dadosReserva.economicaItem.codigo}</span>
              </div>
              <div className='w-[40%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span
                  style={{
                    fontSize:
                      dadosReserva.dotacao.economica.desc.length > 53
                        ? '7.5pt'
                        : '10pt',
                  }}
                >
                  {dadosReserva.dotacao.economica.desc}
                </span>
              </div>
              <div className='w-[40%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span
                  style={{
                    fontSize:
                      dadosReserva.economicaItem.desc.length > 53
                        ? '7.5pt'
                        : '10pt',
                  }}
                >
                  {dadosReserva.economicaItem.desc}
                </span>
              </div>
            </div>
            <div className='flex w-full border-b-[2px] text-[10pt] whitespace-nowrap'>
              <div className='w-[21%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1 text-left'>
                <span className='mr-2 font-bold'>FUNCIONAL</span>
                <span className='text-[10pt]'>
                  {dadosReserva.dotacao.funcional.codigo}
                </span>
              </div>
              <div className='w-[40%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span
                  style={{
                    fontSize:
                      dadosReserva.dotacao.funcional.desc.length > 53
                        ? '7.5pt'
                        : '10pt',
                  }}
                >
                  {dadosReserva.dotacao.funcional.desc}
                </span>
              </div>
              <div className='w-[8%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>FONTE</span>
                <span>{dadosReserva.dotacao.fonte}</span>
              </div>
              <div className='w-[20%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>CÓD. APLICAÇÃO</span>
                <span>{dadosReserva.dotacao.codAplicacao}</span>
              </div>
              <div className='w-[12%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>DESPESA</span>
                <span>{dadosReserva.dotacao.despesa}</span>
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-center text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>ITEM</span>
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>QUANT.</span>
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>UNID.</span>
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>DESCRIÇÃO</span>
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>UNITÁRIO</span>
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                <span className='mr-2 font-bold'>TOTAL</span>
              </div>
              <div className='w-[17%] overflow-hidden border-l-[2px] bg-slate-200 pt-[3px] pr-1 pb-0 pl-1'>
                <span className='text-[10pt] font-bold'>
                  CRONOGRAMA FINANCEIRO
                </span>
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[0] && page[0].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[0] && page[0].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[0] &&
                  page[0].unidade !== null &&
                  Unidades[page[0].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[0] && page[0].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[0] &&
                  page[0].valor !== null &&
                  toCurrency(page[0].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[0] &&
                  page[0].valor !== null &&
                  page[0].quantidade &&
                  toCurrency(page[0].valor)
                    .multiply(page[0].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>JAN.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes1).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[1] && page[1].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[1] && page[1].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[1] &&
                  page[1].unidade !== null &&
                  Unidades[page[1].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[1] && page[1].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[1] &&
                  page[1].valor !== null &&
                  toCurrency(page[1].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[1] &&
                  page[1].valor !== null &&
                  page[1].quantidade &&
                  toCurrency(page[1].valor)
                    .multiply(page[1].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>FEV.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes2).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[2] && page[2].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[2] && page[2].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[2] &&
                  page[2].unidade !== null &&
                  Unidades[page[2].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[2] && page[2].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[2] &&
                  page[2].valor !== null &&
                  toCurrency(page[2].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[2] &&
                  page[2].valor !== null &&
                  page[2].quantidade &&
                  toCurrency(page[2].valor)
                    .multiply(page[2].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>MAR.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes3).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[3] && page[3].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[3] && page[3].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[3] &&
                  page[3].unidade !== null &&
                  Unidades[page[3].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[3] && page[3].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[3] &&
                  page[3].valor !== null &&
                  toCurrency(page[3].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[3] &&
                  page[3].valor !== null &&
                  page[3].quantidade &&
                  toCurrency(page[3].valor)
                    .multiply(page[3].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>ABR.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes4).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[4] && page[4].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[4] && page[4].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[4] &&
                  page[4].unidade !== null &&
                  Unidades[page[4].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[4] && page[4].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[4] &&
                  page[4].valor !== null &&
                  toCurrency(page[4].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[4] &&
                  page[4].valor !== null &&
                  page[4].quantidade &&
                  toCurrency(page[4].valor)
                    .multiply(page[4].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>MAIO</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes5).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[5] && page[5].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[5] && page[5].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[5] &&
                  page[5].unidade !== null &&
                  Unidades[page[5].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[5] && page[5].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[5] &&
                  page[5].valor !== null &&
                  toCurrency(page[5].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[5] &&
                  page[5].valor !== null &&
                  page[5].quantidade &&
                  toCurrency(page[5].valor)
                    .multiply(page[5].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>JUN.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes6).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[6] && page[6].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[6] && page[6].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[6] &&
                  page[6].unidade !== null &&
                  Unidades[page[6].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[6] && page[6].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[6] &&
                  page[6].valor !== null &&
                  toCurrency(page[6].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[6] &&
                  page[6].valor !== null &&
                  page[6].quantidade &&
                  toCurrency(page[6].valor)
                    .multiply(page[6].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>JUL.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes7).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[7] && page[7].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[7] && page[7].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[7] &&
                  page[7].unidade !== null &&
                  Unidades[page[7].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[7] && page[7].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[7] &&
                  page[7].valor !== null &&
                  toCurrency(page[7].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[7] &&
                  page[7].valor !== null &&
                  page[7].quantidade &&
                  toCurrency(page[7].valor)
                    .multiply(page[7].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>AGO.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes8).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[8] && page[8].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[8] && page[8].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[8] &&
                  page[8].unidade !== null &&
                  Unidades[page[8].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[8] && page[8].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[8] &&
                  page[8].valor !== null &&
                  toCurrency(page[8].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[8] &&
                  page[8].valor !== null &&
                  page[8].quantidade &&
                  toCurrency(page[8].valor)
                    .multiply(page[8].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>SET.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes9).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[9] && page[9].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[9] && page[9].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[9] &&
                  page[9].unidade !== null &&
                  Unidades[page[9].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[9] && page[9].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[9] &&
                  page[9].valor !== null &&
                  toCurrency(page[9].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[9] &&
                  page[9].valor !== null &&
                  page[9].quantidade &&
                  toCurrency(page[9].valor)
                    .multiply(page[9].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>OUT.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes10).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[10] && page[10].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[10] && page[10].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[10] &&
                  page[10].unidade !== null &&
                  Unidades[page[10].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[10] && page[10].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[10] &&
                  page[10].valor !== null &&
                  toCurrency(page[10].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[10] &&
                  page[10].valor !== null &&
                  page[10].quantidade &&
                  toCurrency(page[10].valor)
                    .multiply(page[10].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>NOV.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes11).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[5%] overflow-hidden pt-[3px] pr-1 pb-0 pl-1'>
                {page[11] && page[11].numero}
              </div>
              <div className='w-[7%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[11] && page[11].quantidade}
              </div>
              <div className='w-[5%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {page[11] &&
                  page[11].unidade !== null &&
                  Unidades[page[11].unidade]}
              </div>
              <div className='w-[44%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-2 text-left'>
                {page[11] && page[11].desc}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[11] &&
                  page[11].valor !== null &&
                  toCurrency(page[11].valor).format().replace(/0$/, '')}
              </div>
              <div className='w-[11%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-right'>
                {page[11] &&
                  page[11].valor !== null &&
                  page[11].quantidade &&
                  toCurrency(page[11].valor)
                    .multiply(page[11].quantidade)
                    .format()
                    .replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='text-center font-bold'>DEZ.</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                {toCurrency(dadosReserva.usarMes12).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
              <div className='w-[72%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center font-bold'>
                VALOR ESTIMADO
              </div>
              <div className='flex w-[11%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center text-[10pt]'>
                {toCurrency(dadosReserva.usarTotal).format().replace(/0$/, '')}
              </div>
              <div className='flex w-[5%] justify-center overflow-hidden border-l-[2px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
                <span className='font-bold'>TOTAL</span>
              </div>
              <div className='flex w-[12%] justify-end overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
                {toCurrency(dadosReserva.usarTotal).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='flex w-full border-b-[1px] pt-[3px] pr-1 pb-0 pl-1 text-[10pt] whitespace-nowrap'>
              <span className='mr-2 font-bold'>OBSERVAÇÕES</span>
              <span
                className='overflow-hidden'
                style={{
                  fontSize: '10pt',
                }}
              >
                {dadosReserva.obs.toUpperCase()}
              </span>
            </div>
            {(assinaturaDigital && i === paginatedItems.length - 1) ||
            !assinaturaDigital ? (
              dadosReserva.configsExercicio?.habilitaAssinatura2 &&
              dadosReserva.economicaItem.codigo.startsWith(
                dadosReserva.configsExercicio.economicaAssinatura2
              ) ? (
                <div className='flex h-[214px] w-full text-[10pt] whitespace-nowrap'>
                  <div className='flex w-[33%] flex-col overflow-hidden pt-[3px] pr-1 pb-0 pl-1 text-center'>
                    <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                      {((dadosReserva.gerente && dadosReserva.incluirGestor) ||
                        !dadosReserva.gerente) && (
                        <>
                          <span className='font-bold'>
                            {dadosReserva.gestor.nome?.toUpperCase()}
                          </span>
                          <span className='text-[10pt]'>
                            GESTOR(A) DE ORÇAMENTO
                          </span>
                        </>
                      )}
                    </div>
                    <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                      <span className='font-bold'>
                        {dadosReserva.configsExercicio?.habilitaAssinatura1
                          ? dadosReserva.diretor.nome.toUpperCase()
                          : null}
                      </span>
                      <span className='text-[10pt]'>
                        {dadosReserva.configsExercicio?.habilitaAssinatura1
                          ? dadosReserva.configsExercicio.assinatura1Linha1.toUpperCase()
                          : null}
                      </span>
                    </div>
                  </div>
                  <div className='w-[36%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center'>
                    <div className='justify-top flex h-[45%] w-full flex-col align-bottom'>
                      <span className='p-2 text-[10pt] font-bold whitespace-normal'>
                        EM CONFORMIDADE AO PPA, LDO E LOA, DECLARO DISPOR DE
                        DOTAÇÃO ORÇAMENTÁRIA SUFICIENTE.
                      </span>
                      <span className='text-[10pt]'>
                        {siteConfig.nomeAbreviado.toUpperCase()},{' '}
                        {dadosReserva.data
                          .toLocaleDateString('pt-br', {
                            day: '2-digit',
                          })
                          .toUpperCase()}{' '}
                        DE{' '}
                        {dadosReserva.data
                          .toLocaleDateString('pt-br', {
                            month: 'long',
                          })
                          .toUpperCase()}{' '}
                        DE{' '}
                        {dadosReserva.data
                          .toLocaleDateString('pt-br', {
                            year: 'numeric',
                          })
                          .toUpperCase()}
                        .
                      </span>
                    </div>
                    <div className='mt-1 flex h-[48%] w-full flex-col justify-end align-bottom'>
                      <span className='font-bold'>
                        {dadosReserva.secretario.nome.toUpperCase()}
                      </span>
                      <span className='text-[10pt]'>{secretaria.nome}</span>
                    </div>
                  </div>
                  <div className='w-[30%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center'>
                    <div className='flex h-[96%] w-full flex-col justify-end align-bottom'>
                      <span className='mt-6'>AUTORIZO</span>
                      <span className='font-bold'>
                        {dadosReserva.prefeito.nome.toUpperCase()}
                      </span>
                      <span className='text-[10pt]'>
                        {dadosReserva.configsExercicio.assinatura2Linha1.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className='flex h-[214px] w-full text-[10pt] whitespace-nowrap'>
                  <div className='flex w-[50%] flex-col overflow-hidden pt-[3px] pr-1 pb-0 pl-1 text-center'>
                    <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                      {((dadosReserva.gerente && dadosReserva.incluirGestor) ||
                        !dadosReserva.gerente) && (
                        <>
                          <span className='font-bold'>
                            {dadosReserva.gestor.nome?.toUpperCase()}
                          </span>
                          <span className='text-[10pt]'>
                            GESTOR(A) DE ORÇAMENTO
                          </span>
                        </>
                      )}
                    </div>
                    <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                      <span className='font-bold'>
                        {dadosReserva.configsExercicio?.habilitaAssinatura1
                          ? dadosReserva.diretor.nome.toUpperCase()
                          : null}
                      </span>
                      <span className='text-[10pt]'>
                        {dadosReserva.configsExercicio?.habilitaAssinatura1
                          ? dadosReserva.configsExercicio.assinatura1Linha1.toUpperCase()
                          : null}
                      </span>
                    </div>
                  </div>
                  <div className='w-[50%] overflow-hidden border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 text-center'>
                    <div className='justify-top flex h-[45%] w-full flex-col align-bottom'>
                      <span className='p-2 text-[10pt] font-bold whitespace-normal'>
                        EM CONFORMIDADE AO PPA, LDO E LOA, DECLARO DISPOR DE
                        DOTAÇÃO ORÇAMENTÁRIA SUFICIENTE.
                      </span>
                      <span className='text-[10pt]'>
                        {siteConfig.nomeAbreviado.toUpperCase()},{' '}
                        {dadosReserva.data
                          .toLocaleDateString('pt-br', {
                            day: '2-digit',
                          })
                          .toUpperCase()}{' '}
                        DE{' '}
                        {dadosReserva.data
                          .toLocaleDateString('pt-br', {
                            month: 'long',
                          })
                          .toUpperCase()}{' '}
                        DE{' '}
                        {dadosReserva.data
                          .toLocaleDateString('pt-br', {
                            year: 'numeric',
                          })
                          .toUpperCase()}
                        .
                      </span>
                    </div>
                    <div className='flex h-[48%] w-full flex-col justify-end align-bottom'>
                      <span className='mt-6 font-bold'>
                        {dadosReserva.secretario.nome.toUpperCase()}
                      </span>
                      <span className='text-[10pt]'>{secretaria.nome}</span>
                    </div>
                  </div>
                </div>
              )
            ) : (
              <div className='flex h-[214px] w-full items-end justify-center text-center text-[10pt] whitespace-nowrap'>
                <span>ASSINADO DIGITALMENTE</span>
              </div>
            )}
          </div>
        </div>
      ))}
    </>
  );
};
