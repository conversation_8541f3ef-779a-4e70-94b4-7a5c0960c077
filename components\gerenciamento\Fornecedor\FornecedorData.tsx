import { ErrorAlert } from '@/components/error-alert';
import { listarFornecedores } from '@/lib/database/gerenciamento/fornecedores';
import FornecedorDatatable from './FornecedorDataTable';

export default async function FornecedoresData() {
  const result = await listarFornecedores();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Fornecedores não encontrados.'} />;

  return <FornecedorDatatable data={result} />;
}
