'use client';
import { ColumnDef } from '@tanstack/react-table';
import { AlertDesativarFuncional } from './alertDesativarFuncional';
import { AlertAtivarFuncional } from './alertAtivarFuncional';
import { DialogDescricaoFuncional } from './dialogDescricaoFuncional';
import { ReusableDatatableFiltroCodigoDesc } from '@/components/datatable/reusableDatatableFiltroCodigoDesc';
import { listarFuncionais } from '@/lib/database/gerenciamento/funcionais';

export default function FuncionalsDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarFuncionais>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Código',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'desc',
      header: 'Descrição',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <DialogDescricaoFuncional
            idFuncional={row.original.id}
            descFuncional={row.original.desc}
            codigoFuncional={row.original.codigo}
          />
          {row.original.ativo ? (
            <AlertDesativarFuncional
              idFuncional={row.original.id}
              descFuncional={row.original.desc}
              codigoFuncional={row.original.codigo}
            />
          ) : (
            <AlertAtivarFuncional
              idFuncional={row.original.id}
              descFuncional={row.original.desc}
              codigoFuncional={row.original.codigo}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatableFiltroCodigoDesc columns={columns} data={data.data} />
    </div>
  );
}
