'use client';

import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons';

import {
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useEffect, useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { listarReservas } from '@/lib/database/movimento/reservas';
import { ComboboxSelecionaSecretaria } from '../movimento/reserva/comboboxSelecionarSecretaria';
import { ComboboxSelecionaDepartamento } from '../movimento/reserva/comboboxSelecionarDepartamento';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  secretarias: Required<
    Awaited<ReturnType<typeof listarReservas>>
  >['data']['secretarias'];
  departamentos: Required<
    Awaited<ReturnType<typeof listarReservas>>
  >['data']['departamentos'];
}

export function ReusableDatatableFiltroNumeroDespesaSecretariaDepartamento<
  TData,
  TValue,
>({
  columns,
  data,
  secretarias,
  departamentos,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
    enableMultiRowSelection: true,
    initialState: {
      columnVisibility: {
        secretariaId: false,
        departamentoId: false,
      },
    },
  });
  const [secretariaId, setSecretariaId] = useState<number | null>(null);
  const [departamentoId, setDepartamentoId] = useState<number | null>(null);

  useEffect(() => {
    if (secretariaId) {
      table.getColumn('secretariaId')?.setFilterValue(secretariaId);
      if (
        departamentoId &&
        departamentos.find((departamento) => departamento.id === departamentoId)
          ?.secretariaId !== secretariaId
      ) {
        setDepartamentoId(0);
      }
    } else {
      table.getColumn('secretariaId')?.setFilterValue(undefined);
    }
  }, [secretariaId, departamentoId, departamentos, table]);

  useEffect(() => {
    if (departamentoId) {
      table.getColumn('departamentoId')?.setFilterValue(departamentoId);
    } else {
      table.getColumn('departamentoId')?.setFilterValue(undefined);
    }
  }, [departamentoId, table]);

  return (
    <>
      <div className='flex flex-wrap items-center gap-4 py-4'>
        <Input
          placeholder='Filtrar número...'
          value={(table.getColumn('numero')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('id')?.setFilterValue(event.target.value)
          }
          className='max-w-[150px]'
        />
        <Input
          placeholder='Filtrar despesa...'
          value={(table.getColumn('despesa')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('despesa')?.setFilterValue(event.target.value)
          }
          className='max-w-[150px]'
        />
        {secretarias.length > 0 && (
          <div className='w-fit'>
            <ComboboxSelecionaSecretaria
              secretarias={secretarias}
              secretariaId={secretariaId}
              setSecretariaId={setSecretariaId}
            />
          </div>
        )}
        {departamentos.length > 0 && (
          <div className='w-fit'>
            <ComboboxSelecionaDepartamento
              departamentos={
                secretariaId
                  ? departamentos.filter(
                      (departamento) =>
                        departamento.secretariaId === secretariaId
                    )
                  : departamentos
              }
              departamentoId={departamentoId}
              setDepartamentoId={setDepartamentoId}
            />
          </div>
        )}
      </div>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} {...cell.column.columnDef.meta}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  Nenhum Resultado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className='mr-4 flex items-center justify-end space-x-2 py-4'>
        <div className='flex w-[120px] items-center justify-center text-sm font-medium'>
          Página {table.getState().pagination.pageIndex + 1} de{' '}
          {table.getPageCount()}
        </div>
        <div className='flex items-center space-x-2'>
          <Button
            variant='outline'
            className='hidden h-8 w-8 p-0 lg:flex'
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <span className='sr-only'>Ir para a primeira página</span>
            <DoubleArrowLeftIcon className='h-4 w-4' />
          </Button>
          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <span className='sr-only'>Ir para a página anterior</span>
            <ChevronLeftIcon className='h-4 w-4' />
          </Button>
          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <span className='sr-only'>Ir para a próxima página</span>
            <ChevronRightIcon className='h-4 w-4' />
          </Button>
          <Button
            variant='outline'
            className='hidden h-8 w-8 p-0 lg:flex'
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <span className='sr-only'>Ir para a última página</span>
            <DoubleArrowRightIcon className='h-4 w-4' />
          </Button>
        </div>
      </div>
    </>
  );
}
