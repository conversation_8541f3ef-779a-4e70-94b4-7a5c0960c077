'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArrowLeft, Loader2, PlusCircle } from 'lucide-react';
import { cn, currencyOptionsNoSymbol, toastAlgoDeuErrado } from '@/lib/utils';
import currency from 'currency.js';
import { criarLiquidacaoInicialSchema } from '@/lib/validation';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  criarLiquidacao,
  consultarSaldoEmpenhoParaLiquidacao,
} from '@/lib/database/movimento/liquidacoes';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { ComboboxSelecionarEmpenho } from './comboboxSelecionarEmpenho';

export default function FormCriarLiquidacao() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [loading, setLoading] = useState(false);
  const [empenhoData, setEmpenhoData] = useState<Awaited<
    ReturnType<typeof consultarSaldoEmpenhoParaLiquidacao>
  > | null>(null);

  const form = useForm<z.infer<typeof criarLiquidacaoInicialSchema>>({
    resolver: zodResolver(criarLiquidacaoInicialSchema),
    defaultValues: {
      idEmpenho: 0,
      resumo: '',
      obs: '',
      mesReferencia: '',
    },
  });

  // Load empenho from URL params if provided
  useEffect(() => {
    const empenhoId = searchParams?.get('empenho');
    if (empenhoId) {
      form.setValue('idEmpenho', parseInt(empenhoId));
      buscarEmpenho(parseInt(empenhoId));
    }
  }, [searchParams, form]);

  const buscarEmpenho = async (idEmpenho: number) => {
    try {
      setLoading(true);
      const res = await consultarSaldoEmpenhoParaLiquidacao({ id: idEmpenho });
      if (res?.error) {
        setLoading(false);
        toast.error(res.error);
        setEmpenhoData(null);
      } else {
        setLoading(false);
        setEmpenhoData(res);
      }
    } catch (error: any) {
      setLoading(false);
      console.log(JSON.stringify(error));
      toast.error(toastAlgoDeuErrado);
    }
  };

  const onSubmit = async (
    values: z.infer<typeof criarLiquidacaoInicialSchema>
  ) => {
    try {
      setLoading(true);
      const res = await criarLiquidacao(values);
      if (res?.error) {
        setLoading(false);
        toast.error(res.error, { duration: 10000 });
      } else {
        toast.success('Liquidação criada com sucesso!', {
          description: 'Agora você pode adicionar os arquivos dos documentos',
        });
        router.push('/movimento/liquidacoes/editar/' + res?.data?.idLiquidacao);
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex items-end gap-2'>
              <div className='flex-1'>
                <ComboboxSelecionarEmpenho
                  form={form}
                  onEmpenhoSelect={buscarEmpenho}
                />
              </div>
              {loading && <Loader2 className='size-4 animate-spin' />}
            </div>

            {empenhoData?.data && (
              <>
                <div className='mt-8 grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Empenho</Label>
                    <Input
                      disabled
                      value={`${empenhoData.data.empenho.numero}/${empenhoData.data.empenho.exercicio}`}
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Fornecedor</Label>
                    <Input
                      disabled
                      value={empenhoData.data.empenho.fornecedor?.nome || ''}
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Despesa</Label>
                    <Input
                      disabled
                      value={`${empenhoData.data.empenho.despesa} - ${empenhoData.data.empenho.descricaoDespesa}`}
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>
                      Valor Total do Empenho
                    </Label>
                    <Input
                      disabled
                      value={currency(
                        empenhoData.data.saldo.valorTotal,
                        currencyOptionsNoSymbol
                      )
                        .format()
                        .replace(/0$/, '')}
                      className='text-right'
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Já Liquidado</Label>
                    <Input
                      disabled
                      value={currency(
                        empenhoData.data.saldo.jaLiquidado,
                        currencyOptionsNoSymbol
                      )
                        .format()
                        .replace(/0$/, '')}
                      className='text-right text-red-600'
                    />
                  </div>
                  <div className='text-left'>
                    <Label className='w-full text-left'>Saldo Disponível</Label>
                    <Input
                      disabled
                      value={currency(
                        empenhoData.data.saldo.saldoDisponivel,
                        currencyOptionsNoSymbol
                      )
                        .format()
                        .replace(/0$/, '')}
                      className={cn(
                        'text-right',
                        empenhoData.data.saldo.saldoDisponivel > 0
                          ? 'text-green-600'
                          : 'text-red-600'
                      )}
                    />
                  </div>
                </div>

                <Separator className='my-8' />

                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <FormField
                    control={form.control}
                    name='resumo'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='w-full text-left'>
                          Resumo
                        </FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='mesReferencia'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='w-full text-left'>
                          Mês de Referência
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='Ex: Janeiro/2024' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='mt-4'>
                  <FormField
                    control={form.control}
                    name='obs'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='w-full text-left'>
                          Observações
                        </FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Separator className='my-8' />

                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>Documentos</h3>
                  </div>

                  <div className='rounded-lg border border-blue-200 bg-blue-50 p-6 text-center'>
                    <div className='flex flex-col items-center space-y-2'>
                      <div className='text-4xl'>📄</div>
                      <h4 className='font-semibold text-blue-900'>
                        Documentos serão adicionados após a criação
                      </h4>
                      <p className='text-sm text-blue-700'>
                        Após criar a liquidação, você será redirecionado para a
                        página de edição onde poderá adicionar os documentos com
                        seus respectivos arquivos.
                      </p>
                    </div>
                  </div>
                </div>

                <div className='mt-8 flex gap-4'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => router.back()}
                  >
                    <ArrowLeft className='mr-2 size-4' />
                    Voltar
                  </Button>
                  <Button type='submit' disabled={loading}>
                    {loading ? (
                      <Loader2 className='mr-2 size-4 animate-spin' />
                    ) : (
                      <PlusCircle className='mr-2 size-4' />
                    )}
                    Criar Liquidação
                  </Button>
                </div>
              </>
            )}
          </form>
        </Form>
      </div>
    </>
  );
}
