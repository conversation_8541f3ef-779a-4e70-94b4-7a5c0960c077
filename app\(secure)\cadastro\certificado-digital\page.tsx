import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import FormCertificadoDigital from '@/components/user/formUploadCertificado';
import { Lock } from 'lucide-react';

export default function CertificadoDigitalPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Certificado Digital</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='flex w-full justify-center'>
          <Alert className='max-w-xl'>
            <Lock className='h-4 w-4' />
            <AlertTitle className='text-left'>Segurança</AlertTitle>
            <AlertDescription>
              Os Certificados Digitais são armazenados de forma segura com
              criptografia ponta a ponta e podem ser utilizados exclusivamente
              pelo usuário titular, mediante o uso da senha do arquivo PFX.
            </AlertDescription>
          </Alert>
        </div>
        <FormCertificadoDigital />
      </PageContent>
    </PageWrapper>
  );
}
