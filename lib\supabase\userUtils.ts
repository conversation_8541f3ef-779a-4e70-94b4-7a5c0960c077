'use server';

import { createClient } from './server';

export const isExternalUser = async (): Promise<{
  isExternal: boolean | null;
  error?: string;
}> => {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      return { isExternal: null, error: 'Usuário não autenticado.' };
    }

    if (!data.user) {
      return { isExternal: null, error: 'Usuário não encontrado.' };
    }

    // Check user_type in metadata
    const userType = data.user.user_metadata?.user_type;

    if (!userType) {
      // If user_type is not set, we can't determine the type
      return { isExternal: null, error: 'Tipo de usuário não definido.' };
    }

    return { isExternal: userType === 'external' };
  } catch (error: any) {
    console.error('Error checking user type:', error);
    return { isExternal: null, error: 'Erro ao verificar tipo de usuário.' };
  }
};
