'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import { usuarioExternoSchema } from '@/lib/validation';
import { criarUsuarioExterno } from '@/lib/database/gerenciamento/usuariosExternos';
import { toastAlgoDeuErrado } from '@/lib/utils';
import Link from 'next/link';

export default function CriarUsuarioExternoForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof usuarioExternoSchema>>({
    resolver: zodResolver(usuarioExternoSchema),
    defaultValues: {
      nome: '',
      email: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof usuarioExternoSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof usuarioExternoSchema> = {
        nome: values.nome,
        email: values.email,
      };
      const res = await criarUsuarioExterno(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Usuário externo adicionado.');
        router.push('/gerenciamento/usuarios-externos');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex items-center'>
        <Link href='/gerenciamento/usuarios-externos'>
          <Button type='button' variant='outline'>
            <ArrowLeft className='mr-2 size-4' /> Voltar
          </Button>
        </Link>
      </div>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className='flex flex-col gap-4 rounded-lg border p-4'
        >
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome</FormLabel>
                <FormControl>
                  <Input placeholder='Nome do usuário externo' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder='Email do usuário externo' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type='submit' disabled={loading} className='mt-4 self-end'>
            {loading ? (
              <>
                <Icons.loader className='mr-2 size-4 animate-spin' /> Aguarde
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 size-4' /> Adicionar
              </>
            )}
          </Button>
        </form>
      </Form>
    </div>
  );
}
