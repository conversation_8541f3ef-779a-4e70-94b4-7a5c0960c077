'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, RotateCcw, FileText, Download } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import currency from 'currency.js';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { StatusLiquidacao } from '@/lib/enums';
import { toast } from 'sonner';
import {
  obterLiquidacao,
  obterUrlDocumentoLiquidacao,
  estornarLiquidacao,
} from '@/lib/database/movimento/liquidacoes';
import Link from 'next/link';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';

interface VisualizarLiquidacaoProps {
  liquidacao: NonNullable<Awaited<ReturnType<typeof obterLiquidacao>>['data']>;
}

export function VisualizarLiquidacao({
  liquidacao,
}: VisualizarLiquidacaoProps) {
  const router = useRouter();
  const [estornando, setEstornando] = useState(false);
  const [motivoEstorno, setMotivoEstorno] = useState('');
  const [estornandoLoading, setEstornandoLoading] = useState(false);

  const getStatusBadge = (status: number) => {
    switch (status) {
      case StatusLiquidacao.LIQUIDADA:
        return (
          <Badge variant='default' className='bg-green-100 text-green-800'>
            Liquidada
          </Badge>
        );
      case StatusLiquidacao.ESTORNADA:
        return <Badge variant='destructive'>Estornada</Badge>;
      default:
        return <Badge variant='secondary'>Desconhecido</Badge>;
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
  };

  const handleDownload = async (documento: any) => {
    if (!documento.id) {
      toast.error('ID do documento não encontrado');
      return;
    }

    try {
      const result = await obterUrlDocumentoLiquidacao(documento.id);
      if (result.error) {
        toast.error(result.error);
      } else if (result.data) {
        window.open(result.data.url, '_blank');
      }
    } catch (error) {
      console.error('Erro ao abrir documento:', error);
      toast.error('Erro ao abrir documento');
    }
  };

  const handleEstornar = () => {
    setEstornando(true);
    setMotivoEstorno('');
  };

  const confirmarEstorno = async () => {
    if (!motivoEstorno.trim()) {
      toast.error('Motivo do estorno é obrigatório');
      return;
    }

    try {
      setEstornandoLoading(true);
      const res = await estornarLiquidacao({
        id: liquidacao.id,
        motivo: motivoEstorno,
      });

      if (res?.error) {
        toast.error(res.error);
      } else {
        toast.success('Liquidação estornada com sucesso!');
        setEstornando(false);
        setMotivoEstorno('');
        router.refresh();
      }
    } catch (error) {
      console.error('Erro ao estornar liquidação:', error);
      toast.error('Erro ao estornar liquidação');
    } finally {
      setEstornandoLoading(false);
    }
  };

  const cancelarEstorno = () => {
    setEstornando(false);
    setMotivoEstorno('');
  };

  return (
    <div className='space-y-6'>
      {/* Header Actions */}
      <div className='flex items-center gap-4'>
        <Button variant='outline' onClick={() => router.back()}>
          <ArrowLeft className='mr-2 size-4' />
          Voltar
        </Button>
        {liquidacao.status === StatusLiquidacao.LIQUIDADA && (
          <>
            <Button variant='outline' asChild>
              <Link href={`/movimento/liquidacoes/editar/${liquidacao.id}`}>
                <Edit className='mr-2 size-4' />
                Editar
              </Link>
            </Button>
            <Button
              variant='outline'
              className='text-red-600 hover:text-red-700'
              onClick={handleEstornar}
            >
              <RotateCcw className='mr-2 size-4' />
              Estornar
            </Button>
          </>
        )}
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            Informações da Liquidação
            {getStatusBadge(liquidacao.status)}
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Número
              </label>
              <p className='font-medium'>
                {liquidacao.numero}/{liquidacao.exercicio}
              </p>
            </div>
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Data
              </label>
              <p className='font-medium'>
                {format(liquidacao.data, 'dd/MM/yyyy', { locale: ptBR })}
              </p>
            </div>
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Valor Total
              </label>
              <p className='font-medium text-green-600'>
                {currency(liquidacao.valorTotal, currencyOptionsNoSymbol)
                  .format()
                  .replace(/0$/, '')}
              </p>
            </div>
          </div>

          {liquidacao.resumo && (
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Resumo
              </label>
              <p>{liquidacao.resumo}</p>
            </div>
          )}

          {liquidacao.mesReferencia && (
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Mês de Referência
              </label>
              <p>{liquidacao.mesReferencia}</p>
            </div>
          )}

          {liquidacao.obs && (
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Observações
              </label>
              <p>{liquidacao.obs}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Empenho Information */}
      <Card>
        <CardHeader>
          <CardTitle>Empenho Relacionado</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Número do Empenho
              </label>
              <p className='font-medium'>
                {liquidacao.empenho.numero}/{liquidacao.empenho.exercicio}
              </p>
            </div>
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Valor do Empenho
              </label>
              <p className='font-medium text-blue-600'>
                {currency(
                  liquidacao.empenho.valorTotal,
                  currencyOptionsNoSymbol
                )
                  .format()
                  .replace(/0$/, '')}
              </p>
            </div>
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Fornecedor
              </label>
              <p className='font-medium'>
                {liquidacao.empenho.fornecedor?.nome}
              </p>
              <p className='text-muted-foreground text-sm'>
                {liquidacao.empenho.fornecedor?.cnpjCpf}
              </p>
            </div>
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Despesa
              </label>
              <p className='font-medium'>
                {liquidacao.empenho.reserva?.dotacao.despesa}
              </p>
              <p className='text-muted-foreground text-sm'>
                {liquidacao.empenho.reserva?.dotacao.desc}
              </p>
            </div>
          </div>
          {liquidacao.empenho.resumo && (
            <div>
              <label className='text-muted-foreground text-sm font-medium'>
                Resumo do Empenho
              </label>
              <p>{liquidacao.empenho.resumo}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader>
          <CardTitle>
            Documentos ({liquidacao.liquidacoes_documentos.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Número</TableHead>
                <TableHead>Data Emissão</TableHead>
                <TableHead>Data Recebimento</TableHead>
                <TableHead>Valor</TableHead>
                <TableHead>Arquivo</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {liquidacao.liquidacoes_documentos.map((documento) => (
                <TableRow key={documento.id}>
                  <TableCell className='font-medium'>
                    {documento.numeroDocumento}
                  </TableCell>
                  <TableCell>
                    {format(documento.dataEmissao, 'dd/MM/yyyy', {
                      locale: ptBR,
                    })}
                  </TableCell>
                  <TableCell>
                    {format(documento.dataRecebimento, 'dd/MM/yyyy', {
                      locale: ptBR,
                    })}
                  </TableCell>
                  <TableCell className='text-right font-medium text-green-600'>
                    {currency(documento.valorDocumento, currencyOptionsNoSymbol)
                      .format()
                      .replace(/0$/, '')}
                  </TableCell>
                  <TableCell>
                    {documento.nomeArquivo ? (
                      <div className='flex items-center gap-2'>
                        <FileText className='size-4 text-blue-600' />
                        <div className='flex flex-col'>
                          <span className='max-w-[150px] truncate text-sm'>
                            {documento.nomeArquivo}
                          </span>
                          <span className='text-muted-foreground text-xs'>
                            {formatFileSize(documento.tamanho || 0)}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <span className='text-muted-foreground text-sm'>
                        Sem arquivo
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    {documento.caminhoArquivo && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => handleDownload(documento)}
                      >
                        <Download className='size-4' />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Estorno Dialog */}
      <Dialog
        open={estornando}
        onOpenChange={() => !estornandoLoading && cancelarEstorno()}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Estornar Liquidação</DialogTitle>
            <DialogDescription>
              Esta ação irá estornar a liquidação. O empenho voltará ao status
              anterior. Informe o motivo do estorno:
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-2'>
            <Label htmlFor='motivo'>Motivo do Estorno *</Label>
            <Textarea
              id='motivo'
              value={motivoEstorno}
              onChange={(e) => setMotivoEstorno(e.target.value)}
              placeholder='Descreva o motivo do estorno...'
              disabled={estornandoLoading}
            />
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={cancelarEstorno}
              disabled={estornandoLoading}
            >
              Cancelar
            </Button>
            <Button
              variant='destructive'
              onClick={confirmarEstorno}
              disabled={estornandoLoading || !motivoEstorno.trim()}
            >
              {estornandoLoading ? (
                <>
                  <Loader2 className='mr-2 size-4 animate-spin' />
                  Estornando...
                </>
              ) : (
                'Confirmar Estorno'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
