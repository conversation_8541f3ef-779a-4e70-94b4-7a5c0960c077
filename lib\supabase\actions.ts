'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { createClient } from '@/lib/supabase/server';
import { SignInWithPasswordCredentials } from '@supabase/supabase-js';
import {
  salvarAuditoriaAcesso,
  verificarSeUsuarioAtivoPorUuid,
} from '@/lib/database/usuarios';
import { AuditoriaAcesso } from '../enums';
import { auditoriaAcessoSchema } from '../validation';
import z from 'zod';

export async function login(values: SignInWithPasswordCredentials) {
  const supabase = await createClient();

  // type-casting here for convenience
  // in practice, you should validate your inputs

  const { data, error } = await supabase.auth.signInWithPassword(values);
  console.log(JSON.stringify(error));
  if (error) {
    return {
      error: JSON.stringify(error),
    };
  }
  revalidatePath('/dashboard', 'layout');
  const parametrosAuditoria: z.infer<typeof auditoriaAcessoSchema> = {
    acao: AuditoriaAcesso.LOGIN,
  };
  await salvarAuditoriaAcesso(parametrosAuditoria);
  return {
    data,
  };
}

export async function ensureAuth() {
  const supabase = await createClient();
  const userData = await supabase.auth.getUser();
  if (!userData.data.user) {
    redirect('/login');
  }

  const { data, error } =
    await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
  if (error) {
    throw error;
  }
  if (data.nextLevel === 'aal2' && data.nextLevel !== data.currentLevel) {
    redirect('/mfa');
  }

  // const userFullName = await getUserName();
  // if (userFullName.error || !userFullName.data || userFullName.data === '') {
  //   redirect('/cadastro/novo');
  // }
  const usuarioAtivo = await verificarSeUsuarioAtivoPorUuid({
    uuid: userData.data.user.id,
  });
  if (!usuarioAtivo.data) {
    redirect('/login/desativado');
  }

  return {
    user: userData.data.user,
  };
}

export async function ensureNotAuth() {
  const supabase = await createClient();
  try {
    const userData = await supabase.auth.getUser();

    if (userData.data.user) {
      const { data, error } =
        await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
      if (error) {
        throw error;
      }
      if (data.nextLevel === 'aal2' && data.nextLevel !== data.currentLevel) {
        redirect('/mfa');
      }
      redirect('/dashboard');
    }
  } catch (e) {
    redirect('/dashboard');
  }
}

export async function isAuth() {
  const supabase = await createClient();
  try {
    const userData = await supabase.auth.getUser();

    if (!userData.data.user) {
      return false;
    }

    const { data, error } =
      await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
    if (error) {
      throw error;
    }
    if (data.nextLevel === 'aal2' && data.nextLevel !== data.currentLevel) {
      return false;
    }

    return true;
  } catch (e) {
    return false;
  }
}

export async function unenrollMFA() {
  const supabase = await createClient();
  const factors = await supabase.auth.mfa.listFactors();
  if (factors.error) {
    throw factors.error;
  }
  const totpFactor = factors.data.totp[0];

  if (!totpFactor) {
    throw new Error('No TOTP factors found!');
  }
  const { error } = await supabase.auth.mfa.unenroll({
    factorId: totpFactor.id,
  });
  if (error) {
    throw error;
  }
}
export async function validateToptp(token: string) {
  const supabase = await createClient();

  const factors = await supabase.auth.mfa.listFactors();
  if (factors.error) {
    throw factors.error;
  }
  const totpFactor = factors.data.totp[0];

  if (!totpFactor) {
    throw new Error('No TOTP factors found!');
  }

  const { data, error } = await supabase.auth.mfa.challengeAndVerify({
    code: token,
    factorId: totpFactor.id,
  });
  if (error) {
    return { error };
  }
  return {
    data,
  };
}
export async function revalidateLayoutAndRedirectHome() {
  revalidatePath('/dashboard', 'layout');
  redirect('/dashboard');
}
export async function signup(formData: FormData) {
  const supabase = await createClient();

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  };

  const { error } = await supabase.auth.signUp(data);

  if (error) {
    redirect('/error');
  }

  revalidatePath('/dashboard', 'layout');
  redirect('/dashboard');
}
