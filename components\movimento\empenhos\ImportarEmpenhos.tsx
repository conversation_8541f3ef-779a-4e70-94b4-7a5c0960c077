'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import {
  Upload,
  Download,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RotateCcw,
  Eye,
  EyeOff,
} from 'lucide-react';
import {
  importarEmpenhos,
  type ImportarResultado,
  type ImportarErro,
  type ImportarProgresso,
  gerarModeloCSV,
} from '@/lib/database/movimento/empenhos/importar';

interface ImportarEmpenhosProps {
  onImportComplete?: (resultado: ImportarResultado) => void;
  onClose?: () => void;
}

export default function ImportarEmpenhos({
  onImportComplete,
  onClose,
}: ImportarEmpenhosProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importando, setImportando] = useState(false);
  const [progresso, setProgresso] = useState<ImportarProgresso | null>(null);
  const [resultado, setResultado] = useState<ImportarResultado | null>(null);
  const [mostrarErros, setMostrarErros] = useState(false);
  const [opcoes, setOpcoes] = useState({
    criarFornecedores: false,
    ignorarErros: false,
    atualizarExistentes: false,
    validarDisponibilidade: true,
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validar arquivo
      if (!file.name.endsWith('.csv')) {
        toast.error('Apenas arquivos CSV são permitidos');
        return;
      }

      if (file.size === 0) {
        toast.error('Arquivo está vazio');
        return;
      }

      if (file.size > 10 * 1024 * 1024) {
        // 10MB
        toast.error('Arquivo muito grande. Máximo 10MB');
        return;
      }

      setSelectedFile(file);
      setResultado(null);
      setProgresso(null);
    }
  };

  const handleDownloadModelo = async () => {
    try {
      const csvContent = await gerarModeloCSV();
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = 'modelo_importacao_empenhos.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Modelo CSV baixado com sucesso!');
    } catch (error) {
      console.error('Erro ao baixar modelo:', error);
      toast.error('Erro ao baixar modelo CSV');
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error('Selecione um arquivo CSV');
      return;
    }

    setImportando(true);
    setProgresso(null);
    setResultado(null);

    try {
      const resultado = await importarEmpenhos(
        {
          arquivo: selectedFile,
          opcoes,
        },
        (progresso) => {
          setProgresso(progresso);
        }
      );

      setResultado(resultado);
      onImportComplete?.(resultado);

      if (resultado.sucesso) {
        toast.success(
          `Importação concluída! ${resultado.empenhosCriados} empenhos criados, ${resultado.empenhosAtualizados} atualizados.`
        );
      } else {
        toast.error(`Importação concluída com erros. Verifique os detalhes.`);
      }
    } catch (error) {
      console.error('Erro na importação:', error);
      toast.error('Erro ao realizar importação');
    } finally {
      setImportando(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getErroIcon = (tipo: ImportarErro['tipo']) => {
    switch (tipo) {
      case 'validacao':
        return <XCircle className='h-4 w-4 text-red-600' />;
      case 'negocio':
        return <AlertTriangle className='h-4 w-4 text-yellow-600' />;
      case 'banco':
        return <XCircle className='h-4 w-4 text-red-600' />;
      case 'sistema':
        return <XCircle className='h-4 w-4 text-red-600' />;
      default:
        return <XCircle className='h-4 w-4 text-red-600' />;
    }
  };

  const getErroColor = (tipo: ImportarErro['tipo']) => {
    switch (tipo) {
      case 'validacao':
        return 'border-red-200 bg-red-50';
      case 'negocio':
        return 'border-yellow-200 bg-yellow-50';
      case 'banco':
        return 'border-red-200 bg-red-50';
      case 'sistema':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-red-200 bg-red-50';
    }
  };

  return (
    <Card className='mx-auto w-full max-w-4xl'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Upload className='h-5 w-5' />
          Importar Empenhos
        </CardTitle>
        <CardDescription>
          Importe múltiplos empenhos a partir de um arquivo CSV
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        <Tabs defaultValue='importar' className='w-full'>
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger value='importar'>Importar Arquivo</TabsTrigger>
            <TabsTrigger value='instrucoes'>Instruções</TabsTrigger>
          </TabsList>

          <TabsContent value='importar' className='space-y-6'>
            {/* File Selection */}
            <div className='space-y-4 rounded-lg border p-4'>
              <div className='flex items-center justify-between'>
                <Label htmlFor='arquivo-csv'>Arquivo CSV</Label>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleDownloadModelo}
                  className='flex items-center gap-2'
                >
                  <Download className='h-4 w-4' />
                  Baixar Modelo
                </Button>
              </div>

              <Input
                id='arquivo-csv'
                type='file'
                accept='.csv'
                onChange={handleFileSelect}
                disabled={importando}
                className='cursor-pointer'
              />

              {selectedFile && (
                <div className='text-muted-foreground space-y-1 text-sm'>
                  <p>
                    <strong>Arquivo selecionado:</strong> {selectedFile.name}
                  </p>
                  <p>
                    <strong>Tamanho:</strong>{' '}
                    {formatFileSize(selectedFile.size)}
                  </p>
                  <p>
                    <strong>Tipo:</strong> {selectedFile.type || 'CSV'}
                  </p>
                </div>
              )}
            </div>

            {/* Options */}
            <div className='space-y-4 rounded-lg border p-4'>
              <h3 className='font-medium'>Opções de Importação</h3>
              <div className='space-y-3'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='criar-fornecedores'>
                      Criar fornecedores automaticamente
                    </Label>
                    <p className='text-muted-foreground text-sm'>
                      Cria novos fornecedores quando não encontrados pelo
                      CNPJ/CPF
                    </p>
                  </div>
                  <Switch
                    id='criar-fornecedores'
                    checked={opcoes.criarFornecedores}
                    onCheckedChange={(checked) =>
                      setOpcoes((prev) => ({
                        ...prev,
                        criarFornecedores: checked,
                      }))
                    }
                    disabled={importando}
                  />
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='atualizar-existentes'>
                      Atualizar empenhos existentes
                    </Label>
                    <p className='text-muted-foreground text-sm'>
                      Atualiza empenhos existentes com mesmo número/exercício
                    </p>
                  </div>
                  <Switch
                    id='atualizar-existentes'
                    checked={opcoes.atualizarExistentes}
                    onCheckedChange={(checked) =>
                      setOpcoes((prev) => ({
                        ...prev,
                        atualizarExistentes: checked,
                      }))
                    }
                    disabled={importando}
                  />
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='ignorar-erros'>
                      Continuar mesmo com erros
                    </Label>
                    <p className='text-muted-foreground text-sm'>
                      Continua o processamento mesmo que algumas linhas tenham
                      erros
                    </p>
                  </div>
                  <Switch
                    id='ignorar-erros'
                    checked={opcoes.ignorarErros}
                    onCheckedChange={(checked) =>
                      setOpcoes((prev) => ({ ...prev, ignorarErros: checked }))
                    }
                    disabled={importando}
                  />
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='validar-disponibilidade'>
                      Validar disponibilidade orçamentária
                    </Label>
                    <p className='text-muted-foreground text-sm'>
                      Verifica se há saldo suficiente nas dotações antes de
                      criar empenhos
                    </p>
                  </div>
                  <Switch
                    id='validar-disponibilidade'
                    checked={opcoes.validarDisponibilidade}
                    onCheckedChange={(checked) =>
                      setOpcoes((prev) => ({
                        ...prev,
                        validarDisponibilidade: checked,
                      }))
                    }
                    disabled={importando}
                  />
                </div>
              </div>
            </div>

            {/* Progress */}
            {progresso && (
              <div className='space-y-3 rounded-lg border p-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>
                    {progresso.status === 'iniciando' &&
                      'Iniciando importação...'}
                    {progresso.status === 'processando' &&
                      `Processando linha ${progresso.linhaAtual} de ${progresso.totalLinhas}`}
                    {progresso.status === 'finalizando' &&
                      'Finalizando importação...'}
                    {progresso.status === 'concluido' &&
                      'Importação concluída!'}
                    {progresso.status === 'erro' && 'Erro na importação'}
                  </span>
                  <Badge
                    variant={
                      progresso.status === 'concluido' ? 'default' : 'secondary'
                    }
                  >
                    {Math.round(
                      (progresso.linhaAtual / progresso.totalLinhas) * 100
                    )}
                    %
                  </Badge>
                </div>

                <Progress
                  value={(progresso.linhaAtual / progresso.totalLinhas) * 100}
                  className='w-full'
                />

                <div className='grid grid-cols-3 gap-4 text-sm'>
                  <div className='text-center'>
                    <p className='font-medium text-green-600'>
                      {progresso.empenhosCriados}
                    </p>
                    <p className='text-muted-foreground'>Criados</p>
                  </div>
                  <div className='text-center'>
                    <p className='font-medium text-blue-600'>
                      {progresso.empenhosAtualizados}
                    </p>
                    <p className='text-muted-foreground'>Atualizados</p>
                  </div>
                  <div className='text-center'>
                    <p className='font-medium text-red-600'>
                      {progresso.erros.length}
                    </p>
                    <p className='text-muted-foreground'>Erros</p>
                  </div>
                </div>
              </div>
            )}

            {/* Results */}
            {resultado && (
              <div className='space-y-4'>
                <div
                  className={`rounded-lg border p-4 ${
                    resultado.sucesso
                      ? 'border-green-200 bg-green-50'
                      : 'border-red-200 bg-red-50'
                  }`}
                >
                  <div className='mb-3 flex items-center gap-2'>
                    {resultado.sucesso ? (
                      <CheckCircle className='h-5 w-5 text-green-600' />
                    ) : (
                      <XCircle className='h-5 w-5 text-red-600' />
                    )}
                    <h3 className='font-medium'>
                      {resultado.sucesso
                        ? 'Importação concluída com sucesso!'
                        : 'Importação concluída com erros'}
                    </h3>
                  </div>

                  <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
                    <div className='text-center'>
                      <p className='font-medium text-green-600'>
                        {resultado.empenhosCriados}
                      </p>
                      <p className='text-muted-foreground'>Criados</p>
                    </div>
                    <div className='text-center'>
                      <p className='font-medium text-blue-600'>
                        {resultado.empenhosAtualizados}
                      </p>
                      <p className='text-muted-foreground'>Atualizados</p>
                    </div>
                    <div className='text-center'>
                      <p className='font-medium text-red-600'>
                        {resultado.erros.length}
                      </p>
                      <p className='text-muted-foreground'>Erros</p>
                    </div>
                    <div className='text-center'>
                      <p className='font-medium'>
                        {formatDuration(resultado.tempoTotal)}
                      </p>
                      <p className='text-muted-foreground'>Duração</p>
                    </div>
                  </div>
                </div>

                {/* Errors */}
                {resultado.erros.length > 0 && (
                  <div className='space-y-3 rounded-lg border p-4'>
                    <div className='flex items-center justify-between'>
                      <h3 className='flex items-center gap-2 font-medium'>
                        <AlertTriangle className='h-4 w-4 text-yellow-600' />
                        Erros encontrados ({resultado.erros.length})
                      </h3>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => setMostrarErros(!mostrarErros)}
                        className='flex items-center gap-2'
                      >
                        {mostrarErros ? (
                          <EyeOff className='h-4 w-4' />
                        ) : (
                          <Eye className='h-4 w-4' />
                        )}
                        {mostrarErros ? 'Ocultar' : 'Mostrar'}
                      </Button>
                    </div>

                    {mostrarErros && (
                      <ScrollArea className='max-h-96'>
                        <div className='space-y-2'>
                          {resultado.erros.map((erro, index) => (
                            <Alert
                              key={index}
                              className={getErroColor(erro.tipo)}
                            >
                              <div className='flex items-start gap-3'>
                                {getErroIcon(erro.tipo)}
                                <div className='flex-1 space-y-1'>
                                  <div className='flex items-center justify-between'>
                                    <p className='text-sm font-medium'>
                                      Linha {erro.linha} - {erro.tipo}
                                    </p>
                                    <Badge
                                      variant='outline'
                                      className='text-xs'
                                    >
                                      {erro.recuperavel
                                        ? 'Recuperável'
                                        : 'Crítico'}
                                    </Badge>
                                  </div>
                                  <AlertDescription className='text-sm'>
                                    {erro.mensagem}
                                  </AlertDescription>
                                </div>
                              </div>
                            </Alert>
                          ))}
                        </div>
                      </ScrollArea>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Actions */}
            <div className='flex gap-2'>
              <Button
                onClick={handleImport}
                disabled={!selectedFile || importando}
                className='flex-1'
              >
                {importando ? (
                  <>
                    <RotateCcw className='mr-2 h-4 w-4 animate-spin' />
                    Importando...
                  </>
                ) : (
                  <>
                    <Upload className='mr-2 h-4 w-4' />
                    Importar Empenhos
                  </>
                )}
              </Button>

              {onClose && (
                <Button
                  variant='outline'
                  onClick={onClose}
                  disabled={importando}
                >
                  Fechar
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value='instrucoes' className='space-y-4'>
            <div className='space-y-4'>
              <Alert>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>
                  <strong>Importante:</strong> Faça um backup dos dados antes de
                  realizar importações em massa.
                </AlertDescription>
              </Alert>

              <div className='space-y-4'>
                <div>
                  <h4 className='mb-2 font-medium'>Formato do Arquivo CSV</h4>
                  <ul className='text-muted-foreground space-y-1 text-sm'>
                    <li>
                      • O arquivo deve estar no formato CSV (valores separados
                      por vírgula)
                    </li>
                    <li>
                      • A primeira linha deve conter os cabeçalhos das colunas
                    </li>
                    <li>
                      • Use o modelo fornecido para garantir o formato correto
                    </li>
                    <li>• Tamanho máximo do arquivo: 10MB</li>
                  </ul>
                </div>

                <div>
                  <h4 className='mb-2 font-medium'>Colunas Obrigatórias</h4>
                  <ul className='text-muted-foreground space-y-1 text-sm'>
                    <li>
                      • <code>id_reserva</code> ou <code>id_dotacao</code> (pelo
                      menos um)
                    </li>
                    <li>
                      • <code>id_fornecedor</code> ou{' '}
                      <code>fornecedor_nome</code> +{' '}
                      <code>fornecedor_cnpj_cpf</code>
                    </li>
                    <li>
                      • <code>valor_total</code>
                    </li>
                    <li>
                      • Pelo menos um valor mensal (<code>usar_mes1</code> a{' '}
                      <code>usar_mes12</code>)
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className='mb-2 font-medium'>Colunas Opcionais</h4>
                  <ul className='text-muted-foreground space-y-1 text-sm'>
                    <li>
                      • <code>numero</code> - Número do empenho (gerado
                      automaticamente se não informado)
                    </li>
                    <li>
                      • <code>exercicio</code> - Ano do empenho (usa o exercício
                      do usuário se não informado)
                    </li>
                    <li>
                      • <code>resumo</code> - Descrição do empenho
                    </li>
                    <li>
                      • <code>obs</code> - Observações
                    </li>
                    <li>
                      • <code>data</code> - Data do empenho (usa data atual se
                      não informado)
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className='mb-2 font-medium'>Validações</h4>
                  <ul className='text-muted-foreground space-y-1 text-sm'>
                    <li>
                      • A soma das cotas mensais deve ser igual ao valor total
                    </li>
                    <li>
                      • O usuário deve ter permissão para acessar as dotações
                      informadas
                    </li>
                    <li>
                      • Fornecedores devem existir ou ter dados para criação
                      automática
                    </li>
                    <li>
                      • Saldo orçamentário disponível (se validação estiver
                      ativada)
                    </li>
                    <li>
                      • Reservas devem estar com status &quot;Reservado&quot;
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className='mb-2 font-medium'>Recomendações</h4>
                  <ul className='text-muted-foreground space-y-1 text-sm'>
                    <li>
                      • Comece com importações pequenas para testar o processo
                    </li>
                    <li>
                      • Mantenha um registro dos empenhos importados para
                      auditoria
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
