'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import FormCriarReserva from '@/components/movimento/reserva/formCriarReserva';

export default async function NovaReservaPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Nova Reserva</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormCriarReserva />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
