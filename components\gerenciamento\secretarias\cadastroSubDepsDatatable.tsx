'use client';
import { ColumnDef } from '@tanstack/react-table';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Departamento } from '@/types/app';
import { codigoSecretariaMask } from '@/lib/utils';

export default function CadastroSubDepsDatatable({
  data,
  removerSubDep,
  codSecretaria,
}: {
  data: Departamento[];
  removerSubDep: (codigoDep: number, codigoSubDep: number) => void;
  codSecretaria: number;
}) {
  if (!data) return null;

  const subdepsConfigurados = data
    .map(
      (dep) =>
        dep.subdepartamentos.map((subDep) => {
          return {
            codigoCompleto: `${codigoSecretariaMask(codSecretaria.toString())}.${codigoSecretariaMask(dep.codigo.toString())}.${codigoSecretariaMask(subDep.codigo.toString())}`,
            nome: subDep.nome,
          };
        }) || []
    )
    .flat();

  const columns: ColumnDef<(typeof subdepsConfigurados)[0]>[] = [
    {
      accessorKey: 'codigoCompleto',
      header: 'Código',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <Button
          type='button'
          variant='ghost'
          aria-description='Remover Subdepartamento'
          onClick={() => {
            const codigos = (row.getValue('codigoCompleto') as string).split(
              '.'
            );
            removerSubDep(Number(codigos[1]), Number(codigos[2]));
          }}
        >
          <Trash className='size-4' />
        </Button>
      ),
    },
  ];
  return (
    <div className='overflow-x-scroll'>
      <ReusableDatatable columns={columns} data={subdepsConfigurados} />
    </div>
  );
}
