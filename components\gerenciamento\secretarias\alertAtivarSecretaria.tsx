import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ativarSecretaria } from '@/lib/database/gerenciamento/secretarias';
import { codigoSecretariaMask, toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Check } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertAtivarSecretaria({
  idSecretaria,
  nomeSecretaria,
  codigoSecretaria,
}: {
  idSecretaria: number;
  nomeSecretaria: string;
  codigoSecretaria: number;
}) {
  const [loading, setLoading] = useState(false);

  const ativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idSecretaria,
      };

      const res = await ativarSecretaria(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Secretaria ativada.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='min-w-[115px] text-green-800'
        >
          <Check className='mr-2 size-4' /> Ativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Ativar Secretaria?</AlertDialogTitle>
          <AlertDialogDescription>
            A secretaria{' '}
            <span className='font-bold'>
              {codigoSecretariaMask(codigoSecretaria.toString())}.00.00 -{' '}
              {nomeSecretaria}
            </span>{' '}
            será ativada.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              ativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Ativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
