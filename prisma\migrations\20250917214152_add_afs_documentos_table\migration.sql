-- CreateTable
CREATE TABLE "afs_documentos" (
    "id" SERIAL NOT NULL,
    "idAF" INTEGER NOT NULL,
    "tipoDocumento" SMALLINT NOT NULL,
    "nomeArquivo" VARCHAR(255) NOT NULL,
    "caminhoArquivo" VARCHAR(500) NOT NULL,
    "tamanho" INTEGER NOT NULL,
    "dataUpload" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "afs_documentos_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "afs_documentos_idAF_tipoDocumento_idx" ON "afs_documentos"("idAF", "tipoDocumento");

-- CreateIndex
CREATE INDEX "afs_documentos_ativo_idx" ON "afs_documentos"("ativo");

-- AddForeignKey
ALTER TABLE "afs_documentos" ADD CONSTRAINT "afs_documentos_idAF_fkey" FOREIGN KEY ("idAF") REFERENCES "afs"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
