'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { obterCotasPorId } from '@/lib/database/gerenciamento/dotacoes';
import FormCotas from '@/components/gerenciamento/dotacoes/formCotas';

export default async function AlterarCotasPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const dotacao = await obterCotasPorId({
    id: Number(id),
  });

  if (dotacao.error) {
    return <ErrorAlert error={dotacao.error} />;
  }
  if (!dotacao.data) {
    return <ErrorAlert error='Falha ao obter dotação.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Cotas</PageTitle>
      </PageHeader>
      <PageContent>
        <h2>
          <span className='font-bold'>Despesa:</span> {dotacao.data.despesa} -{' '}
          {dotacao.data.desc}
        </h2>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormCotas dotacao={dotacao} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
