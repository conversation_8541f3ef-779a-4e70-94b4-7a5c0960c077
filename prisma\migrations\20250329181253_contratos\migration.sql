-- CreateTable
CREATE TABLE "contratos" (
    "id" SERIAL NOT NULL,
    "exercicio" INTEGER NOT NULL,
    "idFornecedor" INTEGER NOT NULL,
    "processo" INTEGER NOT NULL,
    "processoAno" INTEGER NOT NULL,
    "valor" DECIMAL(18,3) NOT NULL,
    "nomeGestor" TEXT NOT NULL,
    "cpfGestor" VARCHAR(14) NOT NULL,
    "condPagamento" TEXT NOT NULL,
    "numAf" INTEGER NOT NULL,
    "dataInicio" TIMESTAMP(3) NOT NULL,
    "dataFinal" TIMESTAMP(3) NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "contratos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "contratos_audit" (
    "id" SERIAL NOT NULL,
    "idContrato" INTEGER NOT NULL,
    "idFornecedor" INTEGER NOT NULL,
    "processo" INTEGER NOT NULL,
    "processoAno" INTEGER NOT NULL,
    "nomeGestor" TEXT NOT NULL,
    "cpfGestor" VARCHAR(14) NOT NULL,
    "condPagamento" TEXT NOT NULL,
    "numAf" INTEGER NOT NULL,
    "dataInicio" TIMESTAMP(3) NOT NULL,
    "dataFinal" TIMESTAMP(3) NOT NULL,
    "valor" DECIMAL(18,3) NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "ip" INET NOT NULL,
    "acao" SMALLINT NOT NULL,

    CONSTRAINT "contratos_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "contratos_ativo_idx" ON "contratos"("ativo");

-- AddForeignKey
ALTER TABLE "contratos" ADD CONSTRAINT "contratos_idFornecedor_fkey" FOREIGN KEY ("idFornecedor") REFERENCES "fornecedores"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "contratos_audit" ADD CONSTRAINT "contratos_audit_idContrato_fkey" FOREIGN KEY ("idContrato") REFERENCES "contratos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "contratos_audit" ADD CONSTRAINT "contratos_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
