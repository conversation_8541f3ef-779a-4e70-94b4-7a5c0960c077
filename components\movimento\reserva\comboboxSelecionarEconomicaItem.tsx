'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { UseFormReturn } from 'react-hook-form';
import { buscarDespesa } from '@/lib/database/movimento/reservas';
import { z } from 'zod';
import { criarReservaSchema } from '@/lib/validation';
import { Input } from '@/components/ui/input';

export function ComboboxSelecionarEconomicaItem({
  despesa,
  form,
}: {
  despesa: Awaited<ReturnType<typeof buscarDespesa>> | null;
  form: UseFormReturn<z.infer<typeof criarReservaSchema>>;
}) {
  const [open, setOpen] = React.useState(false);

  const economicaSelecionada =
    despesa?.data?.economicasItens.find((economica) => {
      return form.getValues('economicaItemId') === economica.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild disabled={!despesa}>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {economicaSelecionada
              ? economicaSelecionada.codigo + ' - ' + economicaSelecionada.desc
              : 'Selecione o item...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar econômica...' />
            <CommandEmpty>Econômica não encontrada.</CommandEmpty>
            <CommandList>
              {despesa?.data?.economicasItens.map((eco) => (
                <CommandItem
                  key={eco.id}
                  value={`${eco.codigo} - ${eco.desc}`}
                  onSelect={() => {
                    form.setValue('economicaItemId', eco.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      form.getValues('economicaItemId') === eco.id
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  {eco.codigo} - {eco.desc}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <Input
        disabled={true}
        value={
          economicaSelecionada
            ? economicaSelecionada.info ||
              'Item - Nenhuma informação extra cadastrada.'
            : ''
        }
      />
    </>
  );
}
