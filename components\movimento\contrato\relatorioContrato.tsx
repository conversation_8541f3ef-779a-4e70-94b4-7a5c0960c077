import { siteConfig } from '@/config/site';
import { obterContratoParaRelatorio } from '@/lib/database/movimento/contratos';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export const RelatorioContrato = ({
  contrato,
}: {
  contrato: Awaited<ReturnType<typeof obterContratoParaRelatorio>>;
}) => {
  const dataContrato = contrato.data;
  const fornecedor = contrato.data?.fornecedor;

  if (!dataContrato || !fornecedor) return null;

  return (
    <>
      <div
        className={cn('relative mt-8 text-center print:mt-0')}
        style={{ breakAfter: 'always' }}
      >
        <div className='w-[277.5mm] border-[1px]'>
          <div className='flex h-[50px] border-b-[1px]'>
            <div className='flex justify-center border-r-[1px] align-middle'>
              <Image
                className='p-1'
                src='/imagens/brasao.jpg' //Sempre utilizar jpg para não ter problema com a transparência no PDF/A
                width={46}
                height={46}
                alt='brasão'
                priority={true}
              />
            </div>
            <div className='my-auto flex pt-[3px] pr-1 pb-0 pl-1 align-middle'>
              <span className='mx-8 text-[12pt] font-bold'>
                {siteConfig.cliente.toUpperCase()}
              </span>
            </div>
            <div className='my-auto flex justify-center border-l-[1px] pt-[3px] pr-1 pb-0 pl-1 align-middle'>
              <span className='mr-8 ml-36 text-[10pt] font-bold'>
                Contrato Nº {dataContrato.id}
              </span>
            </div>
          </div>
          <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
            <div className='mr-4 flex w-[33%] pt-[3px] pr-1 pb-0 pl-1'>
              <span className='mr-2 font-bold'>Processo: </span>
              <span>{dataContrato.processo}</span>
            </div>
            <div className='mx-4 flex w-[33%] border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
              <span className='mr-2 font-bold'>Ano Processo: </span>
              <span>{dataContrato.processoAno}</span>
            </div>
            <div className='mx-4 flex w-[33%] border-l-[1px] pt-[3px] pr-1 pb-0 pl-1'>
              <span className='mr-2 font-bold'>Período: </span>
              <span>
                {dataContrato.dataInicio.toLocaleDateString('pt-br', {})}
                {' Até '}
                {dataContrato.dataFinal.toLocaleDateString('pt-br', {})}
              </span>
            </div>
          </div>
          <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
            <div className='overflow-hidden pt-[3px] pr-1 pb-0 pl-1 text-left'>
              <span className='mr-2 font-bold'>Fornecedor:</span>
              <span>{`${fornecedor.codigo} - ${fornecedor.nome}`}</span>
            </div>
          </div>
          <div className='flex w-full border-b-[1px] text-[10pt] whitespace-nowrap'>
            <div className='flex w-full border-b-[1px] pt-[3px] pr-1 pb-0 pl-1 text-[10pt] whitespace-nowrap'>
              <span className='mr-2 font-bold'>Forma de Pagamento: </span>
              <span
                className='overflow-hidden'
                style={{
                  fontSize: '10pt',
                }}
              >
                {dataContrato.condPagamento.toUpperCase()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
