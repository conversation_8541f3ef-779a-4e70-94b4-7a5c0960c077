import { DialogNovaFuncional } from '@/components/gerenciamento/funcionais/dialogNovaFuncional';
import FuncionalsDatatableWrapper from '@/components/gerenciamento/funcionais/funcionaisDatatableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';

export default function GerenciamentoDeFuncionaisPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Gerenciamento de Funcionais</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='flex w-full justify-end pr-6'>
            <DialogNovaFuncional />
          </div>
          <FuncionalsDatatableWrapper />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
