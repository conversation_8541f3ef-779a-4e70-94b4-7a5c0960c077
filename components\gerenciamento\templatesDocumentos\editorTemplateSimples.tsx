'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Eye,
} from 'lucide-react';
import { sanitizarHtmlTemplate } from '@/lib/utils/seguranca';

interface EditorTemplateProps {
  value: string;
  onChange: (content: string) => void;
  height?: number;
  disabled?: boolean;
}

export function EditorTemplateSimples({
  value,
  onChange,
  height = 400,
  disabled = false,
}: EditorTemplateProps) {
  const [showPreview, setShowPreview] = useState(false);

  const insertText = (before: string, after: string = '') => {
    const textarea = document.getElementById(
      'template-editor'
    ) as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);

    const newText =
      value.substring(0, start) +
      before +
      selectedText +
      after +
      value.substring(end);
    onChange(newText);

    // Restaurar foco e posição do cursor
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + before.length,
        start + before.length + selectedText.length
      );
    }, 0);
  };

  const insertVariable = (variable: string) => {
    insertText(`{{${variable}}}`);
  };

  const formatButtons = [
    {
      icon: Bold,
      action: () => insertText('<strong>', '</strong>'),
      title: 'Negrito',
    },
    {
      icon: Italic,
      action: () => insertText('<em>', '</em>'),
      title: 'Itálico',
    },
    {
      icon: Underline,
      action: () => insertText('<u>', '</u>'),
      title: 'Sublinhado',
    },
    {
      icon: List,
      action: () => insertText('<ul><li>', '</li></ul>'),
      title: 'Lista',
    },
    {
      icon: ListOrdered,
      action: () => insertText('<ol><li>', '</li></ol>'),
      title: 'Lista Numerada',
    },
    {
      icon: AlignLeft,
      action: () => insertText('<div style="text-align: left;">', '</div>'),
      title: 'Alinhar Esquerda',
    },
    {
      icon: AlignCenter,
      action: () => insertText('<div style="text-align: center;">', '</div>'),
      title: 'Centralizar',
    },
    {
      icon: AlignRight,
      action: () => insertText('<div style="text-align: right;">', '</div>'),
      title: 'Alinhar Direita',
    },
  ];

  const variableButtons = [
    { label: 'Nome', variable: 'NOME_USUARIO' },
    { label: 'Data', variable: 'DATA_ATUAL' },
    { label: 'Número', variable: 'NUMERO_DOCUMENTO' },
    { label: 'Ano', variable: 'ANO_ATUAL' },
    { label: 'Mês', variable: 'MES_ATUAL' },
  ];

  return (
    <div className='rounded-md border'>
      {/* Barra de Ferramentas */}
      <div className='flex flex-wrap gap-1 border-b bg-gray-50 p-2'>
        {/* Botões de Formatação */}
        <div className='mr-4 flex gap-1'>
          {formatButtons.map((button, index) => (
            <Button
              key={index}
              variant='outline'
              size='sm'
              onClick={button.action}
              disabled={disabled}
              title={button.title}
              className='h-8 w-8 p-0'
            >
              <button.icon className='h-4 w-4' />
            </Button>
          ))}
        </div>

        {/* Separador */}
        <div className='mx-2 border-l' />

        {/* Botões de Variáveis */}
        <div className='mr-4 flex gap-1'>
          {variableButtons.map((button, index) => (
            <Button
              key={index}
              variant='outline'
              size='sm'
              onClick={() => insertVariable(button.variable)}
              disabled={disabled}
              title={`Inserir variável ${button.label}`}
              className='h-8 px-2 text-xs'
            >
              {button.label}
            </Button>
          ))}
        </div>

        {/* Separador */}
        <div className='mx-2 border-l' />

        {/* Botões de Visualização */}
        <div className='flex gap-1'>
          <Button
            variant={showPreview ? 'default' : 'outline'}
            size='sm'
            onClick={() => setShowPreview(!showPreview)}
            disabled={disabled}
            title='Alternar Preview'
            className='h-8 px-2 text-xs'
          >
            <Eye className='mr-1 h-4 w-4' />
            Preview
          </Button>
        </div>
      </div>

      {/* Área de Edição */}
      <div className='flex'>
        {/* Editor */}
        <div className={showPreview ? 'w-1/2 border-r' : 'w-full'}>
          <Textarea
            id='template-editor'
            value={value}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            className='resize-none rounded-none border-0 focus-visible:ring-0'
            style={{ height: `${height}px` }}
            placeholder='Digite o conteúdo do template aqui...

Você pode usar HTML básico:
- <p>Parágrafo</p>
- <strong>Negrito</strong>
- <em>Itálico</em>
- <u>Sublinhado</u>
- <br> para quebra de linha
- <ul><li>Lista</li></ul>
- <ol><li>Lista numerada</li></ol>

Variáveis disponíveis:
- {{NOME_USUARIO}}
- {{DATA_ATUAL}}
- {{NUMERO_DOCUMENTO}}
- {{ANO_ATUAL}}
- {{MES_ATUAL}}'
          />
        </div>

        {/* Preview */}
        {showPreview && (
          <div
            className='w-1/2 overflow-auto bg-white p-4'
            style={{ height: `${height}px` }}
          >
            <div
              className='prose max-w-none text-sm'
              dangerouslySetInnerHTML={{
                __html: sanitizarHtmlTemplate(value),
              }}
            />
          </div>
        )}
      </div>

      {/* Rodapé com Informações */}
      <div className='border-t bg-gray-50 p-2 text-xs text-gray-600'>
        <div className='flex justify-between'>
          <span>Use HTML básico para formatação</span>
          <span>{value.length} caracteres</span>
        </div>
      </div>
    </div>
  );
}
