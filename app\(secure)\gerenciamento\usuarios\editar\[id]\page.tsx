'use server';

import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { listarCargosAtivos } from '@/lib/database/gerenciamento/usuarios';
import { obterUsuarioECargos } from '@/lib/database/gerenciamento/usuarios';
import EditarUsuarioForm from '@/components/gerenciamento/usuarios/editarUsuarioForm';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export default async function EditarUsuarioPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const cargosPromise = listarCargosAtivos();
  const { id } = await params;
  const usuarioPromise = obterUsuarioECargos({
    id,
  });

  const [cargos, cargosUsuario] = await Promise.all([
    cargosPromise,
    usuarioPromise,
  ]);

  if (cargos.error) {
    return <ErrorAlert error={cargos.error} />;
  }
  if (!cargos.data) {
    return <ErrorAlert error='Falha ao obter cargo.' />;
  }

  if (cargosUsuario.error) {
    return <ErrorAlert error={cargosUsuario.error} />;
  }
  if (!cargosUsuario.data) {
    return <ErrorAlert error='Falha ao obter cargos do usuário.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Alterar Usuario</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <EditarUsuarioForm cargos={cargos} usuario={cargosUsuario} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
