'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import CriarSuperavitForm from '@/components/gerenciamento/superavits/criarSuperavitForm';
import { listarSuperavits } from '@/lib/database/gerenciamento/superavit';
import { ErrorAlert } from '@/components/error-alert';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export default async function NovoSuperavitPage() {
  const superavitsPromise = listarSuperavits();

  const [superavits] = await Promise.all([superavitsPromise]);

  if (!superavits) {
    return <ErrorAlert error='Falha ao obter superavits.' />;
  }
  if (superavits.error) {
    return <ErrorAlert error={superavits.error} />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Novo Superavit</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <CriarSuperavitForm fornecedores={superavits} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
