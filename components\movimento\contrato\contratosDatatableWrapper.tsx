import { ErrorAlert } from '@/components/error-alert';
import ContratosDatatable from './contratosDataTable';
import {
  listarContratos,
  listarFornecedores,
} from '@/lib/database/movimento/contratos';

export default async function ContratosDatatableWrapper() {
  const result = await listarContratos();
  const fornecedores = await listarFornecedores();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data) return <ErrorAlert error={'Contratos não encontrados.'} />;

  if (fornecedores.error) return <ErrorAlert error={fornecedores.error} />;
  if (!fornecedores.data)
    return <ErrorAlert error={'Fornecedores não encontrados.'} />;

  return <ContratosDatatable data={result} fornecedores={fornecedores} />;
}
