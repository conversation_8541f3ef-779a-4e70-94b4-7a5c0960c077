'use client';

import * as React from 'react';
import { ArrowDown, Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ModulosDesc } from '@/lib/modulos';
import { ConfigModulo } from '@/types/app';
import { Checkbox } from '@/components/ui/checkbox';

export function ComboboxSelecionarModulo({
  modulosConfigurados,
  setModulosConfigurados,
}: {
  modulosConfigurados: ConfigModulo[];
  setModulosConfigurados: React.Dispatch<React.SetStateAction<ConfigModulo[]>>;
}) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState('');
  const [read, setRead] = React.useState(false);
  const [write, setWrite] = React.useState(false);
  const [update, setUpdate] = React.useState(false);
  const [_delete, setDelete] = React.useState(false); //delete é um nome reservado

  const modulosConfiguradosIds = modulosConfigurados.map((modulo) => {
    return modulo.id;
  });

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {value ? ModulosDesc[Number(value)] : 'Selecione o módulo...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar módulo...' />
            <CommandEmpty>Módulo não encontrado.</CommandEmpty>
            <CommandList>
              {Object.keys(ModulosDesc)
                .filter(
                  (v) =>
                    !isNaN(Number(v)) &&
                    Number(v) > 0 &&
                    !modulosConfiguradosIds.includes(Number(v))
                )
                .map((v) => (
                  <CommandItem
                    key={v}
                    value={v}
                    onSelect={(currentValue) => {
                      setValue(currentValue !== v ? '' : currentValue);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === v ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    {ModulosDesc[Number(v)]}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <div className='sm: flex w-full flex-wrap items-center justify-center gap-8 md:gap-4'>
        <div className='flex items-center space-x-2'>
          <Checkbox
            id='read'
            checked={read}
            onClick={() => setRead(!read)}
            disabled={value === ''}
          />
          <label
            htmlFor='read'
            className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Acessar
          </label>
        </div>
        <div className='flex items-center space-x-2'>
          <Checkbox
            id='write'
            checked={write}
            onClick={() => setWrite(!write)}
            disabled={value === ''}
          />
          <label
            htmlFor='write'
            className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Criar
          </label>
        </div>
        <div className='flex items-center space-x-2'>
          <Checkbox
            id='update'
            checked={update}
            onClick={() => setUpdate(!update)}
            disabled={value === ''}
          />
          <label
            htmlFor='update'
            className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Alterar
          </label>
        </div>
        <div className='flex items-center space-x-2'>
          <Checkbox
            id='delete'
            checked={_delete}
            onClick={() => setDelete(!_delete)}
            disabled={value === ''}
          />
          <label
            htmlFor='delete'
            className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Excluir
          </label>
        </div>
      </div>
      <Button
        variant='secondary'
        disabled={value === '' || (!read && !write && !update && !_delete)}
        onClick={(e) => {
          e.preventDefault();

          const modulos = modulosConfigurados.filter(
            (m) => m.id !== Number(value)
          );
          setModulosConfigurados([
            {
              nome: ModulosDesc[Number(value)],
              id: Number(value),
              read,
              write,
              update,
              delete: _delete,
            },
            ...modulos,
          ]);

          setValue('');
        }}
      >
        <ArrowDown className='mr-2 h-4 w-4' /> Adicionar Módulo
      </Button>
    </>
  );
}
