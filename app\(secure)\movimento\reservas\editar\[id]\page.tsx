'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { obterReserva } from '@/lib/database/movimento/reservas';
import { ErrorAlert } from '@/components/error-alert';
import FormEditarReserva from '@/components/movimento/reserva/formEditarReserva';

export default async function EditarReservaPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const reserva = await obterReserva({
    id: Number(id),
  });
  if (reserva.error) {
    return <ErrorAlert error={reserva.error} />;
  }
  if (!reserva.data) {
    return <ErrorAlert error='Falha ao obter reserva.' />;
  }
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Editar Reserva</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormEditarReserva reserva={reserva} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
