'use client';
import { toast } from 'sonner';
import { createClient } from './client';
import * as tus from 'tus-js-client';
import { SUPABASE_PROJECT_ID } from '../consts';

//Não validando com zod por estar no client, reavaliar depois

// Nova função uploadPDF com callback de progresso e retorno padronizado
export async function uploadPDF(
  file: File,
  bucketName: string,
  fileName: string,
  onProgress?: (progress: number) => void
): Promise<{ data?: { path: string }; error?: string }> {
  const supabase = createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    return { error: 'Usuário não autenticado.' };
  }

  return new Promise<{ data?: { path: string }; error?: string }>((resolve) => {
    const upload = new tus.Upload(file, {
      endpoint: `https://${SUPABASE_PROJECT_ID}.supabase.co/storage/v1/upload/resumable`,
      retryDelays: [0, 3000, 5000, 10000, 20000],
      headers: {
        authorization: `Bearer ${session.access_token}`,
        'x-upsert': 'true',
      },
      uploadDataDuringCreation: true,
      removeFingerprintOnSuccess: true,
      metadata: {
        bucketName: bucketName,
        objectName: fileName,
        contentType: 'application/pdf',
      },
      chunkSize: 6 * 1024 * 1024,
      onError: function (error) {
        console.log('Failed because: ' + error);
        resolve({ error: error.message || 'Erro no upload' });
      },
      onProgress: function (bytesUploaded, bytesTotal) {
        const percentage = (bytesUploaded / bytesTotal) * 100;
        if (onProgress) {
          onProgress(percentage);
        }
      },
      onSuccess: function () {
        resolve({ data: { path: fileName } });
      },
    });

    // Check if there are any previous uploads to continue.
    upload.findPreviousUploads().then((previousUploads) => {
      // Found previous uploads so we select the first one.
      if (previousUploads.length) {
        upload.resumeFromPreviousUpload(previousUploads[0]);
      }
      // Start the upload
      upload.start();
    });
  });
}

// Manter função original para compatibilidade com código existente
export async function uploadPDFLegacy(
  bucketName: string,
  fileName: string,
  file: File
) {
  const result = await uploadPDF(file, bucketName, fileName);
  if (result.error) {
    toast.error(result.error);
    throw new Error(result.error);
  }
}

//Derivar AES da senha do certificafo do usuário
async function deriveAESKey(
  password: string,
  salt: Uint8Array
): Promise<CryptoKey> {
  return window.crypto.subtle
    .importKey(
      'raw',
      new TextEncoder().encode(password),
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    )
    .then((key) => {
      return window.crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: salt as unknown as BufferSource,
          iterations: 100000,
          hash: 'SHA-256',
        },
        key,
        { name: 'AES-GCM', length: 256 },
        false,
        ['encrypt', 'decrypt']
      );
    });
}

async function encryptFile(
  file: ArrayBuffer,
  password: string
): Promise<Uint8Array> {
  try {
    // Gerar salt e IV
    const salt = window.crypto.getRandomValues(new Uint8Array(16)); // 16 bytes de salt
    const iv = window.crypto.getRandomValues(new Uint8Array(12)); // 12 bytes para IV

    // Derivar a chave AES
    const key = await deriveAESKey(password, salt);

    // Criptografar com AES-GCM
    const encryptedData = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv,
        tagLength: 128, // 128 bits de auth tag (16 bytes)
      },
      key,
      file
    );

    const encryptedArray = new Uint8Array(encryptedData);
    // Extrair a auth tag (últimos 16 bytes)
    const tag = encryptedArray.slice(encryptedArray.length - 16);
    // O ciphertext real é o restante
    const ciphertext = encryptedArray.slice(0, encryptedArray.length - 16);

    // Combinar salt, IV, auth tag e ciphertext
    const combinedArray = new Uint8Array(
      salt.length + iv.length + tag.length + ciphertext.length
    );
    combinedArray.set(salt, 0);
    combinedArray.set(iv, salt.length);
    combinedArray.set(tag, salt.length + iv.length);
    combinedArray.set(ciphertext, salt.length + iv.length + tag.length);

    return combinedArray;
  } catch (error) {
    console.error('Error encrypting file:', error);
    throw error;
  }
}

export async function uploadPFX(file: File, password: string) {
  const supabase = createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    return toast.error('Usuário não autenticado.');
  }

  // Encrypt the file before uploading

  //File to ArrayBuffer
  const fileBuffer = await file.arrayBuffer();

  const encryptedFile = await encryptFile(fileBuffer, password);
  const encryptedFileBuffer = new Blob([new Uint8Array(encryptedFile)], {
    type: 'application/octet-stream',
  });
  //Acesso controlado via supabase RLS, baseado no id do usuário autenticado
  return new Promise<void>(async (resolve, reject) => {
    var upload = new tus.Upload(encryptedFileBuffer, {
      endpoint: `https://${SUPABASE_PROJECT_ID}.supabase.co/storage/v1/upload/resumable`,
      retryDelays: [0, 3000, 5000, 10000, 20000],
      headers: {
        authorization: `Bearer ${session.access_token}`,
        'x-upsert': 'true',
      },
      uploadDataDuringCreation: true,
      removeFingerprintOnSuccess: true, // Important if you want to allow re-uploading the same file https://github.com/tus/tus-js-client/blob/main/docs/api.md#removefingerprintonsuccess
      metadata: {
        bucketName: 'certificados',
        objectName: `${session.user.id}/cert.pfx.enc`,
        contentType: 'application/octet-stream',
      },
      chunkSize: 6 * 1024 * 1024, // NOTE: it must be set to 6MB (for now) do not change it
      onError: function (error) {
        console.log('Failed because: ' + error);
        reject(error);
      },
      onProgress: function (bytesUploaded, bytesTotal) {
        var percentage = ((bytesUploaded / bytesTotal) * 100).toFixed(2);
        console.log(bytesUploaded, bytesTotal, percentage + '%');
      },
      onSuccess: function () {
        resolve();
      },
    });

    // Check if there are any previous uploads to continue.
    const previousUploads = await upload.findPreviousUploads();
    // Found previous uploads so we select the first one.
    if (previousUploads.length) {
      upload.resumeFromPreviousUpload(previousUploads[0]);
    }
    // Start the upload
    upload.start();
  });
}
