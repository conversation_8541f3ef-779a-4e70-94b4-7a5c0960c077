import { ErrorAlert } from '@/components/error-alert';
import { LiquidacoesDataTable } from './liquidacoesDataTable';
import { listarLiquidacoes } from '@/lib/database/movimento/liquidacoes';

export async function LiquidacoesDatatableWrapper() {
  const result = await listarLiquidacoes();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Liquidações não encontradas.'} />;

  return <LiquidacoesDataTable data={result} />;
}
