'use client';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Eye, Pencil } from 'lucide-react';
import { StatusProtocolo } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol, montarCodigoDepartamento } from '@/lib/utils';
import { useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { DialogoAlterarStatusLote } from './dialogoAlterarStatusLote';
import { BotaoImprimirProtocolos } from './botaoImprimirProtocolos';
import { IndicadorStatus } from './indicadorStatus';
import {
  atualizarStatusProtocolosLote,
  listarProtocolos,
} from '@/lib/database/movimento/protocolos';
import { toast } from 'sonner';
import { ReusableDatatableFiltroProtocolos } from '@/components/datatable/reusableDatatableFiltroProtocolos';
import {
  DatatableActionsDropdown,
  DatatableAction,
} from '@/components/datatable/DatatableActionsDropdown';

export default function ProtocolosDataTable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarProtocolos>>;
}) {
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);

  if (!data.data) {
    return null;
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(data.data?.protocolos.map((row) => row.id) || []);
    } else {
      setSelectedRows([]);
    }
  };

  const handleSelectRow = (rowId: number, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, rowId]);
    } else {
      setSelectedRows(selectedRows.filter((id) => id !== rowId));
    }
  };

  const handleAlterarStatusLote = async (ids: number[], novoStatus: number) => {
    try {
      const result = await atualizarStatusProtocolosLote({
        ids,
        novoStatus,
        obs: 'Alteração',
      });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(
          `Status alterado com sucesso para ${ids.length} protocolo(s)`
        );
        setSelectedRows([]);
      }
    } catch (error) {
      toast.error('Erro ao alterar status dos protocolos');
    }
  };

  const handleImprimirProtocolos = async (_ids: number[], _options: any) => {
    try {
      // Implement print functionality - generate PDF or open print dialog
    } catch (error) {
      toast.error('Erro ao gerar impressão');
    }
  };

  const columns: ColumnDef<(typeof data.data.protocolos)[0]>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(checked) => {
            table.toggleAllPageRowsSelected(checked === true);
            handleSelectAll(checked === true);
          }}
          aria-label='Selecionar tudo'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='block'
          checked={row.getIsSelected()}
          onCheckedChange={(checked) => {
            row.toggleSelected(checked === true);
            handleSelectRow(row.original.id, checked === true);
          }}
          aria-label='Selecionar linha'
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'id',
      header: 'Protocolo',
      filterFn: 'includesString',
    },
    {
      id: 'reserva',
      accessorFn: (row) => row.reserva?.id,
      header: 'Reserva',
      filterFn: 'includesString',
      cell: ({ row }) => row.original.reserva?.id || '-',
    },
    {
      id: 'despesa',
      accessorFn: (row) => row.reserva?.dotacao?.despesa,
      header: 'Despesa',
      filterFn: 'includesString',
      cell: ({ row }) => row.original.reserva?.dotacao?.despesa || '-',
    },
    {
      accessorKey: 'resumo',
      header: 'Resumo',
      filterFn: 'includesString',
      cell: ({ row }) => row.original.resumo || '-',
    },
    {
      accessorKey: 'obs',
      header: 'Obs',
      filterFn: 'includesString',
      cell: ({ row }) => row.original.obs || '-',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        return <IndicadorStatus status={getValue<number>()} />;
      },
    },
    {
      accessorKey: 'numeroAF',
      header: 'AF',
      cell: ({ row }) => {
        const { numeroAF, exercicioAF } = row.original;
        return numeroAF ? `${numeroAF}/${exercicioAF}` : '-';
      },
    },
    {
      accessorKey: 'numeroEmpenho',
      header: 'Empenho',
      cell: ({ row }) => {
        const { numeroEmpenho, exercicioEmpenho } = row.original;
        return numeroEmpenho ? `${numeroEmpenho}/${exercicioEmpenho}` : '-';
      },
    },
    {
      accessorKey: 'reserva.usarTotal',
      header: 'Valor',
      cell: ({ row }) => {
        return row.original.reserva?.usarTotal
          ? currency(row.original.reserva.usarTotal, currencyOptionsNoSymbol)
              .format()
              .replace(/0$/, '')
          : '-';
      },
    },
    {
      id: 'secretariaId',
      accessorFn: (row) => row.secretariaId,
      header: 'Secretaria',
      filterFn: 'equals',
      cell: ({ row }) => row.original.reserva?.dotacao?.secretaria?.nome || '-',
    },
    {
      id: 'departamentoId',
      accessorFn: (row) => row.departamentoId,
      header: 'Departamento',
      filterFn: 'equals',
      cell: ({ row }) =>
        montarCodigoDepartamento(
          row.original.secretariaId || 0,
          row.original.departamentoId || 0
        ),
    },
    {
      accessorKey: 'dataAbertura',
      header: 'Data Abertura',
      cell: ({ getValue }) => {
        return new Date(getValue<string>()).toLocaleDateString('pt-BR');
      },
    },
    {
      accessorKey: 'dataStatus',
      header: 'Data Status',
      cell: ({ getValue }) => {
        return new Date(getValue<string>()).toLocaleDateString('pt-BR');
      },
    },
    {
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => {
        const editableStatuses = [
          StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS,
          StatusProtocolo.RECEBIMENTO_EM_COMPRAS,
          StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE,
          StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE,
          StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE,
          StatusProtocolo.RECEBIMENTO_EM_GABINETE,
          StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM,
        ];

        const actions: DatatableAction[] = [
          {
            label: 'Editar',
            icon: <Pencil className='size-4' />,
            href: `/movimento/protocolo/editar/${row.original.id}`,
            show: editableStatuses.includes(row.original.status),
          },
        ];

        return (
          <div className='flex items-center gap-2'>
            <Link href={`/movimento/protocolo/visualizar/${row.original.id}`}>
              <Button variant='ghost' size='sm'>
                <Eye className='size-4' />
              </Button>
            </Link>
            <DatatableActionsDropdown actions={actions} />
          </div>
        );
      },
    },
  ];

  return (
    <div className='container mx-auto'>
      {selectedRows.length > 0 && (
        <div className='mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4'>
          <div className='flex items-center justify-between'>
            <span className='font-medium text-blue-800'>
              {selectedRows.length} protocolo(s) selecionado(s)
            </span>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => setIsStatusDialogOpen(true)}
              >
                Alterar Status
              </Button>
              <BotaoImprimirProtocolos
                selectedIds={selectedRows}
                onPrint={handleImprimirProtocolos}
              />
            </div>
          </div>
        </div>
      )}
      <ReusableDatatableFiltroProtocolos
        columns={columns}
        data={data.data?.protocolos || []}
        secretarias={data.data?.secretarias || []}
        departamentos={data.data?.departamentos || []}
      />

      {/* Batch Status Change Dialog */}
      <DialogoAlterarStatusLote
        isOpen={isStatusDialogOpen}
        onClose={() => setIsStatusDialogOpen(false)}
        selectedIds={selectedRows}
        onConfirm={handleAlterarStatusLote}
      />
    </div>
  );
}
