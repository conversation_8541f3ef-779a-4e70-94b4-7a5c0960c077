'use client';
import { ColumnDef } from '@tanstack/react-table';
import { Network } from 'lucide-react';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { codigoSecretariaMask } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import { listarDepartamentos } from '@/lib/database/gerenciamento/departamentos';
import { AlertDesativarDepartamento } from './alertDesativarDepartamento';
import { AlertAtivarDepartamento } from './alertAtivarDepartamento';
import { DialogRenomearDepartamento } from './dialogRenomearDepartamento';
import { obterSecretaria } from '@/lib/database/gerenciamento/secretarias';

export default function DepartamentosDatatable({
  data,
  secretaria,
}: {
  data: Awaited<ReturnType<typeof listarDepartamentos>>;
  secretaria: Awaited<ReturnType<typeof obterSecretaria>>;
}) {
  if (!data.data || !secretaria.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Código',
      filterFn: 'includesString',
      cell: ({ row }) =>
        `${codigoSecretariaMask(secretaria.data?.codigo.toString())}.${codigoSecretariaMask(row.getValue('codigo'))}.00`,
    },
    {
      accessorKey: 'nome',
      header: 'Departamento',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link
            href={`/gerenciamento/secretarias/${secretaria.data.id}/departamentos/${row.original.id}/subdepartamentos`}
          >
            <Button type='button' variant='secondary'>
              <Network className='mr-2 size-4' /> Subdepartamentos
            </Button>
          </Link>
          <Separator orientation='vertical' />
          <DialogRenomearDepartamento
            idSecretaria={secretaria.data.id}
            idDepartamento={row.original.id}
            nomeDepartamento={row.original.nome}
            codigoDepartamento={row.original.codigo}
            codigoSecretaria={secretaria.data.codigo}
          />
          {row.original.ativo ? (
            <AlertDesativarDepartamento
              idSecretaria={secretaria.data.id}
              idDepartamento={row.original.id}
              nomeDepartamento={row.original.nome}
              codigoDepartamento={row.original.codigo}
              codigoSecretaria={secretaria.data.codigo}
            />
          ) : (
            <AlertAtivarDepartamento
              idSecretaria={secretaria.data.id}
              idDepartamento={row.original.id}
              nomeDepartamento={row.original.nome}
              codigoDepartamento={row.original.codigo}
              codigoSecretaria={secretaria.data.codigo}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <>
      <div>
        <span className='mr-2 font-bold'>Secretaria:</span>{' '}
        {`${codigoSecretariaMask(secretaria.data?.codigo.toString())}.00.00`} -{' '}
        {secretaria.data.nome}
      </div>
      <div className='container mx-auto'>
        <ReusableDatatable columns={columns} data={data.data} />
      </div>
    </>
  );
}
