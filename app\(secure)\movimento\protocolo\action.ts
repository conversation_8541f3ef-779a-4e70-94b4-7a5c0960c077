'use server';

import { revalidatePath } from 'next/cache';
import {
  atualizarStatusProtocolo,
  vincularAF,
  vincularEmpenho,
} from '@/lib/database/movimento/protocolos';
import {
  alterarStatusProtocoloSchema,
  vincularAFSchema,
  vincularEmpenhoSchema,
} from '@/lib/validation';
import { toastAlgoDeuErrado } from '@/lib/utils';

export async function atualizarStatusProtocoloAction(formData: FormData) {
  try {
    const parsedData = alterarStatusProtocoloSchema.safeParse({
      id: parseInt(formData.get('id') as string),
      novoStatus: parseInt(formData.get('novoStatus') as string),
      obs: formData.get('obs') as string,
    });

    if (!parsedData.success) {
      return { error: 'Dados inválidos' };
    }

    const result = await atualizarStatusProtocolo(parsedData.data);

    if (result.error) {
      return { error: result.error };
    }

    revalidatePath('/movimento/protocolo');
    revalidatePath(`/movimento/protocolo/visualizar/${parsedData.data.id}`);
    return { success: true, data: result.data };
  } catch (error) {
    console.error('Erro ao atualizar status do protocolo:', error);
    return { error: toastAlgoDeuErrado };
  }
}

export async function vincularAFAction(formData: FormData) {
  try {
    const parsedData = vincularAFSchema.safeParse({
      id: parseInt(formData.get('id') as string),
      numeroAF: parseInt(formData.get('numeroAF') as string),
      exercicioAF: parseInt(formData.get('exercicioAF') as string),
    });

    if (!parsedData.success) {
      return { error: 'Dados inválidos' };
    }

    const result = await vincularAF(parsedData.data);

    if (result.error) {
      return { error: result.error };
    }

    revalidatePath('/movimento/protocolo');
    revalidatePath(`/movimento/protocolo/visualizar/${parsedData.data.id}`);
    return { success: true, data: result.data };
  } catch (error) {
    console.error('Erro ao vincular AF:', error);
    return { error: toastAlgoDeuErrado };
  }
}

export async function vincularEmpenhoAction(formData: FormData) {
  try {
    const parsedData = vincularEmpenhoSchema.safeParse({
      id: parseInt(formData.get('id') as string),
      numeroEmpenho: parseInt(formData.get('numeroEmpenho') as string),
      exercicioEmpenho: parseInt(formData.get('exercicioEmpenho') as string),
    });

    if (!parsedData.success) {
      return { error: 'Dados inválidos' };
    }

    const result = await vincularEmpenho(parsedData.data);

    if (result.error) {
      return { error: result.error };
    }

    revalidatePath('/movimento/protocolo');
    revalidatePath(`/movimento/protocolo/visualizar/${parsedData.data.id}`);
    return { success: true, data: result.data };
  } catch (error) {
    console.error('Erro ao vincular empenho:', error);
    return { error: toastAlgoDeuErrado };
  }
}
