'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { cancelarReservaSchema } from '@/lib/validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { DialogDescription } from '@radix-ui/react-dialog';
import { devolverReserva } from '@/lib/database/assinatura/reservas';

export function DialogDevolverReserva({ idReserva }: { idReserva: number }) {
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof cancelarReservaSchema>>({
    resolver: zodResolver(cancelarReservaSchema),
    defaultValues: {
      id: idReserva,
      motivo: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof cancelarReservaSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof cancelarReservaSchema> = {
        id: idReserva,
        motivo: values.motivo,
      };
      const res = await devolverReserva(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        setOpen(false);
        setLoading(false);
        toast.success('Reserva devolvida com sucesso.');
      }
    } catch (error: any) {
      setLoading(false);
      setOpen(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='w-[110px] text-red-800'
        >
          <X className='size-4' /> Recusar
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Devolver Reserva</DialogTitle>
          <DialogDescription>
            Cancelar assinaturas da reserva?
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex flex-wrap gap-4 py-4'>
              <FormField
                control={form.control}
                name='motivo'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Motivo
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={255}
                        minLength={2}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogFooter>
          <Button
            type='submit'
            variant='destructive'
            disabled={loading || !form.formState.isValid}
            onClick={form.handleSubmit(onSubmit)}
          >
            {loading ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <X className='h-4 w-4' /> Recusar Assinatura
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
