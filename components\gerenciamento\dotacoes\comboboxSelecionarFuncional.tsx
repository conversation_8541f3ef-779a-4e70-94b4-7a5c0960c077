'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarFuncionaisAtivas } from '@/lib/database/gerenciamento/dotacoes';
import { UseFormReturn } from 'react-hook-form';

export function ComboboxSelecionaFuncional({
  funcionais,
  form,
}: {
  funcionais: Awaited<ReturnType<typeof listarFuncionaisAtivas>>;
  form: UseFormReturn<
    {
      desc: string;
      despesa: number;
      fonte: number;
      codAplicacao: number;
      funcionalId: number;
      economicaId: number;
      valorInicial: number;
      cotaReducaoInicial: number;
      suplementacao: number;
      anulacao: number;
      secretariaId?: number | undefined;
      departamentoId?: number | undefined;
      subdepartamentoId?: number | undefined;
    },
    any,
    undefined
  >;
}) {
  const [open, setOpen] = React.useState(false);

  const funcionalSelecionada =
    funcionais.data?.find((funcional) => {
      return form.getValues('funcionalId') === funcional.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {funcionalSelecionada
              ? funcionalSelecionada.codigo + ' - ' + funcionalSelecionada.desc
              : 'Selecione a funcional...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar funcional...' />
            <CommandEmpty>Econômica não encontrada.</CommandEmpty>
            <CommandList>
              {funcionais.data?.map((func) => (
                <CommandItem
                  key={func.id}
                  value={`${func.codigo} - ${func.desc}`}
                  onSelect={() => {
                    form.setValue('funcionalId', func.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      form.getValues('funcionalId') === func.id
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  {func.codigo} - {func.desc}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
