import { siteConfig } from '@/config/site';
import { StatusLiquidacao, StatusLiquidacaoDesc } from '@/lib/enums';
import { toCurrency } from '@/lib/serverUtils';

interface RelatorioLiquidacoesProps {
  liquidacoes: NonNullable<
    Required<
      Awaited<
        ReturnType<
          typeof import('@/lib/database/relatorios/liquidacoes').gerarRelatorioLiquidacoes
        >
      >
    >
  >['data']['liquidacoes'];
  titulo?: string;
  filtros?: {
    exercicio?: number;
    secretaria?: string;
    departamento?: string;
    subdepartamento?: string;
    fornecedor?: string;
    status?: number[];
    dataInicio?: string;
    dataFim?: string;
    incluirProcessados?: boolean;
    incluirNaoProcessados?: boolean;
  };
  resumo?: {
    totalLiquidacoes: number;
    valorTotal: number;
    valorTotalProcessado: number;
    valorTotalNaoProcessado: number;
    mediaValor: number;
  };
}

export const RelatorioLiquidacoes = ({
  liquidacoes,
  titulo = 'Relatório de Liquidações',
  filtros,
  resumo,
}: RelatorioLiquidacoesProps) => {
  const getStatusDescription = (status: number) => {
    return StatusLiquidacaoDesc[status as StatusLiquidacao] || 'N/A';
  };

  const getStatusText = () => {
    if (!filtros?.status || filtros.status.length === 0) return 'Todos';
    if (filtros.status.length === 1)
      return getStatusDescription(filtros.status[0]);
    return 'Múltiplos';
  };

  const getProcessingText = () => {
    if (filtros?.incluirProcessados && filtros?.incluirNaoProcessados)
      return 'Todos';
    if (filtros?.incluirProcessados) return 'Processados';
    if (filtros?.incluirNaoProcessados) return 'Não Processados';
    return 'Todos';
  };

  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
            {filtros.secretaria && <p>Secretaria: {filtros.secretaria}</p>}
            {filtros.departamento && (
              <p>Departamento: {filtros.departamento}</p>
            )}
            {filtros.subdepartamento && (
              <p>Subdepartamento: {filtros.subdepartamento}</p>
            )}
            {filtros.fornecedor && <p>Fornecedor: {filtros.fornecedor}</p>}
            {filtros.status && filtros.status.length > 0 && (
              <p>Status: {getStatusText()}</p>
            )}
            {(filtros.incluirProcessados !== undefined ||
              filtros.incluirNaoProcessados !== undefined) && (
              <p>Processamento: {getProcessingText()}</p>
            )}
            {filtros.dataInicio && filtros.dataFim && (
              <p>
                Período: {filtros.dataInicio} a {filtros.dataFim}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Resumo */}
      {resumo && (
        <div className='mb-6 rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Resumo</h3>
          <div className='grid grid-cols-5 gap-3 text-sm'>
            <div className='text-center'>
              <div className='font-medium'>Total de Liquidações</div>
              <div className='font-mono'>{resumo.totalLiquidacoes}</div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Total</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotal).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Processado</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotalProcessado)
                  .format()
                  .replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Não Processado</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotalNaoProcessado)
                  .format()
                  .replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Médio</div>
              <div className='font-mono'>
                {toCurrency(resumo.mediaValor).format().replace(/0$/, '')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabela */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Liquidação</th>
              <th className='p-1 text-left font-bold'>Empenho</th>
              <th className='p-1 text-left font-bold'>Fornecedor</th>
              <th className='p-1 text-left font-bold'>Secretaria</th>
              <th className='p-1 text-left font-bold'>Departamento</th>
              <th className='p-1 text-left font-bold'>Documento</th>
              <th className='p-1 text-left font-bold'>Status</th>
              <th className='p-1 text-left font-bold'>Data</th>
              <th className='p-1 text-right font-bold'>Valor</th>
              <th className='p-1 text-right font-bold'>Mês Ref.</th>
            </tr>
          </thead>
          <tbody>
            {liquidacoes.map((liq, index) => (
              <tr
                key={liq.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {liq.numero}/{liq.exercicio}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {liq.empenhoOrigem}
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={liq.fornecedor?.nome || 'N/A'}
                  >
                    {liq.fornecedor?.nome || 'N/A'}
                  </div>
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={liq.secretaria?.nome || 'N/A'}
                  >
                    {liq.secretaria?.nome || 'N/A'}
                  </div>
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={liq.departamento?.nome || 'N/A'}
                  >
                    {liq.departamento?.nome || 'N/A'}
                  </div>
                </td>
                <td className='border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={liq.numero.toString() || 'N/A'}
                  >
                    {liq.numero || 'N/A'}
                  </div>
                </td>
                <td className='border-b border-gray-200 p-1'>
                  <span
                    className={`rounded px-1 py-0.5 text-xs ${
                      liq.status === StatusLiquidacao.PROCESSADO
                        ? 'bg-green-100 text-green-800'
                        : liq.status === StatusLiquidacao.NAO_PROCESSADO
                          ? 'bg-yellow-100 text-yellow-800'
                          : liq.status === StatusLiquidacao.ESTORNADA
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {getStatusDescription(liq.status)}
                  </span>
                </td>
                <td className='border-b border-gray-200 p-1'>
                  {new Date(liq.data).toLocaleDateString('pt-BR')}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(liq.valorTotal).format().replace(/0$/, '')}
                </td>
                <td className='border-b border-gray-200 p-1 text-center'>
                  {liq.mesReferencia || '-'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Distribuição Mensal */}
      {liquidacoes.some((liq) => liq.valoresMensais.length > 0) && (
        <div className='mt-6'>
          <h3 className='mb-3 font-semibold'>Distribuição Mensal</h3>
          <div className='overflow-x-auto'>
            <table className='w-full border-collapse text-xs'>
              <thead>
                <tr className='border-b-2 border-gray-800'>
                  <th className='p-1 text-center font-bold'>Empenho</th>
                  <th className='p-1 text-center font-bold'>Liquidação</th>
                  <th className='p-1 text-center font-bold'>Jan</th>
                  <th className='p-1 text-center font-bold'>Fev</th>
                  <th className='p-1 text-center font-bold'>Mar</th>
                  <th className='p-1 text-center font-bold'>Abr</th>
                  <th className='p-1 text-center font-bold'>Mai</th>
                  <th className='p-1 text-center font-bold'>Jun</th>
                  <th className='p-1 text-center font-bold'>Jul</th>
                  <th className='p-1 text-center font-bold'>Ago</th>
                  <th className='p-1 text-center font-bold'>Set</th>
                  <th className='p-1 text-center font-bold'>Out</th>
                  <th className='p-1 text-center font-bold'>Nov</th>
                  <th className='p-1 text-center font-bold'>Dez</th>
                </tr>
              </thead>
              <tbody>
                {liquidacoes.map((liq, index) => (
                  <tr
                    key={`monthly-${liq.id}`}
                    className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                  >
                    <td className='border-b border-gray-200 p-1 text-center font-mono'>
                      {liq.empenhoOrigem}
                    </td>
                    <td className='border-b border-gray-200 p-1 text-center font-mono'>
                      {liq.numero}/{liq.exercicio}
                    </td>
                    {Array.from({ length: 12 }, (_, i) => {
                      const mesData = liq.valoresMensais.find(
                        (vm) =>
                          vm.mes ===
                          [
                            'Jan',
                            'Fev',
                            'Mar',
                            'Abr',
                            'Mai',
                            'Jun',
                            'Jul',
                            'Ago',
                            'Set',
                            'Out',
                            'Nov',
                            'Dez',
                          ][i]
                      );
                      return (
                        <td
                          key={i}
                          className='border-b border-gray-200 p-1 text-right font-mono'
                        >
                          {mesData
                            ? toCurrency(mesData.valorLiquidado)
                                .format()
                                .replace(/0$/, '')
                            : '-'}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de registros: {liquidacoes.length}</p>
        <p className='mt-1'>{siteConfig.name}</p>
      </div>
    </div>
  );
};
