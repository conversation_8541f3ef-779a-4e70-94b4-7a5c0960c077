'use client';
import { ColumnDef } from '@tanstack/react-table';
import { obterCotasReducao } from '@/lib/database/gerenciamento/dotacoes';
import { ReusableDatatableSemFiltro } from '@/components/datatable/reusableDatatableSemFiltro';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import currency from 'currency.js';

export default function CotasReducaoDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof obterCotasReducao>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'data',
      header: 'Data',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        const date = new Date(getValue<Date>());
        return (
          <>
            {date.toLocaleDateString('pt-BR') +
              ' ' +
              date.toLocaleTimeString('pt-BR')}
          </>
        );
      },
    },
    {
      accessorKey: 'motivo',
      header: 'Motivo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'valorTotal',
      header: 'Valor',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'reducao',
      header: 'Tipo',
      cell: ({ row }) => {
        if (row.original.reducao === true) {
          return `Dotação => Redução`;
        } else {
          return `Redução => Dotação`;
        }
      },
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatableSemFiltro columns={columns} data={data.data} />
    </div>
  );
}
