'use client';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Eye, Pencil, FileText, Plus } from 'lucide-react';
import { listarReservas } from '@/lib/database/movimento/reservas';
import { StatusReserva, StatusProtocoloDesc } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { ReusableDatatableFiltroIdDespesaSecretariaDepartamento } from '@/components/datatable/reusableDatatableFiltroIdDespesaSecretariaDepartamento';
import { criarProtocolo } from '@/lib/database/movimento/protocolos';
import { toast } from 'sonner';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  DatatableActionsDropdown,
  DatatableAction,
} from '@/components/datatable/DatatableActionsDropdown';
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export default function ReservasDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarReservas>>;
}) {
  const [isCreatingProtocol, setIsCreatingProtocol] = useState<number | null>(
    null
  );
  const router = useRouter();
  if (!data.data?.reservas) return null;

  const handleCriarProtocolo = async (reservaId: number, resumo: string) => {
    setIsCreatingProtocol(reservaId);
    try {
      const result = await criarProtocolo({
        idReserva: reservaId,
        resumo: resumo,
        obs: 'Protocolo criado automaticamente a partir da reserva',
      });

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Protocolo criado com sucesso!');
        // Optionally redirect to protocol page or refresh data
        router.push('/movimento/protocolo');
      }
    } catch (error) {
      toast.error('Erro ao criar protocolo');
    } finally {
      setIsCreatingProtocol(null);
    }
  };
  const columns: ColumnDef<(typeof data.data.reservas)[0]>[] = [
    {
      accessorKey: 'id',
      header: 'Reserva',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'dotacao.despesa',
      id: 'despesa',
      header: 'Despesa',
      filterFn: 'includesString',
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='text-xs font-medium'>
            {row.original.dotacao.despesa}
          </span>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className='text-muted-foreground max-w-[150px] cursor-help truncate text-xs'>
                {row.original.dotacao.desc}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className='max-w-xs'>{row.original.dotacao.desc}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      ),
    },
    {
      accessorKey: 'resumo',
      header: 'Resumo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'obs',
      header: 'Obs',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      filterFn: 'includesString',
      cell: ({ row }) => {
        const reservaStatus = row.original.status;
        const protocolo = row.original.protocolo;

        if (protocolo) {
          return (
            <div className='space-y-1'>
              <div className='text-sm font-medium'>
                {StatusReserva[reservaStatus]}
              </div>
              <div className='text-xs text-blue-600'>
                Protocolo #{protocolo.numero}:{' '}
                {
                  StatusProtocoloDesc[
                    protocolo.status as keyof typeof StatusProtocoloDesc
                  ]
                }
              </div>
            </div>
          );
        }

        return StatusReserva[reservaStatus];
      },
    },
    {
      accessorKey: 'usarTotal',
      header: 'Valor Total',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'secretariaId',
      header: 'Secretaria',
      filterFn: 'equals',
    },
    {
      accessorKey: 'departamentoId',
      header: 'Departamento',
      filterFn: 'equals',
    },
    {
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => {
        const actions: DatatableAction[] = [
          {
            label: 'Editar',
            icon: <Pencil className='size-4' />,
            href: `/movimento/reservas/editar/${row.original.id}`,
            show:
              row.original.status === StatusReserva.Reservado ||
              row.original.status === StatusReserva.Devolvido,
          },
          {
            label: 'Criar Empenho',
            icon: <Plus className='size-4' />,
            href: `/movimento/empenhos/novo?reserva=${row.original.id}`,
            show: row.original.status === StatusReserva.Reservado,
          },
          {
            label: 'Criar Protocolo',
            icon: <FileText className='size-4' />,
            onClick: () =>
              handleCriarProtocolo(
                row.original.id,
                row.original.resumo ||
                  `Protocolo para reserva ${row.original.id}`
              ),
            disabled: isCreatingProtocol === row.original.id,
            loading: isCreatingProtocol === row.original.id,
            show:
              row.original.status === StatusReserva.Reservado &&
              !row.original.protocolo,
          },
          {
            label: 'Ver Protocolo',
            icon: <Eye className='size-4' />,
            href: `/movimento/protocolo/visualizar/${row.original.protocolo.id}`,
            show: !!row.original.protocolo,
          },
        ];

        return (
          <div className='flex items-center gap-2'>
            <Link href={`/movimento/reservas/visualizar/${row.original.id}`}>
              <Button variant='ghost' size='sm'>
                <Eye className='size-4' />
              </Button>
            </Link>
            <DatatableActionsDropdown actions={actions} />
          </div>
        );
      },
    },
  ];
  return (
    <TooltipProvider>
      <div className='container mx-auto'>
        <ReusableDatatableFiltroIdDespesaSecretariaDepartamento
          columns={columns}
          data={data.data.reservas}
          secretarias={data.data.secretarias}
          departamentos={data.data.departamentos}
        />
      </div>
    </TooltipProvider>
  );
}
