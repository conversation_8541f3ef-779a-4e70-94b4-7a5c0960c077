'use server';
import { z } from 'zod';
import { ensureAuth } from '@/lib/supabase/actions';
import { ErrorAlert } from '@/components/error-alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { RelatorioAFLista } from '@/components/relatorios/relatorioAFLista';
import { listarAFsParaRelatorio } from '@/lib/database/relatorios/afs';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';

async function RelatorioAFsContent() {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_AFS,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const result = await temPermissao({ ...parametrosPermissao, bearer: '' });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const params = {
    exercicio: result.exercicio,
    bearer: '',
  };

  const afsResult = await listarAFsParaRelatorio(params);

  if (afsResult.error) {
    return <ErrorAlert error={afsResult.error} />;
  }

  if (!afsResult.data) {
    return <ErrorAlert error='Falha ao obter AFs.' />;
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Relatório de AFs
          </h1>
          <p className='text-muted-foreground'>
            Visualize e exporte o relatório de Autorizações de Fornecimento
          </p>
        </div>
        <div className='flex gap-4'>
          <BotaoDownloadPDF
            url='/movimento/af/imprimir'
            title='Imprimir Relatório'
          />
        </div>
      </div>

      <div className='rounded-lg bg-white p-6 shadow'>
        <RelatorioAFLista
          afs={afsResult.data.afs}
          titulo='Relatório de Autorizações de Fornecimento'
          resumo={afsResult.data.resumo}
        />
      </div>
    </div>
  );
}

export default async function RelatorioAFsPage() {
  await ensureAuth();

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de AFs</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <RelatorioAFsContent />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
