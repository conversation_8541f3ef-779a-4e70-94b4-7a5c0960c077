'use client';
import { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { FormCadastro } from '@/components/user/formCadastro';

export default function CadastroPage() {
  const [isMounted, setIsMounted] = useState(false);
  const isOpen = true;

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted && isOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cadastro</AlertDialogTitle>
          </AlertDialogHeader>
          <FormCadastro />
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
