import { siteConfig } from '@/config/site';
import { StatusAF, StatusAFDesc } from '@/lib/enums';
import { toCurrency } from '@/lib/serverUtils';
import { AFReportData } from '@/lib/database/relatorios/afs';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';

interface RelatorioAFProps {
  af: AFReportData;
}

export const RelatorioAF = ({ af }: RelatorioAFProps) => {
  // Calculate utilization with cross-empenho support
  const valorUtilizado = af.liquidacoes.reduce(
    (sum, liq) => sum + liq.valorTotal,
    0
  );
  const valorCrossEmpenho =
    af.liquidacoesCrossEmpenho?.reduce((sum, liq) => sum + liq.valorTotal, 0) ||
    0;
  const totalUtilizado = valorUtilizado + valorCrossEmpenho;
  const saldoDisponivel = currency(
    af.valorTotal,
    currencyOptionsNoSymbol
  ).subtract(totalUtilizado).value;

  return (
    <div className='mx-auto max-w-[900px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>
          AUTORIZAÇÃO DE FORNECIMENTO (AF)
        </h2>
        <div className='text-sm text-gray-600'>
          <p>Data: {new Date().toLocaleDateString('pt-BR')}</p>
          <p>Hora: {new Date().toLocaleTimeString('pt-BR')}</p>
        </div>
      </div>

      {/* Informações Principais */}
      <div className='mb-6'>
        <h3 className='mb-3 border-b pb-1 font-semibold'>Dados da AF</h3>
        <div className='grid grid-cols-2 gap-3 text-sm'>
          <div>
            <span className='font-medium'>AF Nº:</span> {af.numero}/
            {af.exercicio}
          </div>
          <div>
            <span className='font-medium'>Status:</span>{' '}
            {StatusAFDesc[af.status as StatusAF] || 'N/A'}
          </div>
          <div className='col-span-2'>
            <span className='font-medium'>Data de Emissão:</span>{' '}
            {new Date(af.data).toLocaleDateString('pt-BR')}
          </div>
          <div className='col-span-2'>
            <span className='font-medium'>Resumo:</span> {af.resumo || 'N/A'}
          </div>
          {af.obs && (
            <div className='col-span-2'>
              <span className='font-medium'>Observações:</span> {af.obs}
            </div>
          )}
        </div>
      </div>

      {/* Informações do Empenho */}
      <div className='mb-6'>
        <h3 className='mb-3 border-b pb-1 font-semibold'>Dados do Empenho</h3>
        <div className='grid grid-cols-2 gap-3 text-sm'>
          <div>
            <span className='font-medium'>Empenho Nº:</span> {af.empenho.numero}
            /{af.empenho.exercicio}
          </div>
          <div>
            <span className='font-medium'>Valor Total:</span>{' '}
            {toCurrency(af.empenho.valorTotal).value}
          </div>
          {af.empenho.fornecedor && (
            <div className='col-span-2'>
              <span className='font-medium'>Fornecedor:</span>{' '}
              {af.empenho.fornecedor.nome}
            </div>
          )}
          {af.empenho.contrato && (
            <div className='col-span-2'>
              <span className='font-medium'>Contrato:</span>{' '}
              {af.empenho.contrato.processo}/{af.empenho.contrato.processoAno}
            </div>
          )}
          {af.empenho.resumo && (
            <div className='col-span-2'>
              <span className='font-medium'>Resumo do Empenho:</span>{' '}
              {af.empenho.resumo}
            </div>
          )}
        </div>
      </div>

      {/* Informações da Dotação */}
      {af.empenho.dotacao && (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Dados da Dotação</h3>
          <div className='grid grid-cols-2 gap-3 text-sm'>
            {af.empenho.dotacao.secretaria && (
              <div>
                <span className='font-medium'>Secretaria:</span>{' '}
                {af.empenho.dotacao.secretaria.nome}
              </div>
            )}
            {af.empenho.dotacao.departamento && (
              <div>
                <span className='font-medium'>Departamento:</span>{' '}
                {af.empenho.dotacao.departamento.nome}
              </div>
            )}
            {af.empenho.dotacao.subdepartamento && (
              <div>
                <span className='font-medium'>Subdepartamento:</span>{' '}
                {af.empenho.dotacao.subdepartamento.nome}
              </div>
            )}
            <div>
              <span className='font-medium'>Despesa:</span>{' '}
              {af.empenho.dotacao.despesa}
            </div>
            {af.empenho.dotacao.economica && (
              <div>
                <span className='font-medium'>Econômica:</span>{' '}
                {af.empenho.dotacao.economica.codigo}
              </div>
            )}
            {af.empenho.dotacao.funcional && (
              <div>
                <span className='font-medium'>Funcional:</span>{' '}
                {af.empenho.dotacao.funcional.codigo}
              </div>
            )}
            <div className='col-span-2'>
              <span className='font-medium'>Descrição:</span>{' '}
              {af.empenho.dotacao.desc}
            </div>
          </div>
        </div>
      )}

      {/* Resumo Financeiro */}
      <div className='mb-6'>
        <h3 className='mb-3 border-b pb-1 font-semibold'>Resumo Financeiro</h3>
        <div className='grid grid-cols-2 gap-3 text-sm'>
          <div className='text-center'>
            <div className='font-medium'>Valor Total da AF</div>
            <div className='font-mono'>{toCurrency(af.valorTotal).value}</div>
          </div>
          <div className='text-center'>
            <div className='font-medium'>Saldo Disponível</div>
            <div
              className={`font-mono ${saldoDisponivel < 0 ? 'text-red-600' : ''}`}
            >
              {toCurrency(saldoDisponivel).value}
            </div>
          </div>
        </div>

        {/* Detalhamento da Utilização */}
        <div className='mt-4'>
          <h4 className='mb-2 text-sm font-medium'>
            Detalhamento da Utilização
          </h4>
          <div className='grid grid-cols-2 gap-3 text-center text-sm'>
            <div className='rounded bg-gray-50 p-2'>
              <div className='text-gray-600'>Liquidações Diretas</div>
              <div className='font-mono'>
                {toCurrency(valorUtilizado).value}
              </div>
            </div>
            <div className='rounded bg-blue-50 p-2'>
              <div className='text-gray-600'>Liquidações Cross-Empenho</div>
              <div className='font-mono'>
                {toCurrency(valorCrossEmpenho).value}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Liquidações */}
      {af.liquidacoes.length > 0 && (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Liquidações</h3>
          <div className='overflow-x-auto'>
            <table className='w-full border-collapse text-xs'>
              <thead>
                <tr className='border-b-2 border-gray-800'>
                  <th className='p-1 text-left font-bold'>Tipo</th>
                  <th className='p-1 text-left font-bold'>Documento</th>
                  <th className='p-1 text-left font-bold'>Origem</th>
                  <th className='p-1 text-left font-bold'>Mês Ref.</th>
                  <th className='p-1 text-left font-bold'>Data</th>
                  <th className='p-1 text-right font-bold'>Valor</th>
                </tr>
              </thead>
              <tbody>
                {af.liquidacoes.map((liquidacao, index) => (
                  <tr
                    key={liquidacao.id}
                    className={`${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} ${liquidacao.tipo === 'CROSS' ? 'bg-blue-50' : ''}`}
                  >
                    <td className='border-b border-gray-200 p-1'>
                      <span
                        className={`rounded px-2 py-1 text-xs font-medium ${
                          liquidacao.tipo === 'DIRETA'
                            ? 'bg-gray-100 text-gray-700'
                            : 'bg-blue-100 text-blue-700'
                        }`}
                      >
                        {liquidacao.tipo === 'DIRETA'
                          ? 'Direta'
                          : 'Cross-Empenho'}
                      </span>
                    </td>
                    <td className='border-b border-gray-200 p-1'>
                      <div>
                        <div className='font-medium'>
                          {liquidacao.documento ||
                            `Nº ${liquidacao.numero}/${liquidacao.exercicio}`}
                        </div>
                        {liquidacao.marca_documento && (
                          <div className='text-xs text-gray-500'>
                            Marca: {liquidacao.marca_documento}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className='border-b border-gray-200 p-1'>
                      <span className='text-xs text-gray-600'>
                        {liquidacao.empenhoOrigem}
                      </span>
                    </td>
                    <td className='border-b border-gray-200 p-1'>
                      {liquidacao.mesReferencia || '-'}
                    </td>
                    <td className='border-b border-gray-200 p-1'>
                      {new Date(liquidacao.dataLiquidacao).toLocaleDateString(
                        'pt-BR'
                      )}
                    </td>
                    <td className='border-b border-gray-200 p-1 text-right font-mono'>
                      {toCurrency(liquidacao.valorTotal)
                        .format()
                        .replace(/0$/, '')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Documentos Anexos */}
      {af.afs_documentos.length > 0 && (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Documentos Anexos
          </h3>
          <div className='space-y-2 text-sm'>
            {af.afs_documentos.map((documento) => (
              <div
                key={documento.id}
                className='flex items-center justify-between rounded bg-gray-50 p-2'
              >
                <span>{documento.nomeArquivo}</span>
                <span className='text-xs text-gray-500'>
                  {new Date(documento.dataUpload).toLocaleDateString('pt-BR')}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Histórico de Alterações */}
      {af.auditoria.length > 0 && (
        <div className='mb-6'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>
            Histórico de Alterações
          </h3>
          <div className='space-y-2 text-sm'>
            {af.auditoria.map((audit) => (
              <div key={audit.id} className='border-b pb-2'>
                <div className='flex justify-between'>
                  <span className='font-medium'>
                    {audit.acao} - {audit.obs || 'Sem observação'}
                  </span>
                  <span className='text-gray-600'>
                    {new Date(audit.data).toLocaleDateString('pt-BR')}
                  </span>
                </div>
                {audit.usuario && (
                  <p className='mt-1 text-xs text-gray-500'>
                    Por: {audit.usuario.nome}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Rodapé */}
      <div className='mt-8 border-t pt-4 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p className='mt-1'>{siteConfig.name}</p>
      </div>
    </div>
  );
};
