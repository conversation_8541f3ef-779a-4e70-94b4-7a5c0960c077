import { ErrorAlert } from '@/components/error-alert';
import { listarAlteracoesOrcamentarias } from '@/lib/database/movimento/alteracaoOrcamentaria';
import AlteracaoOrcamentariaDatatable from './alteracaoOrcamentariaDatatable';

export default async function alteracaoOrcamentariaDatatableWrapper() {
  const result = await listarAlteracoesOrcamentarias();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Alterações Orçamentárias não encontradas.'} />;

  return <AlteracaoOrcamentariaDatatable data={result} />;
}
