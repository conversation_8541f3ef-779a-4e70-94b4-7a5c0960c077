'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  obterCotasPorId,
  obterCotasReducao,
} from '@/lib/database/gerenciamento/dotacoes';
import CotasReducaoDatatable from '@/components/gerenciamento/dotacoes/cotasReducaoDataTable';
import { Label } from '@/components/ui/label';
import { currencyOptions } from '@/lib/utils';
import currency from 'currency.js';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft, PlusCircle } from 'lucide-react';

export default async function CotasReducaoPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const dotacaoPromise = obterCotasPorId({
    id: Number(id),
  });
  const cotasReducaoPromise = obterCotasReducao({
    id: Number(id),
  });

  const [dotacao, cotasReducao] = await Promise.all([
    dotacaoPromise,
    cotasReducaoPromise,
  ]);
  if (dotacao.error) {
    return <ErrorAlert error={dotacao.error} />;
  }
  if (!dotacao.data) {
    return <ErrorAlert error='Falha ao obter dotação.' />;
  }

  if (cotasReducao.error) {
    return <ErrorAlert error={cotasReducao.error} />;
  }
  if (!cotasReducao.data) {
    return <ErrorAlert error='Falha ao obter cotas de redução.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Cotas de Redução</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='mb-4 flex w-full flex-wrap justify-start gap-4 px-5'>
          <Link href={`/gerenciamento/dotacoes`}>
            <Button variant={'secondary'}>
              <ArrowLeft className='mr-2 h-4 w-4' /> Dotações
            </Button>
          </Link>
        </div>
        <div className='mx-6 mt-8 flex flex-wrap justify-between gap-6 text-left'>
          <h2 className='w-full text-center'>
            <span className='font-bold'>Despesa:</span> {dotacao.data.despesa} -{' '}
            {dotacao.data.desc}
          </h2>
          <span>
            <Label>Cota Redução Inicial:</Label>{' '}
            {currency(dotacao.data!.cotaReducaoInicial, currencyOptions)
              .format()
              .replace(/0$/, '')}
          </span>
          <span>
            <Label>Cota Redução Atual:</Label>{' '}
            {currency(dotacao.data!.cotaReducao, currencyOptions)
              .format()
              .replace(/0$/, '')}
          </span>
          <span>
            <Label>Valor Disponível:</Label>{' '}
            {currency(dotacao.data!.valorAtual, currencyOptions)
              .format()
              .replace(/0$/, '')}
          </span>
        </div>
        <div className='mt-6 flex w-full justify-end gap-6 pr-6'>
          <Link
            href={`/gerenciamento/dotacoes/${id}/cotas-de-reducao/reducao-para-dotacao`}
          >
            <Button>
              Cota de Redução para Dotação{' '}
              <PlusCircle className='ml-1 h-4 w-4' />
            </Button>
          </Link>
          <Link
            href={`/gerenciamento/dotacoes/${id}/cotas-de-reducao/dotacao-para-reducao`}
          >
            <Button>
              Dotação para Cota de Redução{' '}
              <PlusCircle className='ml-1 h-4 w-4' />
            </Button>
          </Link>
        </div>
        <div className='mt-4 w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <CotasReducaoDatatable data={cotasReducao} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
