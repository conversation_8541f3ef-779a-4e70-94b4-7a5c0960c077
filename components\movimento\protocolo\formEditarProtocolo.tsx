'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  atualizarStatusProtocoloAction,
  vincularAFAction,
  vincularEmpenhoAction,
} from '@/app/(secure)/movimento/protocolo/action';
import { StatusProtocolo } from '@/lib/enums';
import { toast } from 'sonner';
import { obterProtocolo } from '@/lib/database/movimento/protocolos';
import { AlterarStatusForm } from '@/components/movimento/protocolo/alterarStatusForm';
import { VincularAfeEmpenhoForm } from '@/components/movimento/protocolo/vincularAfeEmpenhoForm';

interface FormEditarProtocoloProps {
  protocolo: Required<Awaited<ReturnType<typeof obterProtocolo>>['data']>;
}

export default function FormEditarProtocolo({
  protocolo,
}: FormEditarProtocoloProps) {
  const router = useRouter();
  const [actionLoading, setActionLoading] = useState(false);

  if (!protocolo) {
    return <div>Protocolo não encontrado</div>;
  }

  const handleAlterarStatus = async (data: {
    novoStatus: number;
    obs: string;
  }) => {
    setActionLoading(true);
    try {
      const formData = new FormData();
      formData.append('id', protocolo.id.toString());
      formData.append('novoStatus', data.novoStatus.toString());
      formData.append('obs', data.obs);

      const result = await atualizarStatusProtocoloAction(formData);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Status alterado com sucesso!');
        router.push('/movimento/protocolo');
      }
    } catch (error) {
      toast.error('Erro ao alterar status do protocolo');
    } finally {
      setActionLoading(false);
    }
  };

  const handleVincularAF = async (data: {
    numeroAF: number;
    exercicioAF: number;
  }) => {
    setActionLoading(true);
    try {
      const formData = new FormData();
      formData.append('id', protocolo.id.toString());
      formData.append('numeroAF', data.numeroAF.toString());
      formData.append('exercicioAF', data.exercicioAF.toString());

      const result = await vincularAFAction(formData);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF vinculada com sucesso!');
        router.refresh();
      }
    } catch (error) {
      toast.error('Erro ao vincular AF');
    } finally {
      setActionLoading(false);
    }
  };

  const handleVincularEmpenho = async (data: {
    numeroEmpenho: number;
    exercicioEmpenho: number;
  }) => {
    setActionLoading(true);
    try {
      const formData = new FormData();
      formData.append('id', protocolo.id.toString());
      formData.append('numeroEmpenho', data.numeroEmpenho.toString());
      formData.append('exercicioEmpenho', data.exercicioEmpenho.toString());

      const result = await vincularEmpenhoAction(formData);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Empenho vinculado com sucesso!');
        router.refresh();
      }
    } catch (error) {
      toast.error('Erro ao vincular empenho');
    } finally {
      setActionLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      <h1 className='text-2xl font-bold'>Editar Protocolo #{protocolo.id}</h1>

      <div className='text-sm text-gray-600'>
        Status atual: {StatusProtocolo[protocolo.status]?.replace(/_/g, ' ')}
      </div>

      <Tabs defaultValue='status' className='w-full'>
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='status'>Alterar Status</TabsTrigger>
          <TabsTrigger value='af-empenho'>Vincular AF/Empenho</TabsTrigger>
        </TabsList>
        <TabsContent value='status' className='mt-6'>
          <AlterarStatusForm
            currentStatus={protocolo.status}
            onSubmit={handleAlterarStatus}
            onCancel={() =>
              router.push(`/movimento/protocolo/visualizar/${protocolo.id}`)
            }
            isLoading={actionLoading}
          />
        </TabsContent>
        <TabsContent value='af-empenho' className='mt-6'>
          <VincularAfeEmpenhoForm
            protocolo={protocolo}
            onVincularAF={handleVincularAF}
            onVincularEmpenho={handleVincularEmpenho}
            onCancel={() =>
              router.push(`/movimento/protocolo/visualizar/${protocolo.id}`)
            }
            isLoading={actionLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
