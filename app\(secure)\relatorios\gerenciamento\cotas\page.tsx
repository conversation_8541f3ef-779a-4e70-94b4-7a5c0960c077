'use server';
import { z } from 'zod';
import { ensureAuth } from '@/lib/supabase/actions';
import { ErrorAlert } from '@/components/error-alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { RelatorioCotaReducao } from '@/components/relatorios/cotaReducao';
import { obterCotasReducaoParaRelatorio } from '@/lib/database/relatorios/dotacoes';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';

async function RelatorioCotasContent() {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const result = await temPermissao({ ...parametrosPermissao, bearer: '' });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const params = {
    exercicio: result.exercicio,
    bearer: '',
  };

  const cotas = await obterCotasReducaoParaRelatorio(params);

  if (cotas.error) {
    return <ErrorAlert error={cotas.error} />;
  }

  if (!cotas.data) {
    return <ErrorAlert error='Falha ao obter cotas/reduções.' />;
  }

  const filtros = {
    exercicio: params.exercicio || result.exercicio,
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Relatório de Cotas
          </h1>
          <p className='text-muted-foreground'>
            Visualize e exporte o relatório de cotas e reduções
          </p>
        </div>
        <div className='flex gap-4'>
          <div className='flex gap-2'>
            <BotaoDownloadPDF
              url='/gerenciamento/cotas/imprimir'
              title='Imprimir Cotas'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
                tipo: 'cota',
              }}
            />
            <BotaoDownloadPDF
              url='/gerenciamento/cotas/imprimir'
              title='Imprimir Reduções'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
                tipo: 'reducao',
              }}
            />
            <BotaoDownloadPDF
              url='/gerenciamento/cotas/imprimir'
              title='Imprimir Ambos'
              params={{
                exercicio: filtros.exercicio?.toString() || '',
              }}
            />
          </div>
        </div>
      </div>

      <div className='rounded-lg bg-white p-6 shadow'>
        <RelatorioCotaReducao
          cotas={cotas.data}
          titulo='Relatório de Cotas e Reduções'
          filtros={filtros}
        />
      </div>
    </div>
  );
}

export default async function RelatorioCotasPage() {
  await ensureAuth();

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de Cotas</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <RelatorioCotasContent />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
