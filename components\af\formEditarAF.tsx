'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Loader2 } from 'lucide-react';
import {
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { editarAFSchema } from '@/lib/validation';
import { toast } from 'sonner';
import { editarAF, obterAF } from '@/lib/database/movimento/afs';
import currency from 'currency.js';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default function FormEditarAF({ id }: { id: number }) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [af, setAF] = useState<Awaited<ReturnType<typeof obterAF>> | null>(
    null
  );

  const form = useForm<z.infer<typeof editarAFSchema>>({
    resolver: zodResolver(editarAFSchema),
    defaultValues: {
      id: id,
      resumo: '',
      obs: '',
      valorTotal: 0,
    },
  });

  const [valorTotalBRL, setValorTotalBRL] = useState(
    currency(0, currencyOptionsNoSymbol).format().replace(/0$/, '')
  );

  useEffect(() => {
    const loadAF = async (afId: number) => {
      try {
        const result = await obterAF({ id: afId });
        if (result.error) {
          toast.error(result.error);
          router.push('/movimento/af');
        } else {
          setAF(result);
          form.setValue('resumo', result.data?.resumo || '');
          form.setValue('obs', result.data?.obs || '');
          form.setValue('valorTotal', result.data?.valorTotal || 0);
          form.setValue('dataEmissao', result.data?.dataEmissao || undefined);
          form.setValue(
            'dataVencimento',
            result.data?.dataVencimento || undefined
          );
          setValorTotalBRL(
            currency(result.data?.valorTotal || 0, currencyOptionsNoSymbol)
              .format()
              .replace(/0$/, '')
          );
        }
      } catch (error) {
        toast.error('Erro ao carregar AF');
        router.push('/movimento/af');
      }
    };
    loadAF(id);
  }, [form, id, router]);

  const onSubmit = async (values: z.infer<typeof editarAFSchema>) => {
    setLoading(true);
    try {
      const result = await editarAF({
        id: values.id,
        resumo: values.resumo,
        obs: values.obs,
        valorTotal: values.valorTotal,
        dataEmissao: values.dataEmissao,
        dataVencimento: values.dataVencimento,
      });

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('AF atualizada com sucesso!');
        router.push('/movimento/af');
      }
    } catch (error) {
      toast.error(toastAlgoDeuErrado);
    } finally {
      setLoading(false);
    }
  };

  if (!af?.data) {
    return (
      <div className='flex items-center justify-center p-8'>
        <Loader2 className='h-8 w-8 animate-spin' />
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Button variant='ghost' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='mr-1 h-4 w-4' />
            Voltar
          </Button>
          <h1 className='text-2xl font-bold'>Editar AF #{af.data.numero}</h1>
        </div>
      </div>

      <div className='rounded-lg bg-gray-50 p-4'>
        <h3 className='mb-2 font-semibold'>Informações do Empenho</h3>
        <div className='grid grid-cols-1 gap-2 text-sm md:grid-cols-2'>
          <div>
            <span className='font-medium'>Empenho:</span> #{af.data.empenho.id}
          </div>
          <div>
            <span className='font-medium'>Despesa:</span>{' '}
            {af.data.empenho.dotacao?.despesa}
          </div>
          <div>
            <span className='font-medium'>Fornecedor:</span>{' '}
            {af.data.empenho.fornecedor?.nome}
          </div>
          <div>
            <span className='font-medium'>Saldo do Empenho:</span>{' '}
            {currency(af.data.saldo || 0, currencyOptionsNoSymbol)
              .format()
              .replace(/0$/, '')}
          </div>
        </div>
      </div>

      <div className='rounded-lg bg-blue-50 p-4'>
        <h3 className='mb-2 font-semibold'>Informações da AF</h3>
        <div className='grid grid-cols-1 gap-2 text-sm md:grid-cols-3'>
          <div>
            <span className='font-medium'>Número AF:</span> {af.data.numero}
          </div>
          <div>
            <span className='font-medium'>Status:</span> {af.data.status}
          </div>
          <div>
            <span className='font-medium'>Data:</span>{' '}
            {new Date(af.data.data).toLocaleDateString('pt-BR')}
          </div>
          {af.data.dataUtilizacao && (
            <div>
              <span className='font-medium'>Data de Utilização:</span>{' '}
              {new Date(af.data.dataUtilizacao).toLocaleDateString('pt-BR')}
            </div>
          )}
          {af.data.dataCancelamento && (
            <div>
              <span className='font-medium'>Data de Cancelamento:</span>{' '}
              {new Date(af.data.dataCancelamento).toLocaleDateString('pt-BR')}
            </div>
          )}
          {af.data.dataReativacao && (
            <div>
              <span className='font-medium'>Data de Reativação:</span>{' '}
              {new Date(af.data.dataReativacao).toLocaleDateString('pt-BR')}
            </div>
          )}
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          {/* Campos de data */}
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <FormField
              control={form.control}
              name='dataEmissao'
              render={({ field }) => (
                <FormItem className='flex flex-col'>
                  <FormLabel>Data de Emissão</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={'outline'}
                          className={'w-full pl-3 text-left font-normal'}
                        >
                          {field.value ? (
                            format(field.value, 'dd/MM/yyyy', { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className='w-auto p-0' align='start'>
                      <Calendar
                        mode='single'
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date > new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='dataVencimento'
              render={({ field }) => (
                <FormItem className='flex flex-col'>
                  <FormLabel>Data de Vencimento</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={'outline'}
                          className={'w-full pl-3 text-left font-normal'}
                        >
                          {field.value ? (
                            format(field.value, 'dd/MM/yyyy', { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className='w-auto p-0' align='start'>
                      <Calendar
                        mode='single'
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => {
                          const dataEmissao = form.getValues('dataEmissao');
                          return dataEmissao ? date < dataEmissao : false;
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='resumo'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Resumo</FormLabel>
                <FormControl>
                  <Input placeholder='Digite um resumo para a AF' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='obs'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Observações</FormLabel>
                <FormControl>
                  <Input placeholder='Observações (opcional)' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='valorTotal'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Valor Total</FormLabel>
                <FormControl>
                  <Input
                    placeholder='0,00'
                    value={valorTotalBRL}
                    onChange={(e) => {
                      const maskedValue = moneyMask(e.target.value);
                      setValorTotalBRL(maskedValue);
                      field.onChange(moneyUnmask(maskedValue));
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className='flex justify-end gap-2'>
            <Button
              type='button'
              variant='outline'
              onClick={() => router.back()}
            >
              Cancelar
            </Button>
            <Button type='submit' disabled={loading}>
              {loading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
              Salvar Alterações
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
