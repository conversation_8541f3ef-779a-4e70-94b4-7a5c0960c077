'use server';

import { revalidatePath } from 'next/cache';
import { prisma } from '../../prisma';
import {
  auditoriaErroSchema,
  editarSuperavitSchema,
  superavitSchema,
  idSchema,
  permissaoSchema,
} from '../../validation';
import { Modulos } from '@/lib/modulos';
import {
  AuditoriaControleSuperavit,
  Permissoes,
  StatusSuperavitDetalhes,
} from '@/lib/enums';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { inserirErroAudit } from '../auditoria/erro';
import currency from 'currency.js';
import { currencyOptionsNoSymbol, formatDataHora } from '@/lib/utils';
import z from 'zod';

export const listarSuperavits = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    permissao: Permissoes.ACESSAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const superavits = await prisma.controleSuperavits.findMany({
      where: {
        exercicio: resultPermissao.exercicio!,
      },
    });
    return {
      data: superavits.map((row) => {
        return {
          ...row,
          nome:
            row.exercicio.toString() +
            row.codAplicacao.toString() +
            row.fonte.toString(),
          valorReceita: row.valorReceita.toNumber(),
        };
      }),
    };
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao obter Superavits.`,
    };
  }
};

export const criarSuperavit = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = superavitSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { exercicio, fonte, codAplicacao, valorReceita } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const dotacao = await tx.dotacoes.findFirst({
        select: {
          id: true,
          valorInicial: true,
        },
        where: {
          exercicio: resultPermissao.exercicio!,
          fonte: fonte,
          codAplicacao: codAplicacao,
        },
      });

      const result = await tx.controleSuperavits.create({
        data: {
          exercicio: exercicio,
          fonte: fonte!,
          codAplicacao: codAplicacao!,
          // valor: valor, // 0,
          valorReceita: valorReceita!,
          idDotacao: dotacao ? dotacao.id : null,
        },
      });

      await tx.controleSuperavits_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idSuperavit: result.id,
          acao: AuditoriaControleSuperavit.CRIAR_SUPERAVIT,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/superavits');
  } catch (e: unknown) {
    console.log(e);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Código do Superavit já existe.`,
        };
      }
    }
    return {
      error: `Erro ao criar Superavit.`,
    };
  }
};

export const deletarSuperavit = async (id: number) => {
  try {
    const superavit = await prisma.controleSuperavits.delete({
      where: {
        id,
      },
    });
    revalidatePath('/gerenciamento/superavits');
    return {
      data: superavit,
    };
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao deletar Superavit.`,
    };
  }
};

export const editarSuperavit = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = editarSuperavitSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id, valorReceita } = parsedParams.data;

  try {
    // const pedacoCpf = cpf && cpf !== '' ? cpf.substring(3, 12) : '' // analisar Criptografar
    // const cpfMascarado = pedacoCpf ? `***${pedacoCpf}**` : '' // analisar Criptografar
    //Prisma ainda não implementou deep nested queries, fazer via transação e loops
    const ip = (await obterIpUsuarioConectado()).data;

    const superavitAntigo = await prisma.controleSuperavits.findUnique({
      where: {
        id: id,
      },
      select: {
        exercicio: true,
        fonte: true,
        codAplicacao: true,
        valorReceita: true,
      },
    });

    const dataAudit = await criarAuditAlter(
      superavitAntigo,
      parsedParams,
      resultPermissao.idUsuario!,
      id,
      ip
    );

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.controleSuperavits.update({
        where: {
          id: id,
        },
        data: {
          /*exercicio: exercicio, 
          fonte: fonte,
          codAplicacao: codAplicacao,*/
          valorReceita: valorReceita,
        },
      });

      const auditPromise = tx.controleSuperavits_audit.createMany({
        data: dataAudit,
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/superavits');
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao Editar Superavit.`,
    };
  }
};

const criarAuditAlter = (
  obj: any,
  params: any,
  idUsuario: number,
  idSuperavit: number,
  ip: string
) => {
  const data = params.data;
  let arr: any[] = [];

  Object.keys(obj).map((key) => {
    Object.keys(data).map((key1) => {
      if (key == key1) {
        if (obj[key] != data[key1]) {
          let objAux = {
            idUsuario: idUsuario,
            idSuperavit: idSuperavit,
            acao: AuditoriaControleSuperavit.ALTERAR_SUPERAVIT,
            de: obj[key].toString(),
            para: data[key1].toString(),
            ip,
          };
          arr.push(objAux);
        }
      }
    });
  });

  return arr;
};

export const obterSuperavit = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);
  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const superavit = await prisma.controleSuperavits.findFirst({
      where: {
        id: id,
      },
    });

    if (!superavit) {
      return {
        error: 'Superavit não encontrado.',
      };
    }

    let dotacao: any = null;
    if (typeof superavit?.idDotacao == 'number') {
      dotacao = await prisma.dotacoes.aggregate({
        _sum: {
          valorInicial: true,
        },
        where: {
          id: superavit?.idDotacao,
          exercicio: superavit?.exercicio,
          codAplicacao: superavit?.codAplicacao,
        },
      });

      if (!dotacao) {
        return {
          error: 'dotação associada ao superavit não encontrada.',
        };
      }
    }

    const superavitDetalhes = await prisma.controleSuperavitsDetalhes.aggregate(
      {
        _sum: {
          valor: true,
          valorReservado: true,
          valorSuplementado: true,
        },
        where: {
          idSuperavit: superavit?.id,
          exercicio: superavit?.exercicio,
          status: {
            not: StatusSuperavitDetalhes['Reprovado'],
          },
        },
      }
    );

    const superavitDetalhesDespesasAgrupadas =
      await prisma.controleSuperavitsDetalhes.groupBy({
        by: ['despesa'],
        where: {
          idSuperavit: superavit?.id,
          exercicio: superavit?.exercicio,
          status: {
            not: StatusSuperavitDetalhes['Reprovado'],
          },
        },
      });

    let despesasString = '';
    if (superavitDetalhesDespesasAgrupadas) {
      superavitDetalhesDespesasAgrupadas.map((desp, index) => {
        if (index > 1) {
          despesasString += ',' + desp.despesa;
        } else {
          despesasString += desp.despesa;
        }
      });
    }

    const suplementado = superavitDetalhes._sum?.valorSuplementado
      ? superavitDetalhes._sum.valorSuplementado.toNumber()
      : 0;
    const valReserva = superavitDetalhes._sum?.valorReservado
      ? Number(superavitDetalhes._sum.valorReservado || 0)
      : 0;
    const saldoAtual = currency(
      superavit!.valorReceita.toNumber(),
      currencyOptionsNoSymbol
    ).subtract(dotacao ? dotacao._sum.valorInicial?.toNumber() : 0); //.subtract(suplementado) // .subtract(valReserva)
    const valorReceita = currency(
      superavit!.valorReceita.toNumber(),
      currencyOptionsNoSymbol
    )
      .add(suplementado)
      .add(valReserva);

    return {
      data: {
        ...superavit,
        nome: superavit!.exercicio + superavit!.fonte + superavit!.codAplicacao,
        id: superavit!.id,
        exercicio: superavit!.exercicio,
        fonte: superavit!.fonte,
        codAplicacao: superavit!.codAplicacao,
        valorReceita: valorReceita.value,
        dotInicial: dotacao?._sum ? dotacao._sum.valorInicial?.toNumber() : 0, // dotacao!._sum.valorInicial?.toNumber(),
        suplementado: suplementado,
        valReserva: valReserva,
        saldoAtual: saldoAtual.value,
        despesas: despesasString,
      },
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    };
    await inserirErroAudit(auditData);
    return {
      error: 'Erro ao obter Superavit.',
    };
  }
};

export const ativarSuperavit = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.controleSuperavits.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });
      const auditPromise = tx.controleSuperavits_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idSuperavit: id,
          acao: AuditoriaControleSuperavit.ATIVAR_SUPERAVIT,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/superavits');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar superavit.`,
    };
  }
};

export const desativarSuperavit = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.controleSuperavits.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });
      const auditPromise = tx.controleSuperavits_audit.create({
        data: {
          idUsuario: resultPermissao.idUsuario!,
          idSuperavit: id,
          acao: AuditoriaControleSuperavit.DESATIVAR_SUPERAVIT,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/superavits');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SUPERAVIT,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar superavit.`,
    };
  }
};

export const obterDotacao = async (
  exercicio: number,
  fonte: number,
  codAplicacao: number
) => {
  try {
    const dotacao = await prisma.dotacoes.findFirst({
      select: {
        valorInicial: true,
      },
      where: {
        exercicio: exercicio,
        fonte: fonte,
        codAplicacao: codAplicacao,
      },
    });
    return dotacao ? dotacao?.valorInicial : 0;
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao obter Dotaçao.`,
    };
  }
};

export const listarSuperavitsDetalhes = async (
  /*exercicio: number, fonte: number, codAplicacao: number,*/ id: number
) => {
  try {
    const detalhes = await prisma.controleSuperavitsDetalhes.findMany({
      where: {
        idSuperavit: id,
        /*exercicio: exercicio,
        fonte: fonte,
        codAplicacao: codAplicacao,*/
        status: {
          not: StatusSuperavitDetalhes['Reprovado'],
        },
      },
    });
    return {
      data: detalhes.map((row) => {
        return {
          ...row,
          valor: Number(row.valor),
          valorReservado: Number(row.valorReservado),
          valorSuplementado: Number(row.valorSuplementado),
          nome:
            row.despesa.toString() +
            row.codAplicacao.toString() +
            row.fonte.toString(),
          data: formatDataHora(row.data),
        };
      }),
    };
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao obter Superavits.`,
    };
  }
};
