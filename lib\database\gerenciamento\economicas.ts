'use server';

import { AuditoriaGerenciamentoEconomicas, Permissoes } from '@/lib/enums';
import { Mo<PERSON>los } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  criarEconomicaSchema,
  editarEconomicaSchema,
  idSchema,
  permissaoSchema,
} from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { revalidatePath } from 'next/cache';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

export const listarEconomicas = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const economicas = await prisma.economicas.findMany({
      orderBy: { codigo: 'asc' },
    });
    return {
      data: economicas,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter econômicas.`,
    };
  }
};

export const desativarEconomica = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.economicas.update({
        where: {
          id,
        },
        data: {
          ativo: false,
        },
      });

      const auditPromise = tx.gerenciamentoEconomicas_audit.create({
        data: {
          idEconomica: id,
          acao: AuditoriaGerenciamentoEconomicas.DESATIVAR_ECONOMICA,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/economicas');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar econômica.`,
    };
  }
};

export const ativarEconomica = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.economicas.update({
        where: {
          id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.gerenciamentoEconomicas_audit.create({
        data: {
          idEconomica: id,
          acao: AuditoriaGerenciamentoEconomicas.ATIVAR_ECONOMICA,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });
    revalidatePath('/gerenciamento/economicas');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar econômica.`,
    };
  }
};

export const alterarDescricaoEconomica = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = editarEconomicaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, desc, info, obs } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const descActiga = await tx.economicas.findUnique({
        where: {
          id,
        },
        select: {
          desc: true,
          info: true,
          obs: true,
        },
      });
      const resultPromise = tx.economicas.update({
        where: {
          id,
        },
        data: {
          desc,
          info,
          obs,
        },
      });
      const auditDescPromise = tx.gerenciamentoEconomicas_audit.create({
        data: {
          idEconomica: id,
          acao: AuditoriaGerenciamentoEconomicas.ALTERAR_ECONOMICA,
          idUsuario: resultPermissao.idUsuario!,
          de: descActiga?.desc,
          para: desc,
          ip,
        },
      });
      const auditInfoPromise = tx.gerenciamentoEconomicas_audit.create({
        data: {
          idEconomica: id,
          acao: AuditoriaGerenciamentoEconomicas.ALTERAR_ECONOMICA,
          idUsuario: resultPermissao.idUsuario!,
          de: descActiga?.info,
          para: info,
          ip,
        },
      });
      const auditObsPromise = tx.gerenciamentoEconomicas_audit.create({
        data: {
          idEconomica: id,
          acao: AuditoriaGerenciamentoEconomicas.ALTERAR_ECONOMICA,
          idUsuario: resultPermissao.idUsuario!,
          de: descActiga?.obs,
          para: obs,
          ip,
        },
      });

      await Promise.all([
        resultPromise,
        auditDescPromise,
        auditInfoPromise,
        auditObsPromise,
      ]);
    });
    revalidatePath('/gerenciamento/economicas');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao alterar nome da econômia.`,
    };
  }
};

export const criarEconomica = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = criarEconomicaSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { codigo, desc, info, obs } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    const economicaArr = codigo.split('.');
    await prisma.$transaction(async (tx) => {
      const result = await tx.economicas.create({
        data: {
          codigo,
          desc,
          info,
          obs,
          categoria: Number(economicaArr[0]),
          grupo: Number(economicaArr[1]),
          modalidade: Number(economicaArr[2]),
          elemento: Number(economicaArr[3]),
          subelemento: Number(economicaArr[4]),
        },
      });
      await tx.gerenciamentoEconomicas_audit.create({
        data: {
          idEconomica: result.id,
          acao: AuditoriaGerenciamentoEconomicas.CRIAR_ECONOMICA,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });
    });
    revalidatePath('/gerenciamento/economicas');
  } catch (e) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Econômica já existe.`,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_ECONOMICAS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar econômia.`,
    };
  }
};
