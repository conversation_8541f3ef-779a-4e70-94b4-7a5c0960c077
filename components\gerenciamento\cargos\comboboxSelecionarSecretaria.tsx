'use client';

import * as React from 'react';
import { ArrowDown, Check, ChevronsUpDown } from 'lucide-react';

import { cn, codigoSecretariaMask } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { AcessoCargo } from '@/types/app';
import { listarSecretarias } from '@/lib/database/gerenciamento/secretarias';

export function ComboboxSelecionarSecretaria({
  acessosConfigurados,
  setAcessosConfigurados,
  secretarias,
}: {
  acessosConfigurados: AcessoCargo[];
  setAcessosConfigurados: React.Dispatch<React.SetStateAction<AcessoCargo[]>>;
  secretarias: Awaited<ReturnType<typeof listarSecretarias>>;
}) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState('');

  if (!secretarias.data) return <></>;

  const secretariaSelecionada = secretarias.data.find(
    (secretaria) => secretaria.id === Number(value)
  );

  const setAcessos = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();

    if (!secretariaSelecionada || !value) return;

    const acessos = acessosConfigurados.filter((m) => m.id !== Number(value));
    setAcessosConfigurados([
      {
        id: Number(value),
        nome: secretariaSelecionada.nome,
        codigo: `${codigoSecretariaMask(secretariaSelecionada.codigo.toString())}.00.00`,
      },
      ...acessos,
    ]);

    setValue('');
  };

  const acessosConfiguradosIds = acessosConfigurados
    .map((acesso) => {
      return acesso.id;
    })
    .filter((id) => id !== Number(value));

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {value && secretariaSelecionada
              ? `${codigoSecretariaMask(secretariaSelecionada.codigo.toString())}.00.00 - ${secretariaSelecionada?.nome}`
              : 'Selecione a secretaria...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar secretaria...' />
            <CommandEmpty>Secretaria não encontrada.</CommandEmpty>
            <CommandList>
              {secretarias.data
                .filter(
                  (secretaria) =>
                    !acessosConfiguradosIds.includes(secretaria.id)
                )
                .map((secretaria) => (
                  <CommandItem
                    key={secretaria.id}
                    value={`${secretaria.id}`}
                    onSelect={(currentValue) => {
                      setValue(
                        Number(currentValue) !== secretaria.id
                          ? ''
                          : currentValue
                      );
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        Number(value) === secretaria.id
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                    />
                    {codigoSecretariaMask(secretaria.codigo.toString())}.00.00 -{' '}
                    {secretaria.nome}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <Button
        type='button'
        variant='secondary'
        disabled={value === ''}
        onClick={setAcessos}
      >
        <ArrowDown className='mr-2 h-4 w-4' /> Adicionar Secretaria
      </Button>
    </>
  );
}
