'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { ErrorAlert } from '@/components/error-alert';
import { obterReservaParaRelatorio } from '@/lib/database/movimento/reservas';
import { RelatorioReserva } from '@/components/movimento/reserva/relatorioReserva';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft, Pencil, Eye } from 'lucide-react';
import { DialogCancelarReserva } from '@/components/movimento/reserva/dialogCancelarReserva';
import { StatusReserva, StatusProtocoloDesc } from '@/lib/enums';
import { Checkbox } from '@/components/ui/checkbox';
import { usuarioEhGerente } from '@/lib/database/usuarios';
import { BotaoDownloadPDFReserva } from '@/components/movimento/reserva/botaoDownloadPDF';
import { BotaoCriarProtocoloGenerico } from '@/components/movimento/botaoCriarProtocoloGenerico';

export default async function VisualizarReservaPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const gerentePromise = usuarioEhGerente();

  const reservaPromise = obterReservaParaRelatorio({
    id: Number(id),
  });

  const [reserva, gerente] = await Promise.all([
    reservaPromise,
    gerentePromise,
  ]);

  if (reserva.error) {
    return <ErrorAlert error={reserva.error} />;
  }
  if (!reserva.data) {
    return <ErrorAlert error='Falha ao obter reserva.' />;
  }
  if (gerente.error) {
    return <ErrorAlert error={gerente.error} />;
  }

  const statusReserva = reserva.data.status;
  const protocolo = reserva.data.protocolos?.[0] || null;
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Visualizar Reserva N° {reserva.data.id}</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='mx-6 flex w-full flex-wrap justify-between gap-4'>
          <Link href='/movimento/reservas'>
            <Button className='mb-4' variant='secondary'>
              <ArrowLeft className='mr-2 size-4' /> Voltar
            </Button>
          </Link>
          <div className='flex flex-wrap gap-4'>
            {statusReserva !== StatusReserva.Cancelado && (
              <DialogCancelarReserva idReserva={Number(id)} />
            )}
            {(statusReserva === StatusReserva.Reservado ||
              statusReserva === StatusReserva.Devolvido) && (
              <Link href={`/movimento/reservas/editar/${id}`}>
                <Button type='button' variant='outline'>
                  <Pencil className='mr-1 size-4' /> Editar
                </Button>
              </Link>
            )}
            <BotaoDownloadPDFReserva
              url={`/movimento/reservas/imprimir/${id}`}
            />
            {/* {statusReserva === StatusReserva.Reservado && (
              <BotaoDownloadPDFReserva
                url={`/movimento/reservas/imprimir/${id}`}
              />
            )} */}
            {/* {(statusReserva === StatusReserva.Reservado ||
              statusReserva === StatusReserva.Assinado) && (
              <Button>
                Encaminhar <Forward className='size-4' />
              </Button>
            )} */}
            {/* {statusReserva === StatusReserva.Reservado && (
              <Link
                href={`/movimento/reservas/visualizar/${id}/solicitar-assinaturas`}
              >
                <Button>
                  Solicitar Assinaturas Digitais
                  <Signature className='size-4' />
                </Button>
              </Link>
            )} */}
            {statusReserva === StatusReserva.Reservado && !protocolo && (
              <BotaoCriarProtocoloGenerico
                reservaId={Number(id)}
                resumo={reserva.data.resumo || `Protocolo para reserva ${id}`}
              />
            )}
            {protocolo && (
              <Link href={`/movimento/protocolo/visualizar/${protocolo.id}`}>
                <Button type='button' variant='outline'>
                  <Eye className='mr-1 size-4' /> Ver Protocolo #
                  {protocolo.numero}
                </Button>
              </Link>
            )}
          </div>
        </div>
        {gerente.data && statusReserva === StatusReserva.Reservado && (
          <div className='flex items-center space-x-2'>
            <Checkbox id='incluirGestor' defaultChecked />
            <label
              htmlFor='incluirGestor'
              className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
            >
              Incluir assinatura gestor(a)
            </label>
          </div>
        )}
        <div className='flex w-full flex-wrap justify-center'>
          {statusReserva === StatusReserva.Cancelado && (
            <span className='text-red-500'>
              Reserva cancelada. Motivo: {reserva.data.motivoCancelamento}
            </span>
          )}
          {statusReserva === StatusReserva.Devolvido && (
            <span className='text-red-500'>
              Reserva devolvida. Motivo: {reserva.data.motivoCancelamento}
            </span>
          )}
          <div className='mt-8 flex w-full flex-col items-center justify-center space-y-2 text-sm'>
            <div>
              <span className='font-bold'>Status:</span>&nbsp;
              <span className='font-bold'>{StatusReserva[statusReserva]}</span>
            </div>
            {protocolo && (
              <div className='text-blue-600'>
                <span className='font-bold'>Protocolo:</span>&nbsp;
                <span className='font-bold'>
                  #{protocolo.numero} -{' '}
                  {
                    StatusProtocoloDesc[
                      protocolo.status as keyof typeof StatusProtocoloDesc
                    ]
                  }
                </span>
              </div>
            )}
          </div>
          {/* {statusReserva === StatusReserva['Aguardando Assinaturas'] && (
            <Card className='my-8 w-full max-w-xl'>
              <CardTitle>Assinaturas</CardTitle>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className='font-bold'>Nome</TableHead>
                      <TableHead className='font-bold'>Situação</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reserva.data.reservas_assinaturas.map((assinatura) => (
                      <TableRow key={assinatura.id}>
                        <TableHead>{assinatura.usuario.nome}</TableHead>
                        <TableHead>
                          {assinatura.assinado ? 'Assinado' : 'Pendente'}
                        </TableHead>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )} */}

          {/* Componente de Assinatura Externa */}
          {/* <div className='mx-auto mt-8 w-full max-w-4xl'>
            <AssinaturaExternaWrapper
              idReserva={reserva.data.id}
              reserva={{
                id: reserva.data.id,
                resumo: reserva.data.resumo,
                dotacao: {
                  id: reserva.data.dotacao.id,
                  despesa: reserva.data.dotacao.despesa,
                },
                exercicio: reserva.data.exercicio,
              }}
              statusReserva={statusReserva}
            />
          </div> */}

          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <RelatorioReserva reserva={reserva} />
          </Suspense>
          {/* {statusReserva === StatusReserva.Reservado ||
          statusReserva === StatusReserva.Cancelado ||
          statusReserva === StatusReserva.Devolvido ? (
            <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
              <RelatorioReserva reserva={reserva} />
            </Suspense>
          ) : (
            <IframePDFReserva
              url={montarPathPdfReserva(
                reserva.data.id,
                reserva.data.idDotacao,
                reserva.data.exercicio,
                reserva.data.ultimaModificacao,
              )}
            />
          )} */}
        </div>
      </PageContent>
    </PageWrapper>
  );
}
