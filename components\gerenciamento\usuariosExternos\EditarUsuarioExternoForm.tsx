'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save } from 'lucide-react';
import { editarUsuarioSchema } from '@/lib/validation';
import { editarUsuarioExterno } from '@/lib/database/gerenciamento/usuariosExternos';
import { toastAlgoDeuErrado } from '@/lib/utils';
import Link from 'next/link';

type EditarUsuarioExternoFormProps = {
  usuario: {
    id: number;
    nome: string;
    email: string;
    ativo: boolean;
  };
};

export default function EditarUsuarioExternoForm({
  usuario,
}: EditarUsuarioExternoFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof editarUsuarioSchema>>({
    resolver: zodResolver(editarUsuarioSchema),
    defaultValues: {
      id: usuario.id,
      nome: usuario.nome,
      cargos: [], // Not used for external users, but required by the schema
    },
  });

  const onSubmit = async (values: z.infer<typeof editarUsuarioSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof editarUsuarioSchema> = {
        id: values.id,
        nome: values.nome,
        cargos: [], // Not used for external users, but required by the schema
      };
      const res = await editarUsuarioExterno(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Usuário externo atualizado.');
        router.push('/gerenciamento/usuarios-externos');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex items-center'>
        <Link href='/gerenciamento/usuarios-externos'>
          <Button type='button' variant='outline'>
            <ArrowLeft className='mr-2 size-4' /> Voltar
          </Button>
        </Link>
      </div>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className='flex flex-col gap-4 rounded-lg border p-4'
        >
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome</FormLabel>
                <FormControl>
                  <Input placeholder='Nome do usuário externo' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className='flex flex-col gap-2'>
            <p className='text-sm font-medium'>Email</p>
            <p className='text-muted-foreground text-sm'>{usuario.email}</p>
          </div>
          <Button type='submit' disabled={loading} className='mt-4 self-end'>
            {loading ? (
              <>
                <Icons.loader className='mr-2 size-4 animate-spin' /> Aguarde
              </>
            ) : (
              <>
                <Save className='mr-2 size-4' /> Salvar
              </>
            )}
          </Button>
        </form>
      </Form>
    </div>
  );
}
