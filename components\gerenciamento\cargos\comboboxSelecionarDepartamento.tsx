'use client';

import * as React from 'react';
import { ArrowDown, Check, ChevronsUpDown } from 'lucide-react';

import { cn, codigoSecretariaMask } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { AcessoCargo } from '@/types/app';
import { listarDepartamentosAtivos } from '@/lib/database/gerenciamento/cargos';

export function ComboboxSelecionarDepartamento({
  acessosConfigurados,
  setAcessosConfigurados,
  departamentos,
}: {
  acessosConfigurados: AcessoCargo[];
  setAcessosConfigurados: React.Dispatch<React.SetStateAction<AcessoCargo[]>>;
  departamentos: Awaited<ReturnType<typeof listarDepartamentosAtivos>>;
}) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState('');

  if (!departamentos.data) return <></>;

  const departamentoSelecionado = departamentos.data.find(
    (departamento) => departamento.id === Number(value)
  );

  const codigoString = `${departamentoSelecionado?.secretaria.codigo.toString()}.${departamentoSelecionado?.codigo.toString()}.00`;

  const setAcessos = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();

    if (!departamentoSelecionado || !value) return;

    const acessos = acessosConfigurados.filter((m) => m.id !== Number(value));
    setAcessosConfigurados([
      {
        id: Number(value),
        nome: departamentoSelecionado.nome,
        codigo: codigoString,
      },
      ...acessos,
    ]);

    setValue('');
  };

  const acessosConfiguradosIds = acessosConfigurados.map((acesso) => {
    return acesso.id;
  });

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {value && departamentoSelecionado
              ? `${codigoString} - ${departamentoSelecionado?.nome}`
              : 'Selecione o departamento...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar departamento...' />
            <CommandEmpty>Departamento não encontrado.</CommandEmpty>
            <CommandList>
              {departamentos.data
                .filter(
                  (departamento) =>
                    !acessosConfiguradosIds.includes(departamento.id)
                )
                .map((departamento) => (
                  <CommandItem
                    key={departamento.id}
                    value={`${departamento.id}`}
                    onSelect={(currentValue) => {
                      setValue(
                        Number(currentValue) !== departamento.id
                          ? ''
                          : currentValue
                      );
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        Number(value) === departamento.id
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                    />
                    {codigoSecretariaMask(
                      departamento.secretaria.codigo.toString()
                    )}
                    .{codigoSecretariaMask(departamento.codigo.toString())}.00 -{' '}
                    {departamento.nome}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <Button
        type='button'
        variant='secondary'
        disabled={value === ''}
        onClick={setAcessos}
      >
        <ArrowDown className='mr-2 h-4 w-4' /> Adicionar Departamento
      </Button>
    </>
  );
}
