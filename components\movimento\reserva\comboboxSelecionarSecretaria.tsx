'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn, montarCodigoSecretaria } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarReservas } from '@/lib/database/movimento/reservas';

export function ComboboxSelecionaSecretaria({
  secretarias,
  secretariaId,
  setSecretariaId,
}: {
  secretarias: Required<
    Awaited<ReturnType<typeof listarReservas>>
  >['data']['secretarias'];
  secretariaId: number | null;
  setSecretariaId: React.Dispatch<React.SetStateAction<number | null>>;
}) {
  const [open, setOpen] = React.useState(false);

  const secretariaSelecionada =
    secretarias.find((secretaria) => {
      return secretariaId === secretaria.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {secretariaSelecionada
              ? montarCodigoSecretaria(secretariaSelecionada.codigo) +
                ' - ' +
                secretariaSelecionada.nome
              : 'Selecione a secretaria...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar secretaria...' />
            <CommandEmpty>Secretaria não encontrada.</CommandEmpty>
            <CommandList>
              <CommandItem
                key={0}
                value={`Todas as secretarias`}
                onSelect={() => {
                  setSecretariaId(0);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    secretariaId === 0 ? 'opacity-100' : 'opacity-0'
                  )}
                />
                Todas as secretarias
              </CommandItem>
              {secretarias.map((sec) => (
                <CommandItem
                  key={sec.id}
                  value={`${montarCodigoSecretaria(sec.codigo)} - ${sec.nome}`}
                  onSelect={() => {
                    setSecretariaId(sec.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      secretariaId === sec.id ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  {montarCodigoSecretaria(sec.codigo)} - {sec.nome}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
