import { siteConfig } from '@/config/site';
import { listarAFsParaRelatorio } from '@/lib/database/relatorios/afs';
import { StatusAF, StatusAFDesc } from '@/lib/enums';
import { toCurrency } from '@/lib/serverUtils';

interface RelatorioAFListaProps {
  afs: NonNullable<
    Required<Awaited<ReturnType<typeof listarAFsParaRelatorio>>>
  >['data']['afs'];
  titulo?: string;
  filtros?: {
    exercicio?: number;
    secretaria?: string;
    departamento?: string;
    subdepartamento?: string;
    status?: number;
    dataInicio?: string;
    dataFim?: string;
  };
  resumo?: {
    totalAFs: number;
    valorTotal: number;
    valorTotalUtilizado: number;
    valorDisponivel: number;
  };
}

export const RelatorioAFLista = ({
  afs,
  titulo = 'Relatório de Autorizações de Fornecimento',
  filtros,
  resumo,
}: RelatorioAFListaProps) => {
  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
            {filtros.secretaria && <p>Secretaria: {filtros.secretaria}</p>}
            {filtros.departamento && (
              <p>Departamento: {filtros.departamento}</p>
            )}
            {filtros.subdepartamento && (
              <p>Subdepartamento: {filtros.subdepartamento}</p>
            )}
            {filtros.status !== undefined && (
              <p>
                Status: {StatusAFDesc[filtros.status as StatusAF] || 'Todos'}
              </p>
            )}
            {filtros.dataInicio && filtros.dataFim && (
              <p>
                Período: {filtros.dataInicio} a {filtros.dataFim}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Resumo */}
      {resumo && (
        <div className='mb-6 rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Resumo</h3>
          <div className='grid grid-cols-4 gap-3 text-sm'>
            <div className='text-center'>
              <div className='font-medium'>Total de AFs</div>
              <div className='font-mono'>{resumo.totalAFs}</div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Total</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotal).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Utilizado</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotalUtilizado)
                  .format()
                  .replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Disponível</div>
              <div
                className={`font-mono ${resumo.valorDisponivel < 0 ? 'text-red-600' : ''}`}
              >
                {toCurrency(resumo.valorDisponivel).format().replace(/0$/, '')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabela */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>AF</th>
              <th className='p-1 text-left font-bold'>Empenho</th>
              <th className='p-1 text-left font-bold'>Fornecedor</th>
              <th className='p-1 text-left font-bold'>Secretaria</th>
              <th className='p-1 text-left font-bold'>Departamento</th>
              <th className='p-1 text-left font-bold'>Despesa</th>
              <th className='p-1 text-left font-bold'>Status</th>
              <th className='p-1 text-left font-bold'>Data</th>
              <th className='p-1 text-right font-bold'>Valor AF</th>
            </tr>
          </thead>
          <tbody>
            {afs.map((af, index) => (
              <tr
                key={af.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {af.numero}/{af.exercicio}
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {af.empenho.numero}/{af.empenho.exercicio}
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={af.empenho.fornecedor?.nome || 'N/A'}
                  >
                    {af.empenho.fornecedor?.nome || 'N/A'}
                  </div>
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={af.empenho.dotacao?.secretaria?.nome || 'N/A'}
                  >
                    {af.empenho.dotacao?.secretaria?.nome || 'N/A'}
                  </div>
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={af.empenho.dotacao?.departamento?.nome || 'N/A'}
                  >
                    {af.empenho.dotacao?.departamento?.nome || 'N/A'}
                  </div>
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {af.empenho.dotacao?.despesa || 'N/A'}
                </td>
                <td className='border-b border-gray-200 p-1'>
                  <span
                    className={`rounded px-1 py-0.5 text-xs ${
                      af.status === StatusAF.ATIVA
                        ? 'bg-green-100 text-green-800'
                        : af.status === StatusAF.UTILIZADA
                          ? 'bg-blue-100 text-blue-800'
                          : af.status === StatusAF.CANCELADA
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {StatusAFDesc[af.status as StatusAF] || 'N/A'}
                  </span>
                </td>
                <td className='border-b border-gray-200 p-1'>
                  {new Date(af.data).toLocaleDateString('pt-BR')}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(af.valorTotal).format().replace(/0$/, '')}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de registros: {afs.length}</p>
        <p className='mt-1'>{siteConfig.name}</p>
      </div>
    </div>
  );
};
