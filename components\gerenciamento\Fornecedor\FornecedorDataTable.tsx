'use client';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import { listarFornecedores } from '@/lib/database/gerenciamento/fornecedores';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';
import { AlertDesativarFornecedor } from './alertDesativarFornecedor';
import { AlertAtivarFornecedor } from './alertAtivarFornecedor';
import { cnpjMask, cpfMask } from '@/lib/utils';

export default function FornecedorDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarFornecedores>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Codigo',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'cnpjCpf',
      header: 'CNPJ/CPF',
      filterFn: 'includesString',
      cell: ({ row }) => {
        const cod: string = row.getValue('cnpjCpf');
        return cod.length < 14 ? cpfMask(cod) : cnpjMask(cod);
      },
    },

    {
      accessorKey: 'email',
      header: 'E-Mail',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'phone',
      header: 'Contato',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link href={`/gerenciamento/fornecedores/editar/${row.original.id}`}>
            <Button type='button' variant='outline'>
              <Pencil className='mr-2 size-4' /> Editar
            </Button>
          </Link>
          {row.original.ativo ? (
            <AlertDesativarFornecedor
              idFornecedor={row.original.id}
              nomeFornecedor={row.original.nome}
            />
          ) : (
            <AlertAtivarFornecedor
              idFornecedor={row.original.id}
              nomeFornecedor={row.original.nome}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatable columns={columns} data={data.data} />
    </div>
  );
}
