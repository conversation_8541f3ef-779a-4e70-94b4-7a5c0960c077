'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  listarDepartamentosAtivos,
  listarEconomicasAtivas,
  listarFuncionaisAtivas,
  listarSecretariasAtivas,
  obterAlteracaoPorId,
} from '@/lib/database/movimento/alteracaoOrcamentaria';
import FormEditarAlteracaoOrcamentaria from '@/components/movimento/alteracaoOrcamentaria/formEditarAlteracaoOrcamentaria';

export default async function AlterarAlteracaoOrcamentariaPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const alterOrca = await obterAlteracaoPorId({
    id: Number(id),
  });

  const secretariasPromise = listarSecretariasAtivas();
  const departamentosPromise = listarDepartamentosAtivos();
  const economicasPromise = listarEconomicasAtivas();
  const funcionaisPromise = listarFuncionaisAtivas();

  const [
    secretarias,
    departamentos,
    /*subdepartamentos,*/ economicas,
    funcionais,
  ] = await Promise.all([
    secretariasPromise,
    departamentosPromise,
    economicasPromise,
    funcionaisPromise,
  ]);

  if (alterOrca.error) {
    return <ErrorAlert error={alterOrca.error} />;
  }
  if (!alterOrca.data) {
    return <ErrorAlert error='Falha ao obter Alteração Orçamentária.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Alteração Orçamentária</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormEditarAlteracaoOrcamentaria
              secretarias={secretarias}
              departamentos={departamentos}
              economicas={economicas}
              funcionais={funcionais}
              alteracaoOrcamentariaData={alterOrca}
            />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
