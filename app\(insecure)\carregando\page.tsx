'use client';
import { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Icons } from '@/components/icons';

export default function CarregandoPage() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Aguarde</AlertDialogTitle>
          </AlertDialogHeader>
          <div className='flex items-center justify-start'>
            Carregando... <Icons.loader className='ml-2 size-4 animate-spin' />
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
