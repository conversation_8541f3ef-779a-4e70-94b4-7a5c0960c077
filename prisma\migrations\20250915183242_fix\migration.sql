/*
  Warnings:

  - You are about to drop the `AlteracaoOrcamentaria` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `ControleAlteracaoOrcamentaria_audit` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "AlteracaoOrcamentaria" DROP CONSTRAINT "AlteracaoOrcamentaria_idDepto_fkey";

-- DropForeignKey
ALTER TABLE "AlteracaoOrcamentaria" DROP CONSTRAINT "AlteracaoOrcamentaria_idEconomica_fkey";

-- DropForeignKey
ALTER TABLE "AlteracaoOrcamentaria" DROP CONSTRAINT "AlteracaoOrcamentaria_idFuncional_fkey";

-- DropForeignKey
ALTER TABLE "AlteracaoOrcamentaria" DROP CONSTRAINT "AlteracaoOrcamentaria_idSecretaria_fkey";

-- DropForeignKey
ALTER TABLE "AlteracaoOrcamentaria" DROP CONSTRAINT "AlteracaoOrcamentaria_idSecretario_fkey";

-- DropForeignKey
ALTER TABLE "AlteracaoOrcamentaria" DROP CONSTRAINT "AlteracaoOrcamentaria_idUsuario_fkey";

-- DropForeignKey
ALTER TABLE "ControleAlteracaoOrcamentaria_audit" DROP CONSTRAINT "ControleAlteracaoOrcamentaria_audit_idAlterOrca_fkey";

-- DropForeignKey
ALTER TABLE "ControleAlteracaoOrcamentaria_audit" DROP CONSTRAINT "ControleAlteracaoOrcamentaria_audit_idUsuario_fkey";

-- DropForeignKey
ALTER TABLE "controleSuperavitsDetalhes" DROP CONSTRAINT "controleSuperavitsDetalhes_idAltOrcament_fkey";

-- DropTable
DROP TABLE "AlteracaoOrcamentaria";

-- DropTable
DROP TABLE "ControleAlteracaoOrcamentaria_audit";

-- CreateTable
CREATE TABLE "alteracaoOrcamentaria" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "tipoAlteracao" SMALLINT NOT NULL,
    "tipoAcao" SMALLINT NOT NULL,
    "status" SMALLINT NOT NULL,
    "valorTotal" DECIMAL(18,3) NOT NULL,
    "valorMes1" DECIMAL(18,3) NOT NULL,
    "valorMes2" DECIMAL(18,3) NOT NULL,
    "valorMes3" DECIMAL(18,3) NOT NULL,
    "valorMes4" DECIMAL(18,3) NOT NULL,
    "valorMes5" DECIMAL(18,3) NOT NULL,
    "valorMes6" DECIMAL(18,3) NOT NULL,
    "valorMes7" DECIMAL(18,3) NOT NULL,
    "valorMes8" DECIMAL(18,3) NOT NULL,
    "valorMes9" DECIMAL(18,3) NOT NULL,
    "valorMes10" DECIMAL(18,3) NOT NULL,
    "valorMes11" DECIMAL(18,3) NOT NULL,
    "valorMes12" DECIMAL(18,3) NOT NULL,
    "dataAbertura" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dataStatus" TIMESTAMP(3) NOT NULL,
    "fonte" SMALLINT NOT NULL,
    "codAplicacao" INTEGER NOT NULL,
    "idSecretaria" INTEGER,
    "idDepto" INTEGER,
    "idEconomica" INTEGER,
    "idFuncional" INTEGER NOT NULL,
    "despesaCopia" INTEGER NOT NULL,
    "despesaAcao" INTEGER NOT NULL,
    "obs" TEXT NOT NULL,
    "idUsuario" INTEGER,
    "dataDecreto" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "tipoDecreto" INTEGER,
    "numDecreto" INTEGER,
    "despesaNova" INTEGER NOT NULL,
    "descrDespNova" TEXT NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,
    "idSecretario" INTEGER,

    CONSTRAINT "alteracaoOrcamentaria_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "controleAlteracaoOrcamentaria_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idAlterOrca" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "controleAlteracaoOrcamentaria_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "alteracaoOrcamentaria_ativo_idx" ON "alteracaoOrcamentaria"("ativo");

-- CreateIndex
CREATE UNIQUE INDEX "alteracaoOrcamentaria_exercicio_id_key" ON "alteracaoOrcamentaria"("exercicio", "id");

-- AddForeignKey
ALTER TABLE "controleSuperavitsDetalhes" ADD CONSTRAINT "controleSuperavitsDetalhes_idAltOrcament_fkey" FOREIGN KEY ("idAltOrcament") REFERENCES "alteracaoOrcamentaria"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "alteracaoOrcamentaria" ADD CONSTRAINT "alteracaoOrcamentaria_idSecretaria_fkey" FOREIGN KEY ("idSecretaria") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "alteracaoOrcamentaria" ADD CONSTRAINT "alteracaoOrcamentaria_idDepto_fkey" FOREIGN KEY ("idDepto") REFERENCES "departamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "alteracaoOrcamentaria" ADD CONSTRAINT "alteracaoOrcamentaria_idEconomica_fkey" FOREIGN KEY ("idEconomica") REFERENCES "economicas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "alteracaoOrcamentaria" ADD CONSTRAINT "alteracaoOrcamentaria_idFuncional_fkey" FOREIGN KEY ("idFuncional") REFERENCES "funcionais"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "alteracaoOrcamentaria" ADD CONSTRAINT "alteracaoOrcamentaria_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "alteracaoOrcamentaria" ADD CONSTRAINT "alteracaoOrcamentaria_idSecretario_fkey" FOREIGN KEY ("idSecretario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "controleAlteracaoOrcamentaria_audit" ADD CONSTRAINT "controleAlteracaoOrcamentaria_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "controleAlteracaoOrcamentaria_audit" ADD CONSTRAINT "controleAlteracaoOrcamentaria_audit_idAlterOrca_fkey" FOREIGN KEY ("idAlterOrca") REFERENCES "alteracaoOrcamentaria"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
