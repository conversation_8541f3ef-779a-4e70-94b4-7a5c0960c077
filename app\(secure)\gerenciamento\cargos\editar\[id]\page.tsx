'use server';

import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import {
  listarDepartamentosAtivos,
  listarSubdepartamentosAtivos,
  obterPermissoesEAcessosCargo,
} from '@/lib/database/gerenciamento/cargos';
import { ErrorAlert } from '@/components/error-alert';
import CargoEditForm from '@/components/gerenciamento/cargos/editarCargoForm';
import { listarSecretariasAtivas } from '@/lib/database/gerenciamento/cargos';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export default async function EditarCargoPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  //Juntar tudo em uma função caso lentidão (várias verificações de permissão)
  const cargoPromise = obterPermissoesEAcessosCargo({
    id: id,
  });
  const secretariasPromise = listarSecretariasAtivas();
  const departamentosPromise = listarDepartamentosAtivos();
  const subdepartamentosPromise = listarSubdepartamentosAtivos();

  const [cargo, secretarias, departamentos, subdepartamentos] =
    await Promise.all([
      cargoPromise,
      secretariasPromise,
      departamentosPromise,
      subdepartamentosPromise,
    ]);
  //Paralelismo não funciona com server actions quando o request é feito pelo client. Deixar para usar parelelismo dentro da função rodando no server.

  if (cargo.error) {
    return <ErrorAlert error={cargo.error} />;
  }
  if (!cargo.data) {
    return <ErrorAlert error='Falha ao obter cargo.' />;
  }

  if (secretarias.error) {
    return <ErrorAlert error={secretarias.error} />;
  }
  if (!secretarias.data) {
    return <ErrorAlert error='Falha ao obter secretarias.' />;
  }

  if (departamentos.error) {
    return <ErrorAlert error={departamentos.error} />;
  }
  if (!departamentos.data) {
    return <ErrorAlert error='Falha ao obter departamentos.' />;
  }

  if (subdepartamentos.error) {
    return <ErrorAlert error={subdepartamentos.error} />;
  }
  if (!subdepartamentos.data) {
    return <ErrorAlert error='Falha ao obter subdepartamentos.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Alterar Cargo</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <CargoEditForm
              cargo={cargo}
              secretarias={secretarias}
              departamentos={departamentos}
              subdepartamentos={subdepartamentos}
            />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
