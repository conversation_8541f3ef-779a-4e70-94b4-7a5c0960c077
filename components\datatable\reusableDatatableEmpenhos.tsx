'use client';

import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsLeft,
  ChevronsRight,
  Search,
  Filter,
  X,
  Calendar,
  DollarSign,
  Building2,
  Hash,
  User,
} from 'lucide-react';

import {
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Badge } from '../ui/badge';
import { Calendar as CalendarComponent } from '../ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { StatusEmpenho, StatusEmpenhoDesc } from '@/lib/enums';
import { ComboboxSelecionaSecretaria } from '../movimento/reserva/comboboxSelecionarSecretaria';
import { ComboboxSelecionaDepartamento } from '../movimento/reserva/comboboxSelecionarDepartamento';

interface EmpenhoAdvancedFilters {
  // Empenho number filters
  empenhoDe?: number;
  empenhoAte?: number;
  empenhoExato?: number;
  empenhoParcial?: string;

  // Date filters
  dataDe?: Date;
  dataAte?: Date;

  // Value filters
  valorDe?: number;
  valorAte?: number;

  // Supplier filters
  fornecedor?: string;
  fornecedorCnpjCpf?: string;

  // Status filters
  statusSelecionados?: StatusEmpenho[];

  // Dotação hierarchy filters
  secretariaId?: number | null;
  departamentoId?: number | null;

  // Exercise filter
  exercicio?: number;

  // Special filters
  apenasSemProtocolo?: boolean;
  apenasComProtocolo?: boolean;
  apenasComSaldo?: boolean;
  apenasAnulados?: boolean;
  apenasLiquidados?: boolean;
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  secretarias?: Array<{
    id: number;
    nome: string;
    ativo: boolean;
    codigo: number;
  }>;
  departamentos?: Array<{
    id: number;
    nome: string;
    secretariaId: number;
    ativo: boolean;
    codigo: number;
    secretaria: { id: number; nome: string; ativo: boolean; codigo: number };
  }>;
  fornecedores?: Array<{ id: number; nome: string; cnpjCpf: string }>;
  exerciciosDisponiveis?: number[];
  onFiltersChange?: (filters: EmpenhoAdvancedFilters) => void;
  initialFilters?: Partial<EmpenhoAdvancedFilters>;
}

export function ReusableDatatableEmpenhos<TData, TValue>({
  columns,
  data,
  secretarias = [],
  departamentos = [],
  fornecedores = [],
  exerciciosDisponiveis = [],
  onFiltersChange,
  initialFilters,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [filters, setFilters] = useState<EmpenhoAdvancedFilters>({
    ...initialFilters,
  });

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
    enableMultiRowSelection: true,
    initialState: {
      columnVisibility: {
        secretariaId: false,
        departamentoId: false,
      },
    },
  });

  // Apply filters to table
  useEffect(() => {
    const activeFilters: any = {};

    // Apply empenho number filters
    if (filters.empenhoExato) {
      activeFilters.id = filters.empenhoExato.toString();
    } else if (filters.empenhoParcial) {
      activeFilters.id = filters.empenhoParcial;
    }

    // Apply supplier filters
    if (filters.fornecedor) {
      activeFilters['fornecedor.nome'] = filters.fornecedor;
    }

    // Apply description filters
    if (filters.fornecedorCnpjCpf) {
      activeFilters['fornecedor.cnpjCpf'] = filters.fornecedorCnpjCpf;
    }

    // Apply status filters
    if (filters.statusSelecionados && filters.statusSelecionados.length > 0) {
      activeFilters.status = filters.statusSelecionados.map((s) =>
        s.toString()
      );
    }

    // Apply hierarchy filters
    if (filters.secretariaId) {
      activeFilters.secretariaId = filters.secretariaId;
    }
    if (filters.departamentoId) {
      activeFilters.departamentoId = filters.departamentoId;
    }

    // Apply date range filters
    if (filters.dataDe || filters.dataAte) {
      activeFilters.data = {
        de: filters.dataDe,
        ate: filters.dataAte,
      };
    }

    // Apply value range filters
    if (filters.valorDe || filters.valorAte) {
      activeFilters.valorTotal = {
        de: filters.valorDe,
        ate: filters.valorAte,
      };
    }

    // Update table filters
    Object.keys(activeFilters).forEach((key) => {
      const column = table.getColumn(key);
      if (column) {
        column.setFilterValue(activeFilters[key]);
      }
    });

    // Notify parent component of filter changes
    if (onFiltersChange) {
      onFiltersChange(filters);
    }
  }, [filters, table, onFiltersChange]);

  const handleFilterChange = (
    key: keyof EmpenhoAdvancedFilters,
    value: any
  ) => {
    const newFilters = { ...filters, [key]: value };

    // Clear conflicting filters
    if (key === 'apenasSemProtocolo' && value) {
      newFilters.apenasComProtocolo = false;
    }
    if (key === 'apenasComProtocolo' && value) {
      newFilters.apenasSemProtocolo = false;
    }
    if (key === 'empenhoExato' && value) {
      newFilters.empenhoDe = undefined;
      newFilters.empenhoAte = undefined;
      newFilters.empenhoParcial = undefined;
    }
    if (
      key === 'empenhoDe' ||
      key === 'empenhoAte' ||
      key === 'empenhoParcial'
    ) {
      newFilters.empenhoExato = undefined;
    }

    setFilters(newFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters: EmpenhoAdvancedFilters = {};
    setFilters(clearedFilters);

    // Clear all table filters
    table.getAllColumns().forEach((column) => {
      column.setFilterValue(undefined);
    });
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) =>
      value !== undefined &&
      value !== null &&
      value !== false &&
      (Array.isArray(value) ? value.length > 0 : true)
  );

  const getStatusBadgeVariant = (status: StatusEmpenho) => {
    switch (status) {
      case StatusEmpenho.EMPENHADO:
        return 'default';
      case StatusEmpenho.LIQUIDADO:
        return 'secondary';
      case StatusEmpenho.ANULADO:
      case StatusEmpenho.ANULADO_PARCIAL:
        return 'destructive';
      case StatusEmpenho.CANCELADO:
        return 'outline';
      default:
        return 'default';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const filteredDepartamentos = filters.secretariaId
    ? departamentos.filter((d) => d.secretariaId === filters.secretariaId)
    : departamentos;

  // Note: fornecedores is available for future supplier search functionality

  return (
    <div className='space-y-4'>
      {/* Search and Filter Header */}
      <div className='flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center'>
        <div className='flex items-center gap-2'>
          <div className='relative'>
            <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              placeholder='Pesquisar empenhos...'
              value={(table.getColumn('id')?.getFilterValue() as string) ?? ''}
              onChange={(event) =>
                table.getColumn('id')?.setFilterValue(event.target.value)
              }
              className='w-[200px] pl-10 sm:w-[300px]'
            />
          </div>
          <Button
            variant='outline'
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className='flex items-center gap-2'
          >
            <Filter className='h-4 w-4' />
            Filtros Avançados
            {hasActiveFilters && (
              <Badge variant='secondary' className='ml-1'>
                {
                  Object.values(filters).filter(
                    (v) =>
                      v !== undefined &&
                      v !== false &&
                      (Array.isArray(v) ? v.length > 0 : true)
                  ).length
                }
              </Badge>
            )}
          </Button>
        </div>

        {hasActiveFilters && (
          <Button
            variant='ghost'
            onClick={clearAllFilters}
            className='text-red-600 hover:text-red-700'
          >
            <X className='mr-2 h-4 w-4' />
            Limpar Filtros
          </Button>
        )}
      </div>

      {/* Advanced Filters Panel */}
      {showAdvancedFilters && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Filter className='h-5 w-5' />
              Filtros Avançados
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* Empenho Number Filters */}
            <div className='space-y-4'>
              <Label className='flex items-center gap-2 text-sm font-medium'>
                <Hash className='h-4 w-4' />
                Número do Empenho
              </Label>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
                <div className='space-y-2'>
                  <Label htmlFor='empenhoExato'>Exato</Label>
                  <Input
                    id='empenhoExato'
                    type='number'
                    placeholder='Número exato'
                    value={filters.empenhoExato || ''}
                    onChange={(e) =>
                      handleFilterChange(
                        'empenhoExato',
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='empenhoDe'>De</Label>
                  <Input
                    id='empenhoDe'
                    type='number'
                    placeholder='De'
                    value={filters.empenhoDe || ''}
                    onChange={(e) =>
                      handleFilterChange(
                        'empenhoDe',
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='empenhoAte'>Até</Label>
                  <Input
                    id='empenhoAte'
                    type='number'
                    placeholder='Até'
                    value={filters.empenhoAte || ''}
                    onChange={(e) =>
                      handleFilterChange(
                        'empenhoAte',
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='empenhoParcial'>Parcial</Label>
                <Input
                  id='empenhoParcial'
                  placeholder='Busca parcial (ex: 2024*)'
                  value={filters.empenhoParcial || ''}
                  onChange={(e) =>
                    handleFilterChange(
                      'empenhoParcial',
                      e.target.value || undefined
                    )
                  }
                />
              </div>
            </div>

            {/* Date Range Filters */}
            <div className='space-y-4'>
              <Label className='flex items-center gap-2 text-sm font-medium'>
                <Calendar className='h-4 w-4' />
                Período
              </Label>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label>Data De</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant='outline'
                        className='w-full justify-start text-left font-normal'
                      >
                        {filters.dataDe ? (
                          format(filters.dataDe, 'PPP', { locale: ptBR })
                        ) : (
                          <span>Selecione a data inicial</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className='w-auto p-0'>
                      <CalendarComponent
                        mode='single'
                        selected={filters.dataDe}
                        onSelect={(date) => handleFilterChange('dataDe', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className='space-y-2'>
                  <Label>Data Até</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant='outline'
                        className='w-full justify-start text-left font-normal'
                      >
                        {filters.dataAte ? (
                          format(filters.dataAte, 'PPP', { locale: ptBR })
                        ) : (
                          <span>Selecione a data final</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className='w-auto p-0'>
                      <CalendarComponent
                        mode='single'
                        selected={filters.dataAte}
                        onSelect={(date) => handleFilterChange('dataAte', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            {/* Value Range Filters */}
            <div className='space-y-4'>
              <Label className='flex items-center gap-2 text-sm font-medium'>
                <DollarSign className='h-4 w-4' />
                Faixa de Valor
              </Label>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='valorDe'>Valor De</Label>
                  <Input
                    id='valorDe'
                    type='number'
                    step='0.01'
                    placeholder='0,00'
                    value={filters.valorDe || ''}
                    onChange={(e) =>
                      handleFilterChange(
                        'valorDe',
                        e.target.value ? parseFloat(e.target.value) : undefined
                      )
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='valorAte'>Valor Até</Label>
                  <Input
                    id='valorAte'
                    type='number'
                    step='0.01'
                    placeholder='0,00'
                    value={filters.valorAte || ''}
                    onChange={(e) =>
                      handleFilterChange(
                        'valorAte',
                        e.target.value ? parseFloat(e.target.value) : undefined
                      )
                    }
                  />
                </div>
              </div>
            </div>

            {/* Supplier Filters */}
            <div className='space-y-4'>
              <Label className='flex items-center gap-2 text-sm font-medium'>
                <User className='h-4 w-4' />
                Fornecedor
              </Label>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='fornecedor'>Nome</Label>
                  <Input
                    id='fornecedor'
                    placeholder='Nome do fornecedor'
                    value={filters.fornecedor || ''}
                    onChange={(e) =>
                      handleFilterChange(
                        'fornecedor',
                        e.target.value || undefined
                      )
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='fornecedorCnpjCpf'>CNPJ/CPF</Label>
                  <Input
                    id='fornecedorCnpjCpf'
                    placeholder='CNPJ ou CPF'
                    value={filters.fornecedorCnpjCpf || ''}
                    onChange={(e) =>
                      handleFilterChange(
                        'fornecedorCnpjCpf',
                        e.target.value || undefined
                      )
                    }
                  />
                </div>
              </div>
            </div>

            {/* Status Filters */}
            <div className='space-y-4'>
              <Label className='text-sm font-medium'>Status</Label>
              <div className='grid grid-cols-2 gap-3 md:grid-cols-3'>
                {[
                  StatusEmpenho.CANCELADO,
                  StatusEmpenho.EMPENHADO,
                  StatusEmpenho.LIQUIDADO,
                  StatusEmpenho.ANULADO,
                  StatusEmpenho.ANULADO_PARCIAL,
                ].map((status) => (
                  <div key={status} className='flex items-center space-x-2'>
                    <Checkbox
                      id={`status-${status}`}
                      checked={
                        filters.statusSelecionados?.includes(status) || false
                      }
                      onCheckedChange={(checked) => {
                        const currentSelection =
                          filters.statusSelecionados || [];
                        const newSelection = checked
                          ? [...currentSelection, status]
                          : currentSelection.filter((s) => s !== status);
                        handleFilterChange('statusSelecionados', newSelection);
                      }}
                    />
                    <Label
                      htmlFor={`status-${status}`}
                      className='cursor-pointer text-sm'
                    >
                      <Badge
                        variant={getStatusBadgeVariant(status)}
                        className='text-xs'
                      >
                        {StatusEmpenhoDesc[status]}
                      </Badge>
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Dotação Hierarchy Filters */}
            <div className='space-y-4'>
              <Label className='flex items-center gap-2 text-sm font-medium'>
                <Building2 className='h-4 w-4' />
                Dotação
              </Label>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                {secretarias.length > 0 && (
                  <div className='space-y-2'>
                    <Label>Secretaria</Label>
                    <ComboboxSelecionaSecretaria
                      secretarias={secretarias}
                      secretariaId={filters.secretariaId ?? null}
                      setSecretariaId={(id) =>
                        handleFilterChange('secretariaId', id)
                      }
                    />
                  </div>
                )}
                {filteredDepartamentos.length > 0 && (
                  <div className='space-y-2'>
                    <Label>Departamento</Label>
                    <ComboboxSelecionaDepartamento
                      departamentos={filteredDepartamentos}
                      departamentoId={filters.departamentoId ?? null}
                      setDepartamentoId={(id) =>
                        handleFilterChange('departamentoId', id)
                      }
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Exercise Filter */}
            {exerciciosDisponiveis.length > 0 && (
              <div className='space-y-4'>
                <Label htmlFor='exercicio'>Exercício</Label>
                <select
                  id='exercicio'
                  className='w-full rounded-md border border-gray-300 p-2'
                  value={filters.exercicio || ''}
                  onChange={(e) =>
                    handleFilterChange(
                      'exercicio',
                      e.target.value ? parseInt(e.target.value) : undefined
                    )
                  }
                >
                  <option value=''>Todos os exercícios</option>
                  {exerciciosDisponiveis.map((exercicio) => (
                    <option key={exercicio} value={exercicio}>
                      {exercicio}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Special Filters */}
            <div className='space-y-4'>
              <Label className='text-sm font-medium'>Filtros Específicos</Label>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='comProtocolo'
                    checked={filters.apenasComProtocolo}
                    onCheckedChange={(checked) =>
                      handleFilterChange('apenasComProtocolo', checked)
                    }
                  />
                  <Label htmlFor='comProtocolo' className='text-sm'>
                    Apenas com protocolo
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='semProtocolo'
                    checked={filters.apenasSemProtocolo}
                    onCheckedChange={(checked) =>
                      handleFilterChange('apenasSemProtocolo', checked)
                    }
                  />
                  <Label htmlFor='semProtocolo' className='text-sm'>
                    Apenas sem protocolo
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='comSaldo'
                    checked={filters.apenasComSaldo}
                    onCheckedChange={(checked) =>
                      handleFilterChange('apenasComSaldo', checked)
                    }
                  />
                  <Label htmlFor='comSaldo' className='text-sm'>
                    Apenas com saldo
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='anulados'
                    checked={filters.apenasAnulados}
                    onCheckedChange={(checked) =>
                      handleFilterChange('apenasAnulados', checked)
                    }
                  />
                  <Label htmlFor='anulados' className='text-sm'>
                    Apenas anulados
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='liquidados'
                    checked={filters.apenasLiquidados}
                    onCheckedChange={(checked) =>
                      handleFilterChange('apenasLiquidados', checked)
                    }
                  />
                  <Label htmlFor='liquidados' className='text-sm'>
                    Apenas liquidados
                  </Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <Card>
          <CardContent className='pt-6'>
            <div className='flex flex-wrap gap-2'>
              {filters.empenhoExato && (
                <Badge variant='secondary'>
                  Empenho: {filters.empenhoExato}
                </Badge>
              )}
              {filters.empenhoDe && filters.empenhoAte && (
                <Badge variant='secondary'>
                  Empenhos: {filters.empenhoDe} - {filters.empenhoAte}
                </Badge>
              )}
              {filters.dataDe && (
                <Badge variant='secondary'>
                  Data De: {format(filters.dataDe, 'dd/MM/yyyy')}
                </Badge>
              )}
              {filters.dataAte && (
                <Badge variant='secondary'>
                  Data Até: {format(filters.dataAte, 'dd/MM/yyyy')}
                </Badge>
              )}
              {filters.valorDe && (
                <Badge variant='secondary'>
                  Valor De: {formatCurrency(filters.valorDe)}
                </Badge>
              )}
              {filters.valorAte && (
                <Badge variant='secondary'>
                  Valor Até: {formatCurrency(filters.valorAte)}
                </Badge>
              )}
              {filters.fornecedor && (
                <Badge variant='secondary'>
                  Fornecedor: {filters.fornecedor}
                </Badge>
              )}
              {filters.statusSelecionados &&
                filters.statusSelecionados.length > 0 && (
                  <Badge variant='secondary'>
                    Status:{' '}
                    {filters.statusSelecionados
                      .map((s) => StatusEmpenhoDesc[s])
                      .join(', ')}
                  </Badge>
                )}
              {filters.secretariaId && (
                <Badge variant='secondary'>
                  Secretaria:{' '}
                  {secretarias.find((s) => s.id === filters.secretariaId)?.nome}
                </Badge>
              )}
              {filters.departamentoId && (
                <Badge variant='secondary'>
                  Departamento:{' '}
                  {
                    departamentos.find((d) => d.id === filters.departamentoId)
                      ?.nome
                  }
                </Badge>
              )}
              {filters.exercicio && (
                <Badge variant='secondary'>
                  Exercício: {filters.exercicio}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Table */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} {...cell.column.columnDef.meta}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  Nenhum resultado encontrado com os filtros aplicados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className='flex items-center justify-between space-x-2 py-4'>
        <div className='text-muted-foreground flex-1 text-sm'>
          {table.getFilteredSelectedRowModel().rows.length} de{' '}
          {table.getFilteredRowModel().rows.length} linha(s) selecionada(s).
        </div>
        <div className='flex items-center space-x-6 lg:space-x-8'>
          <div className='flex w-[100px] items-center justify-center text-sm font-medium'>
            Página {table.getState().pagination.pageIndex + 1} de{' '}
            {table.getPageCount()}
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              className='hidden h-8 w-8 p-0 lg:flex'
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className='sr-only'>Ir para a primeira página</span>
              <ChevronsLeft className='h-4 w-4' />
            </Button>
            <Button
              variant='outline'
              className='h-8 w-8 p-0'
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className='sr-only'>Ir para a página anterior</span>
              <ChevronLeftIcon className='h-4 w-4' />
            </Button>
            <Button
              variant='outline'
              className='h-8 w-8 p-0'
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className='sr-only'>Ir para a próxima página</span>
              <ChevronRightIcon className='h-4 w-4' />
            </Button>
            <Button
              variant='outline'
              className='hidden h-8 w-8 p-0 lg:flex'
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className='sr-only'>Ir para a última página</span>
              <ChevronsRight className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
