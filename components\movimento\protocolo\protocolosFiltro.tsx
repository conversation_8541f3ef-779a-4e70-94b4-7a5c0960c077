'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ComboboxSelecionaSecretaria } from '@/components/movimento/reserva/comboboxSelecionarSecretaria';
import { ComboboxSelecionaDepartamento } from '@/components/movimento/reserva/comboboxSelecionarDepartamento';
import { StatusProtocolo } from '@/lib/enums';

interface ProtocolosFiltroProps {
  onFilterChange: (filters: {
    dias?: number;
    status?: number;
    pedidoDe?: number;
    pedidoAte?: number;
    afDe?: number;
    afAte?: number;
    exercicioAF?: number;
    idSecretaria?: number;
    idDepartamento?: number;
    idDespesa?: string;
  }) => void;
  secretarias: Array<{ id: number; nome: string }>;
  departamentos: Array<{ id: number; nome: string; secretariaId: number }>;
  initialFilters?: any;
}

export function ProtocolosFiltro({
  onFilterChange,
  secretarias,
  departamentos,
  initialFilters,
}: ProtocolosFiltroProps) {
  const [dias, setDias] = useState<number | undefined>(initialFilters?.dias);
  const [status, setStatus] = useState<number | undefined>(
    initialFilters?.status
  );
  const [pedidoDe, setPedidoDe] = useState<number | undefined>(
    initialFilters?.pedidoDe
  );
  const [pedidoAte, setPedidoAte] = useState<number | undefined>(
    initialFilters?.pedidoAte
  );
  const [afDe, setAfDe] = useState<number | undefined>(initialFilters?.afDe);
  const [afAte, setAfAte] = useState<number | undefined>(initialFilters?.afAte);
  const [exercicioAF, setExercicioAF] = useState<number | undefined>(
    initialFilters?.exercicioAF
  );
  const [idSecretaria, setIdSecretaria] = useState<number | null>(
    initialFilters?.idSecretaria || null
  );
  const [idDepartamento, setIdDepartamento] = useState<number | null>(
    initialFilters?.idDepartamento || null
  );
  const [idDespesa, setIdDespesa] = useState<string>(
    initialFilters?.idDespesa || ''
  );

  const handleFilter = () => {
    onFilterChange({
      dias,
      status,
      pedidoDe,
      pedidoAte,
      afDe,
      afAte,
      exercicioAF,
      idSecretaria: idSecretaria || undefined,
      idDepartamento: idDepartamento || undefined,
      idDespesa: idDespesa || undefined,
    });
  };

  const handleClear = () => {
    setDias(undefined);
    setStatus(undefined);
    setPedidoDe(undefined);
    setPedidoAte(undefined);
    setAfDe(undefined);
    setAfAte(undefined);
    setExercicioAF(undefined);
    setIdSecretaria(null);
    setIdDepartamento(null);
    setIdDespesa('');
    onFilterChange({});
  };

  return (
    <div className='mb-4 flex flex-wrap items-center gap-4 rounded-lg border p-4 py-4'>
      <Input
        placeholder='Dias atrás...'
        type='number'
        value={dias || ''}
        onChange={(e) =>
          setDias(e.target.value ? parseInt(e.target.value) : undefined)
        }
        className='max-w-[120px]'
      />

      <select
        value={status || ''}
        onChange={(e) =>
          setStatus(e.target.value ? parseInt(e.target.value) : undefined)
        }
        className='border-input bg-background h-10 max-w-[200px] rounded-md border px-3 py-2 text-sm'
      >
        <option value=''>Todos os status</option>
        {Object.entries(StatusProtocolo)
          .filter(([key]) => isNaN(Number(key)))
          .map(([key, value]) => (
            <option key={value} value={value}>
              {key.replace(/_/g, ' ')}
            </option>
          ))}
      </select>

      <div className='flex gap-2'>
        <Input
          placeholder='Reserva de...'
          type='number'
          value={pedidoDe || ''}
          onChange={(e) =>
            setPedidoDe(e.target.value ? parseInt(e.target.value) : undefined)
          }
          className='max-w-[120px]'
        />
        <Input
          placeholder='Até...'
          type='number'
          value={pedidoAte || ''}
          onChange={(e) =>
            setPedidoAte(e.target.value ? parseInt(e.target.value) : undefined)
          }
          className='max-w-[120px]'
        />
      </div>

      <div className='flex gap-2'>
        <Input
          placeholder='AF de...'
          type='number'
          value={afDe || ''}
          onChange={(e) =>
            setAfDe(e.target.value ? parseInt(e.target.value) : undefined)
          }
          className='max-w-[120px]'
        />
        <Input
          placeholder='Até...'
          type='number'
          value={afAte || ''}
          onChange={(e) =>
            setAfAte(e.target.value ? parseInt(e.target.value) : undefined)
          }
          className='max-w-[120px]'
        />
      </div>

      <Input
        placeholder='Exercício AF'
        type='number'
        value={exercicioAF || ''}
        onChange={(e) =>
          setExercicioAF(e.target.value ? parseInt(e.target.value) : undefined)
        }
        className='max-w-[120px]'
      />

      {secretarias.length > 0 && (
        <div className='w-fit'>
          <ComboboxSelecionaSecretaria
            secretarias={secretarias.map((s) => ({
              ...s,
              ativo: true,
              codigo: s.id,
            }))}
            secretariaId={idSecretaria}
            setSecretariaId={setIdSecretaria}
          />
        </div>
      )}

      {departamentos.length > 0 && (
        <div className='w-fit'>
          <ComboboxSelecionaDepartamento
            departamentos={departamentos
              .filter((d) => !idSecretaria || d.secretariaId === idSecretaria)
              .map((d) => ({
                ...d,
                ativo: true,
                codigo: d.id,
                secretaria: secretarias
                  .map((s) => ({ ...s, ativo: true, codigo: s.id }))
                  .find((s) => s.id === d.secretariaId) || {
                  id: d.secretariaId,
                  nome: '',
                  ativo: true,
                  codigo: d.secretariaId,
                },
              }))}
            departamentoId={idDepartamento}
            setDepartamentoId={setIdDepartamento}
          />
        </div>
      )}

      <Input
        placeholder='Despesa...'
        value={idDespesa}
        onChange={(e) => setIdDespesa(e.target.value)}
        className='max-w-[150px]'
      />

      <Button onClick={handleFilter}>Filtrar</Button>
      <Button variant='outline' onClick={handleClear}>
        Limpar
      </Button>
    </div>
  );
}
