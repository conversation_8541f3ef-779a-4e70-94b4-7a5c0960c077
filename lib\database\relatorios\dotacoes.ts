'use server';

import { prisma } from '@/lib/prisma';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '../usuarios';
import { Prisma } from '@prisma/client';

export interface DotacaoAtualReport {
  id: number;
  exercicio: number;
  despesa: number;
  valorAtual: number;
  desc: string;
  fonte: number;
  codAplicacao: number;
  secretaria?: {
    id: number;
    codigo: number;
    nome: string;
  };
  departamento?: {
    id: number;
    codigo: number;
    nome: string;
  };
  subdepartamento?: {
    id: number;
    codigo: number;
    nome: string;
  };
  economica: {
    id: number;
    codigo: string;
    desc: string;
    categoria: number;
    grupo: number;
    modalidade: number;
    elemento: number;
    subelemento: number;
  };
  funcional: {
    id: number;
    codigo: string;
    desc: string;
    codigoFuncao: number;
    codigoSubfuncao: number;
    codigoPrograma: number;
    codigoAcao: number;
  };
}

export interface CotaReducaoReport {
  id: number;
  exercicio: number;
  despesa: number;
  valorInicial: number;
  cotaReducao: number;
  valorLiberado: number;
  suplementacao: number;
  anulacao: number;
  valorAtual: number;
  desc: string;
  fonte: number;
  codAplicacao: number;
  secretaria?: {
    id: number;
    codigo: number;
    nome: string;
  };
  departamento?: {
    id: number;
    codigo: number;
    nome: string;
  };
  subdepartamento?: {
    id: number;
    codigo: number;
    nome: string;
  };
  economica: {
    id: number;
    codigo: string;
    desc: string;
  };
  funcional: {
    id: number;
    codigo: string;
    desc: string;
  };
  ultimoMotivo?: string;
}

export const obterDotacoesParaRelatorio = async (params: {
  exercicio?: number;
  idSecretaria?: number;
  idDepartamento?: number;
  idSubdepartamento?: number;
  despesa?: number;
  codAplicacaoInicial?: string;
  codAplicacaoFinal?: string;
  economicaInicial?: string;
  economicaFinal?: string;
  fonte?: number;
  bearer?: string;
}) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    bearer: params.bearer,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const where: any = {
      exercicio: params.exercicio || resultPermissao.exercicio,
      ativo: true,
    };

    if (params.idSecretaria) {
      where.secretariaId = params.idSecretaria;
    }
    if (params.idDepartamento) {
      where.departamentoId = params.idDepartamento;
    }
    if (params.idSubdepartamento) {
      where.subdepartamentoId = params.idSubdepartamento;
    }
    if (params.despesa) {
      where.despesa = params.despesa;
    }
    if (params.fonte) {
      where.fonte = params.fonte;
    }

    if (params.codAplicacaoInicial || params.codAplicacaoFinal) {
      where.codAplicacao = {};
      if (params.codAplicacaoInicial) {
        where.codAplicacao.gte = parseInt(params.codAplicacaoInicial);
      }
      if (params.codAplicacaoFinal) {
        where.codAplicacao.lte = parseInt(params.codAplicacaoFinal);
      }
    }

    const result = await prisma.dotacoes.findMany({
      where,
      select: {
        id: true,
        exercicio: true,
        despesa: true,
        valorAtual: true,
        desc: true,
        fonte: true,
        codAplicacao: true,
        secretaria: {
          select: {
            id: true,
            codigo: true,
            nome: true,
          },
        },
        departamento: {
          select: {
            id: true,
            codigo: true,
            nome: true,
            secretaria: {
              select: {
                id: true,
                codigo: true,
                nome: true,
              },
            },
          },
        },
        subdepartamento: {
          select: {
            id: true,
            codigo: true,
            nome: true,
            departamento: {
              select: {
                id: true,
                codigo: true,
                nome: true,
                secretaria: {
                  select: {
                    id: true,
                    codigo: true,
                    nome: true,
                  },
                },
              },
            },
          },
        },
        economica: {
          select: {
            id: true,
            codigo: true,
            desc: true,
            categoria: true,
            grupo: true,
            modalidade: true,
            elemento: true,
            subelemento: true,
          },
        },
        funcional: {
          select: {
            id: true,
            codigo: true,
            desc: true,
            codigoFuncao: true,
            codigoSubfuncao: true,
            codigoPrograma: true,
            codigoAcao: true,
          },
        },
      },
      orderBy: [
        { secretaria: { codigo: 'asc' } },
        { departamento: { codigo: 'asc' } },
        { subdepartamento: { codigo: 'asc' } },
        { despesa: 'asc' },
      ],
    });

    // Apply additional filters for economic code range if specified
    let filteredResult = result;
    if (params.economicaInicial || params.economicaFinal) {
      filteredResult = result.filter((dotacao) => {
        const economica = dotacao.economica.codigo;
        if (params.economicaInicial && economica < params.economicaInicial) {
          return false;
        }
        if (params.economicaFinal && economica > params.economicaFinal) {
          return false;
        }
        return true;
      });
    }

    return {
      data: filteredResult.map((dotacao) => ({
        ...dotacao,
        secretaria:
          dotacao.secretaria ||
          dotacao.departamento?.secretaria ||
          dotacao.subdepartamento?.departamento?.secretaria ||
          null,
        departamento:
          dotacao.departamento || dotacao.subdepartamento?.departamento || null,
        valorAtual: dotacao.valorAtual.toNumber(),
      })) as DotacaoAtualReport[],
    };
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao obter dotações para relatório.`,
    };
  }
};

export const obterCotasReducaoParaRelatorio = async (params: {
  exercicio: number;
  idSecretaria?: number;
  idDepartamento?: number;
  idSubdepartamento?: number;
  reducao?: boolean;
  bearer?: string;
}) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    bearer: params.bearer,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Exercício não encontrado.',
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const where: Prisma.dotacoesWhereInput = {
      exercicio: params.exercicio || resultPermissao.exercicio,
      ativo: true,
    };

    if (params.idSecretaria) {
      where.secretariaId = params.idSecretaria;
    }
    if (params.idDepartamento) {
      where.departamentoId = params.idDepartamento;
    }
    if (params.idSubdepartamento) {
      where.subdepartamentoId = params.idSubdepartamento;
    }

    const dotacoes = await prisma.dotacoes.findMany({
      where,
      select: {
        id: true,
        exercicio: true,
        despesa: true,
        valorInicial: true,
        cotaReducao: true,
        valorLiberado: true,
        suplementacao: true,
        anulacao: true,
        valorAtual: true,
        desc: true,
        fonte: true,
        codAplicacao: true,
        secretaria: {
          select: {
            id: true,
            codigo: true,
            nome: true,
          },
        },
        departamento: {
          select: {
            id: true,
            codigo: true,
            nome: true,
            secretaria: {
              select: {
                id: true,
                codigo: true,
                nome: true,
              },
            },
          },
        },
        subdepartamento: {
          select: {
            id: true,
            codigo: true,
            nome: true,
            departamento: {
              select: {
                id: true,
                codigo: true,
                nome: true,
                secretaria: {
                  select: {
                    id: true,
                    codigo: true,
                    nome: true,
                  },
                },
              },
            },
          },
        },
        economica: {
          select: {
            id: true,
            codigo: true,
            desc: true,
          },
        },
        funcional: {
          select: {
            id: true,
            codigo: true,
            desc: true,
          },
        },
        cotasReducao: {
          select: {
            motivo: true,
            data: true,
            reducao: true,
          },
          orderBy: { data: 'desc' },
          take: 1,
        },
      },
      orderBy: [
        { secretaria: { codigo: 'asc' } },
        { departamento: { codigo: 'asc' } },
        { subdepartamento: { codigo: 'asc' } },
        { despesa: 'asc' },
      ],
    });

    const result: CotaReducaoReport[] = dotacoes.map((dotacao) => ({
      id: dotacao.id,
      exercicio: dotacao.exercicio,
      despesa: dotacao.despesa,
      valorInicial: dotacao.valorInicial.toNumber(),
      cotaReducao: dotacao.cotaReducao.toNumber(),
      valorLiberado: dotacao.valorLiberado.toNumber(),
      suplementacao: dotacao.suplementacao.toNumber(),
      anulacao: dotacao.anulacao.toNumber(),
      valorAtual: dotacao.valorAtual.toNumber(),
      desc: dotacao.desc,
      fonte: dotacao.fonte,
      codAplicacao: dotacao.codAplicacao,
      secretaria:
        dotacao.secretaria ||
        dotacao.departamento?.secretaria ||
        dotacao.subdepartamento?.departamento?.secretaria,
      departamento:
        dotacao.departamento || dotacao.subdepartamento?.departamento,
      subdepartamento: dotacao.subdepartamento || undefined,
      economica: dotacao.economica,
      funcional: dotacao.funcional,
      ultimoMotivo: dotacao.cotasReducao[0]?.motivo || undefined,
    }));

    // Filter by reducao type if specified
    const filteredResult =
      params.reducao !== undefined
        ? result.filter((dotacao) => {
            const hasReducao = dotacao.cotaReducao > 0;
            return params.reducao ? hasReducao : !hasReducao;
          })
        : result;

    return {
      data: filteredResult,
    };
  } catch (e) {
    console.log(e);
    return {
      error: `Erro ao obter cotas/reduções para relatório.`,
    };
  }
};
