import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import Link from 'next/link';
import { Icons } from '../icons';
import { Skeleton } from '../ui/skeleton';
import { createClient } from '@/lib/supabase/server';
import { obterNomeUsuarioConectado } from '@/lib/database/usuarios';

export async function UserNav() {
  const supabase = await createClient();

  const { data, error } = await supabase.auth.getUser();
  if (error || !data?.user) {
    return <Skeleton className='h-[40px] w-[170px]' />;
  }

  const { data: name } = await obterNomeUsuarioConectado();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='secondary'
          className='text-foreground/60 hover:text-foreground/80 relative w-auto transition-colors'
        >
          <Icons.settings className='mr-2 size-4' />
          <span>{name || data.user.email}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-56' align='end' forceMount>
        <DropdownMenuLabel className='font-normal'>
          <div className='flex flex-col space-y-1'>
            <p className='text-sm leading-none font-medium'>
              {name || 'Sem Nome'}
            </p>
            <p className='text-muted-foreground text-xs leading-none'>
              {data.user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <Link href={'/cadastro/autenticacao-multifator'}>
            <DropdownMenuItem>Autenticação Multifator</DropdownMenuItem>
          </Link>
          <Link href={'/cadastro/alterar-senha'}>
            <DropdownMenuItem>Alterar Senha</DropdownMenuItem>
          </Link>
          <Link href={'/cadastro/certificado-digital'}>
            <DropdownMenuItem>Certificado Digital</DropdownMenuItem>
          </Link>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <Link href={'/logout'}>
          <DropdownMenuItem>Sair</DropdownMenuItem>
        </Link>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
