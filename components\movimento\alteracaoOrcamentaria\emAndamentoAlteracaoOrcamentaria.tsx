import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { andamentoAltOrc } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Check } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function EmAndamentoAlteracaoOrcamentaria({
  alteracaoOrcamentariaCheck,
  unmarkSelect,
}: {
  alteracaoOrcamentariaCheck: Array<any>;
  unmarkSelect: Function;
}) {
  const [loading, setLoading] = useState(false);

  const emAndamento = async () => {
    try {
      setLoading(true);

      for (let index = 0; index < alteracaoOrcamentariaCheck.length; index++) {
        const id = alteracaoOrcamentariaCheck[index].original.id;
        if (!alteracaoOrcamentariaCheck[index].original.ativo) {
          toast.error('Alteração Orçamentária ID ' + id + ' está inativa');
          setLoading(false);
          return;
        }
        const data: z.infer<typeof idSchema> = {
          id,
        };

        const res = await andamentoAltOrc(data);
        console.log(res);
        if (res?.error) {
          setLoading(false);
          toast.error('Alteração Orçamentária ID ' + id + ' - ' + res.error);
        } else {
          toast.success('Status atualizado para "Em Andamento" ');
        }
      }
      unmarkSelect();
    } catch (error: any) {
      console.log(error);
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
    setLoading(false);
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline'>
          <Check className='mr-2 size-4' /> Andamento
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Status em Andamento Alterações Orçamentárias
          </AlertDialogTitle>
          <AlertDialogDescription>
            Tem certeza que deseja atualizar o status das alterações
            orçamentárias?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              emAndamento();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Confirmar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
