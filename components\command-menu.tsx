'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { FileIcon, LaptopIcon, MoonIcon, SunIcon } from '@radix-ui/react-icons';
import { useTheme } from 'next-themes';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { menuConfig } from '@/config/menu';
import { ArrowLeft, Folder, MenuIcon } from 'lucide-react';
import { listarPermissoesAcessarUsuarioConectado } from '@/lib/database/usuarios';
import { DialogTitle } from './ui/dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';

export function CommandMenu({
  permissoes,
}: {
  permissoes: Awaited<
    ReturnType<typeof listarPermissoesAcessarUsuarioConectado>
  >;
}) {
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const [pages, setPages] = React.useState<string[]>([]);
  const [search, setSearch] = React.useState('');
  const page = pages[pages.length - 1];
  const { setTheme } = useTheme();

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  const runCommand = React.useCallback((command: () => unknown) => {
    // setOpen(false);
    command();
  }, []);

  const mainNav = menuConfig.mainNav.filter((nav) =>
    nav.items.some(
      (item) => !item.modulo || permissoes.data!.includes(item.modulo)
    )
  );

  return (
    <>
      <Button
        variant='secondary'
        className={cn('relative w-[100px] justify-start text-sm md:w-[120px]')}
        onClick={() => setOpen(true)}
      >
        <span className='hidden lg:inline-flex'>Menu</span>
        <span className='inline-flex lg:hidden'>
          Menu <MenuIcon className='m-auto ml-2 h-5 w-5' />
        </span>
        <kbd className='bg-muted pointer-events-none absolute top-2.5 right-3 hidden h-5 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex'>
          <span className='text-[10px]'>CTRL</span>K
        </kbd>
      </Button>
      <CommandDialog open={open} onOpenChange={setOpen}>
        <VisuallyHidden>
          <DialogTitle>Menu</DialogTitle>
        </VisuallyHidden>
        <CommandInput
          placeholder='Digite um módulo para buscar...'
          value={search}
          onValueChange={setSearch}
        />
        <CommandList>
          <CommandEmpty>Nenhum resultado.</CommandEmpty>
          <CommandGroup heading='Módulos'>
            {!page &&
              search === '' &&
              mainNav.map((group) => (
                <CommandItem
                  key={group.title}
                  value={group.title}
                  onSelect={() => {
                    runCommand(() => {
                      setPages([group.title]);
                    });
                  }}
                  className='cursor-pointer'
                >
                  <div className='mr-2 flex h-4 w-4 items-center justify-center'>
                    <Folder className='h-3 w-3' />
                  </div>
                  {group.title}
                </CommandItem>
              ))}
            {(page || search !== '') && (
              <>
                <CommandItem
                  value='voltar'
                  onSelect={() => {
                    runCommand(() => {
                      setPages([]);
                    });
                  }}
                  className='cursor-pointer'
                >
                  <div className='mr-2 flex h-4 w-4 items-center justify-center'>
                    <ArrowLeft className='h-3 w-3' />
                  </div>
                  Voltar
                </CommandItem>
                {mainNav.map((group) =>
                  group.items.map(
                    (item) =>
                      (page === group.title || search !== '') &&
                      (!item.modulo ||
                        permissoes.data!.includes(item.modulo)) && (
                        <CommandItem
                          key={item.href}
                          value={item.title}
                          onSelect={() => {
                            runCommand(() => router.push(item.href as string));
                            setOpen(false);
                            setPages([]);
                            setSearch('');
                          }}
                          className='cursor-pointer'
                        >
                          <div className='mr-2 flex h-4 w-4 items-center justify-center'>
                            <FileIcon className='h-3 w-3' />
                          </div>
                          {item.title}
                        </CommandItem>
                      )
                  )
                )}
              </>
            )}
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup heading='Tema'>
            <CommandItem
              onSelect={() => runCommand(() => setTheme('light'))}
              className='cursor-pointer'
            >
              <SunIcon className='mr-2 h-4 w-4' />
              Claro
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setTheme('dark'))}
              className='cursor-pointer'
            >
              <MoonIcon className='mr-2 h-4 w-4' />
              Escuro
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => setTheme('system'))}
              className='cursor-pointer'
            >
              <LaptopIcon className='mr-2 h-4 w-4' />
              Sistema
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
