'use client';

import * as React from 'react';
import { ArrowDown, Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ConfigCargo } from '@/types/app';
import { listarCargosAtivos } from '@/lib/database/gerenciamento/usuarios';

export function ComboboxSelecionarCargo({
  cargosConfigurados,
  setCargosConfigurados,
  cargos,
}: {
  cargosConfigurados: ConfigCargo[];
  setCargosConfigurados: React.Dispatch<React.SetStateAction<ConfigCargo[]>>;
  cargos: Awaited<ReturnType<typeof listarCargosAtivos>>;
}) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState('');

  const cargosConfiguradosIds = cargosConfigurados.map((cargo) => {
    return cargo.id;
  });

  if (!cargos.data) return null;

  const cargoSelecionado = cargos.data.find((cargo) => {
    return cargo.id === Number(value);
  });

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {value && cargoSelecionado
              ? cargoSelecionado.nome
              : 'Selecione o cargo...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar cargo...' />
            <CommandEmpty>Cargo não encontrado.</CommandEmpty>
            <CommandList>
              {cargos.data
                .filter((v) => !cargosConfiguradosIds.includes(Number(v.id)))
                .map((v) => (
                  <CommandItem
                    key={v.id}
                    value={`${v.id}`}
                    onSelect={(currentValue) => {
                      setValue(
                        Number(currentValue) !== v.id ? '' : currentValue
                      );
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        Number(value) === v.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    {v.nome}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      <Button
        variant='secondary'
        disabled={value === ''}
        onClick={(e) => {
          e.preventDefault();
          if (!cargoSelecionado) return;

          const cargos = cargosConfigurados.filter(
            (m) => m.id !== Number(value)
          );
          setCargosConfigurados([
            {
              nome: cargoSelecionado.nome,
              id: cargoSelecionado.id,
            },
            ...cargos,
          ]);

          setValue('');
        }}
      >
        <ArrowDown className='mr-2 h-4 w-4' /> Adicionar Cargo
      </Button>
    </>
  );
}
