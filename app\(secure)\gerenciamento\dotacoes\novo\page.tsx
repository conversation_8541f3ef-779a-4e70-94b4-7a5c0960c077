import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import FormCriarDotacao from '@/components/gerenciamento/dotacoes/formCriarDotacao';
import { ErrorAlert } from '@/components/error-alert';
import {
  listarDepartamentosAtivos,
  listarEconomicasDotacao,
  listarFuncionaisAtivas,
  listarSecretariasAtivas,
  listarSubdepartamentosAtivos,
} from '@/lib/database/gerenciamento/dotacoes';

export default async function NovaDotacaoPage() {
  const secretariasPromise = listarSecretariasAtivas();
  const departamentosPromise = listarDepartamentosAtivos();
  const subdepartamentosPromise = listarSubdepartamentosAtivos();
  const economicasPromise = listarEconomicasDotacao();
  const funcionaisPromise = listarFuncionaisAtivas();

  const [secretarias, departamentos, subdepartamentos, economicas, funcionais] =
    await Promise.all([
      secretariasPromise,
      departamentosPromise,
      subdepartamentosPromise,
      economicasPromise,
      funcionaisPromise,
    ]);

  if (secretarias.error) {
    return <ErrorAlert error={secretarias.error} />;
  }
  if (!secretarias.data) {
    return <ErrorAlert error='Falha ao obter secretarias.' />;
  }

  if (departamentos.error) {
    return <ErrorAlert error={departamentos.error} />;
  }
  if (!departamentos.data) {
    return <ErrorAlert error='Falha ao obter departamentos.' />;
  }

  if (subdepartamentos.error) {
    return <ErrorAlert error={subdepartamentos.error} />;
  }
  if (!subdepartamentos.data) {
    return <ErrorAlert error='Falha ao obter subdepartamentos.' />;
  }

  if (economicas.error) {
    return <ErrorAlert error={economicas.error} />;
  }
  if (!economicas.data) {
    return <ErrorAlert error='Falha ao obter econômicas.' />;
  }

  if (funcionais.error) {
    return <ErrorAlert error={funcionais.error} />;
  }
  if (!funcionais.data) {
    return <ErrorAlert error='Falha ao obter funcionais.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Nova Dotação</PageTitle>
      </PageHeader>
      <PageContent>
        <FormCriarDotacao
          secretarias={secretarias}
          departamentos={departamentos}
          subdepartamentos={subdepartamentos}
          economicas={economicas}
          funcionais={funcionais}
        />
      </PageContent>
    </PageWrapper>
  );
}
