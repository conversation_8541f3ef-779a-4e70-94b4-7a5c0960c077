/*
  Warnings:

  - You are about to drop the `configuracoes` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "configuracoes";

-- CreateTable
CREATE TABLE "configuracoesExercicio" (
    "id" SERIAL NOT NULL,
    "travar<PERSON><PERSON><PERSON>s" BOOLEAN NOT NULL DEFAULT false,
    "travar<PERSON>ataPed<PERSON><PERSON>" BOOLEAN NOT NULL DEFAULT false,
    "dataPedidos" TIMESTAMP(3),
    "habilitaAssinatura1" BOOLEAN NOT NULL DEFAULT false,
    "habilitaAssinatura2" BOOLEAN NOT NULL DEFAULT false,
    "economicaAssinatura1" TEXT NOT NULL DEFAULT '',
    "economicaAssinatura2" TEXT NOT NULL DEFAULT '',
    "assinatura1Linha1" TEXT NOT NULL DEFAULT '',
    "assinatura1Linha2" TEXT NOT NULL DEFAULT '',
    "assinatura2Linha1" TEXT NOT NULL DEFAULT '',
    "assinatura2Linha2" TEXT NOT NULL DEFAULT '',
    "nomePrefeito" TEXT NOT NULL DEFAULT '',
    "nomeChefeSecretariaFinancas" TEXT NOT NULL DEFAULT '',
    "exercicio" SMALLINT NOT NULL,

    CONSTRAINT "configuracoesExercicio_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "configuracoesExercicio_exercicio_key" ON "configuracoesExercicio"("exercicio");
