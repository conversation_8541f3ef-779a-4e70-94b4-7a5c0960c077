import { Metadata } from 'next';
import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, FileText, BarChart3 } from 'lucide-react';
import { obterProtocolo } from '@/lib/database/movimento/protocolos';
import { StatusProtocolo } from '@/lib/enums';
import currency from 'currency.js';
import { currencyOptionsNoSymbol, montarCodigoDepartamento } from '@/lib/utils';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageWrapper from '@/components/pages/pageWrapper';
import { ensureAuth } from '@/lib/supabase/actions';
import { temPermissao } from '@/lib/database/usuarios';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';

interface ProtocoloVisualizarPageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({
  params,
}: ProtocoloVisualizarPageProps): Promise<Metadata> {
  const { id } = await params;
  const result = await obterProtocolo({ id: parseInt(id) });

  if (result.error) {
    return {
      title: 'Protocolo não encontrado',
    };
  }

  return {
    title: `Protocolo ${result.data?.id}/${result.data?.exercicio}`,
    description: `Visualização do protocolo ${result.data?.id}/${result.data?.exercicio}`,
  };
}

async function ProtocoloData({ id }: { id: number }) {
  const result = await obterProtocolo({ id });

  if (result.error || !result.data) {
    notFound();
  }

  const protocolo = result.data;

  return (
    <div className='space-y-6'>
      <div className='rounded-lg border p-6'>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
          <div>
            <p className='text-sm text-gray-600'>Protocolo</p>
            <p className='font-semibold'>#{protocolo.id}</p>
          </div>
          <div>
            <p className='text-sm text-gray-600'>Exercício</p>
            <p className='font-semibold'>{protocolo.exercicio}</p>
          </div>
          <div>
            <p className='text-sm text-gray-600'>Status</p>
            <p className='font-semibold'>
              {StatusProtocolo[protocolo.status]?.replace(/_/g, ' ')}
            </p>
          </div>
        </div>
      </div>
      {protocolo.reserva ? (
        <div className='rounded-lg border p-6'>
          <h2 className='mb-4 text-lg font-semibold'>Informações da Reserva</h2>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            <div>
              <p className='text-sm text-gray-600'>Reserva</p>
              <p className='font-semibold'>#{protocolo.reserva.id}</p>
            </div>
            <div>
              <p className='text-sm text-gray-600'>Valor</p>
              <p className='font-semibold'>
                {currency(
                  Number(protocolo.reserva.usarTotal),
                  currencyOptionsNoSymbol
                )
                  .format()
                  .replace(/0$/, '')}
              </p>
            </div>
            <div>
              <p className='text-sm text-gray-600'>Órgão</p>
              <p className='font-semibold'>
                {montarCodigoDepartamento(
                  protocolo.secretariaId || 0,
                  protocolo.departamentoId || 0
                )}{' '}
                - {` `}
                {protocolo.reserva.dotacao?.departamento?.nome || 'N/A'}
              </p>
            </div>
            <div>
              <p className='text-sm text-gray-600'>Despesa</p>
              <p className='font-semibold'>
                {protocolo.reserva.dotacao?.despesa || 'N/A'}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className='rounded-lg border p-6'>
          <h2 className='mb-4 text-lg font-semibold'>Informações da Reserva</h2>
          <div className='py-4 text-center text-gray-500'>
            Nenhuma reserva associada a este protocolo
          </div>
        </div>
      )}
      <div className='rounded-lg border p-6'>
        <h2 className='mb-4 text-lg font-semibold'>Detalhes</h2>
        <div className='space-y-4'>
          <div>
            <p className='text-sm text-gray-600'>Resumo</p>
            <p className='font-medium'>{protocolo.resumo}</p>
          </div>
          {protocolo.obs && (
            <div>
              <p className='text-sm text-gray-600'>Observações</p>
              <p className='font-medium'>{protocolo.obs}</p>
            </div>
          )}
        </div>
      </div>
      {protocolo.numeroAF && (
        <div className='rounded-lg border p-6'>
          <h2 className='mb-4 text-lg font-semibold'>
            Autorização de Fornecimento
          </h2>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <p className='text-sm text-gray-600'>Número AF</p>
              <p className='font-semibold'>
                {protocolo.numeroAF}/{protocolo.exercicioAF}
              </p>
            </div>
          </div>
        </div>
      )}
      {protocolo.numeroEmpenho && (
        <div className='rounded-lg border p-6'>
          <h2 className='mb-4 text-lg font-semibold'>Empenho</h2>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <p className='text-sm text-gray-600'>Número Empenho</p>
              <p className='font-semibold'>
                {protocolo.numeroEmpenho}/{protocolo.exercicioEmpenho}
              </p>
            </div>
          </div>
        </div>
      )}
      <div className='rounded-lg border p-6'>
        <h2 className='mb-4 text-lg font-semibold'>Datas</h2>
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <p className='text-sm text-gray-600'>Data de Abertura</p>
            <p className='font-semibold'>
              {new Date(protocolo.dataAbertura).toLocaleDateString('pt-BR')}
            </p>
          </div>
          <div>
            <p className='text-sm text-gray-600'>Última Alteração</p>
            <p className='font-semibold'>
              {new Date(protocolo.dataStatus).toLocaleDateString('pt-BR')}
            </p>
          </div>
        </div>
      </div>
      {protocolo.protocolos_audit && protocolo.protocolos_audit.length > 0 && (
        <div className='rounded-lg border p-6'>
          <h2 className='mb-4 text-lg font-semibold'>Histórico de Auditoria</h2>
          <div className='space-y-3'>
            {protocolo.protocolos_audit.map((audit) => (
              <div key={audit.id} className='border-b pb-3'>
                <div className='flex justify-between'>
                  <span className='font-medium'>
                    {StatusProtocolo[audit.deStatus]?.replace(/_/g, ' ')} →{' '}
                    {StatusProtocolo[audit.paraStatus]?.replace(/_/g, ' ')}
                  </span>
                  <span className='text-sm text-gray-600'>
                    {new Date(audit.data).toLocaleString('pt-BR')}
                  </span>
                </div>
                {audit.obs && (
                  <p className='mt-1 text-sm text-gray-600'>{audit.obs}</p>
                )}
                <p className='text-xs text-gray-500'>
                  Por: {audit.usuario?.nome || 'Desconhecido'}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

async function VerRelatorioButton({ protocoloId }: { protocoloId: number }) {
  'use server';

  await ensureAuth();

  const temPermissaoRelatorio = await temPermissao({
    modulo: Modulos.RELATORIOS_MOVIMENTO,
    permissao: Permissoes.ACESSAR,
    bearer: '',
  });

  if (!temPermissaoRelatorio) {
    return null;
  }

  return (
    <Link href={`/relatorios/movimento/protocolo?protocolo=${protocoloId}`}>
      <Button variant='outline' size='sm'>
        <BarChart3 className='mr-2 size-4' />
        Ver Relatório
      </Button>
    </Link>
  );
}

export default async function ProtocoloVisualizarPage({
  params,
}: ProtocoloVisualizarPageProps) {
  const { id } = await params;
  const protocoloId = parseInt(id);

  return (
    <PageWrapper>
      <PageHeader>
        <div className='flex w-full items-center justify-between gap-4 px-8'>
          <Link href='/movimento/protocolo'>
            <Button variant='ghost' size='sm'>
              <ArrowLeft className='mr-2 size-4' />
              Voltar
            </Button>
          </Link>
          <div className='flex gap-2'>
            <Link href={`/movimento/protocolo/editar/${protocoloId}`}>
              <Button variant='outline' size='sm'>
                <Edit className='mr-2 size-4' />
                Editar
              </Button>
            </Link>
            <Link href={`/impressao/movimento/protocolo/${protocoloId}`}>
              <Button variant='outline' size='sm'>
                <FileText className='mr-2 size-4' />
                Imprimir
              </Button>
            </Link>
            <VerRelatorioButton protocoloId={protocoloId} />
          </div>
        </div>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<div className='h-[800px] w-full' />}>
          <ProtocoloData id={protocoloId} />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
