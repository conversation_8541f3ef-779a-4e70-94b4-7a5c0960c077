'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
// import { Fornecedor } from '@/types/app';
import { fornecedorSchema } from '@/lib/validation';
import {
  cnpjMask,
  cnpjUnmask,
  cpfMask,
  cpfUnmask,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import {
  criarFornecedor,
  listarFornecedores,
} from '@/lib/database/gerenciamento/fornecedores';

export default function CriarFornecedorForm({}: {
  fornecedores: Awaited<ReturnType<typeof listarFornecedores>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof fornecedorSchema>>({
    resolver: zodResolver(fornecedorSchema),
    defaultValues: {
      //@ts-ignore
      codigo: '',
      nome: '',
      cnpjCpf: '',
      email: '',
      phone: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof fornecedorSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof fornecedorSchema> = {
        codigo: values.codigo,
        nome: values.nome,
        cnpjCpf:
          values.cnpjCpf!.length <= 14
            ? cpfUnmask(values.cnpjCpf!)
            : cnpjUnmask(values.cnpjCpf!),
        email: values.email,
        phone: values.phone,
      };

      const res = await criarFornecedor(data);

      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Fornecedor adicionado.');
        router.push('/gerenciamento/fornecedores');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='grid gap-8 py-4'>
          <FormField
            control={form.control}
            name='codigo'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Codigo</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={1}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Nome</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={1}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='cnpjCpf'
            render={({ field: { onChange } }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  CNPJ/CPF
                </FormLabel>
                <FormControl>
                  <Input
                    // {...field}
                    placeholder='Digite...'
                    maxLength={18}
                    minLength={2}
                    onChange={(e) => {
                      const { value } = e.target;
                      if (value.length <= 14) {
                        e.target.value = cpfMask(value);
                      } else {
                        e.target.value = cnpjMask(value);
                      }
                      onChange(e);
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type='email'
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={2}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='phone'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Telefone
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={2}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className='mt-12 flex justify-between'>
          <Button
            variant={'destructive'}
            disabled={loading}
            onClick={(e) => {
              e.preventDefault();
              router.push('/gerenciamento/fornecedores');
            }}
          >
            <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
          </Button>
          <Button
            type='submit'
            disabled={loading} // || FornecedoresConfigurados.length === 0}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' /> Adicionar Fornecedor
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
