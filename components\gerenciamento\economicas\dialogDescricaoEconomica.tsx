import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { alterarDescricaoEconomica } from '@/lib/database/gerenciamento/economicas';
import { uppercaseMask, toastAlgoDeuErrado } from '@/lib/utils';
import { editarEconomicaSchema } from '@/lib/validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Pencil, Save } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

export function DialogDescricaoEconomica({
  idEconomica,
  descEconomica,
  infoEconomica,
  obsEconomica,
  codigoEconomica,
}: {
  idEconomica: number;
  descEconomica: string;
  infoEconomica: string;
  obsEconomica: string;
  codigoEconomica: string;
}) {
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof editarEconomicaSchema>>({
    resolver: zodResolver(editarEconomicaSchema),
    defaultValues: {
      id: idEconomica,
      desc: descEconomica,
      info: infoEconomica,
      obs: obsEconomica,
    },
  });

  const onSubmit = async (values: z.infer<typeof editarEconomicaSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof editarEconomicaSchema> = {
        id: idEconomica,
        desc: values.desc,
        info: values.info,
        obs: values.obs,
      };
      const res = await alterarDescricaoEconomica(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Econômica alterada.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button type='button' variant='outline'>
          <Pencil className='mr-2 size-4' /> Alterar
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Alterar Econômica</DialogTitle>
          <DialogDescription>{codigoEconomica}</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex flex-wrap gap-4 py-4'>
              <FormField
                control={form.control}
                name='desc'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Descrição
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={150}
                        minLength={2}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('desc', uppercaseMask(value));
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='info'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Info
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={200}
                        minLength={0}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='obs'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Observações
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={200}
                        minLength={0}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogFooter>
          <Button
            type='submit'
            disabled={loading || !form.formState.isValid}
            onClick={form.handleSubmit(onSubmit)}
          >
            {loading ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <Save className='mr-2 h-4 w-4' /> Salvar
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
