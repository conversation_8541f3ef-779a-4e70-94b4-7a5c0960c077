'use client';
import { ColumnDef } from '@tanstack/react-table';
import { listarAlteracoesOrcamentarias } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { AlertAtivarPedDesp } from './alertAtivarPedDesp';
import { AlertDesativarPedDesp } from './alertDesativarPedDesp';
import { SetNovaDespesa } from './setNovaDespesa';
import { AlteracaoOrcamentariaTableCheckBoxFiltroId } from '@/components/movimento/alteracaoOrcamentaria/alteracaoOrcamentariaDatatableCheckBoxFiltroId';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Eye, Pencil } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { currencyOptions } from '@/lib/utils';
import currency from 'currency.js';
import { ReabrirAlteracaoOrcamentaria } from './reabrirAlteracaoOrcamentaria';
import { StatusAlteracaoOrcamentaria, tiposAcao } from '@/lib/enums';

export default function AlteracaoOrcamentariaDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarAlteracoesOrcamentarias>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <div className='flex justify-center gap-4'>
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label='Selecionar Tudo'
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label='Selecionar Registro'
          />
        </div>
      ),
    },
    {
      accessorKey: 'id',
      header: 'Codigo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'dataAbertura',
      header: 'Data',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'tipoAlteracao',
      header: 'Tipo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'tipoAcao',
      header: 'Ação',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'secretaria',
      header: 'Secretaria',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'departamento',
      header: 'Departamento',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'valorTotal',
      header: 'Valor',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptions)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      accessorKey: 'usuario',
      header: 'Usuario',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'despesaNova',
      header: 'Despesa',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      filterFn: 'includesString',
    },
    {
      // accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-start gap-2'>
          <Link
            href={`/movimento/alteracaoOrcamentaria/visualizar/${row.original.id}/`}
          >
            <Button type='button' variant='outline'>
              <Eye className='mr-2 size-4' /> Visualizar
            </Button>
          </Link>
          {row.original.status != StatusAlteracaoOrcamentaria[2] &&
            row.original.status != StatusAlteracaoOrcamentaria[3] && (
              <Link
                href={`/movimento/alteracaoOrcamentaria/editar/${row.original.id}/`}
              >
                <Button type='button' variant='outline'>
                  <Pencil className='mr-2 size-4' /> Editar
                </Button>
              </Link>
            )}
          {row.original.status ==
            StatusAlteracaoOrcamentaria[3] /*Reprovado*/ && (
            <ReabrirAlteracaoOrcamentaria
              idAlteracaoOrcamentaria={row.original.id}
            />
          )}
          {tiposAcao[0] == row.original.tipoAcao &&
            (!row.original.despesaNova ||
              (row.original.status != StatusAlteracaoOrcamentaria[2] &&
                row.original.status != StatusAlteracaoOrcamentaria[3])) && (
              <SetNovaDespesa idPedDesp={row.original.id} />
            )}
          {row.original.status == StatusAlteracaoOrcamentaria[2] ||
          row.original.status ==
            StatusAlteracaoOrcamentaria[3] /*Aprovado/Reprovado*/ ? (
            ''
          ) : row.original.ativo ? (
            <AlertDesativarPedDesp
              idPedDesp={row.original.id}
              descAcao={row.original.despesaAcao.toString()}
              descAlter={row.original.tipoAlteracao.toString()}
              secretaria={row.original.secretaria!.toString()}
              departamento={row.original.departamento!.toString()}
            />
          ) : (
            <AlertAtivarPedDesp
              idPedDesp={row.original.id}
              descAcao={row.original.despesaAcao.toString()}
              descAlter={row.original.tipoAlteracao.toString()}
              secretaria={row.original.secretaria!.toString()}
              departamento={row.original.departamento!.toString()}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <AlteracaoOrcamentariaTableCheckBoxFiltroId
        columns={columns}
        data={data.data}
      />
    </div>
  );
}
