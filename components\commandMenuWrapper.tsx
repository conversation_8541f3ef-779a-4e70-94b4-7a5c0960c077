import { ErrorAlert } from '@/components/error-alert';
import { listarPermissoesAcessarUsuarioConectado } from '@/lib/database/usuarios';
import { CommandMenu } from './command-menu';

export default async function CommandMenuWrapper() {
  const result = await listarPermissoesAcessarUsuarioConectado();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Não foi possível obter as permissões.'} />;

  return <CommandMenu permissoes={result} />;
}
