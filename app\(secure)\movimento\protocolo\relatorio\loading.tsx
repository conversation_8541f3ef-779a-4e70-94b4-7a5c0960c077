import { Skeleton } from '@/components/ui/skeleton';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';

export default function RelatorioProtocolosLoading() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatórios de Protocolos</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
          {/* Configuration Panel */}
          <div className='space-y-6 lg:col-span-2'>
            {/* Report Type Section */}
            <div className='rounded-lg border p-4'>
              <Skeleton className='mb-4 h-6 w-40' />
              <div className='grid grid-cols-1 gap-3 md:grid-cols-2'>
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className='rounded-lg border p-3'>
                    <div className='flex items-center space-x-2'>
                      <Skeleton className='h-4 w-4' />
                      <div className='flex-1'>
                        <Skeleton className='mb-1 h-4 w-24' />
                        <Skeleton className='h-3 w-32' />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Date Range Section */}
            <div className='rounded-lg border p-4'>
              <Skeleton className='mb-4 h-6 w-24' />
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div>
                  <Skeleton className='mb-2 h-4 w-16' />
                  <Skeleton className='h-10 w-full' />
                </div>
                <div>
                  <Skeleton className='mb-2 h-4 w-12' />
                  <Skeleton className='h-10 w-full' />
                </div>
              </div>
            </div>

            {/* Status Filter Section */}
            <div className='rounded-lg border p-4'>
              <Skeleton className='mb-4 h-6 w-32' />
              <div className='grid max-h-40 grid-cols-2 gap-2 overflow-y-auto rounded border p-2 md:grid-cols-3'>
                {Array.from({ length: 9 }).map((_, i) => (
                  <div key={i} className='flex items-center space-x-2'>
                    <Skeleton className='h-4 w-4' />
                    <Skeleton className='h-3 w-20' />
                  </div>
                ))}
              </div>
            </div>

            {/* Grouping Section */}
            <div className='rounded-lg border p-4'>
              <Skeleton className='mb-2 h-6 w-32' />
              <Skeleton className='h-10 w-full' />
            </div>

            {/* Additional Options Section */}
            <div className='rounded-lg border p-4'>
              <Skeleton className='mb-4 h-6 w-40' />
              <div className='space-y-2'>
                {Array.from({ length: 2 }).map((_, i) => (
                  <div key={i} className='flex items-center space-x-2'>
                    <Skeleton className='h-4 w-4' />
                    <Skeleton className='h-4 w-32' />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className='space-y-6'>
            {/* Format Selection */}
            <div className='rounded-lg border p-4'>
              <Skeleton className='mb-4 h-6 w-32' />
              <div className='space-y-2'>
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className='flex items-center space-x-2'>
                    <Skeleton className='h-4 w-4' />
                    <Skeleton className='h-4 w-12' />
                  </div>
                ))}
              </div>
            </div>

            {/* Summary Section */}
            <div className='rounded-lg border p-4'>
              <Skeleton className='mb-4 h-6 w-20' />
              <div className='space-y-3'>
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className='flex justify-between'>
                    <Skeleton className='h-4 w-16' />
                    <Skeleton className='h-4 w-20' />
                  </div>
                ))}
              </div>
            </div>

            {/* Generate Button */}
            <Skeleton className='h-12 w-full' />
          </div>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
