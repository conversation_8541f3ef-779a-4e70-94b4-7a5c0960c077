'use server';

import { AuditoriaGerenciamentoFuncionais, Permissoes } from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  criarFuncionalSchema,
  idSchema,
  nomeEIdSchema,
  permissaoSchema,
} from '@/lib/validation';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { revalidatePath } from 'next/cache';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import z from 'zod';

export const listarFuncionais = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const funcionais = await prisma.funcionais.findMany({
      orderBy: { codigo: 'asc' },
    });
    return {
      data: funcionais,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter funcionais.`,
    };
  }
};

export const desativarFuncional = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.funcionais.update({
        where: {
          id,
        },
        data: {
          ativo: false,
        },
      });

      const auditPromise = tx.gerenciamentoFuncionais_audit.create({
        data: {
          idFuncional: id,
          acao: AuditoriaGerenciamentoFuncionais.DESATIVAR_FUNCIONAL,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/funcionais');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar funcional.`,
    };
  }
};

export const ativarFuncional = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const resultPromise = tx.funcionais.update({
        where: {
          id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.gerenciamentoFuncionais_audit.create({
        data: {
          idFuncional: id,
          acao: AuditoriaGerenciamentoFuncionais.ATIVAR_FUNCIONAL,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });

    revalidatePath('/gerenciamento/funcionais');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar funcional.`,
    };
  }
};

export const alterarDescricaoFuncional = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = nomeEIdSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, nome } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const descAntiga = await tx.funcionais.findUnique({
        where: {
          id,
        },
        select: {
          desc: true,
        },
      });
      const resultPromise = tx.funcionais.update({
        where: {
          id,
        },
        data: {
          desc: nome,
        },
      });
      const auditPromise = tx.gerenciamentoFuncionais_audit.create({
        data: {
          idFuncional: id,
          acao: AuditoriaGerenciamentoFuncionais.ALTERAR_DESCRICAO_FUNCIONAL,
          idUsuario: resultPermissao.idUsuario!,
          de: descAntiga?.desc,
          para: nome,
          ip,
        },
      });

      await Promise.all([resultPromise, auditPromise]);
    });
    revalidatePath('/gerenciamento/funcionais');
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao alterar nome da funcional.`,
    };
  }
};

export const criarFuncional = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = criarFuncionalSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { codigo, desc } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    const funcionalArr = codigo.split(' ');
    if (funcionalArr.length !== 4) {
      return {
        error: 'Funcional inválida.',
      };
    }
    await prisma.$transaction(async (tx) => {
      const promiseFuncao = tx.funcoes.upsert({
        where: { codigo: Number(funcionalArr[0]) },
        create: {
          codigo: Number(funcionalArr[0]),
          desc: '',
        },
        update: {
          codigo: Number(funcionalArr[0]),
        },
      });

      const promiseSubfuncao = tx.subfuncoes.upsert({
        where: { codigo: Number(funcionalArr[1]) },
        create: {
          codigo: Number(funcionalArr[1]),
          desc: '',
        },
        update: {
          codigo: Number(funcionalArr[1]),
        },
      });

      const promisePrograma = tx.programas.upsert({
        where: { codigo: Number(funcionalArr[2]) },
        create: {
          codigo: Number(funcionalArr[2]),
          desc: '',
        },
        update: {
          codigo: Number(funcionalArr[2]),
        },
      });

      const promiseAcao = tx.acoes.upsert({
        where: { codigo: Number(funcionalArr[3]) },
        create: {
          codigo: Number(funcionalArr[3]),
          desc: desc,
        },
        update: {
          codigo: Number(funcionalArr[3]),
        },
      });

      await Promise.all([
        promiseFuncao,
        promiseSubfuncao,
        promisePrograma,
        promiseAcao,
      ]);

      const result = await tx.funcionais.create({
        data: {
          codigo,
          desc,
          funcao: {
            connect: {
              codigo: Number(funcionalArr[0]),
            },
          },
          subfuncao: {
            connect: {
              codigo: Number(funcionalArr[1]),
            },
          },
          programa: {
            connect: {
              codigo: Number(funcionalArr[2]),
            },
          },
          acao: {
            connect: {
              codigo: Number(funcionalArr[3]),
            },
          },
        },
      });
      await tx.gerenciamentoFuncionais_audit.create({
        data: {
          idFuncional: result.id,
          acao: AuditoriaGerenciamentoFuncionais.CRIAR_FUNCIONAL,
          idUsuario: resultPermissao.idUsuario!,
          ip,
        },
      });
    });
    revalidatePath('/gerenciamento/funcionais');
  } catch (e) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Funcional já existe.`,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_FUNCIONAIS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar funcional.`,
    };
  }
};
