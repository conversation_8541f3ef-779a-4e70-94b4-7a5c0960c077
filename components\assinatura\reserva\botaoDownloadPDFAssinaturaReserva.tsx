'use client';

import { useState } from 'react';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { toast } from 'sonner';
import { createClient } from '@/lib/supabase/client';
import { Eye, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const BotaoDownloadPDFAssinaturaReserva = ({ url }: { url: string }) => {
  const [isLoading, setIsLoading] = useState(false);
  const onClick = async () => {
    if (isLoading) return null;
    setIsLoading(true);
    function popUpWasBlocked(popUp: Window | null) {
      return !popUp || popUp.closed || typeof popUp.closed === 'undefined';
    }
    const w = window.open('/carregando', '_blank');
    try {
      if (!w || popUpWasBlocked(w)) {
        setIsLoading(false);
        return toast.error(
          'Janela popup bloqueada. Verifique as configurações do seu navegador.'
        );
      }

      const supabase = createClient();
      const pdf = await supabase.storage.from('reservas').download(url);
      if (pdf.error) {
        setIsLoading(false);
        w.close();
        return toast.error(toastAlgoDeuErrado);
      }
      const fileURL = URL.createObjectURL(pdf.data);
      w.location.href = fileURL;
    } catch (e) {
      setIsLoading(false);
      if (w) {
        w.close();
      }
      toast.error(toastAlgoDeuErrado);
      const env = process.env.NODE_ENV;
      if (env == 'development') {
        console.log(e);
      }
    }
    setIsLoading(false);
  };

  return (
    <Button
      onClick={onClick}
      disabled={isLoading}
      variant={'outline'}
      className='w-[120px]'
    >
      {isLoading ? (
        <Loader2 className='size-4 animate-spin' />
      ) : (
        <Eye className='size-4' />
      )}
      Visualizar
    </Button>
  );
};
