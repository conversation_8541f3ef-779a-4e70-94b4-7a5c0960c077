'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState, useEffect } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { toastAlgoDeuErrado } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { createClient } from '@/lib/supabase/client';
import { emailFormSchema } from '@/lib/validation';

export default function LoginDialog() {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);
  const [isOpen] = useState(true);

  const [loading, setLoading] = useState(false);

  const supabase = createClient();

  const form = useForm<z.infer<typeof emailFormSchema>>({
    resolver: zodResolver(emailFormSchema),
    // defaultValues: {
    //   token: '',
    // },
  });

  const onSubmit = async (values: z.infer<typeof emailFormSchema>) => {
    try {
      setLoading(true);

      await supabase.auth.resetPasswordForEmail(values.email);
      toast.message('Recuperação Solicitada', {
        description:
          'Se o cadastro existir, receberá um email para redefinir sua conta.',
      });
      router.push('/login');
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <>
      <Skeleton className='h-screen w-full' />
      <AlertDialog open={isMounted && isOpen}>
        <AlertDialogContent
          className='sm:max-w-[425px]'
          onOpenAutoFocus={(e) => {
            e.preventDefault();
            form.setFocus('email');
          }}
        >
          <AlertDialogHeader>
            <AlertDialogTitle>Recuperar Conta</AlertDialogTitle>
          </AlertDialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className='grid gap-8 py-4'>
                <FormField
                  control={form.control}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          className='col-span-3'
                          {...field}
                          type='email'
                          placeholder='Digite seu email...'
                          autoComplete='email'
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <AlertDialogFooter>
                <Button type='submit' disabled={loading}>
                  {loading ? (
                    <>
                      <Icons.loader className='mr-2 h-4 w-4 animate-spin' />{' '}
                      Aguarde...
                    </>
                  ) : (
                    <>
                      <Icons.logIn className='mr-2 h-4 w-4' /> Recuperar Conta
                    </>
                  )}
                </Button>
              </AlertDialogFooter>
            </form>
          </Form>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
