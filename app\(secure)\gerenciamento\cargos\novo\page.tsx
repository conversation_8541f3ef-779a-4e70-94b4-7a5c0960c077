'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import CriarCargoForm from '@/components/gerenciamento/cargos/criarCargoForm';
import {
  listarDepartamentosAtivos,
  listarSecretariasAtivas,
  listarSubdepartamentosAtivos,
} from '@/lib/database/gerenciamento/cargos';
import { ErrorAlert } from '@/components/error-alert';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export default async function NovoCargoPage() {
  const secretariasPromise = listarSecretariasAtivas();
  const departamentosPromise = listarDepartamentosAtivos();
  const subdepartamentosPromise = listarSubdepartamentosAtivos();

  const [secretarias, departamentos, subdepartamentos] = await Promise.all([
    secretariasPromise,
    departamentosPromise,
    subdepartamentosPromise,
  ]);

  if (!secretarias) {
    return <ErrorAlert error='Falha ao obter secretarias.' />;
  }
  if (secretarias.error) {
    return <ErrorAlert error={secretarias.error} />;
  }

  if (!departamentos) {
    return <ErrorAlert error='Falha ao obter departamentos.' />;
  }
  if (departamentos.error) {
    return <ErrorAlert error={departamentos.error} />;
  }

  if (!subdepartamentos) {
    return <ErrorAlert error='Falha ao obter subdepartamentos.' />;
  }
  if (subdepartamentos.error) {
    return <ErrorAlert error={subdepartamentos.error} />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Novo Cargo</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <CriarCargoForm
              secretarias={secretarias}
              departamentos={departamentos}
              subdepartamentos={subdepartamentos}
            />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
