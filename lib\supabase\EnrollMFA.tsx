'use client';
import { useEffect, useState } from 'react';
import { createClient } from './client';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

/**
 * EnrollMFA shows a simple enrollment dialog. When shown on screen it calls
 * the `enroll` API. Each time a user clicks the Enable button it calls the
 * `challenge` and `verify` APIs to check if the code provided by the user is
 * valid.
 * When enrollment is successful, it calls `onEnrolled`. When the user clicks
 * Cancel the `onCancelled` callback is called.
 */
export function EnrollMFA({ onEnrolled }: { onEnrolled: () => void }) {
  const [factorId, setFactorId] = useState('');
  const [qr, setQR] = useState(''); // holds the QR code image SVG
  const [code, setCode] = useState(''); // holds the code
  const [verifyCode, setVerifyCode] = useState(''); // contains the code entered by the user
  const [error, setError] = useState(''); // holds an error message
  const supabase = createClient();

  const onEnableClicked = () => {
    setError('');
    (async () => {
      try {
        const challenge = await supabase.auth.mfa.challenge({ factorId });
        if (challenge.error) {
          setError('Erro, tente novamente mais tarde');
          //throw challenge.error
          return;
        }

        const challengeId = challenge.data.id;

        const verify = await supabase.auth.mfa.verify({
          factorId,
          challengeId,
          code: verifyCode,
        });
        if (verify.error) {
          setError('Código Inválido');
          return;
          //throw verify.error
        }

        onEnrolled();

        toast.success('Autenticação Multifator ativada.');
      } catch (e) {
        console.log(e);
      }
    })();
  };

  useEffect(() => {
    (async () => {
      try {
        const { data, error } = await supabase.auth.mfa.enroll({
          factorType: 'totp',
        });
        if (error) {
          //throw error
          return;
        }

        setFactorId(data.id);

        // Supabase Auth returns an SVG QR code which you can convert into a data
        // URL that you can place in an <img> tag.
        setQR(data.totp.qr_code);
        setCode(data.totp.secret);
      } catch (e) {
        console.log(e);
      }
    })();
  }, [supabase.auth.mfa]);

  return (
    <>
      <p></p>
      <img alt='QR' src={qr} className='text-center' />
      <p className='text-xs text-wrap break-words'>Cadastro Manual: {code}</p>
      <Input
        type='text'
        value={verifyCode}
        placeholder='Digite o código...'
        onChange={(e) => setVerifyCode(e.target.value.trim())}
      />
      {error && <div className='error w-full text-center'>{error}</div>}
      <Button className='w-full' onClick={onEnableClicked}>
        Ativar Autenticação Multifator
      </Button>
    </>
  );
}
