'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { obterProtocolo } from '@/lib/database/movimento/protocolos';

interface VincularAfeEmpenhoFormProps {
  protocolo: Required<Awaited<ReturnType<typeof obterProtocolo>>['data']>;
  onVincularAF: (data: { numeroAF: number; exercicioAF: number }) => void;
  onVincularEmpenho: (data: {
    numeroEmpenho: number;
    exercicioEmpenho: number;
  }) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function VincularAfeEmpenhoForm({
  protocolo,
  onVincularAF,
  onVincularEmpenho,
  onCancel,
  isLoading = false,
}: VincularAfeEmpenhoFormProps) {
  const [numeroAF, setNumeroAF] = useState<string>('');
  const [exercicioAF, setExercicioAF] = useState<string>('');
  const [numeroEmpenho, setNumeroEmpenho] = useState<string>('');
  const [exercicioEmpenho, setExercicioEmpenho] = useState<string>('');

  if (!protocolo) {
    return null;
  }

  const handleVincularAF = (e: React.FormEvent) => {
    e.preventDefault();
    if (numeroAF && exercicioAF) {
      onVincularAF({
        numeroAF: parseInt(numeroAF),
        exercicioAF: parseInt(exercicioAF),
      });
    }
  };

  const handleVincularEmpenho = (e: React.FormEvent) => {
    e.preventDefault();
    if (numeroEmpenho && exercicioEmpenho) {
      onVincularEmpenho({
        numeroEmpenho: parseInt(numeroEmpenho),
        exercicioEmpenho: parseInt(exercicioEmpenho),
      });
    }
  };

  return (
    <div className='space-y-6'>
      <div className='mb-4 text-sm text-gray-600'>
        Protocolo: {protocolo.id}
      </div>

      {!protocolo.numeroAF && (
        <div className='rounded-lg border p-4'>
          <h3 className='mb-4 text-lg font-medium'>Vincular AF</h3>
          <form onSubmit={handleVincularAF} className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <label
                  htmlFor='numeroAF'
                  className='mb-1 block text-sm font-medium'
                >
                  Número AF *
                </label>
                <Input
                  id='numeroAF'
                  type='number'
                  value={numeroAF}
                  onChange={(e) => setNumeroAF(e.target.value)}
                  required
                  placeholder='Número da AF'
                />
              </div>
              <div>
                <label
                  htmlFor='exercicioAF'
                  className='mb-1 block text-sm font-medium'
                >
                  Exercício AF *
                </label>
                <Input
                  id='exercicioAF'
                  type='number'
                  value={exercicioAF}
                  onChange={(e) => setExercicioAF(e.target.value)}
                  required
                  placeholder='Exercício da AF'
                />
              </div>
            </div>
            <Button
              type='submit'
              disabled={isLoading || !numeroAF || !exercicioAF}
            >
              {isLoading ? 'Vinculando...' : 'Vincular AF'}
            </Button>
          </form>
        </div>
      )}

      {!protocolo.numeroEmpenho && (
        <div className='rounded-lg border p-4'>
          <h3 className='mb-4 text-lg font-medium'>Vincular Empenho</h3>
          <form onSubmit={handleVincularEmpenho} className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <label
                  htmlFor='numeroEmpenho'
                  className='mb-1 block text-sm font-medium'
                >
                  Número Empenho *
                </label>
                <Input
                  id='numeroEmpenho'
                  type='number'
                  value={numeroEmpenho}
                  onChange={(e) => setNumeroEmpenho(e.target.value)}
                  required
                  placeholder='Número do empenho'
                />
              </div>
              <div>
                <label
                  htmlFor='exercicioEmpenho'
                  className='mb-1 block text-sm font-medium'
                >
                  Exercício Empenho *
                </label>
                <Input
                  id='exercicioEmpenho'
                  type='number'
                  value={exercicioEmpenho}
                  onChange={(e) => setExercicioEmpenho(e.target.value)}
                  required
                  placeholder='Exercício do empenho'
                />
              </div>
            </div>
            <Button
              type='submit'
              disabled={isLoading || !numeroEmpenho || !exercicioEmpenho}
            >
              {isLoading ? 'Vinculando...' : 'Vincular Empenho'}
            </Button>
          </form>
        </div>
      )}

      {protocolo.numeroAF && protocolo.numeroEmpenho && (
        <div className='text-center text-gray-500'>
          AF e Empenho já estão vinculados a este protocolo
        </div>
      )}

      <div className='flex justify-center pt-4'>
        <Button type='button' variant='outline' onClick={onCancel}>
          Voltar
        </Button>
      </div>
    </div>
  );
}
