import DepartamentosDatatableWrapper from '@/components/gerenciamento/secretarias/departamentos/departamentosDataTableWrapper';
import { DialogNovoDepartamento } from '@/components/gerenciamento/secretarias/departamentos/dialogNovoDepartamento';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Suspense } from 'react';
import * as React from 'react';

export default function GerenciamentoDeDepartamentosPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = React.use(params);
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Gerenciamento de Departamentos</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='mb-4 flex w-full flex-wrap justify-between gap-4 px-5'>
            <Link href={`/gerenciamento/secretarias`}>
              <Button variant={'secondary'}>
                <ArrowLeft className='mr-2 h-4 w-4' /> Secretarias
              </Button>
            </Link>
            <DialogNovoDepartamento idSecretaria={Number(id)} />
          </div>
          <DepartamentosDatatableWrapper idSecretaria={Number(id)} />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
