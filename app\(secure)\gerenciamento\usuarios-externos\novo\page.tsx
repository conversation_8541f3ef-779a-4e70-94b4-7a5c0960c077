'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import CriarUsuarioExternoForm from '@/components/gerenciamento/usuariosExternos/CriarUsuarioExternoForm';

export default async function NovoUsuarioExternoPage() {
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Novo Usuário Externo</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <CriarUsuarioExternoForm />
        </div>
      </PageContent>
    </PageWrapper>
  );
}
