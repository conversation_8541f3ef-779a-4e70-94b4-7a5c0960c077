'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Trash2, FileText, Download } from 'lucide-react';
import { currencyOptionsNoSymbol, toastAlgoDeuErrado } from '@/lib/utils';
import currency from 'currency.js';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { toast } from 'sonner';
import {
  obterUrlDocumentoLiquidacao,
  removerDocumentoLiquidacao,
} from '@/lib/database/movimento/liquidacoes';

interface Documento {
  id?: number;
  numeroDocumento: string;
  dataEmissao: Date;
  dataRecebimento: Date;
  dataMaterialServico?: Date;
  valorDocumento: number;
  nomeArquivo?: string;
  caminhoArquivo?: string;
  tamanho?: number;
}

interface DocumentosDataTableProps {
  documentos: Documento[];
  onRemoverDocumento: (index: number) => void;
}

export function DocumentosDataTable({
  documentos,
  onRemoverDocumento,
}: DocumentosDataTableProps) {
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
  };

  const handleDownload = async (documento: Documento) => {
    if (!documento.id) {
      toast.error('ID do documento não encontrado');
      return;
    }

    try {
      const result = await obterUrlDocumentoLiquidacao(documento.id);
      if (result.error) {
        toast.error(result.error);
      } else if (result.data) {
        window.open(result.data.url, '_blank');
      }
    } catch (error) {
      console.error('Erro ao abrir documento:', error);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const handleRemove = async (documento: Documento, index: number) => {
    if (!documento.id) {
      toast.error('ID do documento não encontrado');
      return;
    }

    if (!confirm('Tem certeza que deseja remover este documento?')) {
      return;
    }

    try {
      const result = await removerDocumentoLiquidacao({ id: documento.id });
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Documento removido com sucesso');
        onRemoverDocumento(index);
      }
    } catch (error) {
      console.error('Erro ao remover documento:', error);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Número</TableHead>
            <TableHead>Data Emissão</TableHead>
            <TableHead>Data Recebimento</TableHead>
            <TableHead>Valor</TableHead>
            <TableHead>Arquivo</TableHead>
            <TableHead>Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documentos.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={6}
                className='text-muted-foreground text-center'
              >
                Nenhum documento adicionado
              </TableCell>
            </TableRow>
          ) : (
            documentos.map((documento, index) => (
              <TableRow key={index}>
                <TableCell className='font-medium'>
                  {documento.numeroDocumento}
                </TableCell>
                <TableCell>
                  {format(documento.dataEmissao, 'dd/MM/yyyy', {
                    locale: ptBR,
                  })}
                </TableCell>
                <TableCell>
                  {format(documento.dataRecebimento, 'dd/MM/yyyy', {
                    locale: ptBR,
                  })}
                </TableCell>
                <TableCell className='text-right font-medium text-green-600'>
                  {currency(documento.valorDocumento, currencyOptionsNoSymbol)
                    .format()
                    .replace(/0$/, '')}
                </TableCell>
                <TableCell>
                  {documento.nomeArquivo ? (
                    <div className='flex items-center gap-2'>
                      <FileText className='size-4 text-blue-600' />
                      <div className='flex flex-col'>
                        <span className='max-w-[150px] truncate text-sm'>
                          {documento.nomeArquivo}
                        </span>
                        <span className='text-muted-foreground text-xs'>
                          {formatFileSize(documento.tamanho)}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <span className='text-muted-foreground text-sm'>
                      Sem arquivo
                    </span>
                  )}
                </TableCell>
                <TableCell>
                  <div className='flex items-center gap-2'>
                    {documento.caminhoArquivo && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => handleDownload(documento)}
                      >
                        <Download className='size-4' />
                      </Button>
                    )}
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => handleRemove(documento, index)}
                      className='text-red-600 hover:text-red-700'
                    >
                      <Trash2 className='size-4' />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      {documentos.length > 0 && (
        <div className='border-t bg-gray-50 p-4'>
          <div className='flex items-center justify-between'>
            <span className='text-muted-foreground text-sm'>
              {documentos.length} documento{documentos.length !== 1 ? 's' : ''}{' '}
              adicionado{documentos.length !== 1 ? 's' : ''}
            </span>
            <span className='font-semibold'>
              Total:{' '}
              {currency(
                documentos.reduce(
                  (total, doc) => total + doc.valorDocumento,
                  0
                ),
                currencyOptionsNoSymbol
              )
                .format()
                .replace(/0$/, '')}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
