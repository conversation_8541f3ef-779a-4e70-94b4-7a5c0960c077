export enum TiposAcesso { // Não alterar a ordem
  NENHUM = 0, //Não utilizar o 0
  TODAS_SECRETARIAS,
  SECRETARIAS,
  DEPARTAMENTOS,
  SUBDEPARTAMENTOS,
}

export enum TiposAcessoDesc { // Não alterar a ordem
  'Nenhum' = 0, //Não utilizar o 0
  'Acesso a todas as secretarias, departamentos e subdepartamentos',
  'Acesso a secretarias específicas e seus respectivos departamentos',
  'Acesso a departamentos específicos e seus respectivos subdepartamentos',
  'Acesso a subdepartamentos específicos',
}

export enum TiposAssinatura { // Não alterar a ordem
  NAOASSINA = 0, //Não utilizar o 0
  SECRETARIO,
  GESTOR,
  DIRETOR,
  PREFEITO,
}

export enum TiposAssinaturaDesc { // Não alterar a ordem
  'Não assina' = 0, //Não utilizar o 0
  'Assina como secretário',
  'Assina como gestor',
  'Assina como diretor',
  'Assina como prefeito',
}

export enum Permissoes { // Não alterar a ordem
  ACESSAR = 0,
  CRIAR,
  ALTERAR,
  DELETAR,
}

export enum AuditoriaAcesso { // Não alterar a ordem
  LOGOUT = 0,
  LOGIN = 1,
}

export enum AuditoriaGerenciamentoSecretarias { // Não alterar a ordem
  CRIAR_SECRETARIA = 0,
  CRIAR_DEPARTAMENTO,
  CRIAR_SUBDEPARTAMENTO,
  RENOMEAR_SECRETARIA,
  RENOMEAR_DEPARTAMENTO,
  RENOMEAR_SUBDEPARTAMENTO,
  DESATIVAR_SECRETARIA,
  DESATIVAR_DEPARTAMENTO,
  DESATIVAR_SUBDEPARTAMENTO,
  ATIVAR_SECRETARIA,
  ATIVAR_DEPARTAMENTO,
  ATIVAR_SUBDEPARTAMENTO,
}

export enum AuditoriaGerenciamentoCargos { // Não alterar a ordem
  CRIAR_CARGO = 0,
  RENOMEAR_CARGO,
  DESATIVAR_CARGO,
  ATIVAR_CARGO,
  ALTERAR_TIPO_ACESSO,
  ALTERAR_PERMISSOES_ACESSOS,
  ALTERAR_TIPO_ASSINATURA,
}

export enum AuditoriaGerenciamentoUsuarios {
  CRIAR_USUARIO = 0,
  DESATIVAR_USUARIO,
  ATIVAR_USUARIO,
  ALTERAR_CARGOS,
}

export enum AuditoriaGerenciamentoFornecedores {
  CRIAR_FORNECEDOR = 0,
  DESATIVAR_FORNECEDOR,
  ATIVAR_FORNECEDOR,
  ALTERAR_FORNECEDOR,
}

export enum AuditoriaGerenciamentoEconomicas {
  CRIAR_ECONOMICA = 0,
  ALTERAR_ECONOMICA,
  DESATIVAR_ECONOMICA,
  ATIVAR_ECONOMICA,
}

export enum AuditoriaGerenciamentoFuncionais {
  CRIAR_FUNCIONAL = 0,
  ALTERAR_DESCRICAO_FUNCIONAL,
  DESATIVAR_FUNCIONAL,
  ATIVAR_FUNCIONAL,
}

export enum AuditoriaGerenciamentoDotacoes {
  CRIAR_DOTACAO = 0,
  ALTERAR_DESCRICAO_DOTACAO,
  DESATIVAR_DOTACAO,
  ATIVAR_DOTACAO,
  ALTERAR_COTAS,
  COTA_RED_PARA_DOTACAO,
  DOTACAO_PARA_COTA_RED,
}

export enum AuditoriaCotasReducao {
  COTA_RED_PARA_DOTACAO,
  DOTACAO_PARA_COTA_RED,
}

export enum Fontes {
  'NENHUM' = 0,
  'TESOURO' = 1,
  'TRANSFERÊNCIA E CONVÊNIOS ESTADUAIS - VINCULADOS' = 2,
  'RECURSOS PRÓPRIOS DE FUNDOS ESPECIAIS DE DESPESA - VINCULADOS' = 3,
  'RECURSOS PRÓPRIOS DA ADMINISTRAÇÃO INDIRETA' = 4,
  'TRANSFERÊNCIA E CONVÊNIOS FEDERAIS - VINCULADOS' = 5,
  'OUTRAS FONTES DE RECURSOS' = 6,
  'OPERAÇÕES DE CRÉDITO' = 7,
  'EMENDAS PARLAMENTARES INDIVIDUAIS - LEGISLATIVO MUNICIPAL' = 8,
  'RECURSOS EXTRAÓRÇAMENTÁRIOS' = 19,
  'TESOURO - Exercícios Anteriores' = 91,
  'TRANSFERÊNCIAS E CONVÊNIOS ESTADUAIS - VINCULADOS - Exercícios Anteriores' = 92,
  'RECURSOS PRÓPRIOS DE FUNDOS ESPECIAIS DE DESPESA - VINCULADOS - Exercícios Anteriores' = 93,
  'RECURSOS PRÓPRIOS DA ADMINISTRAÇÃO INDIRETA - Exercícios Anteriores' = 94,
  'TRANSFERÊNCIAS E CONVÊNIOS FEDERAIS - VINCULADOS - Exercícios Anteriores' = 95,
  'OUTRAS FONTES DE RECURSOS - Exercícios Anteriores' = 96,
  'OPERAÇÕES DE CRÉDITO - Exercícios Anteriores' = 97,
  'EMENDAS PARLAMENTARES INDIVIDUAIS - Exercícios Anteriores' = 98,
}

export enum ErrosDotacoes {
  REDUCAO_MAIOR_DOTACAO = 'Cota de redução maior que o valor atual da dotação.',
  REDUCAO_RESTANTE_MENOR_ZERO = 'Cota de redução restante menor que zero.',
  COTA_MENOR_ZERO = 'Cota de redução menor que zero.',
  FALHA_VALIDACAO = 'Falha ao validar cota de redução.',
}

export enum AuditoriaControleSuperavit {
  CRIAR_SUPERAVIT = 0,
  DESATIVAR_SUPERAVIT,
  ATIVAR_SUPERAVIT,
  ALTERAR_SUPERAVIT,
}

export enum AuditoriaAlteracaoOrcamentaria {
  CRIAR_ALTERACAO_ORCAMENTARIA = 0,
  DESATIVAR_ALTERACAO_ORCAMENTARIA,
  ATIVAR_ALTERACAO_ORCAMENTARIA,
  ALTERAR_ALTERACAO_ORCAMENTARIA,
  APROVAR_ALTERACAO_ORCAMENTARIA,
  REPROVAR_ALTERACAO_ORCAMENTARIA,
  REABRIR_ALTERACAO_ORCAMENTARIA,
}

export enum StatusSuperavitDetalhes {
  'Reservado' = 1,
  'Aprovado' = 2,
  'Reprovado' = 3,
}

export enum tiposAlteracao {
  'Anulação' = 1,
  'Superavit' = 2,
  'Excesso de Arrecadação' = 3,
}

export enum tiposAcao {
  // "Nenhum" = 0,
  'Criação de Despesa' = 0,
  'Debito' = 1,
  'Suplementação' = 2,
}

export enum StatusAlteracaoOrcamentaria {
  'Em Aberto' = 0,
  'Em Andamento' = 1,
  'Aprovado' = 2,
  'Reprovado' = 3,
  'Cancelado' = 4,
}

export enum TiposDecreto {
  'Anulação' = 1,
  'Transposição' = 2,
  'Superávit' = 3,
  'Excesso de Arrecadação' = 4,
  'Crédito Especial' = 5,
}

export enum Unidades { // Não alterar a ordem
  'AO' = 0,
  'AP' = 1,
  'BB' = 2,
  'BJ' = 3,
  'BL' = 4,
  'BO' = 5,
  'BR' = 6,
  'CJ' = 7,
  'CL' = 8,
  'CP' = 9,
  'CT' = 10,
  'CX' = 11,
  'DZ' = 12,
  'ED' = 13,
  'EV' = 14,
  'FA' = 15,
  'FD' = 16,
  'FL' = 17,
  'FR' = 18,
  'GL' = 19,
  'HE' = 20,
  'HR' = 21,
  'JG' = 22,
  'KG' = 23,
  'KT' = 24,
  'LT' = 25,
  'M2' = 26,
  'M3' = 27,
  'MÊ' = 28,
  'MH' = 29,
  'MT' = 30,
  'PÇ' = 31,
  'PR' = 32,
  'RS' = 33,
  'SC' = 34,
  'SV' = 35,
  'UN' = 36,
  'VC' = 37,
  'PT' = 38,
}
export enum UnidadesDesc { // Não alterar a ordem
  'ATOS OFICIAIS' = 0,
  'AMPOLA' = 1,
  'BOMBONA' = 2,
  'BUTIJAO' = 3,
  'BLOCO' = 4,
  'BOBINA' = 5,
  'BARRA' = 6,
  'CONJUNTO' = 7,
  'CILINDRO' = 8,
  'CAPSULA' = 9,
  'CARTELA' = 10,
  'CAIXA' = 11,
  'DUZIA' = 12,
  'EDIÇÕES' = 13,
  'ENVELOPE' = 14,
  'FRASCO AMPOLA' = 15,
  'FARDO' = 16,
  'FOLHA' = 17,
  'FRASCO' = 18,
  'GALÃO' = 19,
  'EQUIPE VERTICAL' = 20,
  'HORAS' = 21,
  'JOGO' = 22,
  'KILOGRAMAS' = 23,
  'KIT' = 24,
  'LITRO' = 25,
  'METRO QUADRADO' = 26,
  'METRO CUBICO' = 27,
  'MÊS' = 28,
  'MILHEIRO' = 29,
  'METRO' = 30,
  'PEÇA' = 31,
  'PARES' = 32,
  'RESMAS' = 33,
  'SACO' = 34,
  'SERVIÇO' = 35,
  'UNIDADE' = 36,
  'VEÍCULO' = 37,
  'PACOTE' = 38,
}

export enum ErrosReserva {
  RESERVA_NAO_ENCONTRADA = 'Reserva não encontrada.',
  RESERVA_CANCELADA = 'Essa reserva foi cancelada e não pode ser alterada.',
  RESERVA_EMPENHO = 'O emprenho foi feito e a reserva não pode ser alterada.',
  RESERVA_AGUARDANDO_ASSINATURA = 'A reserva está aguardando assinatura e não pode ser alterada. Cancele e crie uma nova.',
  RESERVA_ASSINADA = 'A reserva já foi assinada e não pode ser alterada. Cancele e crie uma nova.',
  DESPESA_NAO_ENCONTRADA = 'Despesa não encontrada.',
  COTAS_INCONSISTENTES = 'Despesa inconsistente, soma das cotas diferente do valor liberado. Avise um administrador.',
  VALOR_LIBERADO_DIFERENTE = 'Valor disponível da cota mudou. Busque novamente a despesa e ajuste os valores.',
  VALOR_TOTAL_RESERVA_MAIOR_LIBERADO = 'Valor total da reserva maior que o disponível.',
  VALOR_RESERVA_JANEIRO_MAIOR_COTA = 'Valor a reservar no mês de janeiro maior que a cota disponível.',
  VALOR_RESERVA_FEVEREIRO_MAIOR_COTA = 'Valor a reservar no mês de fevereiro maior que a cota disponível.',
  VALOR_RESERVA_MARCO_MAIOR_COTA = 'Valor a reservar no mês de março maior que a cota disponível.',
  VALOR_RESERVA_ABRIL_MAIOR_COTA = 'Valor a reservar no mês de abril maior que a cota disponível.',
  VALOR_RESERVA_MAIO_MAIOR_COTA = 'Valor a reservar no mês de maio maior que a cota disponível.',
  VALOR_RESERVA_JUNHO_MAIOR_COTA = 'Valor a reservar no mês de junho maior que a cota disponível.',
  VALOR_RESERVA_JULHO_MAIOR_COTA = 'Valor a reservar no mês de julho maior que a cota disponível.',
  VALOR_RESERVA_AGOSTO_MAIOR_COTA = 'Valor a reservar no mês de agosto maior que a cota disponível.',
  VALOR_RESERVA_SETEMBRO_MAIOR_COTA = 'Valor a reservar no mês de setembro maior que a cota disponível.',
  VALOR_RESERVA_OUTUBRO_MAIOR_COTA = 'Valor a reservar no mês de outubro maior que a cota disponível.',
  VALOR_RESERVA_NOVEMBRO_MAIOR_COTA = 'Valor a reservar no mês de novembro maior que a cota disponível.',
  VALOR_RESERVA_DEZEMBRO_MAIOR_COTA = 'Valor a reservar no mês de dezembro maior que a cota disponível.',
  VALOR_RESTANTE_JANEIRO_MENOR_ZERO = 'Valor restante no mês de janeiro menor que zero.',
  VALOR_RESTANTE_FEVEREIRO_MENOR_ZERO = 'Valor restante no mês de fevereiro menor que zero.',
  VALOR_RESTANTE_MARCO_MENOR_ZERO = 'Valor restante no mês de março menor que zero.',
  VALOR_RESTANTE_ABRIL_MENOR_ZERO = 'Valor restante no mês de abril menor que zero.',
  VALOR_RESTANTE_MAIO_MENOR_ZERO = 'Valor restante no mês de maio menor que zero.',
  VALOR_RESTANTE_JUNHO_MENOR_ZERO = 'Valor restante no mês de junho menor que zero.',
  VALOR_RESTANTE_JULHO_MENOR_ZERO = 'Valor restante no mês de julho menor que zero.',
  VALOR_RESTANTE_AGOSTO_MENOR_ZERO = 'Valor restante no mês de agosto menor que zero.',
  VALOR_RESTANTE_SETEMBRO_MENOR_ZERO = 'Valor restante no mês de setembro menor que zero.',
  VALOR_RESTANTE_OUTUBRO_MENOR_ZERO = 'Valor restante no mês de outubro menor que zero.',
  VALOR_RESTANTE_NOVEMBRO_MENOR_ZERO = 'Valor restante no mês de novembro menor que zero.',
  VALOR_RESTANTE_DEZEMBRO_MENOR_ZERO = 'Valor restante no mês de dezembro menor que zero.',
  VALOR_ATUAL_RESTANTE_MENOR_ZERO = 'Valor atual restante menor que zero.',
  VALOR_TOTAL_ITENS_DIFERENTE_RESERVA = 'Valor total dos itens difere do valor total da reserva.',
  STATUS_INVALIDO = 'Status da reserva inválido.',
  NENHUMA_ASSINATURA_PENDENTE = 'Nenhuma assinatura pendente para essa reserva.',
  ASSINATURA_PENDENTE_USUARIO_NAO_ENCONTRADA = 'Assinatura pendente para essa reserva não encontrada.',
  FALHA_AO_ASSINAR = 'Falha ao assinar reserva.',
}

export enum AuditoriaMovimentoReservas {
  CRIAR_RESERVA = 0,
  EDITAR_RESERVA = 1,
  CANCELAR_RESERVA = 2,
  ASSINAR_RESERVA = 3,
  DEVOLVER_RESERVA = 4,
}

export enum StatusReserva {
  'Reservado' = 0,
  'Aguardando Assinaturas' = 1,
  'Assinado' = 2,
  'Empenho' = 3,
  'Cancelado' = 4,
  'Devolvido' = 5,
}

export enum AuditoriaMovimentoContratos {
  CRIAR_CONTRATO = 0,
  EDITAR_CONTRATO = 1,
  ATIVAR_CONTRATO = 2,
  DESATIVAR_CONTRATO = 3,
}

export enum StatusDocumentoExterno {
  'Aguardando Assinatura' = 0,
  'Assinado' = 1,
  'Rejeitado' = 2,
  'Cancelado' = 3,
  'Processando' = 4,
}

export enum TipoOrigemDocumento {
  'Reserva' = 1,
  'Contrato' = 2,
  'Termo' = 3,
  // Preparado para futuras expansões
}

export enum AuditoriaAssinaturaExterna {
  CRIAR_DOCUMENTO_EXTERNO = 0,
  SOLICITAR_ASSINATURA_EXTERNA,
  UPLOAD_DOCUMENTO_ASSINADO,
  VALIDAR_ASSINATURA_DIGITAL,
  APROVAR_ASSINATURA_EXTERNA,
  REJEITAR_ASSINATURA_EXTERNA,
  CANCELAR_ASSINATURA_EXTERNA,
}

export enum AuditoriaGerenciamentoTemplates {
  CRIAR_TEMPLATE = 0,
  EDITAR_TEMPLATE,
  ATIVAR_TEMPLATE,
  DESATIVAR_TEMPLATE,
}

// Status do protocolo (todos os 32 do sistema legado)
export enum StatusProtocolo {
  Nenhum = 0,
  ENVIADO_DE_FINANCAS_PARA_COMPRAS = 1,
  RECEBIMENTO_EM_COMPRAS = 2,
  DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS = 3,
  ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE = 4,
  RECEBIMENTO_EM_CONTABILIDADE = 5,
  DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS = 6,
  ENVIADO_DE_FINANCAS_PARA_GABINETE = 7,
  RECEBIMENTO_EM_GABINETE = 8,
  DEVOLUCAO_DE_GABINETE_PARA_FINANCAS = 9,
  CONTROLE_INTERNO = 10,
  ENVIADO_DE_FINANCAS_PARA_SAUDE = 11,
  RECEBIMENTO_EM_SAUDE = 12,
  DEVOLUCAO_DE_SAUDE_PARA_FINANCAS = 13,
  ENVIADO_DE_FINANCAS_PARA_ESPORTES = 14,
  RECEBIMENTO_EM_ESPORTES = 15,
  DEVOLUCAO_DE_ESPORTES_PARA_FINANCAS = 16,
  ENVIADO_DE_FINANCAS_PARA_EDUCACAO = 17,
  RECEBIMENTO_EM_EDUCACAO = 18,
  DEVOLUCAO_DE_EDUCACAO_PARA_FINANCAS = 19,
  DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM = 20,
  ENTRADA_NO_DPAO = 21,
  ENVIADODEFINANCASPARACONTROLEINTERNO = 22,
  ENCAMINHADOSECRETARIODEFINANCAS = 23,
  ENCAMINHADOSECRETARIODEADMINISTRACAO = 24,
  ENCAMINHADOPARACOMISSAO = 25,
  ENTRADANOCOMPRAS = 26,
  ENVIADOPARADPAO = 27,
  AF_ENCAMINHADA = 28,
  OUTROS = 29,
  DEPARTAMENTO_INFORMATICA = 30,
  DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM_PARA_CORRECAO_PEDIDO_COMPRA = 31,
}

// Descrições dos status para exibição
export const StatusProtocoloDesc = {
  [StatusProtocolo.Nenhum]: 'Nenhum',
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS]:
    'Enviado de Finanças para Compras',
  [StatusProtocolo.RECEBIMENTO_EM_COMPRAS]: 'Recebimento em Compras',
  [StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS]:
    'Devolução de Compras para Finanças',
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_CONTABILIDADE]:
    'Enviado de Finanças para Contabilidade',
  [StatusProtocolo.RECEBIMENTO_EM_CONTABILIDADE]:
    'Recebimento em Contabilidade',
  [StatusProtocolo.DEVOLUCAO_DE_CONTABILIDADE_PARA_FINANCAS]:
    'Devolução de Contabilidade para Finanças',
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE]:
    'Enviado de Finanças para Gabinete',
  [StatusProtocolo.RECEBIMENTO_EM_GABINETE]: 'Recebimento em Gabinete',
  [StatusProtocolo.DEVOLUCAO_DE_GABINETE_PARA_FINANCAS]:
    'Devolução de Gabinete para Finanças',
  [StatusProtocolo.CONTROLE_INTERNO]: 'Controle Interno',
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_SAUDE]:
    'Enviado de Finanças para Saúde',
  [StatusProtocolo.RECEBIMENTO_EM_SAUDE]: 'Recebimento em Saúde',
  [StatusProtocolo.DEVOLUCAO_DE_SAUDE_PARA_FINANCAS]:
    'Devolução de Saúde para Finanças',
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_ESPORTES]:
    'Enviado de Finanças para Esportes',
  [StatusProtocolo.RECEBIMENTO_EM_ESPORTES]: 'Recebimento em Esportes',
  [StatusProtocolo.DEVOLUCAO_DE_ESPORTES_PARA_FINANCAS]:
    'Devolução de Esportes para Finanças',
  [StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_EDUCACAO]:
    'Enviado de Finanças para Educação',
  [StatusProtocolo.RECEBIMENTO_EM_EDUCACAO]: 'Recebimento em Educação',
  [StatusProtocolo.DEVOLUCAO_DE_EDUCACAO_PARA_FINANCAS]:
    'Devolução de Educação para Finanças',
  [StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM]:
    'Devolução para Secretaria de Origem',
  [StatusProtocolo.ENTRADA_NO_DPAO]: 'Entrada no DPAO',
  [StatusProtocolo.ENVIADODEFINANCASPARACONTROLEINTERNO]:
    'Enviado de Finanças para Controle Interno',
  [StatusProtocolo.ENCAMINHADOSECRETARIODEFINANCAS]:
    'Encaminhado ao Secretário de Finanças',
  [StatusProtocolo.ENCAMINHADOSECRETARIODEADMINISTRACAO]:
    'Encaminhado ao Secretário de Administração',
  [StatusProtocolo.ENCAMINHADOPARACOMISSAO]: 'Encaminhado para Comissão',
  [StatusProtocolo.ENTRADANOCOMPRAS]: 'Entrada no Compras',
  [StatusProtocolo.ENVIADOPARADPAO]: 'Enviado para DPAO',
  [StatusProtocolo.AF_ENCAMINHADA]: 'AF Encaminhada',
  [StatusProtocolo.OUTROS]: 'Outros',
  [StatusProtocolo.DEPARTAMENTO_INFORMATICA]: 'Departamento de Informática',
  [StatusProtocolo.DEVOLUCAO_PARA_SECRETARIA_DE_ORIGEM_PARA_CORRECAO_PEDIDO_COMPRA]:
    'Devolução para Secretaria de Origem para Correção do Pedido de Compra',
};

// Ações de auditoria
export enum AuditoriaMovimentoProtocolos {
  CRIAR_PROTOCOLO = 'CRIAR_PROTOCOLO',
  ALTERAR_STATUS = 'ALTERAR_STATUS',
  ALTERAR_STATUS_LOTE = 'ALTERAR_STATUS_LOTE',
  CONSULTAR_HISTORICO = 'CONSULTAR_HISTORICO',
  VINCULAR_AF = 'VINCULAR_AF',
  VINCULAR_EMPENHO = 'VINCULAR_EMPENHO',
}

// Status do Empenho
export enum StatusEmpenho {
  CANCELADO = 0,
  EMPENHADO = 1,
  LIQUIDADO = 2,
  ANULADO = 3,
  ANULADO_PARCIAL = 4,
}

// Descrições dos status para exibição
export const StatusEmpenhoDesc = {
  [StatusEmpenho.CANCELADO]: 'Cancelado',
  [StatusEmpenho.EMPENHADO]: 'Empenhado',
  [StatusEmpenho.LIQUIDADO]: 'Liquidado',
  [StatusEmpenho.ANULADO]: 'Anulado',
  [StatusEmpenho.ANULADO_PARCIAL]: 'Anulado Parcial',
} as const;

// Ações de auditoria para empenhos
export enum AuditoriaMovimentoEmpenhos {
  CRIAR_EMPENHO = 1,
  ALTERAR_EMPENHO = 2,
  CANCELAR_EMPENHO = 3,
  REATIVAR_EMPENHO = 4,
  ANULAR_EMPENHO_PARCIAL = 5,
  ESTORNAR_ANULACAO = 6,
  ATUALIZAR_COTAS_MENSAIS = 7,
  VINCULAR_AF = 8,
  DESVINCULAR_AF = 9,
}

// Status da AF (Autorização de Fornecimento)
export enum StatusAF {
  CANCELADA = 0,
  ATIVA = 1,
  UTILIZADA = 2,
  VENCIDA = 3,
}

// Descrições dos status da AF para exibição
export const StatusAFDesc = {
  [StatusAF.CANCELADA]: 'Cancelada',
  [StatusAF.ATIVA]: 'Ativa',
  [StatusAF.UTILIZADA]: 'Utilizada',
  [StatusAF.VENCIDA]: 'Vencida',
} as const;

// Tipos de documento para AFs
export enum TipoDocumentoAF {
  AF_ORIGINAL = 1,
  AF_ASSINADA = 2,
  COMPROVANTE_ENTREGA = 3,
  NOTA_FISCAL = 4,
  OUTROS = 5,
}

// Descrições dos tipos de documento para exibição
export const TipoDocumentoAFDesc = {
  [TipoDocumentoAF.AF_ORIGINAL]: 'AF Original',
  [TipoDocumentoAF.AF_ASSINADA]: 'AF Assinada',
  [TipoDocumentoAF.COMPROVANTE_ENTREGA]: 'Comprovante de Entrega',
  [TipoDocumentoAF.NOTA_FISCAL]: 'Nota Fiscal',
  [TipoDocumentoAF.OUTROS]: 'Outros',
} as const;

// Ações de auditoria para AFs
export enum AuditoriaAFs {
  CRIAR_AF = 1,
  ALTERAR_AF = 2,
  CANCELAR_AF = 3,
  REATIVAR_AF = 4,
  MARCAR_UTILIZADA = 5,
  UPLOAD_DOCUMENTO = 6,
  REMOVER_DOCUMENTO = 7,
  ATUALIZAR_DATAS = 8, // Nova ação para atualizações de data
}

export const AuditoriaAFsDesc = {
  [AuditoriaAFs.CRIAR_AF]: 'Criar AF',
  [AuditoriaAFs.ALTERAR_AF]: 'Alterar AF',
  [AuditoriaAFs.CANCELAR_AF]: 'Cancelar AF',
  [AuditoriaAFs.REATIVAR_AF]: 'Reativar AF',
  [AuditoriaAFs.MARCAR_UTILIZADA]: 'Marcar AF como Utilizada',
  [AuditoriaAFs.UPLOAD_DOCUMENTO]: 'Upload de Documento',
  [AuditoriaAFs.REMOVER_DOCUMENTO]: 'Remover Documento',
  [AuditoriaAFs.ATUALIZAR_DATAS]: 'Atualizar Datas',
};

export enum ErrosEmpenho {
  FORNECEDOR_OBRIGATORIO = 1,
  VALOR_TOTAL_ZERADO = 2,
  SALDO_INSUFICIENTE = 3,
  COTAS_INVALIDAS = 4,
  EMPENHO_JA_ANULADO = 5,
  EMPENHO_JA_LIQUIDADO = 6,
  RESERVA_NAO_ENCONTRADA = 7,
  RESERVA_NAO_RESERVADA = 8,
  FORNECEDOR_NAO_ENCONTRADO = 9,
  DOTACAO_NAO_ENCONTRADA = 10,
  EMPENHO_NAO_ENCONTRADO = 11,
  EMPENHO_NAO_ATIVO = 12,
  EMPENHO_COM_LIQUIDACOES = 13,
  VALOR_ANULACAO_MAIOR_SALDO = 14,
  ERRO_REDISTRIBUICAO_COTAS = 15,
}

// Status da Liquidação
export enum StatusLiquidacao {
  LIQUIDADA = 1,
  ESTORNADA = 2,
  PROCESSADO = 3,
  NAO_PROCESSADO = 4,
}

// Descrições dos status para exibição
export const StatusLiquidacaoDesc = {
  [StatusLiquidacao.LIQUIDADA]: 'Liquidada',
  [StatusLiquidacao.ESTORNADA]: 'Estornada',
  [StatusLiquidacao.PROCESSADO]: 'Processado',
  [StatusLiquidacao.NAO_PROCESSADO]: 'Não Processado',
} as const;

// Ações de auditoria para liquidações
export enum AuditoriaMovimentoLiquidacoes {
  CRIAR_LIQUIDACAO = 1,
  EDITAR_LIQUIDACAO = 2,
  ESTORNAR_LIQUIDACAO = 3,
  ADICIONAR_DOCUMENTO = 4,
  REMOVER_DOCUMENTO = 5,
}

// Erros específicos de liquidação
export enum ErrosLiquidacao {
  EMPENHO_NAO_ENCONTRADO = 1,
  EMPENHO_NAO_EMPENHADO = 2,
  EMPENHO_JA_LIQUIDADO = 3,
  EMPENHO_CANCELADO = 4,
  EMPENHO_ANULADO = 5,
  VALOR_TOTAL_ZERADO = 6,
  VALOR_MAIOR_SALDO_EMPENHO = 7,
  DOCUMENTOS_OBRIGATORIOS = 8,
  LIQUIDACAO_NAO_ENCONTRADA = 9,
  LIQUIDACAO_JA_ESTORNADA = 10,
  DOCUMENTO_DUPLICADO = 11,
  DATA_EMISSAO_POSTERIOR_RECEBIMENTO = 12,
  VALOR_DOCUMENTO_ZERADO = 13,
  NUMERO_DOCUMENTO_OBRIGATORIO = 14,
}
