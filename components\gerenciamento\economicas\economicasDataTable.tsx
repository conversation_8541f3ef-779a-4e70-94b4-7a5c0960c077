'use client';
import { ColumnDef } from '@tanstack/react-table';
import { listarEconomicas } from '@/lib/database/gerenciamento/economicas';
import { AlertDesativarEconomica } from './alertDesativarEconomica';
import { AlertAtivarEconomica } from './alertAtivarEconomica';
import { DialogDescricaoEconomica } from './dialogDescricaoEconomica';
import { ReusableDatatableFiltroCodigoDesc } from '@/components/datatable/reusableDatatableFiltroCodigoDesc';

export default function EconomicasDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarEconomicas>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'codigo',
      header: 'Código',
      filterFn: 'includesString',
      meta: {
        className: 'w-[120px]',
      },
    },
    {
      accessorKey: 'desc',
      header: 'Descrição',
      filterFn: 'includesString',
      meta: {
        className: 'break-words w-[300px] max-w-[300px]',
      },
    },
    {
      accessorKey: 'info',
      header: 'Info',
      filterFn: 'includesString',
      meta: {
        className: 'break-words w-[200px] max-w-[200px]',
      },
    },
    {
      accessorKey: 'obs',
      header: 'Observações',
      filterFn: 'includesString',
      meta: {
        className: 'break-words w-[200px] max-w-[200px]',
      },
    },
    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <DialogDescricaoEconomica
            idEconomica={row.original.id}
            descEconomica={row.original.desc}
            infoEconomica={row.original.info}
            obsEconomica={row.original.obs}
            codigoEconomica={row.original.codigo}
          />
          {row.original.ativo ? (
            <AlertDesativarEconomica
              idEconomica={row.original.id}
              descEconomica={row.original.desc}
              codigoEconomica={row.original.codigo}
            />
          ) : (
            <AlertAtivarEconomica
              idEconomica={row.original.id}
              descEconomica={row.original.desc}
              codigoEconomica={row.original.codigo}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatableFiltroCodigoDesc columns={columns} data={data.data} />
    </div>
  );
}
