'use server';

import {
  AuditoriaMovimentoLiquidacoes,
  ErrosLiquidacao,
  Permissoes,
  StatusEmpenho,
  StatusLiquidacao,
} from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  criarLiquidacaoInicialSchema,
  criarLiquidacaoSchema,
  editarLiquidacaoSchema,
  estornarLiquidacaoSchema,
  liquidacaoIdSchema,
  permissaoSchema,
  consultarSaldoEmpenhoParaLiquidacaoSchema,
  uploadDocumentoLiquidacaoSchema,
  removerDocumentoLiquidacaoSchema,
} from '@/lib/validation';
import {
  listarIdsDotacoesUsuarioConectadoTemAcesso,
  obterIpUsuarioConectado,
  temPermissao,
} from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { revalidatePath } from 'next/cache';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { toCurrency } from '@/lib/serverUtils';
import { Prisma } from '@prisma/client';
import { createSuperClient } from '@/lib/supabase/server';

const MODULE = Modulos.MOVIMENTO_LIQUIDACAO;
const ROUTE = '/movimento/liquidacoes';

export const listarLiquidacoes = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    let acessos;
    if (!resultPermissao.gerente) {
      acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

      if (acessos.error) {
        return {
          error: acessos.error,
        };
      }

      if (!acessos.data) {
        return {
          error: 'Não foi possível obter acessos do usuário.',
        };
      }
    }

    let where: Prisma.liquidacoesWhereInput = {
      ativo: true,
      exercicio: resultPermissao.exercicio,
    };

    if (!resultPermissao.gerente && acessos?.data?.dotacoes) {
      where.empenho = {
        idDotacao: { in: acessos.data.dotacoes },
      };
    }

    const liquidacoes = await prisma.liquidacoes.findMany({
      select: {
        id: true,
        valorTotal: true,
        numero: true,
        exercicio: true,
        data: true,
        status: true,
        empenho: {
          select: {
            numero: true,
            exercicio: true,
            reserva: {
              select: {
                dotacao: {
                  select: {
                    despesa: true,
                    desc: true,
                    secretariaId: true,
                    departamentoId: true,
                    departamento: {
                      select: {
                        secretariaId: true,
                      },
                    },
                  },
                },
              },
            },
            fornecedor: {
              select: {
                id: true,
                nome: true,
                cnpjCpf: true,
              },
            },
          },
        },
        liquidacoes_documentos: {
          where: { ativo: true },
          select: {
            id: true,
            numeroDocumento: true,
            valorDocumento: true,
            dataEmissao: true,
            dataRecebimento: true,
          },
        },
      },
      where,
      orderBy: { id: 'desc' },
    });

    const secretariasIds = liquidacoes
      .map(
        (liquidacao) =>
          liquidacao.empenho.reserva?.dotacao.secretariaId ||
          liquidacao.empenho.reserva?.dotacao.departamento?.secretariaId ||
          0
      )
      .filter(Boolean);
    const departamentosIds = liquidacoes
      .map(
        (liquidacao) => liquidacao.empenho.reserva?.dotacao.departamentoId || 0
      )
      .filter(Boolean);

    const [secretarias, departamentos] = await Promise.all([
      prisma.secretarias.findMany({
        where: {
          id: { in: secretariasIds },
          ativo: true,
        },
      }),
      prisma.departamentos.findMany({
        where: {
          id: { in: departamentosIds },
          ativo: true,
        },
        include: { secretaria: true },
      }),
    ]);

    return {
      data: {
        liquidacoes: liquidacoes.map((liquidacao) => ({
          ...liquidacao,
          valorTotal: liquidacao.valorTotal.toNumber(),
          liquidacoes_documentos: liquidacao.liquidacoes_documentos.map(
            (doc: any) => ({
              ...doc,
              valorDocumento: doc.valorDocumento.toNumber(),
            })
          ),
        })),
        secretarias,
        departamentos,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter liquidações.`,
    };
  }
};

export const obterLiquidacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = liquidacaoIdSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id } = parsedParams.data;

  try {
    const acessosPromise = listarIdsDotacoesUsuarioConectadoTemAcesso();
    const liquidacaoPromise = prisma.liquidacoes.findUnique({
      where: { id },
      include: {
        empenho: {
          include: {
            reserva: {
              select: {
                dotacao: {
                  select: {
                    id: true,
                    despesa: true,
                    desc: true,
                  },
                },
              },
            },
            fornecedor: true,
          },
        },
        liquidacoes_documentos: {
          where: { ativo: true },
        },
        liquidacoes_itens: true,
      },
    });

    const [acessos, liquidacao] = await Promise.all([
      acessosPromise,
      liquidacaoPromise,
    ]);

    if (acessos.error) {
      return {
        error: acessos.error,
      };
    }

    if (!acessos.data) {
      return {
        error: 'Não foi possível obter acessos do usuário.',
      };
    }

    if (!liquidacao) {
      return {
        error: 'Liquidação não encontrada.',
      };
    }

    if (!resultPermissao.gerente && !acessos.data.acessoTotal) {
      const temAcesso = acessos.data.dotacoes?.includes(
        liquidacao.empenho.idDotacao
      );
      if (!temAcesso) {
        return {
          error: 'Usuário não pode acessar essa liquidação.',
        };
      }
    }

    return {
      data: {
        ...liquidacao,
        valorTotal: liquidacao.valorTotal.toNumber(),
        liquidacoes_documentos: liquidacao.liquidacoes_documentos.map(
          (doc) => ({
            ...doc,
            valorDocumento: doc.valorDocumento.toNumber(),
          })
        ),
        liquidacoes_itens: liquidacao.liquidacoes_itens
          ? {
              ...liquidacao.liquidacoes_itens,
              empenhoJan: liquidacao.liquidacoes_itens.empenhoJan.toNumber(),
              empenhoFev: liquidacao.liquidacoes_itens.empenhoFev.toNumber(),
              empenhoMar: liquidacao.liquidacoes_itens.empenhoMar.toNumber(),
              empenhoAbr: liquidacao.liquidacoes_itens.empenhoAbr.toNumber(),
              empenhoMai: liquidacao.liquidacoes_itens.empenhoMai.toNumber(),
              empenhoJun: liquidacao.liquidacoes_itens.empenhoJun.toNumber(),
              empenhoJul: liquidacao.liquidacoes_itens.empenhoJul.toNumber(),
              empenhoAgo: liquidacao.liquidacoes_itens.empenhoAgo.toNumber(),
              empenhoSet: liquidacao.liquidacoes_itens.empenhoSet.toNumber(),
              empenhoOut: liquidacao.liquidacoes_itens.empenhoOut.toNumber(),
              empenhoNov: liquidacao.liquidacoes_itens.empenhoNov.toNumber(),
              empenhoDez: liquidacao.liquidacoes_itens.empenhoDez.toNumber(),
              usarJan: liquidacao.liquidacoes_itens.usarJan.toNumber(),
              usarFev: liquidacao.liquidacoes_itens.usarFev.toNumber(),
              usarMar: liquidacao.liquidacoes_itens.usarMar.toNumber(),
              usarAbr: liquidacao.liquidacoes_itens.usarAbr.toNumber(),
              usarMai: liquidacao.liquidacoes_itens.usarMai.toNumber(),
              usarJun: liquidacao.liquidacoes_itens.usarJun.toNumber(),
              usarJul: liquidacao.liquidacoes_itens.usarJul.toNumber(),
              usarAgo: liquidacao.liquidacoes_itens.usarAgo.toNumber(),
              usarSet: liquidacao.liquidacoes_itens.usarSet.toNumber(),
              usarOut: liquidacao.liquidacoes_itens.usarOut.toNumber(),
              usarNov: liquidacao.liquidacoes_itens.usarNov.toNumber(),
              usarDez: liquidacao.liquidacoes_itens.usarDez.toNumber(),
            }
          : null,
        empenho: {
          ...liquidacao.empenho,
          usarMes1: liquidacao.empenho.usarMes1.toNumber(),
          usarMes2: liquidacao.empenho.usarMes2.toNumber(),
          usarMes3: liquidacao.empenho.usarMes3.toNumber(),
          usarMes4: liquidacao.empenho.usarMes4.toNumber(),
          usarMes5: liquidacao.empenho.usarMes5.toNumber(),
          usarMes6: liquidacao.empenho.usarMes6.toNumber(),
          usarMes7: liquidacao.empenho.usarMes7.toNumber(),
          usarMes8: liquidacao.empenho.usarMes8.toNumber(),
          usarMes9: liquidacao.empenho.usarMes9.toNumber(),
          usarMes10: liquidacao.empenho.usarMes10.toNumber(),
          usarMes11: liquidacao.empenho.usarMes11.toNumber(),
          usarMes12: liquidacao.empenho.usarMes12.toNumber(),
          valorTotal: liquidacao.empenho.valorTotal.toNumber(),
        },
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter liquidação.`,
    };
  }
};

export const criarLiquidacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  // Try initial schema first (without documents)
  const parsedInitialParams = criarLiquidacaoInicialSchema.safeParse(params);

  let idEmpenho: number;
  let resumo: string | undefined;
  let obs: string | undefined;
  let mesReferencia: string | undefined;
  let dataMaterialServico: Date | undefined;
  let documentos: any[] = [];
  let audespDataInicio: Date | undefined;
  let audespRegimeExecucao: number | undefined;
  let audespCorrenteImportacao: number | undefined;
  let audespExecucaoContrato: number | undefined;
  let distribuicaoMensal: any;

  if (parsedInitialParams.success) {
    // Creating liquidação without documents (initial creation)
    const data = parsedInitialParams.data;
    idEmpenho = data.idEmpenho;
    resumo = data.resumo;
    obs = data.obs;
    mesReferencia = data.mesReferencia;
    dataMaterialServico = data.dataMaterialServico;
    audespDataInicio = data.audespDataInicio;
    audespRegimeExecucao = data.audespRegimeExecucao;
    audespCorrenteImportacao = data.audespCorrenteImportacao;
    audespExecucaoContrato = data.audespExecucaoContrato;
    distribuicaoMensal = data.distribuicaoMensal;
    documentos = []; // Empty for initial creation
  } else {
    // Try full schema (with documents)
    const parsedParams = criarLiquidacaoSchema.safeParse(params);

    if (!parsedParams.success) {
      return {
        error: 'Parâmetros inválidos.',
      };
    }

    const data = parsedParams.data;
    idEmpenho = data.idEmpenho;
    resumo = data.resumo;
    obs = data.obs;
    mesReferencia = data.mesReferencia;
    dataMaterialServico = data.dataMaterialServico;
    documentos = data.documentos;
    audespDataInicio = data.audespDataInicio;
    audespRegimeExecucao = data.audespRegimeExecucao;
    audespCorrenteImportacao = data.audespCorrenteImportacao;
    audespExecucaoContrato = data.audespExecucaoContrato;
    distribuicaoMensal = data.distribuicaoMensal;

    if (!documentos || !documentos.length) {
      return {
        error: 'Liquidação deve ter pelo menos um documento.',
      };
    }
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Calculate total value from documents
    const valorTotalDocumentos = documentos.reduce(
      (total, doc) =>
        currency(total, currencyOptionsNoSymbol).add(doc.valorDocumento).value,
      0
    );

    // Only validate document value if documents are provided
    if (documentos.length > 0 && valorTotalDocumentos <= 0) {
      return {
        error: 'Valor total dos documentos deve ser maior que zero.',
      };
    }

    // Check for duplicate documents across empenhos
    if (documentos.length > 0) {
      const documentosDuplicados = await verificarDocumentosDuplicados(
        documentos,
        idEmpenho
      );
      if (documentosDuplicados.length > 0) {
        const mensagemDuplicados = documentosDuplicados
          .map(
            (doc) =>
              `Documento "${doc}" já utilizado em outra liquidação do mesmo empenho`
          )
          .join('; ');
        return {
          error: `Documentos duplicados encontrados: ${mensagemDuplicados}`,
        };
      }
    }

    let idLiquidacao = 0;

    await prisma.$transaction(async (tx) => {
      // Validate empenho
      const empenho = await tx.empenhos.findUnique({
        where: { id: idEmpenho },
        include: {
          reserva: {
            include: {
              dotacao: true,
            },
          },
          fornecedor: true,
        },
      });

      if (!empenho) {
        throw new Error(
          ErrosLiquidacao[ErrosLiquidacao.EMPENHO_NAO_ENCONTRADO]
        );
      }

      if (empenho.status !== StatusEmpenho.EMPENHADO) {
        throw new Error(ErrosLiquidacao[ErrosLiquidacao.EMPENHO_NAO_EMPENHADO]);
      }

      if (!empenho.ativo) {
        throw new Error(ErrosLiquidacao[ErrosLiquidacao.EMPENHO_CANCELADO]);
      }

      // Calculate empenho balance (total - already liquidated)
      const liquidacoesExistentes = await tx.liquidacoes.aggregate({
        where: {
          idEmpenho,
          status: StatusLiquidacao.LIQUIDADA,
          ativo: true,
        },
        _sum: {
          valorTotal: true,
        },
      });

      const jaLiquidado =
        liquidacoesExistentes._sum.valorTotal?.toNumber() || 0;
      const saldoEmpenho = toCurrency(empenho.valorTotal).subtract(jaLiquidado);

      // Only validate saldo if documents are provided
      if (documentos.length > 0 && valorTotalDocumentos > saldoEmpenho.value) {
        throw new Error(
          ErrosLiquidacao[ErrosLiquidacao.VALOR_MAIOR_SALDO_EMPENHO]
        );
      }

      // Generate sequential number (global per exercicio like protocolos)
      const ultimaLiquidacao = await tx.liquidacoes.findFirst({
        where: { exercicio: resultPermissao.exercicio! },
        orderBy: { numero: 'desc' },
        select: { numero: true },
      });

      const numeroLiquidacao = (ultimaLiquidacao?.numero || 0) + 1;

      // Create liquidacao
      const liquidacao = await tx.liquidacoes.create({
        data: {
          exercicio: resultPermissao.exercicio!,
          numero: numeroLiquidacao,
          idEmpenho,
          valorTotal: valorTotalDocumentos,
          status: StatusLiquidacao.LIQUIDADA,
          resumo,
          obs,
          mesReferencia,
          dataMaterialServico,
          audespDataInicio,
          audespRegimeExecucao,
          audespCorrenteImportacao,
          audespExecucaoContrato,
        },
      });

      // Create document records (only if documents are provided)
      if (documentos && documentos.length > 0) {
        await tx.liquidacoes_documentos.createMany({
          data: documentos.map((doc) => ({
            idLiquidacao: liquidacao.id,
            numeroDocumento: doc.numeroDocumento,
            dataEmissao: doc.dataEmissao,
            dataRecebimento: doc.dataRecebimento,
            dataMaterialServico: doc.dataMaterialServico,
            valorDocumento: doc.valorDocumento,
            // File fields will be updated when files are uploaded
            nomeArquivo: null,
            caminhoArquivo: null,
            tamanho: null,
          })),
        });
      }

      // Create monthly distribution record (optional, for AUDESP)
      if (distribuicaoMensal) {
        await tx.liquidacoes_itens.create({
          data: {
            idLiquidacao: liquidacao.id,
            // Copy empenho monthly quotas for reference
            empenhoJan: empenho.usarMes1,
            empenhoFev: empenho.usarMes2,
            empenhoMar: empenho.usarMes3,
            empenhoAbr: empenho.usarMes4,
            empenhoMai: empenho.usarMes5,
            empenhoJun: empenho.usarMes6,
            empenhoJul: empenho.usarMes7,
            empenhoAgo: empenho.usarMes8,
            empenhoSet: empenho.usarMes9,
            empenhoOut: empenho.usarMes10,
            empenhoNov: empenho.usarMes11,
            empenhoDez: empenho.usarMes12,
            // Set liquidation distribution
            usarJan: distribuicaoMensal.usarJan || 0,
            usarFev: distribuicaoMensal.usarFev || 0,
            usarMar: distribuicaoMensal.usarMar || 0,
            usarAbr: distribuicaoMensal.usarAbr || 0,
            usarMai: distribuicaoMensal.usarMai || 0,
            usarJun: distribuicaoMensal.usarJun || 0,
            usarJul: distribuicaoMensal.usarJul || 0,
            usarAgo: distribuicaoMensal.usarAgo || 0,
            usarSet: distribuicaoMensal.usarSet || 0,
            usarOut: distribuicaoMensal.usarOut || 0,
            usarNov: distribuicaoMensal.usarNov || 0,
            usarDez: distribuicaoMensal.usarDez || 0,
          },
        });
      }

      // Update empenho status to LIQUIDADO if fully liquidated (only when documents are provided)
      if (documentos && documentos.length > 0) {
        const novoSaldoEmpenho = saldoEmpenho.subtract(valorTotalDocumentos);
        if (novoSaldoEmpenho.value <= 0.01) {
          // Consider floating point precision
          await tx.empenhos.update({
            where: { id: idEmpenho },
            data: { status: StatusEmpenho.LIQUIDADO },
          });
        }
      }

      // Create audit record
      await tx.liquidacoes_audit.create({
        data: {
          idLiquidacao: liquidacao.id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoLiquidacoes.CRIAR_LIQUIDACAO,
          ip,
          obs: `Liquidação criada com ${documentos.length} documento(s)`,
        },
      });

      idLiquidacao = liquidacao.id;
    });

    revalidatePath(ROUTE);
    return {
      data: {
        idLiquidacao,
      },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (
        Object.values(ErrosLiquidacao).includes(
          e.message as unknown as ErrosLiquidacao
        )
      ) {
        return {
          error: e.message,
        };
      }
    }
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Liquidação já existe.`,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar liquidação.`,
    };
  }
};

export const editarLiquidacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = editarLiquidacaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    id,
    resumo,
    obs,
    mesReferencia,
    dataMaterialServico,
    documentos,
    audespDataInicio,
    audespRegimeExecucao,
    audespCorrenteImportacao,
    audespExecucaoContrato,
    distribuicaoMensal,
  } = parsedParams.data;

  if (!documentos || !documentos.length) {
    return {
      error: 'Liquidação deve ter pelo menos um documento.',
    };
  }

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    // Calculate total value from documents
    const valorTotalDocumentos = documentos.reduce(
      (total, doc) =>
        currency(total, currencyOptionsNoSymbol).add(doc.valorDocumento).value,
      0
    );

    if (valorTotalDocumentos <= 0) {
      return {
        error: 'Valor total dos documentos deve ser maior que zero.',
      };
    }

    await prisma.$transaction(async (tx) => {
      // Get existing liquidacao
      const liquidacaoExistente = await tx.liquidacoes.findUnique({
        where: { id },
        include: {
          empenho: true,
          liquidacoes_documentos: { where: { ativo: true } },
        },
      });

      if (!liquidacaoExistente) {
        throw new Error(
          ErrosLiquidacao[ErrosLiquidacao.LIQUIDACAO_NAO_ENCONTRADA]
        );
      }

      if (liquidacaoExistente.status === StatusLiquidacao.ESTORNADA) {
        throw new Error(
          ErrosLiquidacao[ErrosLiquidacao.LIQUIDACAO_JA_ESTORNADA]
        );
      }

      // Calculate empenho balance considering the change
      const liquidacoesExistentes = await tx.liquidacoes.aggregate({
        where: {
          idEmpenho: liquidacaoExistente.idEmpenho,
          status: StatusLiquidacao.LIQUIDADA,
          ativo: true,
          id: { not: id }, // Exclude current liquidacao
        },
        _sum: {
          valorTotal: true,
        },
      });

      const jaLiquidado =
        liquidacoesExistentes._sum.valorTotal?.toNumber() || 0;
      const saldoEmpenho = toCurrency(
        liquidacaoExistente.empenho.valorTotal
      ).subtract(jaLiquidado);

      if (valorTotalDocumentos > saldoEmpenho.value) {
        throw new Error(
          ErrosLiquidacao[ErrosLiquidacao.VALOR_MAIOR_SALDO_EMPENHO]
        );
      }

      // Update liquidacao
      await tx.liquidacoes.update({
        where: { id },
        data: {
          valorTotal: valorTotalDocumentos,
          resumo,
          obs,
          mesReferencia,
          dataMaterialServico,
          audespDataInicio,
          audespRegimeExecucao,
          audespCorrenteImportacao,
          audespExecucaoContrato,
        },
      });

      // Deactivate existing documents
      await tx.liquidacoes_documentos.updateMany({
        where: { idLiquidacao: id },
        data: { ativo: false },
      });

      // Create new document records
      await tx.liquidacoes_documentos.createMany({
        data: documentos.map((doc) => ({
          idLiquidacao: id,
          numeroDocumento: doc.numeroDocumento,
          dataEmissao: doc.dataEmissao,
          dataRecebimento: doc.dataRecebimento,
          dataMaterialServico: doc.dataMaterialServico,
          valorDocumento: doc.valorDocumento,
          nomeArquivo: doc.nomeArquivo,
          caminhoArquivo: doc.caminhoArquivo,
          tamanho: doc.tamanho,
        })),
      });

      // Update monthly distribution record (optional, for AUDESP)
      if (distribuicaoMensal) {
        await tx.liquidacoes_itens.upsert({
          where: { idLiquidacao: id },
          create: {
            idLiquidacao: id,
            // Copy empenho monthly quotas for reference
            empenhoJan: liquidacaoExistente.empenho.usarMes1,
            empenhoFev: liquidacaoExistente.empenho.usarMes2,
            empenhoMar: liquidacaoExistente.empenho.usarMes3,
            empenhoAbr: liquidacaoExistente.empenho.usarMes4,
            empenhoMai: liquidacaoExistente.empenho.usarMes5,
            empenhoJun: liquidacaoExistente.empenho.usarMes6,
            empenhoJul: liquidacaoExistente.empenho.usarMes7,
            empenhoAgo: liquidacaoExistente.empenho.usarMes8,
            empenhoSet: liquidacaoExistente.empenho.usarMes9,
            empenhoOut: liquidacaoExistente.empenho.usarMes10,
            empenhoNov: liquidacaoExistente.empenho.usarMes11,
            empenhoDez: liquidacaoExistente.empenho.usarMes12,
            // Set liquidation distribution
            usarJan: distribuicaoMensal.usarJan || 0,
            usarFev: distribuicaoMensal.usarFev || 0,
            usarMar: distribuicaoMensal.usarMar || 0,
            usarAbr: distribuicaoMensal.usarAbr || 0,
            usarMai: distribuicaoMensal.usarMai || 0,
            usarJun: distribuicaoMensal.usarJun || 0,
            usarJul: distribuicaoMensal.usarJul || 0,
            usarAgo: distribuicaoMensal.usarAgo || 0,
            usarSet: distribuicaoMensal.usarSet || 0,
            usarOut: distribuicaoMensal.usarOut || 0,
            usarNov: distribuicaoMensal.usarNov || 0,
            usarDez: distribuicaoMensal.usarDez || 0,
          },
          update: {
            // Update liquidation distribution
            usarJan: distribuicaoMensal.usarJan || 0,
            usarFev: distribuicaoMensal.usarFev || 0,
            usarMar: distribuicaoMensal.usarMar || 0,
            usarAbr: distribuicaoMensal.usarAbr || 0,
            usarMai: distribuicaoMensal.usarMai || 0,
            usarJun: distribuicaoMensal.usarJun || 0,
            usarJul: distribuicaoMensal.usarJul || 0,
            usarAgo: distribuicaoMensal.usarAgo || 0,
            usarSet: distribuicaoMensal.usarSet || 0,
            usarOut: distribuicaoMensal.usarOut || 0,
            usarNov: distribuicaoMensal.usarNov || 0,
            usarDez: distribuicaoMensal.usarDez || 0,
          },
        });
      }

      // Create audit record
      await tx.liquidacoes_audit.create({
        data: {
          idLiquidacao: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoLiquidacoes.EDITAR_LIQUIDACAO,
          ip,
          obs: `Liquidação editada com ${documentos.length} documento(s)`,
        },
      });
    });

    revalidatePath(ROUTE);
    return {
      data: {
        idLiquidacao: id,
      },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosLiquidacao).includes(e.message as any)) {
        return {
          error: e.message,
        };
      }
    }
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Erro de duplicação.`,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao editar liquidação.`,
    };
  }
};

export const estornarLiquidacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = estornarLiquidacaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id, motivo } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      // Get existing liquidacao
      const liquidacao = await tx.liquidacoes.findUnique({
        where: { id },
        include: {
          empenho: true,
        },
      });

      if (!liquidacao) {
        throw new Error(
          ErrosLiquidacao[ErrosLiquidacao.LIQUIDACAO_NAO_ENCONTRADA]
        );
      }

      if (liquidacao.status === StatusLiquidacao.ESTORNADA) {
        throw new Error(
          ErrosLiquidacao[ErrosLiquidacao.LIQUIDACAO_JA_ESTORNADA]
        );
      }

      // Update liquidacao status to ESTORNADA
      await tx.liquidacoes.update({
        where: { id },
        data: {
          status: StatusLiquidacao.ESTORNADA,
        },
      });

      // Check if empenho should be updated back to EMPENHADO
      const liquidacoesAtivas = await tx.liquidacoes.count({
        where: {
          idEmpenho: liquidacao.idEmpenho,
          status: StatusLiquidacao.LIQUIDADA,
          ativo: true,
          id: { not: id }, // Exclude current liquidacao
        },
      });

      // If no more active liquidacoes, update empenho back to EMPENHADO
      if (liquidacoesAtivas === 0) {
        await tx.empenhos.update({
          where: { id: liquidacao.idEmpenho },
          data: { status: StatusEmpenho.EMPENHADO },
        });
      }

      // Create audit record
      await tx.liquidacoes_audit.create({
        data: {
          idLiquidacao: id,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoLiquidacoes.ESTORNAR_LIQUIDACAO,
          ip,
          obs: `Liquidação estornada. Motivo: ${motivo}`,
        },
      });
    });

    revalidatePath(ROUTE);
    return {
      data: {
        idLiquidacao: id,
      },
    };
  } catch (e) {
    if (e instanceof Error) {
      if (Object.values(ErrosLiquidacao).includes(e.message as any)) {
        return {
          error: e.message,
        };
      }
    }
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Erro de duplicação.`,
        };
      }
    }
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao estornar liquidação.`,
    };
  }
};

// Helper function to get empenho balance for liquidation
export const listarEmpenhosParaLiquidacao = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    let acessos;
    if (!resultPermissao.gerente) {
      acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

      if (acessos.error) {
        return {
          error: acessos.error,
        };
      }

      if (!acessos.data) {
        return {
          error: 'Não foi possível obter acessos do usuário.',
        };
      }
    }

    let where: Prisma.empenhosWhereInput = {
      ativo: true,
      exercicio: resultPermissao.exercicio,
      status: {
        in: [StatusEmpenho.EMPENHADO, StatusEmpenho.LIQUIDADO],
      },
    };

    if (!resultPermissao.gerente && acessos?.data?.dotacoes) {
      where.idDotacao = { in: acessos.data.dotacoes };
    }

    const empenhos = await prisma.empenhos.findMany({
      include: {
        reserva: {
          include: {
            dotacao: {
              select: {
                despesa: true,
                desc: true,
              },
            },
          },
        },
        fornecedor: {
          select: {
            id: true,
            nome: true,
            cnpjCpf: true,
          },
        },
      },
      where,
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
    });

    return {
      data: {
        empenhos: empenhos.map((empenho) => ({
          id: empenho.id,
          numero: empenho.numero,
          exercicio: empenho.exercicio,
          valorTotal: empenho.valorTotal.toNumber(),
          resumo: empenho.resumo,
          status: empenho.status,
          fornecedor: empenho.fornecedor,
          despesa: empenho.reserva?.dotacao.despesa || '',
          descricaoDespesa: empenho.reserva?.dotacao.desc || '',
        })),
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter empenhos para liquidação.`,
    };
  }
};

export const consultarSaldoEmpenhoParaLiquidacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams =
    consultarSaldoEmpenhoParaLiquidacaoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const { id: idEmpenho } = parsedParams.data;

  try {
    // Get empenho data
    const empenho = await prisma.empenhos.findUnique({
      where: { id: idEmpenho },
      include: {
        reserva: {
          include: {
            dotacao: {
              select: {
                despesa: true,
                desc: true,
              },
            },
          },
        },
        fornecedor: {
          select: {
            id: true,
            nome: true,
            cnpjCpf: true,
          },
        },
      },
    });

    if (!empenho) {
      return {
        error: 'Empenho não encontrado.',
      };
    }

    if (
      empenho.status !== StatusEmpenho.EMPENHADO &&
      empenho.status !== StatusEmpenho.LIQUIDADO
    ) {
      return {
        error: 'Empenho não está disponível para liquidação.',
      };
    }

    if (!empenho.ativo) {
      return {
        error: 'Empenho cancelado.',
      };
    }

    // Calculate already liquidated amount
    const liquidacoesExistentes = await prisma.liquidacoes.aggregate({
      where: {
        idEmpenho,
        status: StatusLiquidacao.LIQUIDADA,
        ativo: true,
      },
      _sum: {
        valorTotal: true,
      },
    });

    const jaLiquidado = liquidacoesExistentes._sum.valorTotal?.toNumber() || 0;
    const saldoDisponivel = toCurrency(empenho.valorTotal).subtract(
      jaLiquidado
    );

    return {
      data: {
        empenho: {
          id: empenho.id,
          numero: empenho.numero,
          exercicio: empenho.exercicio,
          valorTotal: empenho.valorTotal.toNumber(),
          resumo: empenho.resumo,
          obs: empenho.obs,
          status: empenho.status,
          despesa: empenho.reserva?.dotacao.despesa,
          descricaoDespesa: empenho.reserva?.dotacao.desc,
          fornecedor: empenho.fornecedor,
        },
        saldo: {
          valorTotal: empenho.valorTotal.toNumber(),
          jaLiquidado,
          saldoDisponivel: saldoDisponivel.value,
        },
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao consultar saldo do empenho.`,
    };
  }
};

export const uploadDocumentoLiquidacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);
  if (resultPermissao.error) return resultPermissao;

  const result = uploadDocumentoLiquidacaoSchema.safeParse(params);
  if (!result.success) {
    return { error: result.error.errors[0].message };
  }

  const {
    idLiquidacao,
    numeroDocumento,
    nomeArquivo,
    tamanho,
    caminhoArquivo,
    valorDocumento,
    dataEmissao,
    dataRecebimento,
    dataMaterialServico,
  } = result.data;

  try {
    // Verificar se a liquidação existe e está ativa
    const liquidacao = await prisma.liquidacoes.findFirst({
      where: { id: idLiquidacao, ativo: true },
    });

    if (!liquidacao) {
      return { error: 'Liquidação não encontrada.' };
    }

    if (liquidacao.status === StatusLiquidacao.ESTORNADA) {
      return {
        error:
          'Não é possível adicionar documentos a uma liquidação estornada.',
      };
    }

    // Check for duplicate document across empenhos
    const documentosDuplicados = await verificarDocumentosDuplicados(
      [numeroDocumento],
      liquidacao.idEmpenho
    );
    if (documentosDuplicados.length > 0) {
      return {
        error: `Documento "${numeroDocumento}" já utilizado em outra liquidação do mesmo empenho`,
      };
    }

    const ip = (await obterIpUsuarioConectado()).data;

    const documento = await prisma.$transaction(async (tx) => {
      // Criar registro do documento
      const novoDocumento = await tx.liquidacoes_documentos.create({
        data: {
          idLiquidacao,
          numeroDocumento,
          nomeArquivo,
          caminhoArquivo,
          tamanho,
          valorDocumento,
          dataEmissao,
          dataRecebimento,
          dataMaterialServico,
        },
      });

      // Registrar auditoria
      await tx.liquidacoes_audit.create({
        data: {
          idLiquidacao,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoLiquidacoes.CRIAR_LIQUIDACAO,
          ip,
          obs: `Upload de documento: ${nomeArquivo} (${numeroDocumento})`,
        },
      });

      return novoDocumento;
    });

    return { data: documento };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao fazer upload do documento.' };
  }
};

export const removerDocumentoLiquidacao = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);
  if (resultPermissao.error)
    return {
      error: resultPermissao.error,
    };

  const result = removerDocumentoLiquidacaoSchema.safeParse(params);
  if (!result.success) {
    return { error: result.error.errors[0].message };
  }

  const { id } = result.data;

  try {
    const documento = await prisma.liquidacoes_documentos.findFirst({
      where: { id, ativo: true },
      include: { liquidacao: true },
    });

    if (!documento) {
      return { error: 'Documento não encontrado.' };
    }

    if (documento.liquidacao.status === StatusLiquidacao.ESTORNADA) {
      return {
        error: 'Não é possível remover documentos de uma liquidação estornada.',
      };
    }

    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      // Marcar documento como inativo
      await tx.liquidacoes_documentos.update({
        where: { id },
        data: { ativo: false },
      });

      // Registrar auditoria
      await tx.liquidacoes_audit.create({
        data: {
          idLiquidacao: documento.idLiquidacao,
          idUsuario: resultPermissao.idUsuario!,
          acao: AuditoriaMovimentoLiquidacoes.EDITAR_LIQUIDACAO,
          ip,
          obs: `Remoção de documento: ${documento.nomeArquivo} (${documento.numeroDocumento})`,
        },
      });
    });

    return { data: true };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao remover documento.' };
  }
};

export const obterUrlDocumentoLiquidacao = async (idDocumento: number) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: MODULE,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);
  if (resultPermissao.error)
    return {
      error: resultPermissao.error,
    };

  try {
    const documento = await prisma.liquidacoes_documentos.findFirst({
      where: { id: idDocumento, ativo: true },
    });

    if (!documento) {
      return { error: 'Documento não encontrado.' };
    }

    if (!documento.caminhoArquivo) {
      return { error: 'Caminho do arquivo do documento não encontrado.' };
    }

    // Gerar URL assinada temporária (válida por 1 hora)
    const supabase = createSuperClient();
    const { data, error } = await supabase.storage
      .from('reservas')
      .createSignedUrl(documento.caminhoArquivo, 3600);

    if (error) {
      return { error: 'Erro ao gerar URL do documento.' };
    }

    return {
      data: { url: data.signedUrl, nomeArquivo: documento.nomeArquivo },
      error: null,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: MODULE,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao obter URL do documento.' };
  }
};

// Helper function to check for duplicate documents across empenhos
async function verificarDocumentosDuplicados(
  numerosDocumentos: string[] | { numeroDocumento: string }[],
  idEmpenho: number
): Promise<string[]> {
  const numeros = Array.isArray(numerosDocumentos)
    ? typeof numerosDocumentos[0] === 'string'
      ? (numerosDocumentos as string[])
      : numerosDocumentos.map((doc: any) => doc.numeroDocumento)
    : [];

  if (numeros.length === 0) return [];

  // Find existing documents with same numbers in active liquidações of the same empenho
  const documentosExistentes = await prisma.liquidacoes_documentos.findMany({
    where: {
      numeroDocumento: { in: numeros },
      liquidacao: {
        idEmpenho: idEmpenho,
        status: { not: StatusLiquidacao.ESTORNADA },
        ativo: true,
      },
      ativo: true,
    },
    include: {
      liquidacao: true,
    },
  });

  // Return unique document numbers that already exist
  const numerosDuplicados = new Set(
    documentosExistentes.map((doc) => doc.numeroDocumento)
  );
  return Array.from(numerosDuplicados);
}

// Helper function to calculate document duplication status for reporting
export async function calcularStatusDocumentosDuplicados(
  idLiquidacao: number
): Promise<{ numeroDocumento: string; ehDuplicado: boolean }[]> {
  const liquidacao = await prisma.liquidacoes.findUnique({
    where: { id: idLiquidacao },
    include: {
      liquidacoes_documentos: true,
      empenho: {
        include: {
          liquidacoes: {
            include: {
              liquidacoes_documentos: true,
            },
            where: {
              status: { not: StatusLiquidacao.ESTORNADA },
              ativo: true,
              id: { not: idLiquidacao },
            },
          },
        },
      },
    },
  });

  if (!liquidacao) return [];

  // Collect all document numbers from other liquidações of the same empenho
  const todosDocumentosOutrasLiquidacoes =
    liquidacao.empenho.liquidacoes.flatMap((l) =>
      l.liquidacoes_documentos.map((doc) => doc.numeroDocumento)
    );

  return liquidacao.liquidacoes_documentos.map((doc) => ({
    numeroDocumento: doc.numeroDocumento,
    ehDuplicado: todosDocumentosOutrasLiquidacoes.includes(doc.numeroDocumento),
  }));
}

// Função para busca de liquidações por fornecedor e contrato - baseado no sistema legado
export const listarDocumentosPorFornecedorContrato = async (params: {
  numerosDocumentos: string[];
  idFornecedor: number;
  idContrato: number;
  empenhoExcluir?: number;
  exercicioExcluir?: number;
}) => {
  try {
    // Obter todos os empenhos do contrato
    const empenhosContrato = await prisma.empenhos.findMany({
      where: {
        idContrato: params.idContrato,
        ativo: true,
      },
      select: {
        id: true,
        numero: true,
        exercicio: true,
      },
    });

    // Buscar liquidações em todos os empenhos do contrato
    const liquidacoes = await prisma.liquidacoes.findMany({
      where: {
        AND: [
          { ativo: true },
          { empenho: { fornecedor: { id: params.idFornecedor } } },
          { empenho: { id: { in: empenhosContrato.map((e) => e.id) } } },
          {
            OR: [
              // Buscar por marca_documento (campos do sistema legado)
              { marca_documento: { in: params.numerosDocumentos } },
              // Ou buscar nos documentos relacionados
              {
                liquidacoes_documentos: {
                  some: {
                    numeroDocumento: { in: params.numerosDocumentos },
                    ativo: true,
                  },
                },
              },
            ],
          },
          // Excluir empenho atual se especificado
          ...(params.empenhoExcluir && params.exercicioExcluir
            ? [
                {
                  NOT: {
                    empenho: {
                      numero: params.empenhoExcluir,
                      exercicio: params.exercicioExcluir,
                    },
                  },
                },
              ]
            : []),
        ],
      },
      include: {
        empenho: {
          select: {
            numero: true,
            exercicio: true,
          },
        },
        liquidacoes_documentos: {
          where: { ativo: true },
          select: {
            numeroDocumento: true,
            dataEmissao: true,
            valorDocumento: true,
          },
        },
      },
      orderBy: {
        data: 'asc',
      },
    });

    return { data: liquidacoes };
  } catch (e) {
    console.error('Erro ao listar documentos por fornecedor/contrato:', e);
    return { error: 'Erro ao listar documentos por fornecedor/contrato.' };
  }
};
