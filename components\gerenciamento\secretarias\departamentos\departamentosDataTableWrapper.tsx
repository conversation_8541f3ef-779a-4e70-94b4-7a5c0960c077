import { ErrorAlert } from '@/components/error-alert';
import DepartamentosDatatable from './departamentosDataTable';
import { listarDepartamentos } from '@/lib/database/gerenciamento/departamentos';
import { obterSecretaria } from '@/lib/database/gerenciamento/secretarias';

export default async function DepartamentosDatatableWrapper({
  idSecretaria,
}: {
  idSecretaria: number;
}) {
  const departamentosData = listarDepartamentos({
    id: idSecretaria,
  });

  const secretariaData = obterSecretaria({
    id: idSecretaria,
  });

  const [departamentos, secretaria] = await Promise.all([
    departamentosData,
    secretariaData,
  ]);

  if (departamentos.error) return <ErrorAlert error={departamentos.error} />;
  if (!departamentos.data)
    return <ErrorAlert error={'Departamentos não encontrados.'} />;

  if (secretaria.error) return <ErrorAlert error={secretaria.error} />;
  if (!secretaria.data)
    return <ErrorAlert error={'Secretaria não encontrada.'} />;

  return (
    <DepartamentosDatatable data={departamentos} secretaria={secretaria} />
  );
}
