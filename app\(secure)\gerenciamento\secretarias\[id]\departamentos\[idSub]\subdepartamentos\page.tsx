import { DialogNovoSubdepartamento } from '@/components/gerenciamento/secretarias/departamentos/subdepartamentos/dialogNovoSubdepartamento';
import SubdepartamentosDatatableWrapper from '@/components/gerenciamento/secretarias/departamentos/subdepartamentos/subdepartamentosDataTableWrapper';
import PageContent from '@/components/pages/pageContent';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageWrapper from '@/components/pages/pageWrapper';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Suspense } from 'react';
import * as React from 'react';

export default function GerenciamentoDeSubdepartamentosPage({
  params,
}: {
  params: Promise<{ id: string; idSub: string }>;
}) {
  const { id, idSub } = React.use(params);
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Gerenciamento de Subdepartamentos</PageTitle>
      </PageHeader>
      <PageContent>
        <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
          <div className='mb-4 flex w-full flex-wrap justify-between gap-4 px-5'>
            <Link href={`/gerenciamento/secretarias/${id}/departamentos`}>
              <Button variant={'secondary'}>
                <ArrowLeft className='mr-2 h-4 w-4' /> Departamentos
              </Button>
            </Link>
            <DialogNovoSubdepartamento
              idSecretaria={Number(id)}
              idDepartamento={Number(idSub)}
            />
          </div>
          <SubdepartamentosDatatableWrapper
            idSecretaria={Number(id)}
            idDepartamento={Number(idSub)}
          />
        </Suspense>
      </PageContent>
    </PageWrapper>
  );
}
