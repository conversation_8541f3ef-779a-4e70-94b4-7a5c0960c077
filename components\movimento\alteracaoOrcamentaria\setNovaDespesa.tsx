import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  insereNovaDespesa,
  obterAlteracaoPorId,
} from '@/lib/database/movimento/alteracaoOrcamentaria';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { insereNovaDespesaSchema } from '@/lib/validation';
import { ListPlus, Save } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Icons } from '@/components/icons';

export function SetNovaDespesa({ idPedDesp }: { idPedDesp: number }) {
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof insereNovaDespesaSchema>>({
    resolver: zodResolver(insereNovaDespesaSchema),
    defaultValues: {
      id: idPedDesp,
      despesaNova: 0,
      descrDespNova: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof insereNovaDespesaSchema>) => {
    try {
      setLoading(true);

      const data: z.infer<typeof insereNovaDespesaSchema> = {
        ...values,
        id: idPedDesp,
      };

      const res = await insereNovaDespesa(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        form.setValue('despesaNova', 0);
        form.setValue('descrDespNova', '');
        toast.success('Nova despesa (Dotação) Incluida.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  const onInvalid = (errors: any) => {
    console.log(errors);
    toast.error(JSON.stringify(errors));
  };

  const getDescDespesa = async () => {
    const altOrc = await obterAlteracaoPorId({ id: idPedDesp });
    if (!altOrc) {
      toast.error('Não foi possivel obter a descrição da despesa');
    }
    form.setValue('descrDespNova', altOrc.data?.descrDespNova || '');
    form.setValue('despesaNova', altOrc.data?.despesaNova || 0);
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          onClick={() => {
            getDescDespesa();
          }}
          variant='outline'
        >
          <ListPlus className='mr-2 size-4' /> Despesa
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Nova Despesa</AlertDialogTitle>
          <AlertDialogDescription>
            Insira os dados para atualizar a nova despesa na alteração
            orçamentária.
          </AlertDialogDescription>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit, onInvalid)}>
              <div className='mt-4 flex flex-wrap justify-between gap-6'>
                <FormField
                  name='despesaNova'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='flex text-left'>
                        Despesa Nova
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          pattern='[0-99]*'
                          placeholder='Digite a Nova Despesa'
                          className='max-w-max text-left text-base'
                          onChange={(event) => {
                            const { value } = event.target;
                            form.setValue('despesaNova', Number(value));
                            // getDescDespesa()
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className='mt-4 flex flex-wrap justify-between gap-6'>
                <FormField
                  name='descrDespNova'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='flex text-left'>
                        Descrição Despesa Nova
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder='Digite a Descrição Nova Despesa'
                          className='max-w-max text-left text-base'
                          onChange={(event) => {
                            const { value } = event.target;
                            form.setValue('descrDespNova', value);
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <Button
            onClick={form.handleSubmit(onSubmit, onInvalid)}
            disabled={loading}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <Save className='mr-2 h-4 w-4' /> Salvar
              </>
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
