'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';
import { ReactNode } from 'react';

export interface DatatableAction {
  label: string;
  icon: ReactNode;
  onClick?: () => void;
  href?: string;
  variant?: 'default' | 'destructive';
  disabled?: boolean;
  loading?: boolean;
  show?: boolean;
  custom?: ReactNode;
}

interface DatatableActionsDropdownProps {
  actions: DatatableAction[];
  trigger?: ReactNode;
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
}

export function DatatableActionsDropdown({
  actions,
  trigger,
  align = 'end',
  side = 'bottom',
}: DatatableActionsDropdownProps) {
  const visibleActions = actions.filter((action) => action.show !== false);

  if (visibleActions.length === 0) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {trigger || (
          <Button
            variant='ghost'
            size='sm'
            className='h-8 w-8 p-0'
            aria-label='Mais ações'
          >
            <MoreHorizontal className='size-4' />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} side={side} className='w-48'>
        {visibleActions.map((action, index) => {
          if (action.custom) {
            return (
              <div key={index} className='px-2 py-1.5'>
                {action.custom}
              </div>
            );
          }

          const content = (
            <>
              {action.icon}
              <span>
                {action.loading ? `${action.label}...` : action.label}
              </span>
            </>
          );

          if (action.href) {
            return (
              <DropdownMenuItem
                key={index}
                asChild
                disabled={action.disabled || action.loading}
                data-variant={action.variant}
                className='gap-2'
              >
                <a href={action.href}>{content}</a>
              </DropdownMenuItem>
            );
          }

          return (
            <DropdownMenuItem
              key={index}
              onClick={action.onClick}
              disabled={action.disabled || action.loading}
              data-variant={action.variant}
              className='gap-2'
            >
              {content}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
