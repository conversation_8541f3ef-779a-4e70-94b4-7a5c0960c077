'use server';
import { z } from 'zod';
import { ErrorAlert } from '@/components/error-alert';
import { temPermissao } from '@/lib/database/usuarios';
import { Permissoes } from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import { isNumeric } from '@/lib/utils';
import { permissaoSchema } from '@/lib/validation';

export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  if (!isNumeric(id)) return <ErrorAlert error='ID inválido.' />;

  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS,
    permissao: Permissoes.ALTERAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  if (result.error) {
    return <ErrorAlert error={result.error} />;
  }

  if (!result.temPermissao) {
    return <ErrorAlert error='Usuário sem permissão para alterar.' />;
  }

  return <>{children}</>;
}
