'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { listarEconomicasAtivas } from '@/lib/database/movimento/alteracaoOrcamentaria';
// import { listarEconomicasAtivas } from '@/lib/database/gerenciamento/dotacoes';
// import { UseFormReturn } from 'react-hook-form';

export function ComboboxSelecionaEconomica({
  economicas,
  economicaId,
  setEconomicaId,
  habilitado = false,
  /*economicas,
  form,*/
}: {
  economicas: Awaited<ReturnType<typeof listarEconomicasAtivas>>;
  economicaId: number | null;
  setEconomicaId: React.Dispatch<React.SetStateAction<number | null>>;
  habilitado?: boolean;
  /*economicas: Awaited<ReturnType<typeof listarEconomicasAtivas>>;
  form: UseFormReturn<
    {
      codigo: number;
      exercicio: number;
      tipoAlteracao: number
      tipoAcao: number
      valorTotal?: number
      valorMes1?: number
      valorMes2?: number | undefined
      valorMes3?: number | undefined
      valorMes4?: number | undefined
      valorMes5?: number | undefined
      valorMes6?: number | undefined
      valorMes7?: number | undefined
      valorMes8?: number | undefined
      valorMes9?: number | undefined
      valorMes10?: number | undefined
      valorMes11?: number | undefined
      valorMes12?: number | undefined
      obs?: string | undefined
      fonte?: number | undefined
      codAplicacao?: number | undefined
      idSecretaria: number | undefined
      idDepto?: number | undefined
      idEconomica?: number | undefined
      idFuncional?: number | undefined
      despesaCopia?: number | undefined
      despesaAcao?: number | undefined
      despesaNova?: number | undefined
      descrDespNova?: string | undefined
    },
    any,
    undefined
  >;*/
}) {
  const [open, setOpen] = React.useState(false);

  const economicaSelecionada =
    economicas.data?.find((economica) => {
      return economicaId === economica.id;
      // return form.getValues('idEconomica') === economica.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
            disabled={habilitado}
          >
            {economicaSelecionada
              ? economicaSelecionada.codigo + ' - ' + economicaSelecionada.desc
              : 'Selecione a econômica...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar econômica...' />
            <CommandEmpty>Econômica não encontrada.</CommandEmpty>
            <CommandList>
              {economicas.data?.map((eco) => (
                <CommandItem
                  key={eco.id}
                  value={`${eco.codigo} - ${eco.desc}`}
                  onSelect={() => {
                    setEconomicaId(eco.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      economicaId === eco.id
                        ? // form.getValues('idEconomica') == eco.id
                          'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  {eco.codigo} - {eco.desc}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
