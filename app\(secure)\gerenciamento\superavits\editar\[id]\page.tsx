'use server';

import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { ErrorAlert } from '@/components/error-alert';
import EditarSuperavitForm from '@/components/gerenciamento/superavits/editarSuperavitForm';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { obterSuperavit } from '@/lib/database/gerenciamento/superavit';
import SuperavitsDetalhesData from '@/components/gerenciamento/superavits/SuperavitsDetalhesData';

export default async function EditarSuperavitPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const superavit = await obterSuperavit({
    id: Number(id),
  });

  if (superavit.error) {
    return <ErrorAlert error={superavit.error} />;
  }
  if (!superavit.data) {
    return <ErrorAlert error='Falha ao obter Superavit.' />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Alterar Superavit</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <EditarSuperavitForm superavit={superavit} />
          </Suspense>
        </div>
        <div className='flex-1 space-y-4 p-0 pt-6 md:p-8'>
          <h2 className='text-xl font-bold tracking-tight'>Detalhes</h2>
          <SuperavitsDetalhesData idSuperavit={superavit.data.id} />
        </div>
      </PageContent>
    </PageWrapper>
  );
}
