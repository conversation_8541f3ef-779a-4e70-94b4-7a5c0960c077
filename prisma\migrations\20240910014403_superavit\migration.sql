-- CreateTable
CREATE TABLE "controleSuperavits" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "fonte" SMALLINT NOT NULL,
    "codAplicacao" INTEGER NOT NULL,
    "valor" DECIMAL(18,2) NOT NULL,
    "valorReceita" DECIMAL(18,2) NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "controleSuperavits_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "controleSuperavitsDetalhes" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "despesa" INTEGER NOT NULL,
    "fonte" SMALLINT NOT NULL,
    "codAplicacao" INTEGER NOT NULL,
    "idAltOrcament" INTEGER NOT NULL,
    "data" TIMESTAMP NOT NULL,
    "tela" TEXT NOT NULL,
    "valor" DECIMAL(18,2) NOT NULL,
    "valorReservado" DECIMAL(18,2) NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "controleSuperavitsDetalhes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "controleSuperavits_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idSuperavit" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "controleSuperavits_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "controleSuperavits_ativo_idx" ON "controleSuperavits"("ativo");

-- CreateIndex
CREATE UNIQUE INDEX "controleSuperavits_exercicio_fonte_codAplicacao_key" ON "controleSuperavits"("exercicio", "fonte", "codAplicacao");

-- CreateIndex
CREATE INDEX "controleSuperavitsDetalhes_ativo_idx" ON "controleSuperavitsDetalhes"("ativo");

-- CreateIndex
CREATE UNIQUE INDEX "controleSuperavitsDetalhes_exercicio_despesa_key" ON "controleSuperavitsDetalhes"("exercicio", "despesa");

-- AddForeignKey
ALTER TABLE "controleSuperavits_audit" ADD CONSTRAINT "controleSuperavits_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "controleSuperavits_audit" ADD CONSTRAINT "controleSuperavits_audit_idSuperavit_fkey" FOREIGN KEY ("idSuperavit") REFERENCES "controleSuperavits"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
