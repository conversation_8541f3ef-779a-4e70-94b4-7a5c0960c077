'use server';
import { revalidatePath } from 'next/cache';
import {
  auditoriaErroSchema,
  nomeEIdSchema,
  idSchema,
  novoSubdepartamentoSchema,
  permissaoSchema,
  secretariaEDepartamentoIdsSchema,
} from '../../validation';
import { prisma } from '../../prisma';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { Modulos } from '@/lib/modulos';
import { AuditoriaGerenciamentoSecretarias, Permissoes } from '@/lib/enums';
import { inserirErroAudit } from '../auditoria/erro';
import z from 'zod';

export const listarSubdepartamentos = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id: idDepartamento } = parsedParams.data;

  try {
    const subdepartamentos = await prisma.subdepartamentos.findMany({
      where: { departamentoId: idDepartamento },
      orderBy: { codigo: 'asc' },
    });
    return {
      data: subdepartamentos,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter subdepartamentos.`,
    };
  }
};

export const revalidateSubdepartamentos = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = secretariaEDepartamentoIdsSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { idSecretaria, idDepartamento } = parsedParams.data;
  revalidatePath(
    `/secretarias/${idSecretaria}/departamentos/${idDepartamento}/subdepartamentos`
  );
};

export const desativarSubdepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const subdepPromise = tx.subdepartamentos.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });
      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          subdepartamentoId: id,
          acao: AuditoriaGerenciamentoSecretarias.DESATIVAR_SUBDEPARTAMENTO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([subdepPromise, auditPromise]);
    });

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar subdepartamento.`,
    };
  }
};

export const ativarSubdepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.DELETAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const subdepPromise = tx.subdepartamentos.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          subdepartamentoId: id,
          acao: AuditoriaGerenciamentoSecretarias.ATIVAR_SUBDEPARTAMENTO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
        },
      });

      await Promise.all([subdepPromise, auditPromise]);
    });

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar subdepartamento.`,
    };
  }
};

export const renomearSubdepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = nomeEIdSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { nome, id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const nomeAntigo = await tx.subdepartamentos.findUniqueOrThrow({
        where: {
          id: id,
        },
        select: {
          nome: true,
        },
      });
      const subdepPromise = tx.subdepartamentos.update({
        where: {
          id: id,
        },
        data: {
          nome: nome,
        },
      });

      const auditPromise = tx.gerenciamentoSecretarias_audit.create({
        data: {
          subdepartamentoId: id,
          acao: AuditoriaGerenciamentoSecretarias.RENOMEAR_SUBDEPARTAMENTO,
          usuarioId: resultPermissao.idUsuario!,
          ip,
          de: nomeAntigo.nome,
          para: nome,
        },
      });

      await Promise.all([subdepPromise, auditPromise]);
    });

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao renomear subdepartamento.`,
    };
  }
};

export const novoSubdepartamento = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.CRIAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = novoSubdepartamentoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { nome, departamentoId, codigo } = parsedParams.data;

  try {
    const result = await prisma.subdepartamentos.create({
      data: {
        codigo: codigo,
        nome: nome,
        departamentoId: departamentoId,
      },
    });

    return {
      data: result,
    };
  } catch (e: unknown) {
    console.log(e);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Subdepartamento já existe.`,
        };
      }
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao criar subdepartamento.`,
    };
  }
};
