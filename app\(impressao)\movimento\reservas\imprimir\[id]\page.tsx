'use server';
import { z } from 'zod';
import { obterReservaParaRelatorio } from '@/lib/database/movimento/reservas';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioReserva } from '@/components/movimento/reserva/relatorioReserva';
import { isNumeric } from '@/lib/utils';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permissoes } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function ImprimirReservaPage({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_RESERVA,
    permissao: Permissoes.ACESSAR,
  };
  const currSearchParams = await searchParams;

  const { id } = await params;
  if (!isNumeric(id)) return <ErrorAlert error='ID inválido.' />;
  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }
  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();
  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }
  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }
  const incluirGestor = currSearchParams.incluirGestor !== 'false';
  const assinaturaDigital = currSearchParams.assinaturaDigital === 'true';
  const reserva = await obterReservaParaRelatorio({
    id: Number(id),
    bearer,
    incluirGestor,
  });
  if (reserva.error) {
    return <ErrorAlert error={reserva.error} />;
  }
  if (!reserva.data) {
    return <ErrorAlert error='Falha ao obter reserva.' />;
  }

  return (
    <>
      <RelatorioReserva
        reserva={reserva}
        assinaturaDigital={assinaturaDigital}
      />
      <ClientCompletionTrigger />
    </>
  );
}
