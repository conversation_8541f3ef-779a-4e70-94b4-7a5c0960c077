!(function () {
  'use strict';
  var e = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const t = (e) =>
    'string' ===
    ((e) => {
      const t = typeof e;
      return null === e
        ? 'null'
        : 'object' === t && Array.isArray(e)
          ? 'array'
          : 'object' === t &&
              ((n = o = e),
              (r = String).prototype.isPrototypeOf(n) ||
                (null === (a = o.constructor) || void 0 === a
                  ? void 0
                  : a.name) === r.name)
            ? 'string'
            : t;
      var n, o, r, a;
    })(e);
  const n = (e) => undefined === e;
  const o = (e) => !((e) => null == e)(e),
    r = Object.hasOwnProperty,
    a = (e) => '\ufeff' === e,
    s = (e) => (t) => t.options.get(e),
    l = s('autolink_pattern'),
    i = s('link_default_target'),
    c = s('link_default_protocol'),
    d = s('allow_unsafe_link_target');
  var u = tinymce.util.Tools.resolve('tinymce.dom.TextSeeker');
  const f = (e) => /^[(\[{ \u00a0]$/.test(e),
    g = (e, t, n) => {
      for (let o = t - 1; o >= 0; o--) {
        const t = e.charAt(o);
        if (!a(t) && n(t)) return o;
      }
      return -1;
    },
    m = (e, t) => {
      var o;
      const a = e.schema.getVoidElements(),
        s = l(e),
        { dom: i, selection: d } = e;
      if (null !== i.getParent(d.getNode(), 'a[href]') || e.mode.isReadOnly())
        return null;
      const m = d.getRng(),
        k = u(i, (e) => {
          return (
            i.isBlock(e) ||
            ((t = a), (n = e.nodeName.toLowerCase()), r.call(t, n)) ||
            'false' === i.getContentEditable(e) ||
            null !== i.getParent(e, 'a[href]')
          );
          var t, n;
        }),
        { container: p, offset: y } = ((e, t) => {
          let n = e,
            o = t;
          for (; 1 === n.nodeType && n.childNodes[o]; )
            (n = n.childNodes[o]),
              (o = 3 === n.nodeType ? n.data.length : n.childNodes.length);
          return { container: n, offset: o };
        })(m.endContainer, m.endOffset),
        w =
          null !== (o = i.getParent(p, i.isBlock)) && void 0 !== o
            ? o
            : i.getRoot(),
        h = k.backwards(
          p,
          y + t,
          (e, t) => {
            const n = e.data,
              o = g(n, t, ((r = f), (e) => !r(e)));
            var r, a;
            return -1 === o || ((a = n[o]), /[?!,.;:]/.test(a)) ? o : o + 1;
          },
          w
        );
      if (!h) return null;
      let v = h.container;
      const _ = k.backwards(
          h.container,
          h.offset,
          (e, t) => {
            v = e;
            const n = g(e.data, t, f);
            return -1 === n ? n : n + 1;
          },
          w
        ),
        A = i.createRng();
      _ ? A.setStart(_.container, _.offset) : A.setStart(v, 0),
        A.setEnd(h.container, h.offset);
      const C = A.toString()
        .replace(/\uFEFF/g, '')
        .match(s);
      if (C) {
        let t = C[0];
        return (
          (P = 'www.'),
          (b = t).length >= 4 && b.substr(0, 4) === P
            ? (t = c(e) + '://' + t)
            : ((e, t, o = 0, r) => {
                const a = e.indexOf(t, o);
                return -1 !== a && (!!n(r) || a + t.length <= r);
              })(t, '@') &&
              !((e) => /^([A-Za-z][A-Za-z\d.+-]*:\/\/)|mailto:/.test(e))(t) &&
              (t = 'mailto:' + t),
          { rng: A, url: t }
        );
      }
      var b, P;
      return null;
    },
    k = (e, n) => {
      const { dom: o, selection: r } = e,
        { rng: a, url: s } = n,
        l = r.getBookmark();
      r.setRng(a);
      const c = 'createlink',
        u = { command: c, ui: !1, value: s };
      if (!e.dispatch('BeforeExecCommand', u).isDefaultPrevented()) {
        e.getDoc().execCommand(c, !1, s), e.dispatch('ExecCommand', u);
        const n = i(e);
        if (t(n)) {
          const t = r.getNode();
          o.setAttrib(t, 'target', n),
            '_blank' !== n || d(e) || o.setAttrib(t, 'rel', 'noopener');
        }
      }
      r.moveToBookmark(l), e.nodeChanged();
    },
    p = (e) => {
      const t = m(e, -1);
      o(t) && k(e, t);
    },
    y = p;
  e.add('autolink', (e) => {
    ((e) => {
      const t = e.options.register;
      t('autolink_pattern', {
        processor: 'regexp',
        default: new RegExp(
          '^' +
            /(?:[A-Za-z][A-Za-z\d.+-]{0,14}:\/\/(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?|www\.|[-;:&=+$,.\w]+@)[A-Za-z\d-]+(?:\.[A-Za-z\d-]+)*(?::\d+)?(?:\/(?:[-.~*+=!;:'%@$(),\/\w]*[-~*+=%@$()\/\w])?)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?/g
              .source +
            '$',
          'i'
        ),
      }),
        t('link_default_target', { processor: 'string' }),
        t('link_default_protocol', { processor: 'string', default: 'https' });
    })(e),
      ((e) => {
        e.on('keydown', (t) => {
          13 !== t.keyCode ||
            t.isDefaultPrevented() ||
            ((e) => {
              const t = m(e, 0);
              o(t) && k(e, t);
            })(e);
        }),
          e.on('keyup', (t) => {
            32 === t.keyCode
              ? p(e)
              : ((48 === t.keyCode && t.shiftKey) || 221 === t.keyCode) && y(e);
          });
      })(e);
  });
})();
