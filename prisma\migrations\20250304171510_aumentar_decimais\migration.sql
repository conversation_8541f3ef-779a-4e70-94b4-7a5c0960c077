/*
  Warnings:

  - You are about to alter the column `valorTotal` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes1` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes2` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes3` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes4` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes5` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes6` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes7` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes8` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes9` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes10` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes11` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes12` on the `AlteracaoOrcamentaria` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorReceita` on the `controleSuperavits` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valor` on the `controleSuperavitsDetalhes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorReservado` on the `controleSuperavitsDetalhes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorTotal` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes1` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes2` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes3` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes4` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes5` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes6` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes7` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes8` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes9` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes10` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes11` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorMes12` on the `cotasReducao` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `de` on the `cotasReducao_audit_valores` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `para` on the `cotasReducao_audit_valores` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorInicial` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaReducaoInicial` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorLiberado` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaReducao` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `suplementacao` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `anulacao` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorAtual` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes1` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes2` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes3` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes4` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes5` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes6` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes7` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes8` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes9` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes10` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes11` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes12` on the `dotacoes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valor` on the `reserva_itens` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valor` on the `reserva_itens_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes1` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes2` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes3` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes4` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes5` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes6` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes7` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes8` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes9` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes10` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes11` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes12` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarTotal` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes1` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes2` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes3` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes4` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes5` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes6` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes7` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes8` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes9` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes10` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes11` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes12` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarTotal` on the `reservas` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes1` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes2` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes3` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes4` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes5` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes6` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes7` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes8` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes9` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes10` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes11` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarMes12` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `usarTotal` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes1` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes2` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes3` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes4` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes5` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes6` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes7` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes8` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes9` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes10` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes11` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarMes12` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `pluUsarTotal` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes1` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes10` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes11` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes12` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes2` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes3` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes4` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes5` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes6` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes7` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes8` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `cotaMes9` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.
  - You are about to alter the column `valorTotalCota` on the `reservas_audit` table. The data in that column could be lost. The data in that column will be cast from `Decimal(18,2)` to `Decimal(18,3)`.

*/
-- DropIndex
DROP INDEX "controleSuperavitsDetalhes_exercicio_fonte_codAplicacao_key";

-- AlterTable
ALTER TABLE "AlteracaoOrcamentaria" ALTER COLUMN "valorTotal" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes12" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "controleSuperavits" ALTER COLUMN "valorReceita" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "controleSuperavitsDetalhes" ALTER COLUMN "valor" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorReservado" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "cotasReducao" ALTER COLUMN "valorTotal" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorMes12" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "cotasReducao_audit_valores" ALTER COLUMN "de" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "para" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "dotacoes" ALTER COLUMN "valorInicial" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaReducaoInicial" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorLiberado" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaReducao" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "suplementacao" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "anulacao" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorAtual" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes12" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "reserva_itens" ALTER COLUMN "valor" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "reserva_itens_audit" ALTER COLUMN "valor" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "reservas" ALTER COLUMN "usarMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes12" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarTotal" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes12" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarTotal" SET DATA TYPE DECIMAL(18,3);

-- AlterTable
ALTER TABLE "reservas_audit" ALTER COLUMN "usarMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarMes12" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "usarTotal" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarMes12" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "pluUsarTotal" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes1" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes10" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes11" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes12" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes2" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes3" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes4" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes5" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes6" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes7" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes8" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "cotaMes9" SET DATA TYPE DECIMAL(18,3),
ALTER COLUMN "valorTotalCota" SET DATA TYPE DECIMAL(18,3);
