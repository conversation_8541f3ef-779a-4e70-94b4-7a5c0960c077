import { type ClassValue, clsx } from 'clsx';
import currency from 'currency.js';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const currencyOptions: currency.Options = {
  separator: '.',
  decimal: ',',
  symbol: 'R$ ',
};

export const currencyOptionsNoSymbol: currency.Options = {
  separator: '.',
  decimal: ',',
  symbol: '',
  precision: 3,
};

export const currencyOptionsNoSymbol4Decimals: currency.Options = {
  separator: '.',
  decimal: ',',
  symbol: '',
  precision: 4,
};

export const toastAlgoDeuErrado =
  'Algo deu errado, por favor tente novamente mais tarde.';

export const garantirTimezone = (data: Date) => {
  data.setUTCMilliseconds(180 * 60000);
  return data;
};

export const zerarTimezone = (data: Date) => {
  return new Date(data.toDateString());
};

export const minimizarData = (data: Date) => {
  data.setHours(0, 0, 0, 0);
  return data;
};

export const maximizarData = (data: Date) => {
  data.setHours(23, 59, 59, 999);
  return data;
};

export const cnpjMask = (value: string) => {
  value =
    value === ''
      ? ''
      : value
          .replace('.', '')
          .replace(',', '')
          .replace('/', '')
          .replace('-', '')
          .replace(/\D/g, '');
  const result = value.replace(
    /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
    '$1.$2.$3/$4-$5'
  );
  return result;
};

export const cnpjUnmask = (value: string) => {
  value =
    value === ''
      ? ''
      : value
          .replace('.', '')
          .replace(',', '')
          .replace('/', '')
          .replace('-', '')
          .replace(/\D/g, '');
  return value;
};

export const cpfMask = (value: string) => {
  value =
    value === ''
      ? ''
      : value
          .replace('.', '')
          .replace(',', '')
          .replace('/', '')
          .replace('-', '')
          .replace(/\D/g, '');
  const result = value.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  return result;
};

export const cpfUnmask = (value: string) => {
  value =
    value === ''
      ? ''
      : value
          .replace('.', '')
          .replace(',', '')
          .replace('/', '')
          .replace('-', '')
          .replace(/\D/g, '');
  return value;
};

export const moneyMask = (value: string, decimals = 2) => {
  value =
    value === ''
      ? '0'
      : value.replace('.', '').replace(',', '').replace(/\D/g, '');
  const options = { minimumFractionDigits: decimals };
  const result = new Intl.NumberFormat('pt-BR', options).format(
    parseFloat(value) / 10 ** decimals
  );

  return result;
};

export const moneyUnmask = (value: string, decimals = 2) => {
  const negative = value.includes('-');
  value = value.replace('.', '').replace(',', '').replace(/\D/g, '');
  const newValue = negative ? `-${value}` : value;
  return currency(newValue, { fromCents: true, precision: decimals }).value;
};

export const uppercaseMask = (value: string) => {
  return value.toUpperCase();
};

export const codigoSecretariaMask = (value: string) => {
  if (Number(value) < 10) {
    return `0${value}`;
  } else {
    return `${value}`;
  }
};

export function formatBytes(bytes: number, decimals = 2) {
  if (!+bytes) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = [
    'Bytes',
    'KiB',
    'MiB',
    'GiB',
    'TiB',
    'PiB',
    'EiB',
    'ZiB',
    'YiB',
  ];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

const caracteresEspeciais = {
  '*': '-',
  '&': '-',
  '¨': '-',
  '%': '-',
  $: '-',
  '!': '-',
  '@': '-',
  '|': '-',
  '\\': '-',
  '/': '-',
  '?': '-',
  ':': '-',
  ';': '-',
  ',': '-',
  '[': '-',
  ']': '-',
  '{': '-',
  '}': '-',
  â: 'a',
  Â: 'A',
  à: 'a',
  À: 'A',
  á: 'a',
  Á: 'A',
  ã: 'a',
  Ã: 'A',
  ê: 'e',
  Ê: 'E',
  è: 'e',
  È: 'E',
  é: 'e',
  É: 'E',
  î: 'i',
  Î: 'I',
  ì: 'i',
  Ì: 'I',
  í: 'i',
  Í: 'I',
  õ: 'o',
  Õ: 'O',
  ô: 'o',
  Ô: 'O',
  ò: 'o',
  Ò: 'O',
  ó: 'o',
  Ó: 'O',
  ü: 'u',
  Ü: 'U',
  û: 'u',
  Û: 'U',
  ú: 'u',
  Ú: 'U',
  ù: 'u',
  Ù: 'U',
  ç: 'c',
  Ç: 'C',
};
export function removerCaracteresEspeciais(s: string) {
  return s.replace(/[\W\[\] ]/g, function (a) {
    return (caracteresEspeciais as any)[a] || a;
  });
}

export function isNumeric(value?: string | number): boolean {
  return value != null && value !== '' && !isNaN(Number(value.toString()));
}

export function economicaMask(value: string) {
  value =
    value === ''
      ? ''
      : value
          .replace('.', '')
          .replace(',', '')
          .replace('/', '')
          .replace('-', '')
          .replace(/\D/g, '');
  const result = value.replace(
    /^(\d{1})(\d{1})(\d{2})(\d{2})(\d{2})/,
    '$1.$2.$3.$4.$5'
  );
  return result;
}

export function funcionalMask(value: string) {
  value =
    value === ''
      ? ''
      : value
          .replace('.', '')
          .replace(',', '')
          .replace('/', '')
          .replace('-', '')
          .replace(/\D/g, '');
  const result = value.replace(/^(\d{2})(\d{3})(\d{4})(\d{4})/, '$1 $2 $3 $4');
  return result;
}

export function numeroMask(value: string) {
  value = value === '' ? '' : value.replace(/\D/g, '');
  return value;
}

export const obterAnoAtual = () => {
  return Number(new Date().toLocaleDateString('pt-BR', { year: 'numeric' }));
};

export const obterMesAtual = () => {
  return Number(new Date().toLocaleDateString('pt-BR', { month: 'numeric' }));
};

export const montarCodigoSecretaria = (codigoSecretaria: number) => {
  return `${codigoSecretariaMask(codigoSecretaria.toString())}.00.00`;
};

export const montarCodigoDepartamento = (
  codigoSecretaria: number,
  codigoDepartamento: number
) => {
  return `${codigoSecretariaMask(codigoSecretaria.toString())}.${codigoSecretariaMask(codigoDepartamento.toString())}.00`;
};

export const montarCodigoSubdepartamento = (
  codigoSecretaria: number,
  codigoDepartamento: number,
  codigoSubdepartamento: number
) => {
  return `${codigoSecretariaMask(codigoSecretaria.toString())}.${codigoSecretariaMask(codigoDepartamento.toString())}.${codigoSecretariaMask(codigoSubdepartamento.toString())}`;
};

export function splitString(str: string, n: number) {
  let arr = str?.split(' ');
  let result = [];
  let subStr = arr[0];
  for (let i = 1; i < arr.length; i++) {
    let word = arr[i];
    if (subStr.length + word.length + 1 <= n) {
      subStr = subStr + ' ' + word;
    } else {
      result.push(subStr);
      subStr = word;
    }
  }
  if (subStr.length) {
    result.push(subStr);
  }
  return result;
}

export const obterDataAtual = () => {
  return new Date(); //.toLocaleDateString('pt-BR');
};

export const formatDataHora = (dt: Date) => {
  let dtformatada = new Date(dt).toLocaleDateString('pt-BR', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
  });
  return dtformatada;
};

export const formataData = (dt: Date) => {
  let dtformatada = new Date(dt).toLocaleDateString('pt-BR', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
  });
  return dtformatada;
};

export const montarPathPdfReserva = (
  idReserva: number,
  idDotacao: number,
  exercicio: number,
  data?: Date
) => {
  const nomeDocumento = `Pedido de Compra - Reserva ${idReserva}.pdf`;
  if (!data) {
    return `private/${exercicio}/dotacoes/${idDotacao}/reservas/${nomeDocumento}`;
  }
  return `private/${exercicio}/dotacoes/${idDotacao}/reservas/${nomeDocumento}?version=${data.getTime()}`;
};

export const montarPathDocumentoExterno = (
  idUsuarioExterno: number,
  idDocumento: number,
  tipo: 'original' | 'assinado',
  nomeArquivo: string
): string => {
  const nomeArquivoLimpo = nomeArquivo.replace(/[^a-zA-Z0-9.-]/g, '_');
  return `external/${idUsuarioExterno}/${idDocumento}/${tipo}/${nomeArquivoLimpo}`;
};
