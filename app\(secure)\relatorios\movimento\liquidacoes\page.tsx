'use client';
import { useState } from 'react';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar, FileText, TrendingUp, TrendingDown } from 'lucide-react';
import { BotaoDownloadPDF } from '@/components/relatorios/botaoDownloadPDF';

export default function RelatorioLiquidacoesPage() {
  const [periodoForm, setPeriodoForm] = useState({
    dataInicio: '',
    dataFim: '',
    exercicio: new Date().getFullYear().toString(),
    secretaria: '',
    departamento: '',
    subdepartamento: '',
    status: '',
    fornecedor: '',
    incluirProcessados: true,
    incluirNaoProcessados: true,
  });

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleGerarRelatorioPeriodo = () => {
    if (!periodoForm.dataInicio || !periodoForm.dataFim) {
      return null;
    }

    const params: Record<string, string> = {
      dataInicio: periodoForm.dataInicio,
      dataFim: periodoForm.dataFim,
    };

    if (periodoForm.exercicio) params.exercicio = periodoForm.exercicio;
    if (periodoForm.secretaria) params.secretaria = periodoForm.secretaria;
    if (periodoForm.departamento)
      params.departamento = periodoForm.departamento;
    if (periodoForm.subdepartamento)
      params.subdepartamento = periodoForm.subdepartamento;
    if (periodoForm.status) params.status = periodoForm.status;
    if (periodoForm.fornecedor) params.fornecedor = periodoForm.fornecedor;
    if (periodoForm.incluirProcessados) params.incluirProcessados = 'true';
    if (periodoForm.incluirNaoProcessados)
      params.incluirNaoProcessados = 'true';

    return params;
  };

  const reportOptions: Array<{
    id: string;
    title: string;
    description: string;
    url: string;
    icon: React.ComponentType<{ className?: string }>;
    isPeriod?: boolean;
  }> = [
    {
      id: 'geral',
      title: 'Relatório Geral de Liquidações',
      description: 'Relatório completo de todas as liquidações do sistema',
      url: '/movimento/liquidacoes/imprimir',
      icon: FileText,
    },
    {
      id: 'processados',
      title: 'Relatório de Liquidações Processadas',
      description: 'Relatório de liquidações com status de processadas',
      url: '/movimento/liquidacoes/imprimir/processados',
      icon: TrendingUp,
    },
    {
      id: 'nao-processados',
      title: 'Relatório de Liquidações Não Processadas',
      description: 'Relatório de liquidações pendentes de processamento',
      url: '/movimento/liquidacoes/imprimir/nao-processados',
      icon: TrendingDown,
    },
    {
      id: 'periodo',
      title: 'Relatório de Liquidações por Período',
      description: 'Relatório de liquidações filtrado por período específico',
      url: '/movimento/liquidacoes/imprimir/periodo',
      icon: Calendar,
      isPeriod: true,
    },
  ];

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Relatório de Liquidações</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='space-y-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold tracking-tight'>
                Relatórios de Liquidações
              </h1>
              <p className='text-muted-foreground'>
                Visualize e exporte relatórios de liquidações do sistema
              </p>
            </div>
          </div>

          <div className='grid gap-6 md:grid-cols-2'>
            {reportOptions.map((report) => {
              const Icon = report.icon;

              if (report.isPeriod) {
                return (
                  <Dialog
                    open={isDialogOpen}
                    onOpenChange={setIsDialogOpen}
                    key={report.id}
                  >
                    <Card
                      className='cursor-pointer transition-shadow hover:shadow-lg'
                      onClick={() => setIsDialogOpen(true)}
                    >
                      <CardHeader>
                        <CardTitle className='flex items-center gap-2'>
                          <Icon className='h-5 w-5' />
                          {report.title}
                        </CardTitle>
                        <CardDescription>{report.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Button variant='outline' className='w-full'>
                          Configurar Relatório
                        </Button>
                      </CardContent>
                    </Card>

                    <DialogContent className='max-w-2xl'>
                      <DialogHeader>
                        <DialogTitle>
                          Configurar Relatório por Período
                        </DialogTitle>
                        <DialogDescription>
                          Preencha os parâmetros obrigatórios para gerar o
                          relatório de liquidações por período.
                        </DialogDescription>
                      </DialogHeader>

                      <div className='grid gap-4 py-4'>
                        <div className='grid grid-cols-2 gap-4'>
                          <div>
                            <Label htmlFor='dataInicio'>Data Início *</Label>
                            <Input
                              id='dataInicio'
                              type='date'
                              value={periodoForm.dataInicio}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  dataInicio: e.target.value,
                                }))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor='dataFim'>Data Fim *</Label>
                            <Input
                              id='dataFim'
                              type='date'
                              value={periodoForm.dataFim}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  dataFim: e.target.value,
                                }))
                              }
                            />
                          </div>
                        </div>

                        <div className='grid grid-cols-2 gap-4'>
                          <div>
                            <Label htmlFor='exercicio'>Exercício</Label>
                            <Input
                              id='exercicio'
                              type='number'
                              value={periodoForm.exercicio}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  exercicio: e.target.value,
                                }))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor='fornecedor'>Fornecedor</Label>
                            <Input
                              id='fornecedor'
                              type='number'
                              value={periodoForm.fornecedor}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  fornecedor: e.target.value,
                                }))
                              }
                              placeholder='ID do fornecedor'
                            />
                          </div>
                        </div>

                        <div className='grid grid-cols-3 gap-4'>
                          <div>
                            <Label htmlFor='secretaria'>Secretaria</Label>
                            <Input
                              id='secretaria'
                              type='number'
                              value={periodoForm.secretaria}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  secretaria: e.target.value,
                                }))
                              }
                              placeholder='ID da secretaria'
                            />
                          </div>
                          <div>
                            <Label htmlFor='departamento'>Departamento</Label>
                            <Input
                              id='departamento'
                              type='number'
                              value={periodoForm.departamento}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  departamento: e.target.value,
                                }))
                              }
                              placeholder='ID do departamento'
                            />
                          </div>
                          <div>
                            <Label htmlFor='subdepartamento'>
                              Subdepartamento
                            </Label>
                            <Input
                              id='subdepartamento'
                              type='number'
                              value={periodoForm.subdepartamento}
                              onChange={(e) =>
                                setPeriodoForm((prev) => ({
                                  ...prev,
                                  subdepartamento: e.target.value,
                                }))
                              }
                              placeholder='ID do subdepartamento'
                            />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor='status'>Status</Label>
                          <Input
                            id='status'
                            type='number'
                            value={periodoForm.status}
                            onChange={(e) =>
                              setPeriodoForm((prev) => ({
                                ...prev,
                                status: e.target.value,
                              }))
                            }
                            placeholder='ID do status (opcional)'
                          />
                        </div>

                        <div className='space-y-3'>
                          <Label className='text-sm font-medium'>
                            Tipos de Liquidações
                          </Label>
                          <div className='space-y-2'>
                            <div className='flex items-center space-x-2'>
                              <Checkbox
                                id='processados'
                                checked={periodoForm.incluirProcessados}
                                onCheckedChange={(checked) =>
                                  setPeriodoForm((prev) => ({
                                    ...prev,
                                    incluirProcessados: checked as boolean,
                                  }))
                                }
                              />
                              <Label htmlFor='processados' className='text-sm'>
                                Incluir liquidações processadas
                              </Label>
                            </div>
                            <div className='flex items-center space-x-2'>
                              <Checkbox
                                id='nao-processados'
                                checked={periodoForm.incluirNaoProcessados}
                                onCheckedChange={(checked) =>
                                  setPeriodoForm((prev) => ({
                                    ...prev,
                                    incluirNaoProcessados: checked as boolean,
                                  }))
                                }
                              />
                              <Label
                                htmlFor='nao-processados'
                                className='text-sm'
                              >
                                Incluir liquidações não processadas
                              </Label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <DialogFooter>
                        <Button
                          variant='outline'
                          onClick={() => setIsDialogOpen(false)}
                        >
                          Cancelar
                        </Button>
                        {handleGerarRelatorioPeriodo() ? (
                          <BotaoDownloadPDF
                            url={report.url}
                            title='Gerar Relatório'
                            params={handleGerarRelatorioPeriodo()!}
                          />
                        ) : (
                          <Button disabled className='w-full'>
                            Preencha os campos obrigatórios
                          </Button>
                        )}
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                );
              }

              return (
                <Card
                  key={report.id}
                  className='transition-shadow hover:shadow-lg'
                >
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <Icon className='h-5 w-5' />
                      {report.title}
                    </CardTitle>
                    <CardDescription>{report.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <BotaoDownloadPDF
                      url={report.url}
                      title='Gerar Relatório'
                    />
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
