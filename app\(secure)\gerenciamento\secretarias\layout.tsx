'use server';
import { z } from 'zod';

import { ErrorAlert } from '@/components/error-alert';
import { temPermissao } from '@/lib/database/usuarios';
import { Permissoes } from '@/lib/enums';
import { Modulos } from '@/lib/modulos';
import { permissaoSchema } from '@/lib/validation';

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_SECRETARIAS_E_DEPARTAMENTOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  if (result.error) {
    return <ErrorAlert error={result.error} />;
  }

  if (!result.temPermissao) {
    return <ErrorAlert error='Usuário sem permissão para acessar.' />;
  }

  return <>{children}</>;
}
