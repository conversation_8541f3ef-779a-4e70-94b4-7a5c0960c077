'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, PlusCircle } from 'lucide-react';
import { listarCargosAtivos } from '@/lib/database/gerenciamento/usuarios';
import { ConfigCargo } from '@/types/app';
import { usuarioFormSchema, usuarioSchema } from '@/lib/validation';
import { ComboboxSelecionarCargo } from './comboboxSelecionarCargo';
import ConfigCargosDatatable from './configCargosDatatable';
import { criarUsuario } from '@/lib/database/gerenciamento/usuarios';
import { toastAlgoDeuErrado } from '@/lib/utils';

export default function CriarUsuarioForm({
  cargos,
}: {
  cargos: Awaited<ReturnType<typeof listarCargosAtivos>>;
}) {
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [cargosConfigurados, setCargosConfigurados] = useState<ConfigCargo[]>(
    []
  );

  const form = useForm<z.infer<typeof usuarioFormSchema>>({
    resolver: zodResolver(usuarioFormSchema),
    defaultValues: {
      // nome: '',
      email: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof usuarioFormSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof usuarioSchema> = {
        nome: values.nome,
        email: values.email,
        cargos: cargosConfigurados.map((cargo) => {
          return Number(cargo.id);
        }),
      };
      const res = await criarUsuario(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Usuario adicionado.');
        router.push('/gerenciamento/usuarios');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='grid gap-8 py-4'>
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>
                  Nome Completo
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={2}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='flex w-full text-left'>Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type='email'
                    placeholder='Digite...'
                    maxLength={150}
                    minLength={2}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className='grid gap-4 py-8'>
          <span className='mt-4 text-lg'>Cargos</span>
          <ComboboxSelecionarCargo
            cargos={cargos}
            cargosConfigurados={cargosConfigurados}
            setCargosConfigurados={setCargosConfigurados}
          />
          <ConfigCargosDatatable
            data={cargosConfigurados}
            setCargosConfigurados={setCargosConfigurados}
          />
        </div>
        <div className='mt-12 flex justify-between'>
          <Button
            variant={'destructive'}
            disabled={loading}
            onClick={(e) => {
              e.preventDefault();
              router.push('/gerenciamento/usuarios');
            }}
          >
            <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
          </Button>
          <Button
            type='submit'
            disabled={loading || cargosConfigurados.length === 0}
          >
            {loading ? (
              <>
                <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' /> Adicionar Usuário
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
