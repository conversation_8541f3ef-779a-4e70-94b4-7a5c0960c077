'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';
import { criarContratoSchema } from '@/lib/validation';

export function ComboboxSelecionaFornecedor({
  fornecedores,
  form,
}: {
  fornecedores: any;
  form: UseFormReturn<z.infer<typeof criarContratoSchema>>;
}) {
  const [open, setOpen] = React.useState(false);

  const fornecedorSelecionado =
    fornecedores.find((fornecedor: any) => {
      return form.getValues('idFornecedor') == fornecedor.id;
    }) || null;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {fornecedorSelecionado
              ? fornecedorSelecionado.codigo +
                ' - ' +
                fornecedorSelecionado.nome
              : 'Selecione o Fornecedor...'}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='start' className='p-0 sm:w-[300px] md:w-[620px]'>
          <Command>
            <CommandInput placeholder='Buscar Fornecedor...' />
            <CommandEmpty>Fornecedor não encontrado.</CommandEmpty>
            <CommandList>
              {fornecedores.map((sec: any) => (
                <CommandItem
                  key={sec.id}
                  value={`${sec.codigo} - ${sec.nome}`}
                  onSelect={() => {
                    form.setValue('idFornecedor', sec.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      form.getValues('idFornecedor') === sec.id
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  {sec.codigo} - {sec.nome}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
