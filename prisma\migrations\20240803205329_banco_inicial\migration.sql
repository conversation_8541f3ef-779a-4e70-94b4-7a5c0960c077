-- CreateTable
CREATE TABLE "user_profiles" (
    "id" SERIAL NOT NULL,
    "user_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "gerente" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "user_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "parametros" (
    "id" SERIAL NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "parametros_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "parametros_exercicio" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,

    CONSTRAINT "parametros_exercicio_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cargos" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "cargos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cargos_permissoes" (
    "id" SERIAL NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "moduloId" INTEGER NOT NULL,
    "read" BOOLEAN NOT NULL,
    "write" BOOLEAN NOT NULL,
    "update" BOOLEAN NOT NULL,
    "delete" BOOLEAN NOT NULL,

    CONSTRAINT "cargos_permissoes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "secretarias" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "nome" TEXT NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "secretarias_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "departamentos" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "nome" TEXT NOT NULL,
    "secretariaId" INTEGER NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "departamentos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subdepartamentos" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "nome" TEXT NOT NULL,
    "departamentoId" INTEGER NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "subdepartamentos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "secretarias_secretarios" (
    "id" INTEGER NOT NULL,
    "secretariaId" INTEGER NOT NULL,
    "secretarioId" INTEGER NOT NULL,

    CONSTRAINT "secretarias_secretarios_pkey" PRIMARY KEY ("secretarioId")
);

-- CreateTable
CREATE TABLE "departamentos_gestores" (
    "id" INTEGER NOT NULL,
    "departamentoId" INTEGER NOT NULL,
    "gestorId" INTEGER NOT NULL,

    CONSTRAINT "departamentos_gestores_pkey" PRIMARY KEY ("gestorId")
);

-- CreateTable
CREATE TABLE "subdepartamentos_gestores" (
    "id" INTEGER NOT NULL,
    "subdepartamentoId" INTEGER NOT NULL,
    "gestorId" INTEGER NOT NULL,

    CONSTRAINT "subdepartamentos_gestores_pkey" PRIMARY KEY ("gestorId")
);

-- CreateTable
CREATE TABLE "fornecedores" (
    "id" SERIAL NOT NULL,
    "codigo" INTEGER NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "nome" TEXT NOT NULL,
    "cpf" VARCHAR(12) NOT NULL,
    "cnpj" VARCHAR(18) NOT NULL,

    CONSTRAINT "fornecedores_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "fontes" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "desc" TEXT NOT NULL,
    "exercicio" SMALLINT NOT NULL,

    CONSTRAINT "fontes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "funcoes" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "desc" TEXT NOT NULL,
    "exercicio" SMALLINT NOT NULL,

    CONSTRAINT "funcoes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subfuncoes" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "desc" TEXT NOT NULL,
    "exercicio" SMALLINT NOT NULL,

    CONSTRAINT "subfuncoes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "programas" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "desc" TEXT NOT NULL,
    "exercicio" SMALLINT NOT NULL,

    CONSTRAINT "programas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "acoes" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "desc" TEXT NOT NULL,
    "exercicio" SMALLINT NOT NULL,

    CONSTRAINT "acoes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "funcionais" (
    "id" SERIAL NOT NULL,
    "desc" TEXT NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "funcaoId" INTEGER NOT NULL,
    "subfuncaoId" INTEGER NOT NULL,
    "programaId" INTEGER NOT NULL,
    "acaoId" INTEGER NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "funcionais_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "economicas" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "codigo" VARCHAR(12) NOT NULL,
    "desc" TEXT NOT NULL,
    "grau" SMALLINT NOT NULL,

    CONSTRAINT "economicas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dotacoes" (
    "id" SERIAL NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "despesa" INTEGER NOT NULL,
    "funcionalId" INTEGER NOT NULL,
    "economicaId" INTEGER NOT NULL,
    "departamentoId" INTEGER NOT NULL,
    "valorInicial" DECIMAL(18,2) NOT NULL,
    "cotaReducaoInicial" DECIMAL(18,2) NOT NULL,
    "valorLiberado" DECIMAL(18,2) NOT NULL,
    "cotaReducao" DECIMAL(18,2) NOT NULL,
    "suplementacao" DECIMAL(18,2) NOT NULL,
    "anulacao" DECIMAL(18,2) NOT NULL,
    "valorAtual" DECIMAL(18,2) NOT NULL,
    "cotaMes1" DECIMAL(18,2) NOT NULL,
    "cotaMes2" DECIMAL(18,2) NOT NULL,
    "cotaMes3" DECIMAL(18,2) NOT NULL,
    "cotaMes4" DECIMAL(18,2) NOT NULL,
    "cotaMes5" DECIMAL(18,2) NOT NULL,
    "cotaMes6" DECIMAL(18,2) NOT NULL,
    "cotaMes7" DECIMAL(18,2) NOT NULL,
    "cotaMes8" DECIMAL(18,2) NOT NULL,
    "cotaMes9" DECIMAL(18,2) NOT NULL,
    "cotaMes10" DECIMAL(18,2) NOT NULL,
    "cotaMes11" DECIMAL(18,2) NOT NULL,
    "cotaMes12" DECIMAL(18,2) NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "dotacoes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_profiles_user_id_key" ON "user_profiles"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "parametros_key_key" ON "parametros"("key");

-- CreateIndex
CREATE UNIQUE INDEX "parametros_exercicio_exercicio_key_key" ON "parametros_exercicio"("exercicio", "key");

-- CreateIndex
CREATE UNIQUE INDEX "cargos_name_key" ON "cargos"("name");

-- CreateIndex
CREATE UNIQUE INDEX "cargos_permissoes_cargoId_moduloId_key" ON "cargos_permissoes"("cargoId", "moduloId");

-- CreateIndex
CREATE INDEX "secretarias_secretarios_secretariaId_secretarioId_idx" ON "secretarias_secretarios"("secretariaId", "secretarioId");

-- CreateIndex
CREATE UNIQUE INDEX "secretarias_secretarios_secretariaId_secretarioId_key" ON "secretarias_secretarios"("secretariaId", "secretarioId");

-- CreateIndex
CREATE INDEX "departamentos_gestores_departamentoId_gestorId_idx" ON "departamentos_gestores"("departamentoId", "gestorId");

-- CreateIndex
CREATE UNIQUE INDEX "departamentos_gestores_departamentoId_gestorId_key" ON "departamentos_gestores"("departamentoId", "gestorId");

-- CreateIndex
CREATE INDEX "subdepartamentos_gestores_subdepartamentoId_gestorId_idx" ON "subdepartamentos_gestores"("subdepartamentoId", "gestorId");

-- CreateIndex
CREATE UNIQUE INDEX "subdepartamentos_gestores_subdepartamentoId_gestorId_key" ON "subdepartamentos_gestores"("subdepartamentoId", "gestorId");

-- CreateIndex
CREATE UNIQUE INDEX "fornecedores_codigo_exercicio_key" ON "fornecedores"("codigo", "exercicio");

-- CreateIndex
CREATE UNIQUE INDEX "fontes_codigo_exercicio_key" ON "fontes"("codigo", "exercicio");

-- CreateIndex
CREATE UNIQUE INDEX "funcoes_codigo_exercicio_key" ON "funcoes"("codigo", "exercicio");

-- CreateIndex
CREATE UNIQUE INDEX "subfuncoes_codigo_exercicio_key" ON "subfuncoes"("codigo", "exercicio");

-- CreateIndex
CREATE UNIQUE INDEX "programas_codigo_exercicio_key" ON "programas"("codigo", "exercicio");

-- CreateIndex
CREATE UNIQUE INDEX "acoes_codigo_exercicio_key" ON "acoes"("codigo", "exercicio");

-- CreateIndex
CREATE UNIQUE INDEX "funcionais_exercicio_funcaoId_subfuncaoId_programaId_acaoId_key" ON "funcionais"("exercicio", "funcaoId", "subfuncaoId", "programaId", "acaoId");

-- CreateIndex
CREATE UNIQUE INDEX "economicas_exercicio_codigo_key" ON "economicas"("exercicio", "codigo");

-- CreateIndex
CREATE UNIQUE INDEX "dotacoes_exercicio_despesa_key" ON "dotacoes"("exercicio", "despesa");

-- AddForeignKey
ALTER TABLE "cargos_permissoes" ADD CONSTRAINT "cargos_permissoes_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "departamentos" ADD CONSTRAINT "departamentos_secretariaId_fkey" FOREIGN KEY ("secretariaId") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "subdepartamentos" ADD CONSTRAINT "subdepartamentos_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "departamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "secretarias_secretarios" ADD CONSTRAINT "secretarias_secretarios_secretariaId_fkey" FOREIGN KEY ("secretariaId") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "secretarias_secretarios" ADD CONSTRAINT "secretarias_secretarios_secretarioId_fkey" FOREIGN KEY ("secretarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "departamentos_gestores" ADD CONSTRAINT "departamentos_gestores_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "departamentos_gestores" ADD CONSTRAINT "departamentos_gestores_gestorId_fkey" FOREIGN KEY ("gestorId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "subdepartamentos_gestores" ADD CONSTRAINT "subdepartamentos_gestores_subdepartamentoId_fkey" FOREIGN KEY ("subdepartamentoId") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "subdepartamentos_gestores" ADD CONSTRAINT "subdepartamentos_gestores_gestorId_fkey" FOREIGN KEY ("gestorId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_funcaoId_fkey" FOREIGN KEY ("funcaoId") REFERENCES "funcoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_subfuncaoId_fkey" FOREIGN KEY ("subfuncaoId") REFERENCES "subfuncoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_programaId_fkey" FOREIGN KEY ("programaId") REFERENCES "programas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "funcionais" ADD CONSTRAINT "funcionais_acaoId_fkey" FOREIGN KEY ("acaoId") REFERENCES "acoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "dotacoes" ADD CONSTRAINT "dotacoes_funcionalId_fkey" FOREIGN KEY ("funcionalId") REFERENCES "funcionais"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "dotacoes" ADD CONSTRAINT "dotacoes_economicaId_fkey" FOREIGN KEY ("economicaId") REFERENCES "economicas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "dotacoes" ADD CONSTRAINT "dotacoes_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "departamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
