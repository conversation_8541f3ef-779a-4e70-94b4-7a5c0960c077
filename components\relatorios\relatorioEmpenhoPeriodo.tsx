import { siteConfig } from '@/config/site';
import { toCurrency } from '@/lib/serverUtils';

interface RelatorioEmpenhoPeriodoProps {
  grupos: NonNullable<
    Required<
      Awaited<
        ReturnType<
          typeof import('@/lib/database/relatorios/empenhos').gerarRelatorioEmpenhosPorPeriodo
        >
      >
    >
  >['data']['grupos'];
  totalGeral: NonNullable<
    Required<
      Awaited<
        ReturnType<
          typeof import('@/lib/database/relatorios/empenhos').gerarRelatorioEmpenhosPorPeriodo
        >
      >
    >
  >['data']['totalGeral'];
  agrupadoPor: 'mes' | 'secretaria' | 'departamento' | 'fornecedor';
  periodo: {
    inicio: Date;
    fim: Date;
  };
  titulo?: string;
}

export const RelatorioEmpenhoPeriodo = ({
  grupos,
  totalGeral,
  agrupadoPor,
  periodo,
  titulo = 'Relatório de Empenhos por Período',
}: RelatorioEmpenhoPeriodoProps) => {
  const getAgrupamentoLabel = () => {
    switch (agrupadoPor) {
      case 'mes':
        return 'Mês';
      case 'secretaria':
        return 'Secretaria';
      case 'departamento':
        return 'Departamento';
      case 'fornecedor':
        return 'Fornecedor';
      default:
        return 'Agrupamento';
    }
  };

  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        <div className='space-y-1 text-sm text-gray-600'>
          <p>Agrupado por: {getAgrupamentoLabel()}</p>
          <p>
            Período: {new Date(periodo.inicio).toLocaleDateString('pt-BR')} a{' '}
            {new Date(periodo.fim).toLocaleDateString('pt-BR')}
          </p>
        </div>
      </div>

      {/* Resumo Geral */}
      <div className='mb-6 rounded-lg bg-gray-50 p-4'>
        <h3 className='mb-3 border-b pb-1 font-semibold'>Resumo Geral</h3>
        <div className='grid grid-cols-5 gap-3 text-sm'>
          <div className='text-center'>
            <div className='font-medium'>Total de Empenhos</div>
            <div className='font-mono'>{totalGeral.totalEmpenhos}</div>
          </div>
          <div className='text-center'>
            <div className='font-medium'>Valor Total</div>
            <div className='font-mono'>
              {toCurrency(totalGeral.valorTotal).format().replace(/0$/, '')}
            </div>
          </div>
          <div className='text-center'>
            <div className='font-medium'>Valor Liquidado</div>
            <div className='font-mono'>
              {toCurrency(totalGeral.valorTotalLiquidado)
                .format()
                .replace(/0$/, '')}
            </div>
          </div>
          <div className='text-center'>
            <div className='font-medium'>Valor Processado</div>
            <div className='font-mono'>
              {toCurrency(totalGeral.valorTotalProcessado)
                .format()
                .replace(/0$/, '')}
            </div>
          </div>
          <div className='text-center'>
            <div className='font-medium'>Saldo Disponível</div>
            <div
              className={`font-mono ${totalGeral.saldoTotalDisponivel < 0 ? 'text-red-600' : ''}`}
            >
              {toCurrency(totalGeral.saldoTotalDisponivel)
                .format()
                .replace(/0$/, '')}
            </div>
          </div>
        </div>
      </div>

      {/* Grupos */}
      <div className='space-y-6'>
        {grupos.map((grupo, index) => (
          <div key={grupo.chave} className='rounded-lg bg-gray-50 p-4'>
            <h3 className='mb-3 border-b pb-1 font-semibold'>
              {getAgrupamentoLabel()}: {grupo.chave}
            </h3>

            {/* Resumo do Grupo */}
            <div className='mb-4 rounded border bg-white p-3'>
              <div className='grid grid-cols-5 gap-3 text-sm'>
                <div className='text-center'>
                  <div className='font-medium'>Total de Empenhos</div>
                  <div className='font-mono'>{grupo.resumo.totalEmpenhos}</div>
                </div>
                <div className='text-center'>
                  <div className='font-medium'>Valor Total</div>
                  <div className='font-mono'>
                    {toCurrency(grupo.resumo.valorTotal)
                      .format()
                      .replace(/0$/, '')}
                  </div>
                </div>
                <div className='text-center'>
                  <div className='font-medium'>Valor Liquidado</div>
                  <div className='font-mono'>
                    {toCurrency(grupo.resumo.valorTotalLiquidado)
                      .format()
                      .replace(/0$/, '')}
                  </div>
                </div>
                <div className='text-center'>
                  <div className='font-medium'>Valor Processado</div>
                  <div className='font-mono'>
                    {toCurrency(grupo.resumo.valorTotalProcessado)
                      .format()
                      .replace(/0$/, '')}
                  </div>
                </div>
                <div className='text-center'>
                  <div className='font-medium'>Saldo Disponível</div>
                  <div
                    className={`font-mono ${grupo.resumo.saldoTotalDisponivel < 0 ? 'text-red-600' : ''}`}
                  >
                    {toCurrency(grupo.resumo.saldoTotalDisponivel)
                      .format()
                      .replace(/0$/, '')}
                  </div>
                </div>
              </div>
            </div>

            {/* Tabela do Grupo */}
            <div className='overflow-x-auto'>
              <table className='w-full border-collapse text-xs'>
                <thead>
                  <tr className='border-b-2 border-gray-800'>
                    <th className='p-1 text-left font-bold'>Empenho</th>
                    <th className='p-1 text-left font-bold'>Fornecedor</th>
                    <th className='p-1 text-left font-bold'>Secretaria</th>
                    <th className='p-1 text-left font-bold'>Departamento</th>
                    <th className='p-1 text-left font-bold'>Data</th>
                    <th className='p-1 text-right font-bold'>Valor Total</th>
                    <th className='p-1 text-right font-bold'>
                      Valor Liquidado
                    </th>
                    <th className='p-1 text-right font-bold'>Saldo</th>
                  </tr>
                </thead>
                <tbody>
                  {grupo.empenhos.map((emp, empIndex) => (
                    <tr
                      key={emp.id}
                      className={empIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                    >
                      <td className='border-b border-gray-200 p-1 font-mono'>
                        {emp.numero}/{emp.exercicio}
                      </td>
                      <td className='max-w-xs border-b border-gray-200 p-1'>
                        <div
                          className='truncate'
                          title={emp.fornecedor?.nome || 'N/A'}
                        >
                          {emp.fornecedor?.nome || 'N/A'}
                        </div>
                      </td>
                      <td className='max-w-xs border-b border-gray-200 p-1'>
                        <div
                          className='truncate'
                          title={emp.dotacao.secretaria?.nome || 'N/A'}
                        >
                          {emp.dotacao.secretaria?.nome || 'N/A'}
                        </div>
                      </td>
                      <td className='max-w-xs border-b border-gray-200 p-1'>
                        <div
                          className='truncate'
                          title={emp.dotacao.departamento?.nome || 'N/A'}
                        >
                          {emp.dotacao.departamento?.nome || 'N/A'}
                        </div>
                      </td>
                      <td className='border-b border-gray-200 p-1'>
                        {new Date(emp.data).toLocaleDateString('pt-BR')}
                      </td>
                      <td className='border-b border-gray-200 p-1 text-right font-mono'>
                        {toCurrency(emp.valorTotal).format().replace(/0$/, '')}
                      </td>
                      <td className='border-b border-gray-200 p-1 text-right font-mono'>
                        {toCurrency(emp.valorLiquidado)
                          .format()
                          .replace(/0$/, '')}
                      </td>
                      <td
                        className={`border-b border-gray-200 p-1 text-right font-mono ${
                          emp.saldoDisponivel < 0 ? 'text-red-600' : ''
                        }`}
                      >
                        {toCurrency(emp.saldoDisponivel)
                          .format()
                          .replace(/0$/, '')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
      </div>

      {/* Rodapé */}
      <div className='mt-8 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de grupos: {grupos.length}</p>
        <p>Total de empenhos: {totalGeral.totalEmpenhos}</p>
        <p className='mt-1'>{siteConfig.name}</p>
      </div>
    </div>
  );
};
