/** @type {import('next').NextConfig} */
// import nextra from 'nextra';

const nextConfig = {
  serverExternalPackages: [
    '@prisma/client',
    'crypto',
    'html-pdf-chrome',
    'node-fetch',
  ],
  experimental: {
    staleTimes: {
      dynamic: 300,
    },
  },
};

// const withNextra = require('nextra')({
//   theme: 'nextra-theme-docs',
//   themeConfig: './theme.config.tsx',
// });

// const withNextra = nextra({
//   // theme: 'nextra-theme-docs',
//   // themeConfig: './theme.config.tsx',
// });

// export default withNextra(nextConfig);

export default nextConfig;
