'use client';

import { createClient } from '@/lib/supabase/client';
import { useEffect } from 'react';
import { toast } from 'sonner';

export const IframePDFReserva = ({ url }: { url: string }) => {
  useEffect(() => {
    const carregarPDF = async () => {
      const supabase = createClient();
      const pdf = await supabase.storage.from('reservas').download(url);
      if (pdf.error) {
        return toast.error(
          'Falha ao carregar o documento, por favor recarregue a página.'
        );
      }
      const fileURL = URL.createObjectURL(pdf.data);

      const iframe = document.getElementById('documento') as HTMLIFrameElement;
      iframe.src = fileURL;
    };
    carregarPDF();
  }, [url]);
  return (
    <iframe id='documento' src={`/carregando`} className='h-[800px] w-full' />
  );
};
