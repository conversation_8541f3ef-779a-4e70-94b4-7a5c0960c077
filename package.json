{"name": "gestao-novo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore .", "postinstall": "prisma generate && npm run copy-tinymce", "copy-tinymce": "cp -r node_modules/tinymce/* public/tinymce/ 2>/dev/null || xcopy /E /I node_modules\\tinymce public\\tinymce\\ 2>nul || echo 'TinyMCE files copy skipped'"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@prisma/client": "^6.16.2", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-visually-hidden": "^1.2.3", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.4", "@tailwindcss/postcss": "^4.1.13", "@tanstack/react-table": "^8.21.3", "@tinymce/tinymce-react": "^6.3.0", "@types/node-forge": "^1.3.14", "@uppy/core": "^5.0.2", "@uppy/dashboard": "^5.0.2", "@uppy/drag-drop": "^5.0.2", "@uppy/locales": "^5.0.0", "@uppy/react": "^5.0.3", "@uppy/status-bar": "^5.0.1", "@uppy/tus": "^5.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "isomorphic-dompurify": "^2.28.0", "lucide-react": "^0.544.0", "next": "^15.5.4", "next-themes": "^0.4.6", "nextra": "^4.5.0", "nextra-theme-docs": "^4.5.0", "node-forge": "^1.3.1", "pdf-lib": "^1.17.1", "prettier-plugin-tailwindcss": "^0.6.14", "react": "^19", "react-day-picker": "^9.11.0", "react-dom": "^19", "react-hook-form": "^7.63.0", "react-wrap-balancer": "^1.1.1", "sharp": "^0.34.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tinymce": "^8.1.2", "tus-js-client": "^4.3.1", "zod": "^4.1.11"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.36.0", "@types/dompurify": "^3.2.0", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.44.1", "@typescript-eslint/parser": "^8.44.1", "autoprefixer": "^10.4.21", "encoding": "^0.1.13", "eslint": "^9.36.0", "eslint-config-next": "15.5.4", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-unused-imports": "^4.2.0", "postcss": "^8.5.6", "prettier": "3.6.2", "prisma": "^6.16.2", "tailwindcss": "^4.1.13", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}