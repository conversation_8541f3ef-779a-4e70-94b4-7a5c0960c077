-- CreateTable
CREATE TABLE "AlteracaoOrcamentaria" (
    "id" SERIAL NOT NULL,
    "codigo" SMALLINT NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "tipoAlteracao" SMALLINT NOT NULL,
    "tipoAcao" SMALLINT NOT NULL,
    "status" SMALLINT NOT NULL,
    "valorTotal" DECIMAL(18,2) NOT NULL,
    "valorMes1" DECIMAL(18,2) NOT NULL,
    "valorMes2" DECIMAL(18,2) NOT NULL,
    "valorMes3" DECIMAL(18,2) NOT NULL,
    "valorMes4" DECIMAL(18,2) NOT NULL,
    "valorMes5" DECIMAL(18,2) NOT NULL,
    "valorMes6" DECIMAL(18,2) NOT NULL,
    "valorMes7" DECIMAL(18,2) NOT NULL,
    "valorMes8" DECIMAL(18,2) NOT NULL,
    "valorMes9" DECIMAL(18,2) NOT NULL,
    "valorMes10" DECIMAL(18,2) NOT NULL,
    "valorMes11" DECIMAL(18,2) NOT NULL,
    "valorMes12" DECIMAL(18,2) NOT NULL,
    "dataAbertura" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dataStatus" TIMESTAMP(3) NOT NULL,
    "fonte" SMALLINT NOT NULL,
    "codAplicacao" INTEGER NOT NULL,
    "idSecretaria" INTEGER NOT NULL,
    "idDepto" INTEGER NOT NULL,
    "idEconomica" INTEGER NOT NULL,
    "idFuncional" INTEGER NOT NULL,
    "despesaCopia" INTEGER NOT NULL,
    "despesaAcao" INTEGER NOT NULL,
    "obs" TEXT NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "dataDecreto" TIMESTAMP(3) NOT NULL,
    "tipoDecreto" TEXT NOT NULL,
    "numDecreto" INTEGER NOT NULL,
    "despesaNova" INTEGER NOT NULL,
    "descrDespNova" TEXT NOT NULL,

    CONSTRAINT "AlteracaoOrcamentaria_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ControleAlteracaoOrcamentaria_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idAlterOrca" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ControleAlteracaoOrcamentaria_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cotas" (
    "id" SERIAL NOT NULL,
    "idDotacao" INTEGER NOT NULL,
    "exercicio" SMALLINT NOT NULL,
    "mes" SMALLINT NOT NULL,
    "valorInicial" DECIMAL(18,2) NOT NULL,
    "valorCota" DECIMAL(18,2) NOT NULL,
    "valorAtual" DECIMAL(18,2) NOT NULL,

    CONSTRAINT "cotas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cotas_audit" (
    "id" SERIAL NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "idCota" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "de" TEXT,
    "para" TEXT,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cotas_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AlteracaoOrcamentaria_codigo_key" ON "AlteracaoOrcamentaria"("codigo");

-- AddForeignKey
ALTER TABLE "AlteracaoOrcamentaria" ADD CONSTRAINT "AlteracaoOrcamentaria_idSecretaria_fkey" FOREIGN KEY ("idSecretaria") REFERENCES "secretarias"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AlteracaoOrcamentaria" ADD CONSTRAINT "AlteracaoOrcamentaria_idDepto_fkey" FOREIGN KEY ("idDepto") REFERENCES "departamentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AlteracaoOrcamentaria" ADD CONSTRAINT "AlteracaoOrcamentaria_idEconomica_fkey" FOREIGN KEY ("idEconomica") REFERENCES "economicas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AlteracaoOrcamentaria" ADD CONSTRAINT "AlteracaoOrcamentaria_idFuncional_fkey" FOREIGN KEY ("idFuncional") REFERENCES "funcionais"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AlteracaoOrcamentaria" ADD CONSTRAINT "AlteracaoOrcamentaria_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ControleAlteracaoOrcamentaria_audit" ADD CONSTRAINT "ControleAlteracaoOrcamentaria_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ControleAlteracaoOrcamentaria_audit" ADD CONSTRAINT "ControleAlteracaoOrcamentaria_audit_idAlterOrca_fkey" FOREIGN KEY ("idAlterOrca") REFERENCES "AlteracaoOrcamentaria"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cotas" ADD CONSTRAINT "cotas_idDotacao_fkey" FOREIGN KEY ("idDotacao") REFERENCES "dotacoes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cotas_audit" ADD CONSTRAINT "cotas_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cotas_audit" ADD CONSTRAINT "cotas_audit_idCota_fkey" FOREIGN KEY ("idCota") REFERENCES "cotas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
