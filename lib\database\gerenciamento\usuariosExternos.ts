'use server';

import { prisma } from '@/lib/prisma';
import { obterIpUsuarioConectado, temPermissao } from '../usuarios';
import { Modulos } from '@/lib/modulos';
import { AuditoriaGerenciamentoUsuarios, Permisso<PERSON> } from '@/lib/enums';
import {
  auditoriaErroSchema,
  editarUsuarioSchema,
  idSchema,
  permissaoSchema,
  usuarioExternoSchema,
} from '@/lib/validation';
import { createSuperClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { inserirErroAudit } from '../auditoria/erro';
import { uuidSchema } from '@/lib/validation';
import z from 'zod';

export const listarUsuariosExternos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  try {
    const usuarios = await prisma.external_users.findMany({
      orderBy: { nome: 'asc' },
    });
    return {
      data: usuarios,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter usuários externos.`,
    };
  }
};

export const obterUsuarioExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    permissao: Permissoes.ACESSAR,
  };

  const result = await temPermissao(parametrosPermissao);

  if (!result) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (result.error) {
    return {
      error: result.error,
    };
  }

  if (!result.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const usuario = await prisma.external_users.findUnique({
      where: {
        id: id,
      },
    });

    if (!usuario) {
      return {
        error: 'Usuário externo não encontrado.',
      };
    }

    return {
      data: usuario,
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter usuário externo.`,
    };
  }
};

export const criarUsuarioExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = usuarioExternoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }
  const { nome, email } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    const usuario = await prisma.external_users.findFirst({
      where: {
        email: email,
      },
    });
    if (usuario) {
      return {
        error: 'Email já cadastrado.',
      };
    }

    const supabase = createSuperClient();
    const convite = await supabase.auth.admin.inviteUserByEmail(email, {
      data: {
        user_type: 'external',
      },
    });
    if (convite.error || !convite.data) {
      return {
        error: 'Erro ao convidar usuário externo.',
      };
    }

    await prisma.$transaction(async (tx) => {
      const newUser = await tx.external_users.create({
        data: {
          user_id: convite.data.user.id,
          nome,
          email,
          ativo: true,
        },
      });

      await tx.external_users_audit.create({
        data: {
          usuarioId: newUser.id,
          acao: AuditoriaGerenciamentoUsuarios.CRIAR_USUARIO,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/usuarios-externos');
    return {
      data: true,
    };
  } catch (e: any) {
    console.error('Erro ao criar usuário externo:', e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    };
    await inserirErroAudit(auditData);
    return { error: 'Erro ao criar usuário externo.' };
  }
};

export const editarUsuarioExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = editarUsuarioSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id, nome } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.external_users.update({
        where: {
          id: id,
        },
        data: {
          nome,
        },
      });

      await tx.external_users_audit.create({
        data: {
          usuarioId: result.id,
          acao: AuditoriaGerenciamentoUsuarios.ALTERAR_CARGOS, // Using ALTERAR_CARGOS as a substitute for editing
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/usuarios-externos');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao editar usuário externo.`,
    };
  }
};

export const ativarUsuarioExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.external_users.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });
      await tx.external_users_audit.create({
        data: {
          usuarioId: result.id,
          acao: AuditoriaGerenciamentoUsuarios.ATIVAR_USUARIO,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/usuarios-externos');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao ativar usuário externo.`,
    };
  }
};

export const desativarUsuarioExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.external_users.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });
      await tx.external_users_audit.create({
        data: {
          usuarioId: result.id,
          acao: AuditoriaGerenciamentoUsuarios.DESATIVAR_USUARIO,
          ip,
        },
      });
    });

    revalidatePath('/gerenciamento/usuarios-externos');
    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar usuário externo.`,
    };
  }
};

export const atualizarMetadadoUsuarioExterno = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = uuidSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { uuid } = parsedParams.data;

  try {
    const supabase = createSuperClient();
    const { error } = await supabase.auth.admin.updateUserById(uuid, {
      user_metadata: { user_type: 'external' },
    });

    if (error) {
      throw new Error(
        `Erro ao atualizar metadados do usuário: ${error.message}`
      );
    }

    return {
      data: true,
    };
  } catch (e: unknown) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.GERENCIAMENTO_DE_USUARIOS_EXTERNOS,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao atualizar metadados do usuário externo.`,
    };
  }
};
