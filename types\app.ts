import { Unidades } from '@/lib/enums';

export interface ConfigModulo {
  nome: string;
  id: number;
  read: boolean;
  write: boolean;
  update: boolean;
  delete: boolean;
}

export interface Departamento {
  codigo: number;
  nome: string;
  subdepartamentos: Subdepartamento[];
}

export interface Subdepartamento {
  codigo: number;
  nome: string;
}

export interface AcessoCargo {
  id: number;
  codigo: string; //Código pré formatado
  nome: string;
}

export interface ConfigCargo {
  id: number;
  nome: string;
}

export interface Fornecedor {
  codigo: number;
  nome: string;
  cnpjCpf: string;
  email: string;
  phone: string;
}

export interface Item {
  quantidade: number;
  unidade: Unidades;
  desc: string;
  valor: number;
}

export interface ItemRelatorio {
  numero: number | null;
  quantidade: number | null;
  unidade: Unidades | null;
  desc: string | null;
  valor: number | null;
}
