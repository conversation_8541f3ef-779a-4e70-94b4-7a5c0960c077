'use client';

import { But<PERSON> } from '@/components/ui/button';
import { FileText } from 'lucide-react';
import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { listarProtocolos } from '@/lib/database/movimento/protocolos';
import { StatusProtocolo, StatusProtocoloDesc } from '@/lib/enums';
import { toast } from 'sonner';
import { vincularEmpenho } from '@/lib/database/movimento/protocolos';
import { useRouter } from 'next/navigation';

interface BotaoVincularEmpenhoProtocoloProps {
  empenhoId: number;
  exercicio: number;
  defaultOpen?: boolean;
  onClose?: () => void;
}

export function BotaoVincularEmpenhoProtocolo({
  empenhoId,
  exercicio,
  defaultOpen = false,
  onClose,
}: BotaoVincularEmpenhoProtocoloProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [isLoading, setIsLoading] = useState(false);
  const [protocolos, setProtocolos] = useState<any[]>([]);
  const [loadingProtocolos, setLoadingProtocolos] = useState(false);
  const router = useRouter();

  const loadProtocolos = async () => {
    setLoadingProtocolos(true);
    try {
      const result = await listarProtocolos();
      if (result.error) {
        toast.error(result.error);
        return;
      }

      // Filtrar apenas protocolos que podem ser vinculados:
      // - Do mesmo exercício
      // - Sem empenho vinculado
      // - Status ativos
      const protocolosFiltrados =
        result.data?.protocolos?.filter(
          (p: any) =>
            p.exercicio === exercicio &&
            !p.numeroEmpenho &&
            p.ativo &&
            p.status !== StatusProtocolo.Nenhum
        ) || [];

      if (protocolosFiltrados.length === 0) {
        console.log(
          'Nenhum protocolo encontrado para vinculação. Exercício do empenho:',
          exercicio
        );
        console.log(
          'Total de protocolos recebidos:',
          result.data?.protocolos?.length
        );
      }

      setProtocolos(protocolosFiltrados);
    } catch (error) {
      toast.error('Erro ao carregar protocolos');
    } finally {
      setLoadingProtocolos(false);
    }
  };

  const handleVincular = async (protocolo: any) => {
    setIsLoading(true);
    try {
      const result = await vincularEmpenho({
        id: protocolo.id,
        numeroEmpenho: empenhoId,
        exercicioEmpenho: exercicio,
      });

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Empenho vinculado ao protocolo com sucesso!');
        setIsOpen(false);
        router.refresh(); // Refresh para mostrar o protocolo vinculado
      }
    } catch (error) {
      toast.error('Erro ao vincular empenho ao protocolo');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open && onClose) {
      onClose();
    }
    if (open) {
      loadProtocolos();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button type='button' variant='outline'>
          <FileText className='mr-1 size-4' /> Vincular a Protocolo
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[800px]'>
        <DialogHeader>
          <DialogTitle>Vincular Empenho a Protocolo</DialogTitle>
          <DialogDescription>
            Selecione um protocolo existente para vincular ao empenho{' '}
            {empenhoId}.
          </DialogDescription>
        </DialogHeader>

        <div className='max-h-[400px] overflow-y-auto'>
          {loadingProtocolos ? (
            <div className='flex items-center justify-center py-8'>
              <div className='h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900'></div>
            </div>
          ) : protocolos.length === 0 ? (
            <div className='py-8 text-center text-gray-500'>
              Nenhum protocolo disponível para vinculação no exercício{' '}
              {exercicio}.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Número</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Resumo</TableHead>
                  <TableHead>Data Abertura</TableHead>
                  <TableHead>Ação</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {protocolos.map((protocolo) => (
                  <TableRow key={protocolo.id}>
                    <TableCell className='font-medium'>
                      {protocolo.numero}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          protocolo.status ===
                            StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_COMPRAS ||
                          protocolo.status ===
                            StatusProtocolo.DEVOLUCAO_DE_COMPRAS_PARA_FINANCAS ||
                          protocolo.status ===
                            StatusProtocolo.ENVIADO_DE_FINANCAS_PARA_GABINETE
                            ? 'default'
                            : 'secondary'
                        }
                      >
                        {
                          StatusProtocoloDesc[
                            protocolo.status as keyof typeof StatusProtocoloDesc
                          ]
                        }
                      </Badge>
                    </TableCell>
                    <TableCell className='max-w-xs truncate'>
                      {protocolo.resumo}
                    </TableCell>
                    <TableCell>
                      {new Date(protocolo.dataAbertura).toLocaleDateString(
                        'pt-BR'
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        size='sm'
                        onClick={() => handleVincular(protocolo)}
                        disabled={isLoading}
                      >
                        Vincular
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>

        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => setIsOpen(false)}
          >
            Cancelar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
