'use server';

import { prisma } from '../prisma';
import { obterUsuarioExternoPorUuidSchema } from '../validation';

export const obterUsuarioExternoPorUuid = async (params: unknown) => {
  const parsedParams = obterUsuarioExternoPorUuidSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: `Parâmetros inválidos: ${parsedParams.error.message}`,
    };
  }
  const { uuid } = parsedParams.data;

  try {
    const user = await prisma.external_users.findUnique({
      where: {
        user_id: uuid,
      },
    });
    if (!user) {
      return { error: 'Usuário externo não encontrado.' };
    }
    return { data: user };
  } catch (e: any) {
    console.error('Erro ao obter usuário externo por UUID:', e.message);
    return { error: 'Erro ao obter usuário externo.' };
  }
};
