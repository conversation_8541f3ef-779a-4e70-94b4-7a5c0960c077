/*
  Warnings:

  - You are about to drop the column `cargos_acessoId` on the `gerenciamentoCargos_audit` table. All the data in the column will be lost.
  - You are about to drop the column `cargos_permissoesId` on the `gerenciamentoCargos_audit` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "gerenciamentoCargos_audit" DROP CONSTRAINT "gerenciamentoCargos_audit_cargos_acessoId_fkey";

-- DropForeignKey
ALTER TABLE "gerenciamentoCargos_audit" DROP CONSTRAINT "gerenciamentoCargos_audit_cargos_permissoesId_fkey";

-- AlterTable
ALTER TABLE "gerenciamentoCargos_audit" DROP COLUMN "cargos_acessoId",
DROP COLUMN "cargos_permissoesId";

-- CreateTable
CREATE TABLE "cargos_permissoes_audit" (
    "id" SERIAL NOT NULL,
    "usuarioId" INTEGER NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "moduloId" INTEGER NOT NULL,
    "read" BOOLEAN NOT NULL,
    "write" BOOLEAN NOT NULL,
    "update" BOOLEAN NOT NULL,
    "delete" BOOLEAN NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cargos_permissoes_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cargos_acessos_audit" (
    "id" SERIAL NOT NULL,
    "usuarioId" INTEGER NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "secretariaId" INTEGER,
    "departamentoId" INTEGER,
    "subdepartamentoId" INTEGER,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cargos_acessos_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "erros_audit" (
    "id" SERIAL NOT NULL,
    "usuarioId" INTEGER,
    "moduloId" INTEGER,
    "erro" TEXT NOT NULL,
    "ip" INET,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "erros_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "cargos_permissoes_audit" ADD CONSTRAINT "cargos_permissoes_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_permissoes_audit" ADD CONSTRAINT "cargos_permissoes_audit_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos_audit" ADD CONSTRAINT "cargos_acessos_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos_audit" ADD CONSTRAINT "cargos_acessos_audit_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos_audit" ADD CONSTRAINT "cargos_acessos_audit_secretariaId_fkey" FOREIGN KEY ("secretariaId") REFERENCES "secretarias"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos_audit" ADD CONSTRAINT "cargos_acessos_audit_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "departamentos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos_audit" ADD CONSTRAINT "cargos_acessos_audit_subdepartamentoId_fkey" FOREIGN KEY ("subdepartamentoId") REFERENCES "subdepartamentos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "erros_audit" ADD CONSTRAINT "erros_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
