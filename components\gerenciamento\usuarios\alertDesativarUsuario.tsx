import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { desativarUsuario } from '@/lib/database/gerenciamento/usuarios';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Ban } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertDesativarUsuario({
  idUsuario,
  nomeUsuario,
}: {
  idUsuario: number;
  nomeUsuario: string;
}) {
  const [loading, setLoading] = useState(false);

  const desativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idUsuario,
      };

      const res = await desativarUsuario(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Usuário desativado.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline' className='text-red-800'>
          <Ban className='mr-2 size-4' /> Desativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Desativar Usuário?</AlertDialogTitle>
          <AlertDialogDescription>
            O usuário <span className='font-bold'>{nomeUsuario}</span> será
            desativado.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              desativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Desativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
