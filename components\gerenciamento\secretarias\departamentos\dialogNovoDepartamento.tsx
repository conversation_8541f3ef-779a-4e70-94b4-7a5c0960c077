'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import {
  novoDepartamento,
  revalidateDepartamentos,
} from '@/lib/database/gerenciamento/departamentos';
import { uppercaseMask, toastAlgoDeuErrado } from '@/lib/utils';
import { novoDepartamentoSchema } from '@/lib/validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, PlusCircle } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

export function DialogNovoDepartamento({
  idSecretaria,
}: {
  idSecretaria: number;
}) {
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof novoDepartamentoSchema>>({
    resolver: zodResolver(novoDepartamentoSchema),
    defaultValues: {
      //@ts-ignore
      codigo: '',
      nome: '',
      secretariaId: idSecretaria,
    },
  });

  const onSubmit = async (values: z.infer<typeof novoDepartamentoSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof novoDepartamentoSchema> = {
        codigo: values.codigo,
        nome: values.nome,
        secretariaId: idSecretaria,
      };
      const res = await novoDepartamento(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        await revalidateDepartamentos({
          id: idSecretaria,
        });
        toast.success('Departamento criado.');
        setOpen(false);
        setLoading(false);
        form.reset();
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          Novo Departamento <PlusCircle className='ml-2 h-4 w-4' />
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Novo Departamento</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex flex-wrap gap-4 py-4'>
              <FormField
                control={form.control}
                name='codigo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex w-full text-left'>
                      Código
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        pattern='[0-99]*'
                        placeholder='00'
                        maxLength={2}
                        className='w-[44px]'
                        onKeyDown={(event) => {
                          if (event.key === 'Enter') {
                            event.preventDefault();
                            form.handleSubmit(onSubmit)();
                          }
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='nome'
                render={({ field }) => (
                  <FormItem className='w-[315px]'>
                    <FormLabel className='flex w-full text-left'>
                      Nome
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={150}
                        minLength={2}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('nome', uppercaseMask(value));
                        }}
                        onKeyDown={(event) => {
                          if (event.key === 'Enter') {
                            event.preventDefault();
                            form.handleSubmit(onSubmit)();
                          }
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogFooter>
          <Button
            type='submit'
            disabled={loading}
            onClick={form.handleSubmit(onSubmit)}
          >
            {loading ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <PlusCircle className='mr-2 h-4 w-4' /> Criar
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
