import { Decimal } from '@prisma/client/runtime/library';
import currency from 'currency.js';
import {
  currencyOptionsNoSymbol,
  currencyOptionsNoSymbol4Decimals,
} from './utils';

//usando server pq tipo Decimal não será enviado para o client

const isDecimal = (value: unknown) => {
  return (
    value !== null &&
    typeof value === 'object' &&
    (value as Decimal).add !== undefined
  );
};

export const toCurrency = (number: string | number | Decimal) => {
  if (isDecimal(number))
    return currency((number as Decimal).toNumber(), currencyOptionsNoSymbol);

  if (typeof number === 'number')
    return currency(number as number, currencyOptionsNoSymbol);

  if (typeof number === 'string')
    return currency(number as string, currencyOptionsNoSymbol);

  return currency(Number(number), currencyOptionsNoSymbol);
};

export const toCurrency4Decimals = (number: string | number | Decimal) => {
  if (isDecimal(number))
    return currency(
      (number as Decimal).toNumber(),
      currencyOptionsNoSymbol4Decimals
    );

  if (typeof number === 'number')
    return currency(number as number, currencyOptionsNoSymbol4Decimals);

  if (typeof number === 'string')
    return currency(number as string, currencyOptionsNoSymbol4Decimals);

  return currency(Number(number), currencyOptionsNoSymbol4Decimals);
};
