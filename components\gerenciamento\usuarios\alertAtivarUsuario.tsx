import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ativarUsuario } from '@/lib/database/gerenciamento/usuarios';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { Check } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function AlertAtivarUsuario({
  idUsuario,
  nomeUsuario,
}: {
  idUsuario: number;
  nomeUsuario: string;
}) {
  const [loading, setLoading] = useState(false);

  const ativar = async () => {
    try {
      setLoading(true);

      const data: z.infer<typeof idSchema> = {
        id: idUsuario,
      };

      const res = await ativarUsuario(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Usuario ativado.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type='button'
          variant='outline'
          className='min-w-[115px] text-green-800'
        >
          <Check className='mr-2 size-4' /> Ativar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Ativar Usuário?</AlertDialogTitle>
          <AlertDialogDescription>
            O usuário <span className='font-bold'>{nomeUsuario}</span> será
            ativado.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              ativar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Ativar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
