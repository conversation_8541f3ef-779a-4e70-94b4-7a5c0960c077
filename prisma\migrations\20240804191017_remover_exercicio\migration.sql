/*
  Warnings:

  - You are about to drop the column `exercicio` on the `acoes` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `departamentos` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `economicas` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `fontes` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `fornecedores` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `funcionais` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `funcoes` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `programas` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `secretarias` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `subdepartamentos` table. All the data in the column will be lost.
  - You are about to drop the column `exercicio` on the `subfuncoes` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[codigo]` on the table `acoes` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[codigo]` on the table `economicas` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[codigo]` on the table `fontes` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[codigo]` on the table `fornecedores` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[funcaoId,subfuncaoId,programaId,acaoId]` on the table `funcionais` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[codigo]` on the table `funcoes` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[codigo]` on the table `programas` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[codigo]` on the table `subfuncoes` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "acoes_codigo_exercicio_key";

-- DropIndex
DROP INDEX "economicas_exercicio_codigo_key";

-- DropIndex
DROP INDEX "fontes_codigo_exercicio_key";

-- DropIndex
DROP INDEX "fornecedores_codigo_exercicio_key";

-- DropIndex
DROP INDEX "funcionais_exercicio_funcaoId_subfuncaoId_programaId_acaoId_key";

-- DropIndex
DROP INDEX "funcoes_codigo_exercicio_key";

-- DropIndex
DROP INDEX "programas_codigo_exercicio_key";

-- DropIndex
DROP INDEX "subfuncoes_codigo_exercicio_key";

-- AlterTable
ALTER TABLE "acoes" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "departamentos" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "economicas" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "fontes" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "fornecedores" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "funcionais" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "funcoes" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "programas" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "secretarias" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "subdepartamentos" DROP COLUMN "exercicio";

-- AlterTable
ALTER TABLE "subfuncoes" DROP COLUMN "exercicio";

-- CreateIndex
CREATE UNIQUE INDEX "acoes_codigo_key" ON "acoes"("codigo");

-- CreateIndex
CREATE UNIQUE INDEX "economicas_codigo_key" ON "economicas"("codigo");

-- CreateIndex
CREATE UNIQUE INDEX "fontes_codigo_key" ON "fontes"("codigo");

-- CreateIndex
CREATE UNIQUE INDEX "fornecedores_codigo_key" ON "fornecedores"("codigo");

-- CreateIndex
CREATE UNIQUE INDEX "funcionais_funcaoId_subfuncaoId_programaId_acaoId_key" ON "funcionais"("funcaoId", "subfuncaoId", "programaId", "acaoId");

-- CreateIndex
CREATE UNIQUE INDEX "funcoes_codigo_key" ON "funcoes"("codigo");

-- CreateIndex
CREATE UNIQUE INDEX "programas_codigo_key" ON "programas"("codigo");

-- CreateIndex
CREATE UNIQUE INDEX "subfuncoes_codigo_key" ON "subfuncoes"("codigo");
