'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save } from 'lucide-react';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { z } from 'zod';

interface Empenho {
  id: number;
  numero: number;
  data: string;
  resumo: string;
  valorTotal: number;
  status: number;
  saldo: number;
  dotacao: {
    despesa: string;
    secretaria: {
      id: number;
      nome: string;
    };
    departamento: {
      id: number;
      nome: string;
    };
  };
  fornecedor: {
    id: number;
    nome: string;
  };
}

interface AF {
  id?: number;
  numero?: string;
  data?: string;
  resumo?: string;
  obs?: string;
  valorTotal?: number;
  valorUtilizado?: number;
  status?: number;
  empenho?: Empenho;
}

interface AFFormProps {
  aF?: AF;
  empenhos: Empenho[];
  onSubmit: (data: any) => Promise<void>;
  isEditing?: boolean;
}

export function AFForm({
  aF,
  empenhos,
  onSubmit,
  isEditing = false,
}: AFFormProps) {
  const [selectedEmpenho, setSelectedEmpenho] = useState<Empenho | null>(
    aF?.empenho || null
  );
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const formSchema = z
    .object({
      id: z.coerce.number().int().min(1).optional(),
      idEmpenho: z.coerce.number().int().min(1).optional(),
      resumo: z.string().min(2).max(255).optional(),
      obs: z.string().max(500).optional(),
      valorTotal: z.coerce
        .number()
        .positive('O valor da AF deve ser positivo.'),
    })
    .refine((data) => data.id || data.idEmpenho, {
      message: 'ID ou ID do Empenho é obrigatório',
      path: ['idEmpenho'],
    });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: aF?.id || 0,
      idEmpenho: aF?.empenho?.id || 0,
      resumo: aF?.resumo || '',
      obs: aF?.obs || '',
      // @ts-ignore
      valorTotal: aF?.valorTotal?.toString() || '',
    },
  });

  useEffect(() => {
    if (aF?.empenho) {
      setSelectedEmpenho(aF.empenho);
    }
  }, [aF]);

  const handleEmpenhoChange = (empenhoId: string) => {
    const empenho = empenhos.find((e) => e.id === parseInt(empenhoId));
    setSelectedEmpenho(empenho || null);
    if (!isEditing) {
      form.setValue('idEmpenho', parseInt(empenhoId));
    }
  };

  const handleSubmit = async (data: any) => {
    if (!selectedEmpenho) {
      toast.error('Selecione um empenho válido');
      return;
    }

    setIsLoading(true);
    try {
      const valorTotal = currency(data.valorTotal, currencyOptionsNoSymbol);
      const saldoDisponivel = currency(
        selectedEmpenho.saldo,
        currencyOptionsNoSymbol
      );

      if (valorTotal.value > saldoDisponivel.value) {
        toast.error(
          'O valor da AF não pode exceder o saldo disponível do empenho'
        );
        return;
      }

      const submitData: any = {
        valorTotal: valorTotal.value,
        resumo: data.resumo,
        obs: data.obs,
      };

      if (isEditing) {
        submitData.id = data.id;
      } else {
        submitData.idEmpenho = data.idEmpenho;
      }

      await onSubmit(submitData);

      toast.success(
        isEditing ? 'AF atualizada com sucesso!' : 'AF criada com sucesso!'
      );
      router.push('/movimento/af');
    } catch (error: any) {
      toast.error(error.message || 'Erro ao salvar AF');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='mx-auto max-w-4xl p-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Link href='/movimento/af'>
                <Button variant='outline' size='sm'>
                  <ArrowLeft className='h-4 w-4' />
                </Button>
              </Link>
              {isEditing ? 'Editar AF' : 'Nova AF'}
            </div>
            {aF?.numero && <Badge variant='outline'>AF {aF.numero}</Badge>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className='space-y-6'
            >
              <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                {!isEditing ? (
                  <FormField
                    control={form.control}
                    name='idEmpenho'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Empenho *</FormLabel>
                        <Select
                          onValueChange={handleEmpenhoChange}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Selecione o empenho' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {empenhos.map((empenho) => (
                              <SelectItem
                                key={empenho.id}
                                value={empenho.id.toString()}
                              >
                                #{empenho.id} - {empenho.resumo}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ) : selectedEmpenho ? (
                  <FormItem>
                    <FormLabel>Empenho</FormLabel>
                    <Input
                      value={`#${selectedEmpenho.id} - ${selectedEmpenho.resumo}`}
                      disabled
                    />
                  </FormItem>
                ) : null}

                <FormField
                  control={form.control}
                  name='valorTotal'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor Total *</FormLabel>
                      <FormControl>
                        <Input
                          type='text'
                          placeholder='0,00'
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                              .replace(/[^\d,]/g, '')
                              .replace(',', '.');
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {selectedEmpenho && (
                <Card className='bg-gray-50'>
                  <CardHeader>
                    <CardTitle className='text-sm'>
                      Informações do Empenho
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
                      <div>
                        <strong>Empenho:</strong> #{selectedEmpenho.id}
                      </div>
                      <div>
                        <strong>Data:</strong>{' '}
                        {new Date(selectedEmpenho.data).toLocaleDateString(
                          'pt-BR'
                        )}
                      </div>
                      <div>
                        <strong>Saldo Disponível:</strong>{' '}
                        {currency(
                          selectedEmpenho.saldo,
                          currencyOptionsNoSymbol
                        ).format()}
                      </div>
                      <div>
                        <strong>Fornecedor:</strong>{' '}
                        {selectedEmpenho.fornecedor.nome}
                      </div>
                      <div className='md:col-span-2'>
                        <strong>Despesa:</strong>{' '}
                        {selectedEmpenho.dotacao.despesa}
                      </div>
                      <div className='md:col-span-2'>
                        <strong>Resumo:</strong> {selectedEmpenho.resumo}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <FormField
                control={form.control}
                name='resumo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Resumo</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Descreva o objetivo da AF...'
                        className='min-h-[100px]'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='obs'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observação</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Observações adicionais...'
                        className='min-h-[80px]'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='flex justify-end gap-4'>
                <Link href='/movimento/af'>
                  <Button variant='outline' disabled={isLoading}>
                    Cancelar
                  </Button>
                </Link>
                <Button type='submit' disabled={isLoading}>
                  <Save className='mr-2 h-4 w-4' />
                  {isLoading ? 'Salvando...' : 'Salvar'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
