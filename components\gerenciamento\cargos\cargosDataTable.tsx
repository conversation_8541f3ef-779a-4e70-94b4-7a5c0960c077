'use client';
import { ColumnDef } from '@tanstack/react-table';
import { Pencil } from 'lucide-react';
import { listarCargos } from '@/lib/database/gerenciamento/cargos';
import { ReusableDatatable } from '@/components/datatable/reusableDatatable';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { AlertDesativarCargo } from './alertDesativarCargo';
import { AlertAtivarCargo } from './alertAtivarCargo';

export default function CargosDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarCargos>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'nome',
      header: 'Cargo',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'id',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <Link href={`/gerenciamento/cargos/editar/${row.original.id}`}>
            <Button type='button' variant='outline'>
              <Pencil className='mr-2 size-4' /> Editar
            </Button>
          </Link>
          {row.original.ativo ? (
            <AlertDesativarCargo
              idCargo={row.original.id}
              nomeCargo={row.original.nome}
            />
          ) : (
            <AlertAtivarCargo
              idCargo={row.original.id}
              nomeCargo={row.original.nome}
            />
          )}
        </div>
      ),
    },
  ];
  return (
    <div className='container mx-auto'>
      <ReusableDatatable columns={columns} data={data.data} />
    </div>
  );
}
