-- CreateTable
CREATE TABLE "documentos_externos" (
    "id" SERIAL NOT NULL,
    "idOrigemTipo" INTEGER NOT NULL,
    "idOrigemRegistro" INTEGER NOT NULL,
    "idUsuarioExterno" INTEGER NOT NULL,
    "idUsuarioSolicitante" INTEGER NOT NULL,
    "nomeDocumento" TEXT NOT NULL,
    "descricaoDocumento" TEXT,
    "instrucoes" TEXT,
    "pathDocumentoOriginal" TEXT NOT NULL,
    "pathDocumentoAssinado" TEXT,
    "status" SMALLINT NOT NULL,
    "motivoCancelamento" TEXT,
    "dataCriacao" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dataAssinatura" TIMESTAMP,
    "ativo" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "documentos_externos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "documentos_externos_audit" (
    "id" SERIAL NOT NULL,
    "idDocumentoExterno" INTEGER NOT NULL,
    "idUsuario" INTEGER,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "documentos_externos_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "templates_documentos" (
    "id" SERIAL NOT NULL,
    "nome" TEXT NOT NULL,
    "descricao" TEXT,
    "conteudoHtml" TEXT NOT NULL,
    "ativo" BOOLEAN NOT NULL DEFAULT true,
    "dataCriacao" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "templates_documentos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "templates_documentos_audit" (
    "id" SERIAL NOT NULL,
    "idTemplate" INTEGER NOT NULL,
    "idUsuario" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "templates_documentos_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "external_users_documentos" (
    "id" SERIAL NOT NULL,
    "idUsuarioExterno" INTEGER NOT NULL,
    "idDocumentoExterno" INTEGER NOT NULL,

    CONSTRAINT "external_users_documentos_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "external_users_documentos_idUsuarioExterno_idDocumentoExter_key" ON "external_users_documentos"("idUsuarioExterno", "idDocumentoExterno");

-- AddForeignKey
ALTER TABLE "documentos_externos" ADD CONSTRAINT "documentos_externos_idUsuarioExterno_fkey" FOREIGN KEY ("idUsuarioExterno") REFERENCES "external_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "documentos_externos" ADD CONSTRAINT "documentos_externos_idUsuarioSolicitante_fkey" FOREIGN KEY ("idUsuarioSolicitante") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "documentos_externos_audit" ADD CONSTRAINT "documentos_externos_audit_idDocumentoExterno_fkey" FOREIGN KEY ("idDocumentoExterno") REFERENCES "documentos_externos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "documentos_externos_audit" ADD CONSTRAINT "documentos_externos_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "templates_documentos_audit" ADD CONSTRAINT "templates_documentos_audit_idTemplate_fkey" FOREIGN KEY ("idTemplate") REFERENCES "templates_documentos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "templates_documentos_audit" ADD CONSTRAINT "templates_documentos_audit_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "external_users_documentos" ADD CONSTRAINT "external_users_documentos_idUsuarioExterno_fkey" FOREIGN KEY ("idUsuarioExterno") REFERENCES "external_users"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "external_users_documentos" ADD CONSTRAINT "external_users_documentos_idDocumentoExterno_fkey" FOREIGN KEY ("idDocumentoExterno") REFERENCES "documentos_externos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
