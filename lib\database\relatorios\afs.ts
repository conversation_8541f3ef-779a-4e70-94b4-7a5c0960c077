'use server';

import { prisma } from '@/lib/prisma';
import { Permisso<PERSON> } from '@/lib/enums';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { temPermissao } from '@/lib/database/usuarios';
import { Prisma } from '@prisma/client';
import currency from 'currency.js';
import { currencyOptionsNoSymbol } from '@/lib/utils';
import { listarDocumentosPorFornecedorContrato } from '@/lib/database/movimento/liquidacoes';

export interface AFReportData {
  id: number;
  exercicio: number;
  numero: number;
  resumo: string | null;
  obs: string | null;
  valorTotal: number;
  status: number;
  data: Date;
  dataEmissao: Date | null;
  dataVencimento: Date | null;
  dataUtilizacao: Date | null;
  dataCancelamento: Date | null;
  dataReativacao: Date | null;
  empenho: {
    id: number;
    numero: number;
    exercicio: number;
    resumo: string | null;
    valorTotal: number;
    fornecedor: {
      id: number;
      nome: string;
    } | null;
    contrato: {
      id: number;
      exercicio: number;
      processo: number;
      processoAno: number;
      valor: number;
      nomeGestor: string;
      dataInicio: Date;
      dataFinal: Date;
    } | null;
    dotacao: {
      id: number;
      despesa: number;
      desc: string;
      secretaria: {
        id: number;
        nome: string;
      } | null;
      departamento: {
        id: number;
        nome: string;
      } | null;
      subdepartamento: {
        id: number;
        nome: string;
      } | null;
      economica: {
        id: number;
        codigo: string;
        descricao: string;
      } | null;
      funcional: {
        id: number;
        codigo: string;
        descricao: string;
      } | null;
    } | null;
  };
  liquidacoes: Array<{
    id: number;
    numero: number;
    exercicio: number;
    valorTotal: number;
    dataLiquidacao: Date;
    mesReferencia: string | null;
    resumo: string | null;
    obs: string | null;
    tipo: 'DIRETA' | 'CROSS';
    empenhoOrigem: string;
    marca_documento?: string | null;
    documento?: string | null;
  }>;
  liquidacoesCrossEmpenho: Array<{
    id: number;
    numero: number;
    exercicio: number;
    valorTotal: number;
    dataLiquidacao: Date;
    mesReferencia: string | null;
    resumo: string | null;
    obs: string | null;
    empenhoOrigem: string;
    marca_documento?: string | null;
    documento?: string | null;
  }>;
  afs_documentos: Array<{
    id: number;
    tipoDocumento: number;
    nomeArquivo: string;
    dataUpload: Date;
  }>;
  auditoria: Array<{
    id: number;
    acao: number;
    data: Date;
    obs: string | null;
    usuario: {
      id: number;
      nome: string;
    } | null;
  }>;
}

export interface SaldoDisponivelAFData {
  valorTotalAF: number;
  valorUtilizado: number;
  valorDisponivel: number;
  valorTotalLiquidado: number;
  valorTotalCrossEmpenho: number;
}

export const obterAFParaRelatorio = async (params: {
  id: number;
  bearer?: string;
}) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_AFS,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao({
    ...parametrosPermissao,
    bearer: params.bearer || '',
  });
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const af = await prisma.afs.findUnique({
      where: { id: params.id, ativo: true },
      select: {
        id: true,
        exercicio: true,
        numero: true,
        resumo: true,
        obs: true,
        valorTotal: true,
        status: true,
        data: true,
        dataEmissao: true,
        dataVencimento: true,
        dataUtilizacao: true,
        dataCancelamento: true,
        dataReativacao: true,
        empenho: {
          select: {
            id: true,
            exercicio: true,
            numero: true,
            resumo: true,
            valorTotal: true,
            idFornecedor: true,
            idContrato: true,
            fornecedor: {
              select: {
                id: true,
                nome: true,
              },
            },
            contrato: {
              select: {
                id: true,
                exercicio: true,
                processo: true,
                processoAno: true,
                valor: true,
                nomeGestor: true,
                dataInicio: true,
                dataFinal: true,
              },
            },
            dotacao: {
              select: {
                id: true,
                despesa: true,
                desc: true,
                secretariaId: true,
                departamentoId: true,
                subdepartamentoId: true,
                economicaId: true,
                funcionalId: true,
                secretaria: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
                departamento: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
                subdepartamento: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
                economica: {
                  select: {
                    id: true,
                    codigo: true,
                    desc: true,
                  },
                },
                funcional: {
                  select: {
                    id: true,
                    codigo: true,
                    desc: true,
                  },
                },
              },
            },
            liquidacoes: {
              where: { ativo: true },
              orderBy: { data: 'asc' },
              select: {
                id: true,
                numero: true,
                exercicio: true,
                valorTotal: true,
                data: true,
                mesReferencia: true,
                resumo: true,
                obs: true,
                marca_documento: true,
                liquidacoes_documentos: {
                  where: { ativo: true },
                  select: {
                    numeroDocumento: true,
                    dataEmissao: true,
                    valorDocumento: true,
                  },
                },
              },
            },
          },
        },
        afs_documentos: {
          where: { ativo: true },
          orderBy: { dataUpload: 'desc' },
          select: {
            id: true,
            tipoDocumento: true,
            nomeArquivo: true,
            dataUpload: true,
          },
        },
        afs_audit: {
          orderBy: { data: 'desc' },
          select: {
            id: true,
            acao: true,
            data: true,
            obs: true,
            usuario: {
              select: {
                id: true,
                nome: true,
              },
            },
          },
        },
      },
    });

    if (!af) return { error: 'AF não encontrada.' };

    // Get cross-empenho liquidations
    let liquidacoesCrossEmpenho: any[] = [];
    if (af.empenho.contrato && af.empenho.fornecedor) {
      // Get marked document numbers and related documents
      const documentosMarcados = af.empenho.liquidacoes
        .filter((liq) => liq.marca_documento)
        .map((liq) => liq.marca_documento)
        .filter(
          (marca): marca is string => marca !== null && marca !== undefined
        );

      const documentosRelacionados = af.empenho.liquidacoes.flatMap((liq) =>
        liq.liquidacoes_documentos.map((doc) => doc.numeroDocumento)
      );

      const todosDocumentos = [
        ...documentosMarcados,
        ...documentosRelacionados,
      ].filter((doc, index, self) => self.indexOf(doc) === index);

      if (todosDocumentos.length > 0) {
        const crossResult = await listarDocumentosPorFornecedorContrato({
          numerosDocumentos: todosDocumentos,
          idFornecedor: af.empenho.fornecedor.id,
          idContrato: af.empenho.contrato.id,
          empenhoExcluir: af.empenho.numero,
          exercicioExcluir: af.empenho.exercicio,
        });

        if (!crossResult.error && crossResult.data) {
          liquidacoesCrossEmpenho = crossResult.data;
        }
      }
    }

    // Process liquidations (combine direct and cross-empenho)
    const todasLiquidacoes = [
      ...af.empenho.liquidacoes.map((liq) => ({
        ...liq,
        tipo: 'DIRETA' as const,
        empenhoOrigem: `${af.empenho.numero}/${af.empenho.exercicio}`,
        documento:
          liq.marca_documento ||
          liq.liquidacoes_documentos[0]?.numeroDocumento ||
          `Nº ${liq.numero}/${liq.exercicio}`,
      })),
      ...liquidacoesCrossEmpenho.map((liq) => ({
        ...liq,
        tipo: 'CROSS' as const,
        empenhoOrigem: `${liq.empenho.numero}/${liq.empenho.exercicio}`,
        documento:
          liq.marca_documento ||
          liq.liquidacoes_documentos[0]?.numeroDocumento ||
          `Nº ${liq.numero}/${liq.exercicio}`,
      })),
    ];

    // Process data for report
    const reportData: AFReportData = {
      id: af.id,
      exercicio: af.exercicio,
      numero: af.numero,
      resumo: af.resumo,
      obs: af.obs,
      valorTotal: af.valorTotal.toNumber(),
      status: af.status,
      data: af.data,
      dataEmissao: af.dataEmissao,
      dataVencimento: af.dataVencimento,
      dataUtilizacao: af.dataUtilizacao,
      dataCancelamento: af.dataCancelamento,
      dataReativacao: af.dataReativacao,
      empenho: {
        id: af.empenho.id,
        numero: af.empenho.numero,
        exercicio: af.empenho.exercicio,
        resumo: af.empenho.resumo,
        valorTotal: af.empenho.valorTotal.toNumber(),
        fornecedor: af.empenho.fornecedor
          ? {
              id: af.empenho.fornecedor.id,
              nome: af.empenho.fornecedor.nome,
            }
          : null,
        contrato: af.empenho.contrato
          ? {
              id: af.empenho.contrato.id,
              exercicio: af.empenho.contrato.exercicio,
              processo: af.empenho.contrato.processo,
              processoAno: af.empenho.contrato.processoAno,
              valor: af.empenho.contrato.valor.toNumber(),
              nomeGestor: af.empenho.contrato.nomeGestor,
              dataInicio: af.empenho.contrato.dataInicio,
              dataFinal: af.empenho.contrato.dataFinal,
            }
          : null,
        dotacao: af.empenho.dotacao
          ? {
              id: af.empenho.dotacao.id,
              despesa: af.empenho.dotacao.despesa,
              desc: af.empenho.dotacao.desc,
              secretaria: af.empenho.dotacao.secretaria
                ? {
                    id: af.empenho.dotacao.secretaria.id,
                    nome: af.empenho.dotacao.secretaria.nome,
                  }
                : null,
              departamento: af.empenho.dotacao.departamento
                ? {
                    id: af.empenho.dotacao.departamento.id,
                    nome: af.empenho.dotacao.departamento.nome,
                  }
                : null,
              subdepartamento: af.empenho.dotacao.subdepartamento
                ? {
                    id: af.empenho.dotacao.subdepartamento.id,
                    nome: af.empenho.dotacao.subdepartamento.nome,
                  }
                : null,
              economica: af.empenho.dotacao.economica
                ? {
                    id: af.empenho.dotacao.economica.id,
                    codigo: af.empenho.dotacao.economica.codigo,
                    descricao: af.empenho.dotacao.economica.desc,
                  }
                : null,
              funcional: af.empenho.dotacao.funcional
                ? {
                    id: af.empenho.dotacao.funcional.id,
                    codigo: af.empenho.dotacao.funcional.codigo,
                    descricao: af.empenho.dotacao.funcional.desc,
                  }
                : null,
            }
          : null,
      },
      liquidacoes: todasLiquidacoes.map((liq) => ({
        id: liq.id,
        numero: liq.numero,
        exercicio: liq.exercicio,
        valorTotal: liq.valorTotal.toNumber(),
        dataLiquidacao: liq.data,
        mesReferencia: liq.mesReferencia,
        resumo: liq.resumo,
        obs: liq.obs,
        tipo: liq.tipo,
        empenhoOrigem: liq.empenhoOrigem,
        marca_documento: liq.marca_documento,
        documento: liq.documento,
      })),
      liquidacoesCrossEmpenho: liquidacoesCrossEmpenho.map((liq) => ({
        id: liq.id,
        numero: liq.numero,
        exercicio: liq.exercicio,
        valorTotal: liq.valorTotal.toNumber(),
        dataLiquidacao: liq.data,
        mesReferencia: liq.mesReferencia,
        resumo: liq.resumo,
        obs: liq.obs,
        empenhoOrigem: `${liq.empenho.numero}/${liq.empenho.exercicio}`,
        marca_documento: liq.marca_documento,
        documento:
          liq.marca_documento ||
          liq.liquidacoes_documentos[0]?.numeroDocumento ||
          `Nº ${liq.numero}/${liq.exercicio}`,
      })),
      afs_documentos: af.afs_documentos.map((doc) => ({
        id: doc.id,
        tipoDocumento: doc.tipoDocumento,
        nomeArquivo: doc.nomeArquivo,
        dataUpload: doc.dataUpload,
      })),
      auditoria: af.afs_audit.map((audit) => ({
        id: audit.id,
        acao: audit.acao,
        data: audit.data,
        obs: audit.obs,
        usuario: audit.usuario
          ? {
              id: audit.usuario.id,
              nome: audit.usuario.nome,
            }
          : null,
      })),
    };

    return { data: reportData };
  } catch (e) {
    console.error('Erro ao obter AF para relatório:', e);
    return { error: 'Erro ao obter AF para relatório.' };
  }
};

export const listarAFsParaRelatorio = async (params: {
  exercicio?: number;
  idSecretaria?: number;
  idDepartamento?: number;
  idSubdepartamento?: number;
  status?: number;
  dataInicio?: Date;
  dataFim?: Date;
  bearer?: string;
}) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_AFS,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao({
    ...parametrosPermissao,
    bearer: params.bearer || '',
  });
  if (!resultPermissao)
    return { error: 'Não foi possível verificar as permissões do usuário.' };
  if (resultPermissao.error) return { error: resultPermissao.error };
  if (!resultPermissao.temPermissao) return { error: 'Usuário sem permissão.' };

  try {
    const where: Prisma.afsWhereInput = { ativo: true };

    if (params.exercicio) where.exercicio = params.exercicio;
    else if (resultPermissao.exercicio)
      where.exercicio = resultPermissao.exercicio;

    if (params.status !== undefined) where.status = params.status;

    if (params.dataInicio || params.dataFim) {
      where.data = {};
      if (params.dataInicio) where.data.gte = params.dataInicio;
      if (params.dataFim) where.data.lte = params.dataFim;
    }

    const afs = await prisma.afs.findMany({
      where,
      select: {
        id: true,
        exercicio: true,
        numero: true,
        resumo: true,
        valorTotal: true,
        status: true,
        data: true,
        dataEmissao: true,
        dataVencimento: true,
        dataUtilizacao: true,
        dataCancelamento: true,
        dataReativacao: true,
        empenho: {
          select: {
            id: true,
            exercicio: true,
            numero: true,
            idFornecedor: true,
            fornecedor: {
              select: {
                id: true,
                nome: true,
              },
            },
            dotacao: {
              select: {
                id: true,
                despesa: true,
                desc: true,
                secretariaId: true,
                departamentoId: true,
                subdepartamentoId: true,
                secretaria: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
                departamento: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
                subdepartamento: {
                  select: {
                    id: true,
                    nome: true,
                  },
                },
              },
            },
            liquidacoes: {
              where: { ativo: true },
              select: {
                valorTotal: true,
              },
            },
          },
        },
      },
      orderBy: [{ exercicio: 'desc' }, { numero: 'desc' }],
    });

    // Filter by department hierarchy if specified
    let filteredAFs = afs;
    if (
      params.idSecretaria ||
      params.idDepartamento ||
      params.idSubdepartamento
    ) {
      filteredAFs = afs.filter((af) => {
        if (!af.empenho.dotacao) return false;

        if (
          params.idSecretaria &&
          af.empenho.dotacao.secretariaId !== params.idSecretaria
        ) {
          return false;
        }

        if (
          params.idDepartamento &&
          af.empenho.dotacao.departamentoId !== params.idDepartamento
        ) {
          return false;
        }

        if (
          params.idSubdepartamento &&
          af.empenho.dotacao.subdepartamentoId !== params.idSubdepartamento
        ) {
          return false;
        }

        return true;
      });
    }

    // Calculate summary statistics
    const totalAFs = filteredAFs.length;
    const valorTotal = filteredAFs.reduce((sum, af) => {
      return currency(sum, currencyOptionsNoSymbol).add(
        af.valorTotal.toNumber()
      ).value;
    }, 0);

    // Legacy-style calculation: Sum actual liquidations, but distribute properly
    // Since multiple AFs can share one empenho, we need to track utilization per AF
    const valorTotalUtilizado = filteredAFs.reduce((sum, af) => {
      // For list reports, use status-based calculation to avoid double-counting
      // This is a simplified approach that matches legacy behavior for list views
      const afValorUtilizado = af.status === 2 ? af.valorTotal.toNumber() : 0;
      return currency(sum, currencyOptionsNoSymbol).add(afValorUtilizado).value;
    }, 0);

    const reportData = filteredAFs.map((af) => ({
      id: af.id,
      exercicio: af.exercicio,
      numero: af.numero,
      resumo: af.resumo,
      valorTotal: af.valorTotal.toNumber(),
      status: af.status,
      data: af.data,
      empenho: {
        id: af.empenho.id,
        numero: af.empenho.numero,
        exercicio: af.empenho.exercicio,
        fornecedor: af.empenho.fornecedor,
        dotacao: af.empenho.dotacao
          ? {
              secretaria: af.empenho.dotacao.secretaria,
              departamento: af.empenho.dotacao.departamento,
              subdepartamento: af.empenho.dotacao.subdepartamento,
              despesa: af.empenho.dotacao.despesa,
              desc: af.empenho.dotacao.desc,
            }
          : null,
      },
    }));

    return {
      data: {
        afs: reportData,
        resumo: {
          totalAFs,
          valorTotal,
          valorTotalUtilizado,
          valorDisponivel: valorTotal - valorTotalUtilizado,
        },
      },
    };
  } catch (e) {
    console.error('Erro ao listar AFs para relatório:', e);
    return { error: 'Erro ao listar AFs para relatório.' };
  }
};

// Enhanced AF balance calculation with cross-empenho support
export const calcularSaldoDisponivelAF = async (idAF: number) => {
  try {
    // Get AF details with contract context
    const af = await prisma.afs.findUnique({
      where: { id: idAF, ativo: true },
      include: {
        empenho: {
          include: {
            contrato: true,
            fornecedor: true,
            liquidacoes: {
              where: { ativo: true },
              select: {
                id: true,
                valorTotal: true,
                marca_documento: true,
              },
            },
          },
        },
      },
    });

    if (!af) return { error: 'AF não encontrada.' };

    // Calculate direct liquidations
    const valorTotalLiquidado = af.empenho.liquidacoes.reduce(
      (sum, liq) => sum + liq.valorTotal.toNumber(),
      0
    );

    // If AF has contract context, search for cross-empenho liquidations
    let valorTotalCrossEmpenho = 0;
    if (af.empenho.contrato && af.empenho.fornecedor) {
      // Get marked document numbers
      const documentosMarcados = af.empenho.liquidacoes
        .filter((liq) => liq.marca_documento)
        .map((liq) => liq.marca_documento)
        .filter(
          (marca): marca is string => marca !== null && marca !== undefined
        );

      // Also get document numbers from liquidacoes_documentos
      const documentosRelacionados =
        await prisma.liquidacoes_documentos.findMany({
          where: {
            liquidacao: {
              idEmpenho: af.empenho.id,
              ativo: true,
            },
            ativo: true,
          },
          select: {
            numeroDocumento: true,
          },
        });

      const todosDocumentos = [
        ...documentosMarcados,
        ...documentosRelacionados.map((doc) => doc.numeroDocumento),
      ].filter((doc, index, self) => self.indexOf(doc) === index);

      if (todosDocumentos.length > 0) {
        const crossResult = await listarDocumentosPorFornecedorContrato({
          numerosDocumentos: todosDocumentos,
          idFornecedor: af.empenho.fornecedor.id,
          idContrato: af.empenho.contrato.id,
          empenhoExcluir: af.empenho.numero,
          exercicioExcluir: af.empenho.exercicio,
        });

        if (!crossResult.error && crossResult.data) {
          valorTotalCrossEmpenho = crossResult.data.reduce(
            (sum, liq) => sum + liq.valorTotal.toNumber(),
            0
          );
        }
      }
    }

    const valorTotalAF = af.valorTotal.toNumber();
    const valorUtilizado =
      af.status === 2
        ? valorTotalAF
        : valorTotalLiquidado + valorTotalCrossEmpenho;

    return {
      data: {
        valorTotalAF,
        valorUtilizado,
        valorDisponivel: valorTotalAF - valorUtilizado,
        valorTotalLiquidado,
        valorTotalCrossEmpenho,
      } as SaldoDisponivelAFData,
    };
  } catch (e) {
    console.error('Erro ao calcular saldo disponível da AF:', e);
    return { error: 'Erro ao calcular saldo disponível da AF.' };
  }
};
