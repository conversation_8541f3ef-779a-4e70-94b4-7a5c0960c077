'use client';
import { ColumnDef } from '@tanstack/react-table';
import { ReusableDatatableSemFiltro } from '@/components/datatable/reusableDatatableSemFiltro';
import { listarSuperavitsDetalhes } from '@/lib/database/gerenciamento/superavit';

/* TODO Criar Datatable filtrado com base no superavitDetalhes*/
export default function SuperavitsDetalhesDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarSuperavitsDetalhes>>;
}) {
  if (!data.data) return null;
  const columns: ColumnDef<(typeof data.data)[0]>[] = [
    {
      accessorKey: 'nome',
      header: 'Nome',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'despesa',
      header: 'despesa',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'fonte',
      header: 'Fonte',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'codAplicacao',
      header: '<PERSON><PERSON><PERSON> da Aplicação',
      filterFn: 'includesString',
    },

    {
      accessorKey: 'valor',
      header: 'Valor',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(getValue<number>());
      },
    },

    {
      accessorKey: 'valorReservado',
      header: 'Reservado',
      filterFn: 'includesString',
      cell: ({ getValue }) => {
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(getValue<number>());
      },
    },

    {
      accessorKey: 'data',
      header: 'Data',
      filterFn: 'includesString',
      /* cell: ({ getValue }) => {
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(getValue<number>());
      },*/ // Analisar para formatar data
    },

    {
      accessorKey: 'tela',
      header: 'Funcionalidade',
      filterFn: 'includesString',
    },
  ];

  return (
    <div className='container mx-auto'>
      <ReusableDatatableSemFiltro columns={columns} data={data.data} />
    </div>
  );
}
