import { PrismaClient } from '@prisma/client';
import { tmpdir } from 'os';
import fs from 'fs';

const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Function to write the decoded content to a file
function writeDecodedFile(filename: string, base64Content: string) {
  const filePath = `${tmpdir()}/${filename}`;
  const contentBuffer = Buffer.from(base64Content, 'base64');
  console.log(filePath);

  fs.writeFile(filePath, contentBuffer as unknown as Uint8Array, (err) => {
    if (err) {
      console.log(`Error writing ${filename}:`, err);
    } else {
      console.log(`${filename} successfully written to temp directory`);
    }
  });
}

if (process.env.NODE_ENV === 'production') {
  // Decode and write client-cert.pem
  if (process.env.SUPABASE_CERT_BASE64) {
    writeDecodedFile('supabase-cert.crt', process.env.SUPABASE_CERT_BASE64);
  } else {
    console.log('SUPABASE_CERT_BASE64 environment variable is not set.');
  }
}

export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    // log: ["query"],
  });

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;
