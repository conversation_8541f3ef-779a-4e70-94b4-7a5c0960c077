-- CreateTable
CREATE TABLE "gerenciamentoUsuarios_audit" (
    "id" SERIAL NOT NULL,
    "usuarioId" INTEGER NOT NULL,
    "usuarioModificadoId" INTEGER NOT NULL,
    "acao" SMALLINT NOT NULL,
    "ip" INET NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gerenciamentoUsuarios_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gerenciamentoUsuarios_audit_cargos" (
    "id" SERIAL NOT NULL,
    "auditId" INTEGER NOT NULL,
    "cargoId" INTEGER NOT NULL,

    CONSTRAINT "gerenciamentoUsuarios_audit_cargos_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "gerenciamentoUsuarios_audit" ADD CONSTRAINT "gerenciamentoUsuarios_audit_usuarioId_fkey" FOREIGN KEY ("usuarioId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoUsuarios_audit" ADD CONSTRAINT "gerenciamentoUsuarios_audit_usuarioModificadoId_fkey" FOREIGN KEY ("usuarioModificadoId") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoUsuarios_audit_cargos" ADD CONSTRAINT "gerenciamentoUsuarios_audit_cargos_auditId_fkey" FOREIGN KEY ("auditId") REFERENCES "gerenciamentoUsuarios_audit"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gerenciamentoUsuarios_audit_cargos" ADD CONSTRAINT "gerenciamentoUsuarios_audit_cargos_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
