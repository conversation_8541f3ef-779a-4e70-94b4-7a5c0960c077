/*
  Warnings:

  - Added the required column `usarMes1` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes10` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes11` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes12` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes2` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes3` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes4` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes5` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes6` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes7` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes8` to the `empenhos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `usarMes9` to the `empenhos` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "empenhos" ADD COLUMN     "usarMes1" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes10" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes11" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes12" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes2" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes3" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes4" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes5" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes6" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes7" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes8" DECIMAL(18,3) NOT NULL,
ADD COLUMN     "usarMes9" DECIMAL(18,3) NOT NULL;

-- CreateTable
CREATE TABLE "empenhos_anulacoes" (
    "id" SERIAL NOT NULL,
    "idEmpenho" INTEGER NOT NULL,
    "valorAnulado" DECIMAL(18,3) NOT NULL,
    "motivo" TEXT NOT NULL,
    "data" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ativo" BOOLEAN NOT NULL DEFAULT true,
    "idUsuario" INTEGER NOT NULL,
    "ip" INET NOT NULL,

    CONSTRAINT "empenhos_anulacoes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "empenhos_anulacoes_idEmpenho_ativo_idx" ON "empenhos_anulacoes"("idEmpenho", "ativo");

-- CreateIndex
CREATE INDEX "empenhos_idFornecedor_exercicio_idx" ON "empenhos"("idFornecedor", "exercicio");

-- CreateIndex
CREATE INDEX "empenhos_status_data_idx" ON "empenhos"("status", "data");

-- AddForeignKey
ALTER TABLE "empenhos_anulacoes" ADD CONSTRAINT "empenhos_anulacoes_idEmpenho_fkey" FOREIGN KEY ("idEmpenho") REFERENCES "empenhos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "empenhos_anulacoes" ADD CONSTRAINT "empenhos_anulacoes_idUsuario_fkey" FOREIGN KEY ("idUsuario") REFERENCES "user_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
