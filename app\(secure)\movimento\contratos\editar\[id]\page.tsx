'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { obterContrato } from '@/lib/database/movimento/contratos';
import { ErrorAlert } from '@/components/error-alert';
import FormEditarContrato from '@/components/movimento/contrato/formEditarContrato';

export default async function EditarReservaPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const contrato = await obterContrato({
    id: Number(id),
  });
  if (contrato.error) {
    return <ErrorAlert error={contrato.error} />;
  }
  if (!contrato.data) {
    return <ErrorAlert error='Falha ao obter contrato.' />;
  }
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Editar Contrato</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <FormEditarContrato contrato={contrato} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
