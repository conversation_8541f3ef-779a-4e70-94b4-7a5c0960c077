import { ErrorAlert } from '@/components/error-alert';
import ExternalUsersDatatable from './ExternalUsersDatatable';
import { listarUsuariosExternos } from '@/lib/database/gerenciamento/usuariosExternos';

export default async function ExternalUsersDatatableWrapper() {
  const result = await listarUsuariosExternos();

  if (result.error) return <ErrorAlert error={result.error} />;
  if (!result.data)
    return <ErrorAlert error={'Usuários externos não encontrados.'} />;

  return <ExternalUsersDatatable data={result} />;
}
