'use server';
import { z } from 'zod';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioCotaReducao } from '@/components/relatorios/cotaReducao';
import { obterCotasReducaoParaRelatorio } from '@/lib/database/relatorios/dotacoes';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permisso<PERSON> } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function RelatorioCotasPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.GERENCIAMENTO_DE_DOTACOES,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const currSearchParams = await searchParams;
  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }
  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();
  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }
  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  const params = {
    exercicio: currSearchParams.exercicio
      ? parseInt(currSearchParams.exercicio as string)
      : result.exercicio,
    idSecretaria: currSearchParams.secretaria
      ? parseInt(currSearchParams.secretaria as string)
      : undefined,
    idDepartamento: currSearchParams.departamento
      ? parseInt(currSearchParams.departamento as string)
      : undefined,
    idSubdepartamento: currSearchParams.subdepartamento
      ? parseInt(currSearchParams.subdepartamento as string)
      : undefined,
    reducao: currSearchParams.tipo === 'reducao',
    bearer,
  };

  const cotas = await obterCotasReducaoParaRelatorio(params);

  if (cotas.error) {
    return <ErrorAlert error={cotas.error} />;
  }

  if (!cotas.data) {
    return <ErrorAlert error='Falha ao obter cotas/reduções.' />;
  }

  const filtros = {
    exercicio: params.exercicio || result.exercicio,
    secretaria: currSearchParams.secretariaNome as string,
    departamento: currSearchParams.departamentoNome as string,
    subdepartamento: currSearchParams.subdepartamentoNome as string,
    tipo: currSearchParams.tipo as 'cota' | 'reducao' | undefined,
  };

  const titulo =
    currSearchParams.tipo === 'reducao'
      ? 'Relatório de Reduções'
      : currSearchParams.tipo === 'cota'
        ? 'Relatório de Cotas'
        : 'Relatório de Cotas e Reduções';

  return (
    <>
      <RelatorioCotaReducao
        cotas={cotas.data}
        titulo={titulo}
        filtros={filtros}
      />
      <ClientCompletionTrigger />
    </>
  );
}
