/**
 * Codemod: Replace `Zod.infer` with `z.infer` for Zod v4
 * Also ensures `import { z } from "zod";` exists.
 */
export default function transformer(file, api) {
  const j = api.jscodeshift;
  const root = j(file.source);

  let didReplace = false;

  // --- Case 1: Replace type references `Zod.infer`
  root.find(j.TSTypeReference).forEach(path => {
    const typeName = path.node.typeName;

    if (
      typeName.type === "TSQualifiedName" &&
      typeName.left.type === "Identifier" &&
      typeName.left.name === "Zod" &&
      typeName.right.type === "Identifier" &&
      typeName.right.name === "infer"
    ) {
      // Change Zod.infer → z.infer
      typeName.left.name = "z";
      didReplace = true;
    }
  });

  // --- Case 2: Replace runtime calls `Zod.*` just in case
  root.find(j.MemberExpression, {
    object: { type: "Identifier", name: "<PERSON><PERSON>" }
  }).forEach(path => {
    path.node.object.name = "z";
    didReplace = true;
  });

  // --- Case 3: Replace `import * as Zod from "zod"`
  root.find(j.ImportDeclaration, { source: { value: "zod" } })
    .forEach(path => {
      path.node.specifiers.forEach((spec, i) => {
        if (spec.type === "ImportNamespaceSpecifier" && spec.local.name === "Zod") {
          // Replace with { z }
          path.node.specifiers[i] = j.importSpecifier(j.identifier("z"));
          didReplace = true;
        }
      });
    });

  // --- Ensure `import { z } from "zod";` if we touched anything
  if (didReplace) {
    const importDecl = root.find(j.ImportDeclaration, { source: { value: "zod" } });
    if (importDecl.length) {
      const hasZ = importDecl.find(j.ImportSpecifier, { imported: { name: "z" } });
      if (!hasZ.length) {
        importDecl.get('specifiers').push(j.importSpecifier(j.identifier("z")));
      }
    } else {
      root.get().node.program.body.unshift(
        j.importDeclaration([j.importSpecifier(j.identifier("z"))], j.literal("zod"))
      );
    }
  }

  return root.toSource({ quote: 'single' });
}