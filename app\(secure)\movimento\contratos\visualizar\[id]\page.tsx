'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { ErrorAlert } from '@/components/error-alert';
import { obterContratoParaRelatorio } from '@/lib/database/movimento/contratos';
import { RelatorioContrato } from '@/components/movimento/contrato/relatorioContrato';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { usuarioEhGerente } from '@/lib/database/usuarios';
import { BotaoDownloadPDFContrato } from '@/components/movimento/contrato/botaoDownloadPDF';

export default async function VisualizarContratoPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const gerentePromise = usuarioEhGerente();

  const contratoPromise = obterContratoParaRelatorio({
    id: Number(id),
  });

  const [contrato, gerente] = await Promise.all([
    contratoPromise,
    gerentePromise,
  ]);

  if (contrato.error) {
    return <ErrorAlert error={contrato.error} />;
  }
  if (!contrato.data) {
    return <ErrorAlert error='Falha ao obter contrato.' />;
  }
  if (gerente.error) {
    return <ErrorAlert error={gerente.error} />;
  }
  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Visualizar Contrato</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='mx-6 flex w-full flex-wrap justify-between gap-4'>
          <Link href='/movimento/contrato'>
            <Button className='mb-4' variant='secondary'>
              <ArrowLeft className='mr-2 size-4' /> Voltar
            </Button>
          </Link>
          <div className='flex flex-wrap gap-4'>
            <BotaoDownloadPDFContrato
              url={`/movimento/contratos/imprimir/${id}`}
            />
          </div>
        </div>
        {gerente.data && (
          <div className='flex items-center space-x-2'>
            <Checkbox id='incluirGestor' defaultChecked />
            <label
              htmlFor='incluirGestor'
              className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
            >
              Incluir assinatura gestor(a)
            </label>
          </div>
        )}
        <div className='flex w-full flex-wrap justify-center'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <RelatorioContrato contrato={contrato} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
