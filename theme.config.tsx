import Link from 'next/link';
import { Avatar, AvatarImage } from './components/ui/avatar';

export default {
  logo: (
    <>
      <Avatar>
        <AvatarImage src='/gestao-digital.svg' className='dark:invert' />
      </Avatar>
      <span className='ml-2 hidden font-bold sm:inline-block'>
        Gestão Digital
      </span>
    </>
  ),
  project: {},
  search: {
    emptyResult: 'Nenhum resultado encontrado',
    error: 'Erro ao pesquisar',
    placeholder: 'Pesquisar',
  },
  feedback: {
    content: null,
  },
  editLink: {
    component: null,
  },
  toc: {
    title: 'Índice da página',
  },
  gitTimestamp: null,
  // ... other theme options
};
