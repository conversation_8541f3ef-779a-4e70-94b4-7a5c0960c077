import '@/styles/globals.css';
import { Metadata, Viewport } from 'next';

import { fontSans } from '@/lib/fonts';
import { cn } from '@/lib/utils';
import { SiteHeader } from '@/components/site-header';
import { TailwindIndicator } from '@/components/tailwind-indicator';
import { ThemeProvider } from '@/components/theme-provider';
import { siteConfig } from '@/config/site';
import { Toaster } from '@/components/ui/sonner';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/components/page-header';
import { UserNav } from '@/components/user/user-nav';
import { ExercicioBadge } from '@/components/user/exercicio-badge';
import { Suspense } from 'react';
import CommandMenuWrapper from '@/components/commandMenuWrapper';
import { Skeleton } from '@/components/ui/skeleton';

export const metadata: Metadata = {
  title: siteConfig.name,
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='pt-BR' suppressHydrationWarning>
      <body
        className={cn(
          'bg-background min-h-screen font-sans antialiased',
          fontSans.variable
        )}
      >
        <ThemeProvider attribute='class' defaultTheme='system' enableSystem>
          <div className='relative flex min-h-screen flex-col'>
            <SiteHeader />
            <div className='relative container mb-8'>
              <PageHeader className='page-header pb-8'>
                <PageHeaderHeading className=''>
                  {siteConfig.departamento}
                </PageHeaderHeading>
                <PageHeaderDescription>
                  {siteConfig.cliente}
                </PageHeaderDescription>
                <section className='pb-2 md:pb-10'></section>
              </PageHeader>
              <section>
                <div className='bg-background h-auto overflow-hidden overflow-y-auto rounded-[0.5rem] border shadow'>
                  <div className='border-b'>
                    <div className='flex h-auto flex-wrap items-center gap-2 px-4 py-2 sm:h-16 sm:py-0'>
                      <UserNav />
                      <Suspense
                        fallback={<Skeleton className='h-[40px] w-[120px]' />}
                      >
                        <CommandMenuWrapper />
                      </Suspense>
                      {/* <NavigationButtons />
                    <Separator
                      orientation='vertical'
                      className='hidden lg:block'
                    />
                    <HistoricoProcessos /> */}
                      <div className='ml-auto flex items-center space-x-4'>
                        <ExercicioBadge />
                      </div>
                    </div>
                  </div>
                  {children}
                </div>
              </section>
            </div>
          </div>
          <TailwindIndicator />
          <Toaster richColors />
        </ThemeProvider>
      </body>
    </html>
  );
}
