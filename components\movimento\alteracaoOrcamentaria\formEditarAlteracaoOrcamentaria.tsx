'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Loader2, Save, Search } from 'lucide-react';
import {
  cn,
  // currencyOptions,
  currencyOptionsNoSymbol,
  moneyMask,
  moneyUnmask,
  numeroMask,
  obterMesAtual,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import {
  alteracaoOrcamentariaSchema,
  idExercicioSchema,
} from '@/lib/validation';
import {
  listarDepartamentosAtivos,
  listarSecretariasAtivas,
  listarEconomicasAtivas,
  listarFuncionaisAtivas,
  editarAlteracaoOrcamentaria,
  obterAlteracaoPorId,
  buscarDespesa,
} from '@/lib/database/movimento/alteracaoOrcamentaria';
import currency from 'currency.js';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ComboboxSelecionaSecretaria } from './comboboxSelecionarSecretaria';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ComboboxSelecionaDepartamento } from './comboboxSelecionarDepartamento';
import { ComboboxSelecionaEconomica } from './comboboxSelecionarEconomica';
import { ComboboxSelecionaFuncional } from './comboboxSelecionarFuncional';
import { Fontes, tiposAlteracao } from '@/lib/enums';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function FormEditarAlteracaoOrcamentaria({
  secretarias,
  departamentos,
  economicas,
  funcionais,
  alteracaoOrcamentariaData,
}: {
  secretarias: Awaited<ReturnType<typeof listarSecretariasAtivas>>;
  departamentos: Awaited<ReturnType<typeof listarDepartamentosAtivos>>;
  economicas: Awaited<ReturnType<typeof listarEconomicasAtivas>>;
  funcionais: Awaited<ReturnType<typeof listarFuncionaisAtivas>>;
  alteracaoOrcamentariaData: Awaited<ReturnType<typeof obterAlteracaoPorId>>;
}) {
  const router = useRouter();
  const [despesa, setDespesa] = useState<Awaited<
    ReturnType<typeof buscarDespesa>
  > | null>(null);
  const [despesaNova, setDespesaNova] = useState<Awaited<any>>(null);
  const [economicaId, setEconomicaId] = useState<number | null>(
    alteracaoOrcamentariaData.data?.idEconomica || null
  );
  const [funcionalId, setFuncionalId] = useState<number | null>(
    alteracaoOrcamentariaData.data?.idFuncional || null
  );
  const [loading, setLoading] = useState(false);
  const [tpAlt, setTpAlt] = useState('');
  const [tpAcao, setTpAcao] = useState('');
  const [secretarios, setSecretarios] = useState<any>([{}]);

  const [totalBRL, setTotalBRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorTotal || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes1BRL, setMes1BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes1 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes2BRL, setMes2BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes2 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes3BRL, setMes3BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes3 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes4BRL, setMes4BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes4 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes5BRL, setMes5BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes5 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes6BRL, setMes6BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes6 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes7BRL, setMes7BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes7 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes8BRL, setMes8BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes8 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes9BRL, setMes9BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes9 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes10BRL, setMes10BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes10 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes11BRL, setMes11BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes11 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );
  const [mes12BRL, setMes12BRL] = useState(
    currency(
      alteracaoOrcamentariaData.data?.valorMes12 || 0,
      currencyOptionsNoSymbol
    )
      .format()
      .replace(/0$/, '')
  );

  const mesAtual = obterMesAtual();

  const form = useForm<
    z.infer<
      typeof alteracaoOrcamentariaSchema /*typeof editarAlteracaoOrcamentariaSchema*/
    >
  >({
    resolver: zodResolver(alteracaoOrcamentariaSchema),
    defaultValues: {
      id: alteracaoOrcamentariaData.data?.id,
      exercicio: alteracaoOrcamentariaData.data?.exercicio,
      tipoAlteracao: alteracaoOrcamentariaData.data?.tipoAlteracao,
      tipoAcao: alteracaoOrcamentariaData.data?.tipoAcao,
      status: alteracaoOrcamentariaData.data?.status,
      valorTotal: Number(alteracaoOrcamentariaData.data?.valorTotal),
      valorMes1: Number(alteracaoOrcamentariaData.data?.valorMes1),
      valorMes2: Number(alteracaoOrcamentariaData.data?.valorMes2),
      valorMes3: Number(alteracaoOrcamentariaData.data?.valorMes3),
      valorMes4: Number(alteracaoOrcamentariaData.data?.valorMes4),
      valorMes5: Number(alteracaoOrcamentariaData.data?.valorMes5),
      valorMes6: Number(alteracaoOrcamentariaData.data?.valorMes6),
      valorMes7: Number(alteracaoOrcamentariaData.data?.valorMes7),
      valorMes8: Number(alteracaoOrcamentariaData.data?.valorMes8),
      valorMes9: Number(alteracaoOrcamentariaData.data?.valorMes9),
      valorMes10: Number(alteracaoOrcamentariaData.data?.valorMes10),
      valorMes11: Number(alteracaoOrcamentariaData.data?.valorMes11),
      valorMes12: Number(alteracaoOrcamentariaData.data?.valorMes12),
      obs: alteracaoOrcamentariaData.data?.obs,
      fonte: alteracaoOrcamentariaData.data?.fonte,
      codAplicacao: alteracaoOrcamentariaData.data?.codAplicacao,
      idSecretaria: alteracaoOrcamentariaData.data?.idSecretaria || 0,
      idDepto: alteracaoOrcamentariaData.data?.idDepto || 0,
      idEconomica: alteracaoOrcamentariaData.data?.idEconomica,
      idFuncional: alteracaoOrcamentariaData.data?.idFuncional!,
      despesaCopia: alteracaoOrcamentariaData.data?.despesaCopia,
      despesaAcao: alteracaoOrcamentariaData.data?.despesaAcao,
      despesaNova: alteracaoOrcamentariaData.data?.despesaNova,
      descrDespNova: alteracaoOrcamentariaData.data?.descrDespNova,
      idSecretario: alteracaoOrcamentariaData.data?.idSecretario,
    },
  });

  const total = currency(mes1BRL, currencyOptionsNoSymbol)
    .add(mes2BRL)
    .add(mes3BRL)
    .add(mes4BRL)
    .add(mes5BRL)
    .add(mes6BRL)
    .add(mes7BRL)
    .add(mes8BRL)
    .add(mes9BRL)
    .add(mes10BRL)
    .add(mes11BRL)
    .add(mes12BRL);

  const restante = currency(totalBRL, currencyOptionsNoSymbol).subtract(total);

  const {
    cotaMes1,
    cotaMes2,
    cotaMes3,
    cotaMes4,
    cotaMes5,
    cotaMes6,
    cotaMes7,
    cotaMes8,
    cotaMes9,
    cotaMes10,
    cotaMes11,
    cotaMes12,
    valorAtual,
  } = despesa?.data || {};

  const {
    cotaSupMes1,
    cotaSupMes2,
    cotaSupMes3,
    cotaSupMes4,
    cotaSupMes5,
    cotaSupMes6,
    cotaSupMes7,
    cotaSupMes8,
    cotaSupMes9,
    cotaSupMes10,
    cotaSupMes11,
    cotaSupMes12,
  }: any = despesaNova || {};

  const supMes1 = currency(
    tpAlt == 'anulacao' ? cotaSupMes1 || 0 : form.getValues('valorMes1') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes1'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes1 || 0)
    .format()
    .replace(/0$/, '');
  const supMes2 = currency(
    tpAlt == 'anulacao' ? cotaSupMes2 || 0 : form.getValues('valorMes2') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes2'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes2 || 0)
    .format()
    .replace(/0$/, '');
  const supMes3 = currency(
    tpAlt == 'anulacao' ? cotaSupMes3 || 0 : form.getValues('valorMes3') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes3'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes3 || 0)
    .format()
    .replace(/0$/, '');
  const supMes4 = currency(
    tpAlt == 'anulacao' ? cotaSupMes4 || 0 : form.getValues('valorMes4') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes4'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes4 || 0)
    .format()
    .replace(/0$/, '');
  const supMes5 = currency(
    tpAlt == 'anulacao' ? cotaSupMes5 || 0 : form.getValues('valorMes5') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes5'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes5 || 0)
    .format()
    .replace(/0$/, '');
  const supMes6 = currency(
    tpAlt == 'anulacao' ? cotaSupMes6 || 0 : form.getValues('valorMes6') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes6'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes6 || 0)
    .format()
    .replace(/0$/, '');
  const supMes7 = currency(
    tpAlt == 'anulacao' ? cotaSupMes7 || 0 : form.getValues('valorMes7') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes7'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes7 || 0)
    .format()
    .replace(/0$/, '');
  const supMes8 = currency(
    tpAlt == 'anulacao' ? cotaSupMes8 || 0 : form.getValues('valorMes8') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes8'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes8 || 0)
    .format()
    .replace(/0$/, '');
  const supMes9 = currency(
    tpAlt == 'anulacao' ? cotaSupMes9 || 0 : form.getValues('valorMes9') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes9'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes9 || 0)
    .format()
    .replace(/0$/, '');
  const supMes10 = currency(
    tpAlt == 'anulacao' ? cotaSupMes10 || 0 : form.getValues('valorMes10') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes10'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes10 || 0)
    .format()
    .replace(/0$/, '');
  const supMes11 = currency(
    tpAlt == 'anulacao' ? cotaSupMes11 || 0 : form.getValues('valorMes11') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes11'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes11 || 0)
    .format()
    .replace(/0$/, '');
  const supMes12 = currency(
    tpAlt == 'anulacao' ? cotaSupMes12 || 0 : form.getValues('valorMes12') * -1,
    currencyOptionsNoSymbol
  )
    .add(form.getValues('valorMes12'))
    // .subtract(alteracaoOrcamentariaData.data?.valorMes12 || 0)
    .format()
    .replace(/0$/, '');

  const subMes1 = currency(cotaMes1 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes1'))
    .add(alteracaoOrcamentariaData.data?.valorMes1 || 0)
    .format()
    .replace(/0$/, '');
  const subMes2 = currency(cotaMes2 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes2'))
    .add(alteracaoOrcamentariaData.data?.valorMes2 || 0)
    .format()
    .replace(/0$/, '');
  const subMes3 = currency(cotaMes3 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes3'))
    .add(alteracaoOrcamentariaData.data?.valorMes3 || 0)
    .format()
    .replace(/0$/, '');
  const subMes4 = currency(cotaMes4 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes4'))
    .add(alteracaoOrcamentariaData.data?.valorMes4 || 0)
    .format()
    .replace(/0$/, '');
  const subMes5 = currency(cotaMes5 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes5'))
    .add(alteracaoOrcamentariaData.data?.valorMes5 || 0)
    .format()
    .replace(/0$/, '');
  const subMes6 = currency(cotaMes6 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes6'))
    .add(alteracaoOrcamentariaData.data?.valorMes6 || 0)
    .format()
    .replace(/0$/, '');
  const subMes7 = currency(cotaMes7 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes7'))
    .add(alteracaoOrcamentariaData.data?.valorMes7 || 0)
    .format()
    .replace(/0$/, '');
  const subMes8 = currency(cotaMes8 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes8'))
    .add(alteracaoOrcamentariaData.data?.valorMes8 || 0)
    .format()
    .replace(/0$/, '');
  const subMes9 = currency(cotaMes9 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes9'))
    .add(alteracaoOrcamentariaData.data?.valorMes9 || 0)
    .format()
    .replace(/0$/, '');
  const subMes10 = currency(cotaMes10 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes10'))
    .add(alteracaoOrcamentariaData.data?.valorMes10 || 0)
    .format()
    .replace(/0$/, '');
  const subMes11 = currency(cotaMes11 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes11'))
    .add(alteracaoOrcamentariaData.data?.valorMes11 || 0)
    .format()
    .replace(/0$/, '');
  const subMes12 = currency(cotaMes12 || 0, currencyOptionsNoSymbol)
    .subtract(form.getValues('valorMes12'))
    .add(alteracaoOrcamentariaData.data?.valorMes12 || 0)
    .format()
    .replace(/0$/, '');

  const subValorAtual = currency(valorAtual || 0, currencyOptionsNoSymbol)
    .format()
    .replace(/0$/, '');

  const onSubmit = async (
    values: z.infer<typeof alteracaoOrcamentariaSchema>
  ) => {
    try {
      setLoading(true);

      if (!alteracaoOrcamentariaData.data?.ativo) {
        toast.error('Alteração Orçamentária está inativa');
        setLoading(false);
        return;
      }

      const res = await editarAlteracaoOrcamentaria({
        ...values,
      });
      if (res?.error) {
        toast.error(res.error);
        setLoading(false);
      } else {
        toast.success('Alteração orçamentária editada com sucesso.');
        router.push(`/movimento/alteracaoOrcamentaria`);
        setLoading(false);
      }
    } catch (error: any) {
      toast.error(toastAlgoDeuErrado);
      setLoading(false);
    }
  };

  const onInvalid = (errors: any) => {
    console.error(errors);
    toast.error(JSON.stringify(errors));
  };

  const tipoAlt = (tpAlter: any) => {
    let ret = 'anulacao';
    switch (tpAlter) {
      case 1:
        ret = 'anulacao';
        break;
      case 2:
        ret = 'superavit';
        break;
      case 3:
        ret = 'excesso';
        break;
    }
    return ret;
  };

  const tipoAcao = (tpAcao: any) => {
    let ret = 'suplementacao';
    switch (tpAcao) {
      case 0:
        ret = 'criacaoDespesas';
        break;
      case 1:
        ret = 'suplementacao';
        break;
      case 2:
        ret = 'suplementacao';
        break;
    }
    return ret;
  };

  const carregarDespesaNova = async () => {
    await carregarDespesa('despesaNova');
  };

  const carregarDespesaAcao = async () => {
    await carregarDespesa('despesaAcao');
  };

  const carregarDespesaCopia = async () => {
    await carregarDespesa('despesaCopia');
  };

  const carregarDespesa = async (campo: any) => {
    if (!form.getValues(campo)) {
      return;
    }

    try {
      setLoading(true);
      const data: z.infer<typeof idExercicioSchema> = {
        id: form.getValues(campo), //usando id mas com numero da despesa
        exercicio: form.getValues('exercicio'),
      };
      const res: any = await buscarDespesa(data);

      if (res?.error) {
        toast.error(res.error);
        setDespesa(null);
        setDespesaNova(null);
        setLoading(false);
      } else {
        if (campo == 'despesaAcao') {
          setDespesa(res);
          setSecretarios(res?.data?.secretariosAssinantes || []);
          form.setValue('fonte', res?.data?.fonte || 0);
          form.setValue(
            'idSecretario',
            res?.data?.secretariosAssinantes[0].id || null
          );
        } else if (campo == 'despesaCopia') {
          setFuncionalId(res?.data?.funcional.id);
          setEconomicaId(res?.data?.economica.id);
          setSecretarios(res?.data?.secretariosAssinantes || []);
          form.setValue('idEconomica', res?.data?.economica.id);
          form.setValue('idFuncional', res?.data?.funcional.id);
          form.setValue('codAplicacao', res?.data?.codAplicacao);
          form.setValue(
            'idSecretario',
            res?.data?.secretariosAssinantes[0].id || null
          );
          if (tpAlt != 'anulacao' && tpAcao == 'criacaoDespesas') {
            form.setValue('fonte', res?.data?.fonte || 0);
          }
        } else if ((campo = 'despesaNova')) {
          if (
            tpAlt == 'anulacao' ||
            form.getValues('tipoAlteracao') == tiposAlteracao['Anulação']
          ) {
            const cotasDespNova: any = {
              cotaSupMes1: res?.data?.cotaMes1,
              cotaSupMes2: res?.data?.cotaMes2,
              cotaSupMes3: res?.data?.cotaMes3,
              cotaSupMes4: res?.data?.cotaMes4,
              cotaSupMes5: res?.data?.cotaMes5,
              cotaSupMes6: res?.data?.cotaMes6,
              cotaSupMes7: res?.data?.cotaMes7,
              cotaSupMes8: res?.data?.cotaMes8,
              cotaSupMes9: res?.data?.cotaMes9,
              cotaSupMes10: res?.data?.cotaMes1,
              cotaSupMes11: res?.data?.cotaMes11,
              cotaSupMes12: res?.data?.cotaMes12,
              fonte: res?.data?.fonte,
              secretaria: res?.data?.departamento.secretaria,
              departamento: res?.data?.departamento,
              despesa: res?.data?.despesa,
            };
            form.setValue('fonte', res?.data?.fonte);
            setDespesaNova(cotasDespNova);
          } else {
            form.setValue('fonte', res?.data?.fonte);
            setDespesa(res);
          }
          setFuncionalId(res?.data?.funcional.id);
          setEconomicaId(res?.data?.economica.id);
          setSecretarios(res?.data?.secretariosAssinantes || []);
          form.setValue('idEconomica', res?.data?.economica.id);
          form.setValue('idFuncional', res?.data?.funcional.id);
          form.setValue('codAplicacao', res?.data?.codAplicacao);
          form.setValue(
            'idSecretario',
            res?.data?.secretariosAssinantes[0].id || null
          );
        }
        setLoading(false);
      }
    } catch (error: any) {
      console.log(JSON.stringify(error));
      toast.error(toastAlgoDeuErrado);
      setLoading(false);
    }
  };

  useEffect(() => {
    setTpAcao(tipoAcao(alteracaoOrcamentariaData.data?.tipoAcao));
    setTpAlt(tipoAlt(alteracaoOrcamentariaData.data?.tipoAlteracao));
    // setSecretarios([alteracaoOrcamentariaData.data?.secretario])

    if (
      tipoAlt(alteracaoOrcamentariaData.data?.tipoAlteracao) == 'anulacao' &&
      tipoAcao(alteracaoOrcamentariaData.data?.tipoAcao) == 'suplementacao'
    ) {
      carregarDespesaAcao();
      carregarDespesaNova();
    } else if (
      tipoAlt(alteracaoOrcamentariaData.data?.tipoAlteracao) == 'anulacao' &&
      tipoAcao(alteracaoOrcamentariaData.data?.tipoAcao) == 'criacaoDespesas'
    ) {
      carregarDespesaAcao();
      carregarDespesaCopia();
    } else if (
      tipoAcao(alteracaoOrcamentariaData.data?.tipoAcao) == 'suplementacao'
    ) {
      carregarDespesaNova();
    } else if (
      tipoAcao(alteracaoOrcamentariaData.data?.tipoAcao) == 'criacaoDespesas'
    ) {
      carregarDespesaCopia();
    }
  }, [form]);

  return (
    <>
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit, onInvalid)}>
            <div className='flex w-full justify-between gap-2'>
              <FormField
                name='id'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Codigo</FormLabel>
                    <FormControl>
                      <Input {...field} disabled />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='idSecretaria'
                render={() => (
                  <FormItem>
                    <FormLabel className='flex text-left'>Secretaria</FormLabel>
                    <FormControl>
                      <ComboboxSelecionaSecretaria
                        secretarias={secretarias}
                        form={form}
                        habilitado={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='idDepto'
                render={() => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Departamento
                    </FormLabel>
                    <FormControl>
                      <ComboboxSelecionaDepartamento
                        departamentos={departamentos}
                        // departamentoId={departamentoId}
                        // setDepartamentoId={setDepartamentoId}
                        form={form}
                        habilitado={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-8 flex flex-wrap justify-center gap-6'>
              {secretarios.length > 1 && (
                <FormField
                  control={form.control}
                  name='idSecretario'
                  render={({ field }) => (
                    <FormItem className='w-full'>
                      <FormLabel className='w-full text-left'>
                        Secretário
                      </FormLabel>
                      <Select
                        onValueChange={(e) => {
                          form.setValue('idSecretario', Number(e), {
                            shouldDirty: true,
                          });
                        }}
                        value={`${field.value}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Selecione o secretário que irá assinar...' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {secretarios.map((secretario: any) => (
                            <SelectItem
                              key={secretario.id}
                              value={`${secretario.id}`}
                            >
                              {secretario.nome}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              )}
            </div>
            <div className='mt-8 flex flex-wrap justify-center gap-6'>
              <RadioGroup
                value={tipoAlt(form.getValues('tipoAlteracao'))}
                className='grid-flow-col'
                disabled
              >
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='anulacao' />
                  <Label htmlFor='r1'>Anulação</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='superavit' />
                  <Label htmlFor='r2'>Superávit</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='excesso' />
                  <Label htmlFor='r3'>Excesso de Arrecadação</Label>
                </div>
              </RadioGroup>
            </div>
            <div className='mt-8 flex flex-wrap justify-center gap-6'>
              <RadioGroup
                value={tipoAcao(form.getValues('tipoAcao'))}
                className='grid-flow-col'
                disabled
              >
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='suplementacao' />
                  <Label htmlFor='r1'>Suplementação</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='criacaoDespesas' />
                  <Label htmlFor='r2'>Criação de Despesas</Label>
                </div>
              </RadioGroup>
            </div>
            <Separator className='my-12' />
            <>
              <div>
                {tpAcao == 'suplementacao' ? (
                  <div className='mt-6 flex flex-wrap justify-between gap-6'>
                    {tpAlt == 'anulacao' && (
                      <div className='flex flex-wrap justify-between'>
                        <FormLabel className='flex text-left'>
                          Anular a Despesa
                        </FormLabel>
                        <div className='flex w-full max-w-sm items-center space-x-2'>
                          <FormField
                            name='despesaAcao' //Debitar a Despesa:
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    {...field}
                                    pattern='[0-99]*'
                                    placeholder='0'
                                    className='max-w-[100px] text-right text-base'
                                    onBlur={carregarDespesaAcao}
                                    onChange={(event) => {
                                      const { value } = event.target;
                                      form.setValue(
                                        'despesaAcao',
                                        Number(numeroMask(value))
                                      );
                                      form.setValue('fonte', Number(0));
                                    }}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                          <Button
                            aria-description='Buscar Despesa'
                            disabled={loading || !form.watch('despesaAcao')}
                            onClick={carregarDespesaAcao}
                          >
                            Buscar
                            {loading ? (
                              <Loader2 className='size-4 animate-spin' />
                            ) : (
                              <Search className='size-4' />
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                    <div className='flex flex-wrap justify-between'>
                      <FormLabel className='flex text-left'>
                        Suplementar a Despesa
                      </FormLabel>
                      <div className='flex w-full max-w-sm items-center space-x-2'>
                        <FormField
                          name='despesaNova' //Debitar a Despesa:
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  pattern='[0-99]*'
                                  placeholder='0'
                                  className='max-w-[100px] text-right text-base'
                                  onBlur={carregarDespesaNova}
                                  onChange={(event) => {
                                    const { value } = event.target;
                                    form.setValue(
                                      'despesaNova',
                                      Number(numeroMask(value))
                                    );
                                  }}
                                  value={form.getValues('despesaNova')}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <Button
                          className='mr-8'
                          aria-description='Buscar Despesa'
                          disabled={loading || !form.watch('despesaNova')}
                          onClick={carregarDespesaNova}
                        >
                          Buscar
                          {loading ? (
                            <Loader2 className='size-4 animate-spin' />
                          ) : (
                            <Search className='size-4' />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className='mt-6 flex flex-wrap justify-between gap-6'>
                    {tpAlt == 'anulacao' ? (
                      <div className='flex flex-wrap justify-between'>
                        <FormLabel className='flex text-left'>
                          Anular a Despesa
                        </FormLabel>
                        <div className='flex w-full max-w-sm items-center space-x-2'>
                          <FormField
                            name='despesaAcao' //Debitar a Despesa:
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    {...field}
                                    pattern='[0-99]*'
                                    placeholder='0'
                                    className='max-w-[100px] text-right text-base'
                                    onBlur={carregarDespesaAcao}
                                    onChange={(event) => {
                                      const { value } = event.target;
                                      form.setValue(
                                        'despesaAcao',
                                        Number(numeroMask(value))
                                      );
                                      form.setValue('fonte', Number(0));
                                    }}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                          <Button
                            className='mr-8'
                            aria-description='Buscar Despesa'
                            disabled={loading || !form.watch('despesaAcao')}
                            onClick={carregarDespesaAcao}
                          >
                            Buscar
                            {loading ? (
                              <Loader2 className='size-4 animate-spin' />
                            ) : (
                              <Search className='size-4' />
                            )}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      ''
                    )}
                    <div className='flex flex-wrap justify-between'>
                      <FormLabel className='flex text-left'>
                        Copiar dados da Despesa
                      </FormLabel>
                      <div className='flex w-full max-w-sm items-center space-x-2'>
                        <FormField
                          name='despesaCopia'
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input
                                  {...field}
                                  pattern='[0-99]*'
                                  placeholder='0'
                                  className='max-w-[100px] text-right text-base'
                                  onBlur={carregarDespesaCopia}
                                  onChange={(event) => {
                                    const { value } = event.target;
                                    form.setValue(
                                      'despesaCopia',
                                      Number(numeroMask(value))
                                    );
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <Button
                          aria-description='Buscar Despesa'
                          disabled={loading || !form.watch('despesaCopia')}
                          onClick={carregarDespesaCopia}
                        >
                          Buscar
                          {loading ? (
                            <Loader2 className='size-4 animate-spin' />
                          ) : (
                            <Search className='size-4' />
                          )}
                        </Button>
                      </div>
                    </div>

                    <FormField
                      name='idEconomica'
                      render={() => (
                        <FormItem>
                          <FormLabel className='flex text-left'>
                            Economica
                          </FormLabel>
                          <FormControl>
                            <ComboboxSelecionaEconomica
                              economicas={economicas}
                              economicaId={economicaId}
                              setEconomicaId={setEconomicaId}
                              // habilitado={true}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      name='idFuncional'
                      render={() => (
                        <FormItem>
                          <FormLabel className='flex text-left'>
                            Funcional
                          </FormLabel>
                          <FormControl>
                            <ComboboxSelecionaFuncional
                              funcionais={funcionais}
                              funcionalId={funcionalId}
                              setFuncionalId={setFuncionalId}
                              habilitado={false}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      name='fonte'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className='flex text-left'>
                            Fonte
                          </FormLabel>
                          <Select
                            onValueChange={(e) => {
                              form.setValue('fonte', Number(e), {
                                shouldDirty: true,
                              });
                            }}
                            value={`${field.value}`}
                            //disabled
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder='Selecione a fonte' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.entries(Fontes)
                                .filter(
                                  (v) =>
                                    !isNaN(Number(v[0])) && Number(v[0]) > 0
                                )
                                .map((fonte) => (
                                  <SelectItem
                                    key={fonte[0]}
                                    value={`${fonte[0]}`}
                                  >
                                    {fonte[0]} - {fonte[1]}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                    <FormField
                      name='codAplicacao'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className='flex text-left'>
                            Código da Aplicação
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              pattern='[0-9]*'
                              placeholder='0'
                              className='max-w-[100px] text-right text-base'
                              onChange={(event) => {
                                const { value } = event.target;
                                form.setValue('codAplicacao', Number(value));
                              }}
                              // disabled
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                )}
                {despesa &&
                  !loading &&
                  tpAcao == 'suplementacao' &&
                  tpAlt == 'anulacao' && (
                    <div className='mt-6 w-full'>{`Despesa: ${despesa?.data?.despesa || ''} - Secretaria: ${despesa?.data?.departamento?.secretaria?.nome || ''} - Departamento: ${despesa?.data?.departamento?.nome || ''} - Fonte: ${despesa?.data?.fonte || ''}`}</div>
                  )}
                {despesaNova && !loading && tpAcao == 'suplementacao' && (
                  <div className='mt-4 w-full'>{`Despesa: ${despesaNova?.despesa || ''} - Secretaria: ${despesaNova?.secretaria?.nome || ''} - Departamento: ${despesaNova?.departamento?.nome || ''} - Fonte: ${despesaNova?.fonte || ''}`}</div>
                )}
                {despesa &&
                  !loading &&
                  tpAcao == 'suplementacao' &&
                  tpAlt != 'anulacao' && (
                    <div className='mt-4 w-full'>{`Despesa: ${despesa?.data?.despesa || ''} - Secretaria: ${despesa?.data?.departamento?.secretaria?.nome || ''} - Departamento: ${despesa?.data?.departamento?.nome || ''} - Fonte: ${despesa?.data?.fonte || ''}`}</div>
                  )}
              </div>
            </>
            <Separator className='my-12' />
            <span>Informe os valores</span>
            <div className='mt-8 flex flex-wrap justify-center gap-6'>
              <FormField
                name='valorTotal'
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex text-left'>
                      Valor Total da Operação
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right text-base'
                        value={totalBRL}
                        onChange={(e) => {
                          setTotalBRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorTotal',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name='restante'
                render={({ field }) => (
                  <FormItem className='w-[150px]'>
                    <FormLabel className='flex text-left'>
                      Valor Restante
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className={cn(
                          'text-right text-base disabled:opacity-100',
                          restante.value != 0
                            ? 'text-red-600'
                            : 'text-green-600'
                        )}
                        value={restante.format().replace(/0$/, '')}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              <FormField
                name='valorMes1'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Janeiro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes1') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes1
                            : subMes1
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes1
                          : subMes1
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes1BRL}
                        onChange={(e) => {
                          setMes1BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes1',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 1
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes1) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes1}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes2'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Fevereiro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes2') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes2
                            : subMes2
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes2
                          : subMes2
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes2BRL}
                        onChange={(e) => {
                          setMes2BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes2',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 2
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes2') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes2) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes2}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes3'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Março
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes3') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes3
                            : subMes3
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes3
                          : subMes3
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes3BRL}
                        onChange={(e) => {
                          setMes3BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes3',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 3
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes3) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes3}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes4'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Abril
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes4') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes4
                            : subMes4
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes4
                          : subMes4
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes4BRL}
                        onChange={(e) => {
                          setMes4BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes4',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 4
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes4) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes4}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes5'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Maio
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes5') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes5
                            : subMes5
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes5
                          : subMes5
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes5BRL}
                        onChange={(e) => {
                          setMes5BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes5',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 5
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes5) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes5}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes6'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Junho
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes6') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes6
                            : subMes6
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes6
                          : subMes6
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes6BRL}
                        onChange={(e) => {
                          setMes6BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes6',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 6
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes6) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes6}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes7'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Julho
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes7') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes7
                            : subMes7
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes7
                          : subMes7
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes7BRL}
                        onChange={(e) => {
                          setMes7BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes7',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 7
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes7) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes7}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes8'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Agosto
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes8') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes8
                            : subMes8
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes8
                          : subMes8
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes8BRL}
                        onChange={(e) => {
                          setMes8BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes8',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 8
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes8) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes8}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes9'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Setembro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes9') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes9
                            : subMes9
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes9
                          : subMes9
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes9BRL}
                        onChange={(e) => {
                          setMes9BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes9',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 9
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes9) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes9}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes10'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Outubro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes10') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes10
                            : subMes10
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes10
                          : subMes10
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes10BRL}
                        onChange={(e) => {
                          setMes10BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes10',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 10
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes10) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes10}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes11'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Novembro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes11') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes11
                            : subMes11
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes11
                          : subMes11
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes11BRL}
                        onChange={(e) => {
                          setMes11BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes11',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 11
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes11) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes11}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
              <FormField
                name='valorMes12'
                render={({ field }) => (
                  <FormItem className='flex max-w-[160px] flex-wrap rounded-2xl border p-4 shadow-xs'>
                    <FormLabel className='flex w-full text-left'>
                      Dezembro
                    </FormLabel>
                    <Input
                      {...field}
                      className={cn(
                        'max-w-[150px] text-right',
                        form.getValues('valorMes12') > 0 &&
                          'text-green-600 disabled:opacity-100',
                        moneyUnmask(
                          tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                            ? supMes12
                            : subMes12
                        ) < 0 && 'text-red-600 disabled:opacity-100'
                      )}
                      value={
                        tpAlt != 'anulacao' /*&& tpAcao == 'suplementacao'*/
                          ? supMes12
                          : subMes12
                      }
                      disabled={true}
                    />
                    <FormLabel className='mt-2 flex w-full text-left'>
                      Usar
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='max-w-[150px] text-right'
                        value={mes12BRL}
                        onChange={(e) => {
                          setMes12BRL(moneyMask(e.target.value));
                          form.setValue(
                            'valorMes12',
                            Number(moneyUnmask(e.target.value)),
                            { shouldDirty: true }
                          );
                        }}
                        onFocus={(e) => e.currentTarget.select()}
                        disabled={
                          (tpAlt == 'anulacao' && !despesa) || mesAtual > 12
                        }
                      />
                    </FormControl>
                    {tpAlt == 'anulacao' && tpAcao == 'suplementacao' ? (
                      <div>
                        <FormLabel className='flex w-full text-left'>
                          Suplementado
                        </FormLabel>
                        <Input
                          {...field}
                          className={cn(
                            'max-w-[150px] text-right',
                            form.getValues('valorMes1') > 0 &&
                              'text-green-600 disabled:opacity-100',
                            moneyUnmask(supMes12) < 0 &&
                              'text-red-600 disabled:opacity-100'
                          )}
                          value={supMes12}
                          disabled={true}
                        />
                      </div>
                    ) : (
                      ''
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-12 flex flex-wrap justify-center gap-6'>
              <FormField
                name='valorAtual'
                render={() => (
                  <FormItem className='w-[150px]'>
                    <FormLabel className='flex text-left'>
                      Valor Disponível na Despesa
                    </FormLabel>
                    <FormControl>
                      <Input
                        className={cn(
                          'text-right text-base disabled:opacity-100'
                          /*total.value === 0 ||
                            (despesa?.data?.valorAtual &&
                              total.value > despesa.data.valorAtual)
                            ? 'text-red-600'
                            : 'text-green-600'*/
                        )}
                        value={subValorAtual}
                        disabled={true}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <Separator className='my-12' />
            <div className='mt-8 flex flex-wrap justify-between gap-6'>
              <FormField
                control={form.control}
                name='obs'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Observação
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Digite...'
                        className='resize-none'
                        {...field}
                        maxLength={255}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className='mt-12 flex w-full justify-between'>
              <Button
                variant={'destructive'}
                disabled={loading}
                onClick={(e) => {
                  e.preventDefault();
                  router.push('/movimento/alteracaoOrcamentaria');
                }}
              >
                <ArrowLeft className='mr-2 h-4 w-4' /> Cancelar
              </Button>
              <Button
                onClick={form.handleSubmit(onSubmit, onInvalid)}
                // type='submit'
                /*onClick={() => {
                  onSubmit(form.getValues())
                }}*/
                disabled={
                  loading ||
                  total.value === 0 ||
                  restante.value != 0 ||
                  Object.keys(form.formState.dirtyFields).length === 0
                }
              >
                {loading ? (
                  <>
                    <Icons.loader className='mr-2 h-4 w-4 animate-spin' />
                    Aguarde...
                  </>
                ) : (
                  <>
                    <Save className='mr-2 h-4 w-4' /> Salvar
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
}
