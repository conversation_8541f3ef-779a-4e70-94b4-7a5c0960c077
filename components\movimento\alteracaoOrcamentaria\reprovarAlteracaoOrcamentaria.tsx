import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ReprovarAltOrc } from '@/lib/database/movimento/alteracaoOrcamentaria';
import { toastAlgoDeuErrado } from '@/lib/utils';
import { idSchema } from '@/lib/validation';
import { ThumbsDown } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';

export function ReprovarAlteracaoOrcamentaria({
  alteracaoOrcamentariaCheck,
  unmarkSelect,
}: {
  alteracaoOrcamentariaCheck: Array<any>;
  unmarkSelect: Function;
}) {
  const [loading, setLoading] = useState(false);

  const reprovar = async () => {
    try {
      setLoading(true);

      for (let index = 0; index < alteracaoOrcamentariaCheck.length; index++) {
        const id = alteracaoOrcamentariaCheck[index].original.id;
        if (!alteracaoOrcamentariaCheck[index].original.ativo) {
          toast.error('Alteração Orçamentária ID ' + id + ' está inativa');
          setLoading(false);
          return;
        }

        const data: z.infer<typeof idSchema> = {
          id,
        };

        const res = await ReprovarAltOrc(data);
        if (res?.error) {
          setLoading(false);
          toast.error('Alteração Orçamentária ID ' + id + ' - ' + res.error);
        } else {
          toast.success('Alterações Orçamentárias Reprovadas.');
        }
      }
      unmarkSelect();
    } catch (error: any) {
      console.log(error);
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button type='button' variant='outline'>
          <ThumbsDown className='mr-2 size-4' /> Reprovar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Reprovar Alterações Orçamentárias</AlertDialogTitle>
          <AlertDialogDescription>
            Tem certeza que deseja reprovar as alterações orçamentárias?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              reprovar();
            }}
            disabled={loading}
          >
            {loading ? 'Aguarde...' : 'Reprovar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
