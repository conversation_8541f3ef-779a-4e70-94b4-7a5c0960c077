-- AlterTable
ALTER TABLE "empenhos" ADD COLUMN     "idContrato" INTEGER;

-- AlterTable
ALTER TABLE "liquidacoes" ADD COLUMN     "marca_documento" VARCHAR(50);

-- CreateIndex
CREATE INDEX "empenhos_idContrato_idx" ON "empenhos"("idContrato");

-- CreateIndex
CREATE INDEX "liquidacoes_marca_documento_idx" ON "liquidacoes"("marca_documento");

-- AddForeignKey
ALTER TABLE "empenhos" ADD CONSTRAINT "empenhos_idContrato_fkey" FOREIGN KEY ("idContrato") REFERENCES "contratos"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
