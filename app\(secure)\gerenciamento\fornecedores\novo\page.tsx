'use server';
import PageWrapper from '@/components/pages/pageWrapper';
import PageHeader from '@/components/pages/pageHeader';
import PageTitle from '@/components/pages/pageTitle';
import PageContent from '@/components/pages/pageContent';
import CriarFornecedorForm from '@/components/gerenciamento/Fornecedor/criarFornecedorForm';
import { listarFornecedores } from '@/lib/database/gerenciamento/fornecedores';
import { ErrorAlert } from '@/components/error-alert';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export default async function NovoFornecedorPage() {
  const fornecedoresPromise = listarFornecedores();

  const [fornecedores] = await Promise.all([fornecedoresPromise]);

  if (!fornecedores) {
    return <ErrorAlert error='Falha ao obter fornecedores.' />;
  }
  if (fornecedores.error) {
    return <ErrorAlert error={fornecedores.error} />;
  }

  return (
    <PageWrapper>
      <PageHeader>
        <PageTitle>Novo Fornecedor</PageTitle>
      </PageHeader>
      <PageContent>
        <div className='w-full'>
          <Suspense fallback={<Skeleton className='h-[800px] w-full' />}>
            <CriarFornecedorForm fornecedores={fornecedores} />
          </Suspense>
        </div>
      </PageContent>
    </PageWrapper>
  );
}
