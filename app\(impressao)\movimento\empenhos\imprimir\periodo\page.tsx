'use server';
import { z } from 'zod';
import { ErrorAlert } from '@/components/error-alert';
import { RelatorioEmpenhoPeriodo } from '@/components/relatorios/relatorioEmpenhoPeriodo';
import { gerarRelatorioEmpenhosPorPeriodo } from '@/lib/database/relatorios/empenhos';
import { permissaoSchema } from '@/lib/validation';
import { Modulos } from '@/lib/modulos';
import { Permisso<PERSON> } from '@/lib/enums';
import { temPermissao } from '@/lib/database/usuarios';
import { createClientWithBearer } from '@/lib/supabase/server';
import { headers } from 'next/headers';
import ClientCompletionTrigger from '@/components/clientCompletionTrigger';

export default async function ImprimirEmpenhosPorPeriodoPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_EMPENHO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const currSearchParams = await searchParams;
  const headers_ = await headers();
  if (!headers_.get('Authorization')) {
    return <ErrorAlert error='Sem parâmetros de autenticação' />;
  }

  const bearer = headers_.get('Authorization') || '';
  const supabase = await createClientWithBearer(bearer);
  const user = await supabase.auth.getUser();

  if (!user) {
    return <ErrorAlert error='Não foi possível validar autenticação.' />;
  }

  const result = await temPermissao({ ...parametrosPermissao, bearer });

  if (!result || !result.exercicio) {
    return (
      <ErrorAlert error='Não foi possível verificar as permissões do usuário.' />
    );
  }

  // Validate required parameters for period report
  if (
    !currSearchParams.periodoInicio ||
    !currSearchParams.periodoFim ||
    !currSearchParams.agruparPor
  ) {
    return (
      <ErrorAlert error='Parâmetros obrigatórios não informados: período de início, período de fim e agrupamento.' />
    );
  }

  const params = {
    exercicio: currSearchParams.exercicio
      ? parseInt(currSearchParams.exercicio as string)
      : result.exercicio,
    idSecretaria: currSearchParams.secretaria
      ? parseInt(currSearchParams.secretaria as string)
      : undefined,
    idDepartamento: currSearchParams.departamento
      ? parseInt(currSearchParams.departamento as string)
      : undefined,
    idSubdepartamento: currSearchParams.subdepartamento
      ? parseInt(currSearchParams.subdepartamento as string)
      : undefined,
    status: currSearchParams.status
      ? Array.isArray(currSearchParams.status)
        ? currSearchParams.status.map((s) => parseInt(s as string))
        : [parseInt(currSearchParams.status as string)]
      : undefined,
    idFornecedor: currSearchParams.fornecedor
      ? parseInt(currSearchParams.fornecedor as string)
      : undefined,
    periodoInicio: new Date(currSearchParams.periodoInicio as string),
    periodoFim: new Date(currSearchParams.periodoFim as string),
    agruparPor: currSearchParams.agruparPor as
      | 'mes'
      | 'secretaria'
      | 'departamento'
      | 'fornecedor',
    bearer,
  };

  const empenhosResult = await gerarRelatorioEmpenhosPorPeriodo(params);

  if (empenhosResult.error) {
    return <ErrorAlert error={empenhosResult.error} />;
  }

  if (!empenhosResult.data) {
    return (
      <ErrorAlert error='Falha ao obter relatório de empenhos por período.' />
    );
  }

  return (
    <>
      <RelatorioEmpenhoPeriodo
        grupos={empenhosResult.data.grupos}
        totalGeral={empenhosResult.data.totalGeral}
        agrupadoPor={empenhosResult.data.agrupadoPor}
        periodo={empenhosResult.data.periodo}
        titulo='Relatório de Empenhos por Período'
      />
      <ClientCompletionTrigger />
    </>
  );
}
