'use client';
import { ColumnDef } from '@tanstack/react-table';
import currency from 'currency.js';
import { currencyOptionsNoSymbol, montarPathPdfReserva } from '@/lib/utils';
import { listarAssinaturasReservasPendentes } from '@/lib/database/assinatura/reservas';
import { ReusableDatatableFiltroIdDespesaSecretariaDepartamento } from '@/components/datatable/reusableDatatableFiltroIdDespesaSecretariaDepartamento';
import { BotaoDownloadPDFAssinaturaReserva } from './botaoDownloadPDFAssinaturaReserva';
import {
  Assinatura,
  AssinaturaProvider,
  BotaoAssinarReserva,
  BotaoAssinarReservasSelecionadas,
  DialogAssinarReserva,
} from './componentesAssinarReserva';
import { Checkbox } from '@/components/ui/checkbox';
import { useState } from 'react';
import { DialogDevolverReserva } from './dialogDevolverReserva';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export default function AssinaturaReservasDatatable({
  data,
}: {
  data: Awaited<ReturnType<typeof listarAssinaturasReservasPendentes>>;
}) {
  const [selecao, setSelecao] = useState<Assinatura[]>([]);
  if (!data.data?.assinaturas) return null;
  const columns: ColumnDef<(typeof data.data.assinaturas)[0]>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          className='block'
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => {
            table.toggleAllPageRowsSelected(!!value);
            if (!!value) {
              setSelecao(
                data.data.assinaturas.map((assinatura) => ({
                  idReserva: assinatura.idReserva,
                  idAssinatura: assinatura.id,
                }))
              );
            } else {
              setSelecao([]);
            }
          }}
          aria-label='Selecionar todos'
        />
      ),
      cell: ({ row, table }) => (
        <Checkbox
          className='block'
          checked={row.getIsSelected()}
          onCheckedChange={(value) => {
            row.toggleSelected(!!value);
            const novosSelecionados = table
              .getSelectedRowModel()
              .rows.map((row) => ({
                idReserva: row.original.reserva.id,
                idAssinatura: row.original.id,
              }));
            if (!!value) {
              setSelecao([
                ...novosSelecionados,
                {
                  idReserva: row.original.reserva.id,
                  idAssinatura: row.original.id,
                },
              ]);
            } else {
              setSelecao(
                novosSelecionados.filter(
                  (s) => s.idAssinatura !== row.original.id
                )
              );
            }
          }}
          aria-label='Selecionar linha'
        />
      ),
      meta: {
        className: 'w-[31px]',
      },
    },
    {
      id: 'id',
      accessorKey: 'reserva.id',
      header: 'Reserva',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'reserva.dotacao.despesa',
      id: 'despesa',
      header: 'Despesa',
      filterFn: 'includesString',
      cell: ({ row }) => (
        <div className='flex flex-col'>
          <span className='text-xs font-medium'>
            {row.original.reserva.dotacao.despesa}
          </span>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className='text-muted-foreground max-w-[150px] cursor-help truncate text-xs'>
                {row.original.reserva.dotacao.desc}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className='max-w-xs'>{row.original.reserva.dotacao.desc}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      ),
    },
    {
      accessorKey: 'reserva.resumo',
      header: 'Resumo',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'reserva.obs',
      header: 'Obs',
      filterFn: 'includesString',
    },
    {
      accessorKey: 'reserva.usarTotal',
      header: 'Valor Total',
      cell: ({ getValue }) => {
        return currency(getValue<number>(), currencyOptionsNoSymbol)
          .format()
          .replace(/0$/, '');
      },
    },
    {
      id: 'secretariaId',
      accessorKey: 'reserva.secretariaId',
      header: 'Secretaria',
      filterFn: 'equals',
    },
    {
      id: 'departamentoId',
      accessorKey: 'reserva.departamentoId',
      header: 'Departamento',
      filterFn: 'equals',
    },
    {
      accessorKey: '',
      header: 'Ações',
      cell: ({ row }) => (
        <div className='flex justify-center gap-4'>
          <DialogDevolverReserva idReserva={row.original.reserva.id} />
          <BotaoAssinarReserva
            idReserva={row.original.reserva.id}
            idAssinatura={row.original.id}
          />
          <BotaoDownloadPDFAssinaturaReserva
            url={montarPathPdfReserva(
              row.original.reserva.id,
              row.original.reserva.dotacao.id,
              row.original.reserva.exercicio,
              row.original.reserva.ultimaModificacao
            )}
          />
        </div>
      ),
    },
  ];
  return (
    <TooltipProvider>
      <div className='container mx-auto'>
        <AssinaturaProvider>
          <div className='flex justify-end'>
            <BotaoAssinarReservasSelecionadas assinaturas={selecao} />
          </div>
          <ReusableDatatableFiltroIdDespesaSecretariaDepartamento
            columns={columns}
            data={data.data.assinaturas}
            secretarias={data.data.secretarias}
            departamentos={data.data.departamentos}
          />
          <DialogAssinarReserva />
        </AssinaturaProvider>
      </div>
    </TooltipProvider>
  );
}
