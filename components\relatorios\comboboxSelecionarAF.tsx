'use client';

import { useState, useEffect } from 'react';
import { listarAFsParaRelatorio } from '@/lib/database/relatorios/afs';
import { StatusAF, StatusAFDesc } from '@/lib/enums';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AFOption {
  id: number;
  numero: number;
  exercicio: number;
  resumo: string | null;
  valorTotal: number;
  status: number;
  data: Date;
  empenho: {
    numero: number;
    exercicio: number;
    fornecedor: {
      nome: string;
    } | null;
  };
}

interface ComboboxSelecionarAFProps {
  onAFChange: (af: AFOption | null) => void;
  placeholder?: string;
  exercicio?: number;
  status?: number;
}

export const ComboboxSelecionarAF = ({
  onAFChange,
  placeholder = 'Selecione uma AF...',
  exercicio,
  status,
}: ComboboxSelecionarAFProps) => {
  const [open, setOpen] = useState(false);
  const [selectedAF, setSelectedAF] = useState<AFOption | null>(null);
  const [afs, setAFs] = useState<AFOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadAFs = async () => {
      setLoading(true);
      try {
        const result = await listarAFsParaRelatorio({
          exercicio,
          status,
          bearer: '',
        });

        if (result.data) {
          const options: AFOption[] = result.data.afs.map((af) => ({
            id: af.id,
            numero: af.numero,
            exercicio: af.exercicio,
            resumo: af.resumo,
            valorTotal: af.valorTotal,
            status: af.status,
            data: af.data,
            empenho: {
              numero: af.empenho.numero,
              exercicio: af.empenho.exercicio,
              fornecedor: af.empenho.fornecedor,
            },
          }));
          setAFs(options);
        }
      } catch (error) {
        console.error('Erro ao carregar AFs:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAFs();
  }, [exercicio, status]);

  const handleAFSelect = (af: AFOption) => {
    setSelectedAF(af);
    setOpen(false);
    onAFChange(af);
  };

  if (loading) {
    return (
      <Button variant='outline' className='w-full justify-between' disabled>
        <span>Carregando AFs...</span>
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
        >
          {selectedAF ? (
            <div className='flex flex-col items-start'>
              <span>
                AF {selectedAF.numero}/{selectedAF.exercicio} - Empenho{' '}
                {selectedAF.empenho.numero}/{selectedAF.empenho.exercicio}
              </span>
              {selectedAF.empenho.fornecedor && (
                <span className='text-muted-foreground text-xs'>
                  {selectedAF.empenho.fornecedor.nome}
                </span>
              )}
            </div>
          ) : (
            placeholder
          )}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-full p-0'>
        <Command>
          <CommandInput placeholder='Buscar AF...' />
          <CommandEmpty>Nenhuma AF encontrada.</CommandEmpty>
          <CommandGroup>
            {afs.map((af) => (
              <CommandItem
                key={af.id}
                onSelect={() => handleAFSelect(af)}
                className='flex flex-col items-start py-2'
              >
                <div className='flex w-full items-center'>
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      selectedAF?.id === af.id ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  <div className='flex flex-col'>
                    <span className='font-medium'>
                      AF {af.numero}/{af.exercicio} - Empenho{' '}
                      {af.empenho.numero}/{af.empenho.exercicio}
                    </span>
                    {af.empenho.fornecedor && (
                      <span className='text-muted-foreground text-xs'>
                        {af.empenho.fornecedor.nome}
                      </span>
                    )}
                    {af.resumo && (
                      <span className='text-muted-foreground text-xs'>
                        {af.resumo}
                      </span>
                    )}
                    <div className='mt-1 flex items-center gap-2'>
                      <span
                        className={cn(
                          'rounded px-1 py-0.5 text-xs',
                          af.status === StatusAF.ATIVA
                            ? 'bg-green-100 text-green-800'
                            : af.status === StatusAF.UTILIZADA
                              ? 'bg-blue-100 text-blue-800'
                              : af.status === StatusAF.CANCELADA
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                        )}
                      >
                        {StatusAFDesc[af.status as StatusAF]}
                      </span>
                      <span className='text-muted-foreground text-xs'>
                        {new Date(af.data).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                  </div>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
