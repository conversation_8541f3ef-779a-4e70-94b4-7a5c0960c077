import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { renomearSecretaria } from '@/lib/database/gerenciamento/secretarias';
import {
  codigoSecretariaMask,
  uppercaseMask,
  toastAlgoDeuErrado,
} from '@/lib/utils';
import { nomeSchema, nomeEIdSchema } from '@/lib/validation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Pencil, Save } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

export function DialogRenomearSecretaria({
  idSecretaria,
  nomeSecretaria,
  codigoSecretaria,
}: {
  idSecretaria: number;
  nomeSecretaria: string;
  codigoSecretaria: number;
}) {
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof nomeSchema>>({
    resolver: zodResolver(nomeSchema),
    defaultValues: {
      nome: nomeSecretaria,
    },
  });

  const onSubmit = async (values: z.infer<typeof nomeSchema>) => {
    try {
      setLoading(true);
      const data: z.infer<typeof nomeEIdSchema> = {
        id: idSecretaria,
        nome: values.nome,
      };
      const res = await renomearSecretaria(data);
      if (res?.error) {
        setLoading(false);
        toast(res.error);
      } else {
        toast.success('Secretaria renomeada.');
      }
    } catch (error: any) {
      setLoading(false);
      toast.error(toastAlgoDeuErrado);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button type='button' variant='outline'>
          <Pencil className='mr-2 size-4' /> Renomear
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Renomear Secretaria</DialogTitle>
          <DialogDescription>
            {codigoSecretariaMask(codigoSecretaria.toString())}.00.00 -{' '}
            {nomeSecretaria}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='flex gap-4 py-4'>
              <FormField
                control={form.control}
                name='nome'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='flex w-full text-left'>
                      Nome
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Digite...'
                        maxLength={150}
                        minLength={2}
                        onChange={(event) => {
                          const { value } = event.target;
                          form.setValue('nome', uppercaseMask(value));
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogFooter>
          <Button
            type='submit'
            disabled={loading || !form.formState.isValid}
            onClick={form.handleSubmit(onSubmit)}
          >
            {loading ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Aguarde...
              </>
            ) : (
              <>
                <Save className='mr-2 h-4 w-4' /> Salvar
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
