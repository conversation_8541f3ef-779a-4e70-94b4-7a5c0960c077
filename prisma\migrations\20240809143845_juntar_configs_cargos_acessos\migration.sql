/*
  Warnings:

  - You are about to drop the `cargos_departamentos` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `cargos_secretarias` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `cargos_subdepartamentos` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "cargos_departamentos" DROP CONSTRAINT "cargos_departamentos_cargoId_fkey";

-- DropForeignKey
ALTER TABLE "cargos_departamentos" DROP CONSTRAINT "cargos_departamentos_departamentoId_fkey";

-- DropForeignKey
ALTER TABLE "cargos_secretarias" DROP CONSTRAINT "cargos_secretarias_cargoId_fkey";

-- DropForeignKey
ALTER TABLE "cargos_secretarias" DROP CONSTRAINT "cargos_secretarias_secretariaId_fkey";

-- DropForeignKey
ALTER TABLE "cargos_subdepartamentos" DROP CONSTRAINT "cargos_subdepartamentos_cargoId_fkey";

-- DropForeignKey
ALTER TABLE "cargos_subdepartamentos" DROP CONSTRAINT "cargos_subdepartamentos_subdepartamentoId_fkey";

-- DropTable
DROP TABLE "cargos_departamentos";

-- DropTable
DROP TABLE "cargos_secretarias";

-- DropTable
DROP TABLE "cargos_subdepartamentos";

-- CreateTable
CREATE TABLE "cargos_acessos" (
    "id" SERIAL NOT NULL,
    "cargoId" INTEGER NOT NULL,
    "secretariaId" INTEGER,
    "departamentoId" INTEGER,
    "subdepartamentoId" INTEGER,

    CONSTRAINT "cargos_acessos_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cargos_acessos_cargoId_secretariaId_key" ON "cargos_acessos"("cargoId", "secretariaId");

-- CreateIndex
CREATE UNIQUE INDEX "cargos_acessos_cargoId_departamentoId_key" ON "cargos_acessos"("cargoId", "departamentoId");

-- CreateIndex
CREATE UNIQUE INDEX "cargos_acessos_cargoId_subdepartamentoId_key" ON "cargos_acessos"("cargoId", "subdepartamentoId");

-- AddForeignKey
ALTER TABLE "cargos_acessos" ADD CONSTRAINT "cargos_acessos_cargoId_fkey" FOREIGN KEY ("cargoId") REFERENCES "cargos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos" ADD CONSTRAINT "cargos_acessos_secretariaId_fkey" FOREIGN KEY ("secretariaId") REFERENCES "secretarias"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos" ADD CONSTRAINT "cargos_acessos_departamentoId_fkey" FOREIGN KEY ("departamentoId") REFERENCES "departamentos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cargos_acessos" ADD CONSTRAINT "cargos_acessos_subdepartamentoId_fkey" FOREIGN KEY ("subdepartamentoId") REFERENCES "subdepartamentos"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
