import { siteConfig } from '@/config/site';
import { StatusEmpenho, StatusEmpenhoDesc } from '@/lib/enums';
import { toCurrency } from '@/lib/serverUtils';

interface RelatorioEmpenhoListaProps {
  empenhos: NonNullable<
    Required<
      Awaited<
        ReturnType<
          typeof import('@/lib/database/relatorios/empenhos').gerarRelatorioEmpenhos
        >
      >
    >
  >['data']['empenhos'];
  titulo?: string;
  filtros?: {
    exercicio?: number;
    secretaria?: string;
    departamento?: string;
    subdepartamento?: string;
    fornecedor?: string;
    status?: number[];
    dataInicio?: string;
    dataFim?: string;
    incluirLiquidados?: boolean;
    incluirNaoLiquidados?: boolean;
  };
  resumo?: {
    totalEmpenhos: number;
    valorTotal: number;
    valorTotalLiquidado: number;
    valorTotalProcessado: number;
    valorTotalNaoProcessado: number;
    saldoTotalDisponivel: number;
  };
}

export const RelatorioEmpenhoLista = ({
  empenhos,
  titulo = 'Relatório de Empenhos',
  filtros,
  resumo,
}: RelatorioEmpenhoListaProps) => {
  const getStatusDescription = (status: number) => {
    return StatusEmpenhoDesc[status as StatusEmpenho] || 'N/A';
  };

  const getStatusText = () => {
    if (!filtros?.status || filtros.status.length === 0) return 'Todos';
    if (filtros.status.length === 1)
      return getStatusDescription(filtros.status[0]);
    return 'Múltiplos';
  };

  return (
    <div className='mx-auto max-w-[1200px] p-4'>
      {/* Cabeçalho */}
      <div className='mb-6 text-center'>
        <h1 className='mb-2 text-xl font-bold'>{siteConfig.name}</h1>
        <h2 className='mb-1 text-lg font-semibold'>{titulo}</h2>
        {filtros && (
          <div className='space-y-1 text-sm text-gray-600'>
            {filtros.exercicio && <p>Exercício: {filtros.exercicio}</p>}
            {filtros.secretaria && <p>Secretaria: {filtros.secretaria}</p>}
            {filtros.departamento && (
              <p>Departamento: {filtros.departamento}</p>
            )}
            {filtros.subdepartamento && (
              <p>Subdepartamento: {filtros.subdepartamento}</p>
            )}
            {filtros.fornecedor && <p>Fornecedor: {filtros.fornecedor}</p>}
            {filtros.status && filtros.status.length > 0 && (
              <p>Status: {getStatusText()}</p>
            )}
            {(filtros.incluirLiquidados !== undefined ||
              filtros.incluirNaoLiquidados !== undefined) && (
              <p>
                Liquidação:{' '}
                {filtros.incluirLiquidados && filtros.incluirNaoLiquidados
                  ? 'Todos'
                  : filtros.incluirLiquidados
                    ? 'Liquidados'
                    : filtros.incluirNaoLiquidados
                      ? 'Não Liquidados'
                      : 'Todos'}
              </p>
            )}
            {filtros.dataInicio && filtros.dataFim && (
              <p>
                Período: {filtros.dataInicio} a {filtros.dataFim}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Resumo */}
      {resumo && (
        <div className='mb-6 rounded-lg bg-gray-50 p-4'>
          <h3 className='mb-3 border-b pb-1 font-semibold'>Resumo</h3>
          <div className='grid grid-cols-5 gap-3 text-sm'>
            <div className='text-center'>
              <div className='font-medium'>Total de Empenhos</div>
              <div className='font-mono'>{resumo.totalEmpenhos}</div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Total</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotal).format().replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Liquidado</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotalLiquidado)
                  .format()
                  .replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Valor Processado</div>
              <div className='font-mono'>
                {toCurrency(resumo.valorTotalProcessado)
                  .format()
                  .replace(/0$/, '')}
              </div>
            </div>
            <div className='text-center'>
              <div className='font-medium'>Saldo Disponível</div>
              <div
                className={`font-mono ${resumo.saldoTotalDisponivel < 0 ? 'text-red-600' : ''}`}
              >
                {toCurrency(resumo.saldoTotalDisponivel)
                  .format()
                  .replace(/0$/, '')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabela */}
      <div className='overflow-x-auto'>
        <table className='w-full border-collapse text-xs'>
          <thead>
            <tr className='border-b-2 border-gray-800'>
              <th className='p-1 text-left font-bold'>Empenho</th>
              <th className='p-1 text-left font-bold'>Fornecedor</th>
              <th className='p-1 text-left font-bold'>Secretaria</th>
              <th className='p-1 text-left font-bold'>Departamento</th>
              <th className='p-1 text-left font-bold'>Despesa</th>
              <th className='p-1 text-left font-bold'>Status</th>
              <th className='p-1 text-left font-bold'>Data</th>
              <th className='p-1 text-right font-bold'>Valor Total</th>
              <th className='p-1 text-right font-bold'>Valor Liquidado</th>
              <th className='p-1 text-right font-bold'>Saldo</th>
            </tr>
          </thead>
          <tbody>
            {empenhos.map((emp, index) => (
              <tr
                key={emp.id}
                className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
              >
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {emp.numero}/{emp.exercicio}
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={emp.fornecedor?.nome || 'N/A'}
                  >
                    {emp.fornecedor?.nome || 'N/A'}
                  </div>
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={emp.dotacao.secretaria?.nome || 'N/A'}
                  >
                    {emp.dotacao.secretaria?.nome || 'N/A'}
                  </div>
                </td>
                <td className='max-w-xs border-b border-gray-200 p-1'>
                  <div
                    className='truncate'
                    title={emp.dotacao.departamento?.nome || 'N/A'}
                  >
                    {emp.dotacao.departamento?.nome || 'N/A'}
                  </div>
                </td>
                <td className='border-b border-gray-200 p-1 font-mono'>
                  {emp.dotacao.despesa}
                </td>
                <td className='border-b border-gray-200 p-1'>
                  <span
                    className={`rounded px-1 py-0.5 text-xs ${
                      emp.status === StatusEmpenho.EMPENHADO
                        ? 'bg-green-100 text-green-800'
                        : emp.status === StatusEmpenho.CANCELADO
                          ? 'bg-red-100 text-red-800'
                          : emp.status === StatusEmpenho.ANULADO
                            ? 'bg-orange-100 text-orange-800'
                            : emp.status === StatusEmpenho.LIQUIDADO
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {getStatusDescription(emp.status)}
                  </span>
                </td>
                <td className='border-b border-gray-200 p-1'>
                  {new Date(emp.data).toLocaleDateString('pt-BR')}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(emp.valorTotal).format().replace(/0$/, '')}
                </td>
                <td className='border-b border-gray-200 p-1 text-right font-mono'>
                  {toCurrency(emp.valorLiquidado).format().replace(/0$/, '')}
                </td>
                <td
                  className={`border-b border-gray-200 p-1 text-right font-mono ${
                    emp.saldoDisponivel < 0 ? 'text-red-600' : ''
                  }`}
                >
                  {toCurrency(emp.saldoDisponivel).format().replace(/0$/, '')}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Rodapé */}
      <div className='mt-6 text-center text-xs text-gray-500'>
        <p>Relatório gerado em {new Date().toLocaleString('pt-BR')}</p>
        <p>Total de registros: {empenhos.length}</p>
        <p className='mt-1'>{siteConfig.name}</p>
      </div>
    </div>
  );
};
