'use server';

import { AuditoriaMovimentoContratos, Permisso<PERSON> } from '@/lib/enums';
import { <PERSON><PERSON><PERSON> } from '@/lib/modulos';
import {
  auditoriaErroSchema,
  criarContratoSchema,
  idSchema,
  imprimirContratoSchema,
  permissaoSchema,
} from '@/lib/validation';
import {
  listarIdsDotacoesUsuarioConectadoTemAcesso,
  obterIpUsuarioConectado,
  temPermissao,
} from '../usuarios';
import { prisma } from '@/lib/prisma';
import { inserirErroAudit } from '../auditoria/erro';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { revalidatePath } from 'next/cache';
import { formataData } from '@/lib/utils';
import { Prisma } from '@prisma/client';

export const listarContratos = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    let acessos;
    if (!resultPermissao.gerente) {
      acessos = await listarIdsDotacoesUsuarioConectadoTemAcesso();

      if (acessos.error) {
        return {
          error: acessos.error,
        };
      }

      if (!acessos.data) {
        return {
          error: 'Não foi possível obter acessos do usuário.',
        };
      }
    }

    let where: Prisma.contratosWhereInput | null;

    if (resultPermissao.gerente || acessos?.data?.acessoTotal) {
      where = {
        exercicio: resultPermissao.exercicio,
      };
    } else {
      where = {
        exercicio: resultPermissao.exercicio,
        // idDotacao: { in: acessos?.data?.dotacoes },
      };
    }

    const contratos = await prisma.contratos.findMany({
      include: {
        fornecedor: true,
      },
      where,
      orderBy: { id: 'desc' },
    });

    return {
      data: {
        contratos: contratos.map((contrato) => {
          return {
            ...contrato,
            // exercicio: contrato.exercicio,
            valor: contrato.valor.toNumber(),
            idFornecedor:
              contrato.fornecedor.id.toString() +
              ' - ' +
              contrato.fornecedor.nome,
            dataInicio: formataData(contrato.dataInicio),
            dataFinal: formataData(contrato.dataFinal),
            processoAno: contrato.processoAno,
          };
        }),
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter contratos.`,
    };
  }
};

export const listarFornecedores = async () => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  try {
    const contratos = await prisma.contratos.groupBy({
      by: ['idFornecedor'],
      where: {
        exercicio: resultPermissao.exercicio,
      },
    });

    const idsFornecedores = contratos.map((contrato) => contrato.idFornecedor);
    //const idsFornecedores: any = contratos.filter((obj, i) => contratos.indexOf(obj) === i);

    let where: Prisma.fornecedoresWhereInput | null;
    if (idsFornecedores.length != 0) {
      where = {
        id: {
          in: idsFornecedores,
        },
        // ativo: true,
      };
    } else {
      where = {
        ativo: true,
      };
    }

    const fornecedores = await prisma.fornecedores.findMany({
      where,
      orderBy: { id: 'desc' },
    });

    return {
      data: {
        fornecedores,
      },
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter fornecedores.`,
    };
  }
};

export const criarContrato = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.CRIAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = criarContratoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    idFornecedor,
    processo,
    processoAno,
    valor,
    nomeGestor,
    cpfGestor,
    condPagamento,
    numAf,
    dataInicio,
    dataFinal,
  } = parsedParams.data;

  let idContrato = 0;

  try {
    const ip = (await obterIpUsuarioConectado()).data;

    await prisma.$transaction(async (tx) => {
      const criarContratoReq = await tx.contratos.create({
        data: {
          exercicio: resultPermissao.exercicio!,
          idFornecedor,
          processo,
          processoAno,
          valor,
          nomeGestor,
          cpfGestor,
          condPagamento,
          numAf,
          dataInicio: dataInicio || '',
          dataFinal: dataFinal || '',
        },
      });

      if (!criarContratoReq) {
        throw new Error('Erro ao Criar o contrato');
      }

      await tx.contratos_audit.create({
        data: {
          idContrato: criarContratoReq.id,
          idFornecedor,
          processo,
          processoAno,
          nomeGestor,
          cpfGestor,
          condPagamento,
          numAf,
          dataInicio: dataInicio || '',
          dataFinal: dataFinal || '',
          valor,
          idUsuario: resultPermissao.idUsuario!,
          ip,
          acao: AuditoriaMovimentoContratos.CRIAR_CONTRATO,
        },
      });

      idContrato = criarContratoReq.id;
    });
    revalidatePath('/movimento/contratos');
    return {
      data: {
        idContrato,
      },
    };
  } catch (e: any) {
    console.log(e.message);
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === 'P2002') {
        return {
          error: `Contrato já existe.`,
        };
      }
    }
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Não foi possível Criar o Contrato`,
    };
  }
};

export const obterContrato = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.ACESSAR,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { id } = parsedParams.data;

  try {
    const contrato = await prisma.contratos.findUnique({
      where: { id },
      include: { fornecedor: true },
    });

    if (!contrato) {
      return {
        error: 'Contrato não encontrado.',
      };
    }

    return {
      data: {
        ...contrato,
        valor: contrato.valor.toNumber(),
      },
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter contrato.`,
    };
  }
};

export const editarContrato = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
    retornarExercicioUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Erro ao obter o exercício do usuário conectado.',
    };
  }

  const parsedParams = criarContratoSchema.safeParse(params);

  if (!parsedParams.success) {
    console.log(parsedParams.error);
    return {
      error: 'Parâmetros inválidos.',
    };
  }

  const {
    id,
    idFornecedor,
    processo,
    processoAno,
    valor,
    nomeGestor,
    cpfGestor,
    condPagamento,
    numAf,
    dataInicio,
    dataFinal,
  } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    console.log(processoAno);
    await prisma.$transaction(async (tx) => {
      const contrato = await tx.contratos.update({
        data: {
          idFornecedor,
          processo,
          processoAno,
          valor,
          nomeGestor,
          cpfGestor,
          condPagamento,
          numAf,
          dataInicio: dataInicio || '',
          dataFinal: dataFinal || '',
        },
        where: {
          id,
        },
      });

      if (!contrato) {
        throw new Error('Erro ao Alterar Contrato');
      }

      await tx.contratos_audit.create({
        data: {
          idContrato: id,
          idFornecedor,
          processo,
          processoAno,
          nomeGestor,
          cpfGestor,
          condPagamento,
          numAf,
          dataInicio: dataInicio || '',
          dataFinal: dataFinal || '',
          valor,
          idUsuario: resultPermissao.idUsuario!,
          ip,
          acao: AuditoriaMovimentoContratos.EDITAR_CONTRATO,
        },
      });
    });
    revalidatePath('/movimento/contratos');
    return {
      data: {
        idContrato: id,
      },
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao alterar contrato.`,
    };
  }
};

export const obterContratoParaRelatorio = async (params: unknown) => {
  const parsedParams = imprimirContratoSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos.',
    };
  }
  const { id, bearer, incluirGestor } = parsedParams.data;
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.ACESSAR,
    retornarExercicioUsuario: true,
    bearer,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.exercicio) {
    return {
      error: 'Exercício não encontrado.',
    };
  }

  try {
    const contrato = await prisma.contratos.findUnique({
      where: { id },
      include: {
        fornecedor: true,
      },
    });

    if (!contrato) {
      return {
        error: 'Contrato não encontrado.',
      };
    }

    return {
      data: {
        ...contrato,
        incluirGestor,
        gerente: resultPermissao.gerente,
      },
    };
  } catch (e) {
    console.log(e);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao obter contrato.`,
    };
  }
};

export const ativarContrato = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.contratos.update({
        where: {
          id: id,
        },
        data: {
          ativo: true,
        },
      });

      await tx.contratos_audit.create({
        data: {
          idContrato: id,
          idFornecedor: result.idFornecedor,
          processo: result.processo,
          processoAno: result.processoAno,
          nomeGestor: result.nomeGestor,
          cpfGestor: result.cpfGestor,
          condPagamento: result.condPagamento,
          numAf: result.numAf,
          dataInicio: result.dataInicio || '',
          dataFinal: result.dataFinal || '',
          valor: result.valor,
          idUsuario: resultPermissao.idUsuario!,
          ip,
          acao: AuditoriaMovimentoContratos.ATIVAR_CONTRATO,
        },
      });

      if (!result) {
        throw new Error('Erro atualizar (Ativar) contrato');
      }
    });

    revalidatePath('/movimento/contratos');
    return {
      data: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: e.message || `Erro ao ativar contrato.`,
    };
  }
};

export const desativarContrato = async (params: unknown) => {
  const parametrosPermissao: z.infer<typeof permissaoSchema> = {
    modulo: Modulos.MOVIMENTO_CONTRATO,
    permissao: Permissoes.ALTERAR,
    retornarIdUsuario: true,
  };

  const resultPermissao = await temPermissao(parametrosPermissao);

  if (!resultPermissao) {
    return {
      error: 'Não foi possível verificar as permissões do usuário.',
    };
  }

  if (resultPermissao.error) {
    return {
      error: resultPermissao.error,
    };
  }

  if (!resultPermissao.temPermissao) {
    return {
      error: 'Usuário sem permissão.',
    };
  }

  if (!resultPermissao.idUsuario) {
    return {
      error: 'Erro ao obter o id do usuário conectado.',
    };
  }

  const parsedParams = idSchema.safeParse(params);

  if (!parsedParams.success) {
    return {
      error: 'Parâmetros inválidos',
    };
  }

  const { id } = parsedParams.data;

  try {
    const ip = (await obterIpUsuarioConectado()).data;
    await prisma.$transaction(async (tx) => {
      const result = await tx.contratos.update({
        where: {
          id: id,
        },
        data: {
          ativo: false,
        },
      });

      await tx.contratos_audit.create({
        data: {
          idContrato: id,
          idFornecedor: result.idFornecedor,
          processo: result.processo,
          processoAno: result.processoAno,
          nomeGestor: result.nomeGestor,
          cpfGestor: result.cpfGestor,
          condPagamento: result.condPagamento,
          numAf: result.numAf,
          dataInicio: result.dataInicio || '',
          dataFinal: result.dataFinal || '',
          valor: result.valor,
          idUsuario: resultPermissao.idUsuario!,
          ip,
          acao: AuditoriaMovimentoContratos.DESATIVAR_CONTRATO,
        },
      });

      if (!result) {
        throw new Error('Erro atualizar (Desativar) contrato');
      }
    });

    revalidatePath('/movimento/contratos');
    return {
      data: true,
    };
  } catch (e: any) {
    console.log(e.message);
    const auditData: z.infer<typeof auditoriaErroSchema> = {
      erro: JSON.stringify(e),
      modulo: Modulos.MOVIMENTO_CONTRATO,
    };
    await inserirErroAudit(auditData);
    return {
      error: `Erro ao desativar contrato.`,
    };
  }
};

// Funções específicas para relatórios de AF - baseado no sistema legado
export const obterContratoPorEmpenho = async (params: {
  exercicio: number;
  empenho: number;
  numeroAF?: number;
}) => {
  try {
    const contrato = await prisma.contratos.findFirst({
      where: {
        empenhos: {
          some: {
            exercicio: params.exercicio,
            numero: params.empenho,
          },
        },
      },
    });

    return { data: contrato };
  } catch (e) {
    console.error('Erro ao obter contrato por empenho:', e);
    return { error: 'Erro ao obter contrato.' };
  }
};

export const listarEmpenhosDoContrato = async (idContrato: number) => {
  try {
    const empenhos = await prisma.empenhos.findMany({
      where: {
        idContrato: idContrato,
        ativo: true,
      },
      select: {
        id: true,
        numero: true,
        exercicio: true,
      },
      orderBy: {
        numero: 'asc',
      },
    });

    return { data: empenhos };
  } catch (e) {
    console.error('Erro ao listar empenhos do contrato:', e);
    return { error: 'Erro ao listar empenhos do contrato.' };
  }
};
