'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { listarProtocolosParaCombobox } from '@/lib/database/relatorios/protocolos';

interface ProtocoloOption {
  id: number;
  numero: number;
  exercicio: number;
  dataAbertura: string;
  resumo: string | null;
  status: number;
}

interface ComboboxSelecionarProtocoloProps {
  onProtocoloChange?: (protocolo: ProtocoloOption | null) => void;
}

export function ComboboxSelecionarProtocolo({
  onProtocoloChange,
}: ComboboxSelecionarProtocoloProps) {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState<string>('');
  const [protocolos, setProtocolos] = useState<ProtocoloOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const searchParams = useSearchParams();

  useEffect(() => {
    const fetchProtocolos = async () => {
      try {
        setLoading(true);
        const result = await listarProtocolosParaCombobox();
        const data = result.data || [];
        setProtocolos(data);

        // Check for protocolo ID in URL params
        const protocoloId = searchParams?.get('protocolo');
        if (protocoloId) {
          const selectedProtocolo = data.find(
            (p: ProtocoloOption) => p.id === parseInt(protocoloId)
          );
          if (selectedProtocolo) {
            setValue(selectedProtocolo.id.toString());
            onProtocoloChange?.(selectedProtocolo);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    fetchProtocolos();
  }, [searchParams, onProtocoloChange]);

  const selectedProtocolo = protocolos.find(
    (protocolo) => protocolo.id.toString() === value
  );

  return (
    <div className='space-y-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
          >
            {loading ? (
              <Skeleton className='h-4 w-[200px]' />
            ) : error ? (
              'Erro ao carregar protocolos'
            ) : selectedProtocolo ? (
              `Protocolo #${selectedProtocolo.numero}/${selectedProtocolo.exercicio}`
            ) : (
              'Selecione um protocolo...'
            )}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-full p-0'>
          <Command>
            <CommandInput placeholder='Buscar protocolo...' />
            <CommandEmpty>Nenhum protocolo encontrado.</CommandEmpty>
            <CommandGroup>
              {protocolos.map((protocolo) => (
                <CommandItem
                  key={protocolo.id}
                  onSelect={() => {
                    setValue(protocolo.id.toString());
                    setOpen(false);
                    onProtocoloChange?.(protocolo);
                    // Update URL without navigation
                    const newParams = new URLSearchParams(
                      searchParams?.toString() || ''
                    );
                    newParams.set('protocolo', protocolo.id.toString());
                    window.history.pushState(
                      {},
                      '',
                      `?${newParams.toString()}`
                    );
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      value === protocolo.id.toString()
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  <div className='flex flex-col'>
                    <span className='font-medium'>
                      Protocolo #{protocolo.numero}/{protocolo.exercicio}
                    </span>
                    <span className='text-muted-foreground text-sm'>
                      {protocolo.resumo}
                    </span>
                    <span className='text-muted-foreground text-xs'>
                      {new Date(protocolo.dataAbertura).toLocaleDateString(
                        'pt-BR'
                      )}{' '}
                      - {protocolo.status}
                    </span>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
