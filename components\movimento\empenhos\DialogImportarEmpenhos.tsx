'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import ImportarEmpenhos from './ImportarEmpenhos';
import { type ImportarResultado } from '@/lib/database/movimento/empenhos/importar';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Upload } from 'lucide-react';

export default function DialogImportarEmpenhos() {
  const [showImportDialog, setShowImportDialog] = useState(false);
  const handleImportComplete = (resultado: ImportarResultado) => {
    // Auto-close dialog on successful import
    if (resultado.sucesso) {
      setTimeout(() => {
        setShowImportDialog(false);
      }, 2000);
    }
  };

  return (
    <>
      <Button
        variant='outline'
        onClick={() => setShowImportDialog(true)}
        className='flex items-center gap-2'
      >
        <Upload className='h-4 w-4' />
        Importar CSV
      </Button>
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto'>
          <DialogHeader>
            <DialogTitle>Importar Empenhos em Massa</DialogTitle>
            <DialogDescription>
              Importe múltiplos empenhos a partir de um arquivo CSV formatado
            </DialogDescription>
          </DialogHeader>
          <ImportarEmpenhos
            onImportComplete={handleImportComplete}
            onClose={() => setShowImportDialog(false)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
