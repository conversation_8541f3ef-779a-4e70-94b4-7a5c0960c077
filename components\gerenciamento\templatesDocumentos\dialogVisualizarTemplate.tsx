'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download, FileText } from 'lucide-react';
import { sanitizarHtmlTemplate } from '@/lib/utils/seguranca';
import { gerarPreviewTemplate } from '@/lib/utils/conversorPdf';
import { toast } from 'sonner';

interface DialogVisualizarTemplateProps {
  template: {
    id: number;
    nome: string;
    descricao: string | null;
    conteudoHtml: string;
    dataCriacao: Date;
  };
  children: React.ReactNode;
}

export function DialogVisualizarTemplate({
  template,
  children,
}: DialogVisualizarTemplateProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);

  const handleGerarPreview = async () => {
    setIsGeneratingPdf(true);
    try {
      const result = await gerarPreviewTemplate(template.id);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.data) {
        // Criar URL para download do PDF
        const url = URL.createObjectURL(result.data);
        const link = document.createElement('a');
        link.href = url;
        link.download = `preview-${template.nome}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        toast.success('Preview gerado com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao gerar preview:', error);
      toast.error('Erro ao gerar preview do template');
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='max-h-[80vh] max-w-4xl overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            {template.nome}
          </DialogTitle>
          <DialogDescription>
            {template.descricao || 'Sem descrição'}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div className='text-muted-foreground text-sm'>
              Criado em: {template.dataCriacao.toLocaleDateString('pt-BR')}
            </div>
            <Button
              onClick={handleGerarPreview}
              disabled={isGeneratingPdf}
              variant='outline'
              size='sm'
            >
              <Download className='mr-2 h-4 w-4' />
              {isGeneratingPdf ? 'Gerando...' : 'Preview PDF'}
            </Button>
          </div>

          <div className='min-h-[400px] rounded-md border bg-white p-4'>
            <div
              className='prose max-w-none'
              dangerouslySetInnerHTML={{
                __html: sanitizarHtmlTemplate(template.conteudoHtml),
              }}
            />
          </div>

          <div className='text-muted-foreground text-xs'>
            <strong>Variáveis disponíveis:</strong> {'{'}
            {'{'}`NOME_USUARIO`{'}'}
            {'}'}, {'{'}
            {'{'}`DATA_ATUAL`{'}'}
            {'}'}, {'{'}
            {'{'}`NUMERO_DOCUMENTO`{'}'}
            {'}'}, {'{'}
            {'{'}`ANO_ATUAL`{'}'}
            {'}'}, {'{'}
            {'{'}`MES_ATUAL`{'}'}
            {'}'}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
