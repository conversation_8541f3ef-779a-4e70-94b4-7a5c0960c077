'use client';

import { Editor } from '@tinymce/tinymce-react';
import { useRef, useState, useEffect } from 'react';
import { EditorTemplateSimples } from './editorTemplateSimples';
import dynamic from 'next/dynamic';

interface EditorTemplateProps {
  value: string;
  onChange: (content: string) => void;
  height?: number;
  disabled?: boolean;
}

// Componente interno que só renderiza no cliente
function TinyMCEEditor({
  value,
  onChange,
  height = 400,
  disabled = false,
}: EditorTemplateProps) {
  const editorRef = useRef<any>(null);
  const [useFallback, setUseFallback] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    // Verificar se o TinyMCE está disponível
    const checkTinyMCE = () => {
      if (typeof window !== 'undefined' && (window as any).tinymce) {
        // TinyMCE carregado com sucesso
      } else {
        // Se não carregar em 5 segundos, usar fallback
        setTimeout(() => {
          if (!(window as any).tinymce) {
            setUseFallback(true);
          }
        }, 5000);
      }
    };

    checkTinyMCE();
  }, []);

  // Não renderizar no servidor para evitar hydration mismatch
  if (!isMounted) {
    return (
      <div
        className='flex items-center justify-center rounded-md border bg-gray-50'
        style={{ height: `${height}px` }}
      >
        <span className='text-gray-500'>Carregando editor...</span>
      </div>
    );
  }

  // Se deve usar o editor simples como fallback
  if (useFallback) {
    return (
      <EditorTemplateSimples
        value={value}
        onChange={onChange}
        height={height}
        disabled={disabled}
      />
    );
  }

  return (
    <div className='rounded-md border'>
      <Editor
        // Configuração para versão self-hosted conforme documentação oficial
        tinymceScriptSrc='/tinymce/tinymce.min.js'
        licenseKey='gpl'
        onInit={(evt, editor) => {
          editorRef.current = editor;
        }}
        onLoadContent={() => {
          // TinyMCE carregado com sucesso
        }}
        value={value}
        onEditorChange={onChange}
        disabled={disabled}
        init={{
          height: height,
          menubar: false,
          promotion: false,
          branding: false,
          // Plugins básicos para templates de documentos
          plugins: [
            'advlist',
            'autolink',
            'lists',
            'link',
            'charmap',
            'preview',
            'anchor',
            'searchreplace',
            'visualblocks',
            'code',
            'fullscreen',
            'insertdatetime',
            'table',
            'help',
            'wordcount',
          ],
          toolbar:
            'undo redo | blocks | ' +
            'bold italic forecolor | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'table link | variavel_nome variavel_data variavel_numero | ' +
            'removeformat | help',
          content_style:
            'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; padding: 10px; }',
          language: 'pt_BR',
          // Configurações de segurança
          valid_elements:
            'p,br,strong,em,u,h1,h2,h3,h4,h5,h6,ul,ol,li,table,thead,tbody,tr,td,th,div,span',
          invalid_elements: 'script,object,embed,form,input,button',
          setup: (editor) => {
            // Adicionar botões para variáveis comuns
            editor.ui.registry.addButton('variavel_nome', {
              text: 'Nome',
              tooltip: 'Inserir variável de nome do usuário',
              onAction: () => {
                editor.insertContent('{{NOME_USUARIO}}');
              },
            });

            editor.ui.registry.addButton('variavel_data', {
              text: 'Data',
              tooltip: 'Inserir variável de data atual',
              onAction: () => {
                editor.insertContent('{{DATA_ATUAL}}');
              },
            });

            editor.ui.registry.addButton('variavel_numero', {
              text: 'Número',
              tooltip: 'Inserir variável de número do documento',
              onAction: () => {
                editor.insertContent('{{NUMERO_DOCUMENTO}}');
              },
            });
          },
          // Configurações adicionais para melhor experiência
          resize: false,
          statusbar: false,
          // Configuração de paste para manter formatação simples
          paste_as_text: false,
          paste_auto_cleanup_on_paste: true,
          paste_remove_styles: true,
          paste_remove_styles_if_webkit: true,
          paste_strip_class_attributes: 'all',
        }}
      />
    </div>
  );
}

// Componente principal exportado com dynamic import para evitar SSR
export const EditorTemplate = dynamic(() => Promise.resolve(TinyMCEEditor), {
  ssr: false,
  loading: () => (
    <div className='flex h-96 items-center justify-center rounded-md border bg-gray-50'>
      <span className='text-gray-500'>Carregando editor...</span>
    </div>
  ),
});
